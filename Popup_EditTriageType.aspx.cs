using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditTriageType : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string TriageTypeId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.TRIAGE_TYPE_ID_KEY]))
			{
				this.TriageTypeId = Request.Params[DieboldConstants.TRIAGE_TYPE_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTriageType", this.TriageTypeId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.triageTypeField.Text = DataFormatter.getString(row, "TriageTypeName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;
		if (string.IsNullOrEmpty(TriageTypeId)) {
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertTriageType", this.triageTypeField.Text.Trim());
		}
		else {
			SqlHelper.ExecuteNonQuery("RPT_UpdateTriageType", Convert.ToInt32(this.TriageTypeId), this.triageTypeField.Text.Trim(), this.isActive.Checked);
		}

		if (result == 0) {
			this.ErrorLabel.Visible = true;
		}
		else {
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditTriageType.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditTriageType.aspx';CloseRadWindow(); ", true);
		}
	}
}
