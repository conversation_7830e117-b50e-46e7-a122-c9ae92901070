using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class NewReportWizard_Pareto : System.Web.UI.Page
{
    private string[] SupressedDimensions = new string[] { "Cell", "Session", "Measures", "Time", "Test Session", "Censoring", "Test Location", "Device",
        "Metric", "Metric Distribution", "Metric Values", "Engineering Field", "Engineering Distribution", "Engineering Values", "Engineering Note Number", "Engineering Index Number", 
		"Distribution", "Distribution Values", "Setting", "Setting 2", "Setting 3", "Sensor", "File Number", "Media Number", "Command Number" };

    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            this.RepInfo = Utility.GetReportInfoFromTransfer();
            if (this.RepInfo == null)
                Response.Redirect("popuperror.aspx");

            reportLabel.Text = this.RepInfo.ReportName;
            reportTypeLabel.Text = this.RepInfo.ReportTypeName;
            totalCheck.Checked = this.RepInfo.ParetoInfo.IncludeTotal;
            splitByCell.Checked = this.RepInfo.ParetoInfo.SplitByCell;
            statisticValue.Checked = this.RepInfo.ParetoInfo.ByStatisticValue;

            groupingList.DataSource = Utility.GetDimensionGroupByList();
            groupingList.DataBind();

            rateTypeList.DataSource = Utility.GetParetoRateTypeList();
            rateTypeList.DataBind();

            if (this.RepInfo.ProgressInfo.RateTypeId1 >= 0)
                rateTypeList.SelectedValue = ((int)this.RepInfo.ProgressInfo.RateTypeId1).ToString();
            else
                rateTypeList.SelectedIndex = 0;

            formatTypeList.SelectedValue = this.RepInfo.ProgressInfo.Format1;

            if (this.RepInfo.ProgressInfo.WorkLoad1 != decimal.MinValue)
                workLoadField.Text = this.RepInfo.ProgressInfo.WorkLoad1.ToString("0.#");
            else
                workLoadField.Text = null;

            if (this.RepInfo.ParetoInfo.GroupingId != 0)
                groupingList.SelectedValue = ((int)this.RepInfo.ParetoInfo.GroupingId).ToString();
            else
                groupingList.SelectedIndex = 1;

            if (this.RepInfo.ParetoInfo.MaxColumns != 0)
                this.maxColumnsField.Text = ((int)this.RepInfo.ParetoInfo.MaxColumns).ToString();

            this.minYScale.Text = this.RepInfo.MinYScale;
            this.maxYScale.Text = this.RepInfo.MaxYScale;

            if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
            {
                this.dimensionSel.PopulateDimensions(true, SupressedDimensions);
                this.splitByCell.Text = "Split Columns By Cell";
            }
            else
            {
                this.dimensionSel.PopulateDimensions(false, SupressedDimensions);
                this.splitByCell.Text = "Split Columns By Device";
            }

            this.dimensionSel.DimensionName = this.RepInfo.DimensionName;
            this.dimensionSel.CheckedDimensionMembers = this.RepInfo.DimensionMembers;
            this.dimensionSel.UpdateTreeDisplay();

            dimensionSel.Focus();
        }
    }

    protected void ValidateRate(object sender, ServerValidateEventArgs e)
    {
        e.IsValid = true;
        if (e != null && !string.IsNullOrEmpty(rateTypeList.SelectedValue))
        {
            if (ReportHelper.RateTypeEnum.BY_STAT_VALUE == ((ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue)))
            {
                if (string.IsNullOrEmpty(this.dimensionSel.DimensionName) || !this.dimensionSel.DimensionName.StartsWith("[Statistic]."))
                    e.IsValid = false;
            }
        }
    }

    protected void rateTypeList_selectedIndexChanged(object sender, EventArgs e)
    {
        workLoadField.Enabled = rateTypeList.SelectedItem.Text.ToLower().Contains("workload");

        if (!workLoadField.Enabled)
            workLoadField.Text = null;
    }

    protected void ValidateWorkLoad(object sender, ServerValidateEventArgs e)
    {
        e.IsValid = true;
        if (workLoadField.Enabled)
        {
            try
            {
                decimal val = Convert.ToDecimal(workLoadField.Text);
                if (val <= 0)
                    e.IsValid = false;
            }
            catch
            {
                e.IsValid = false;
            }
        }
    }

    protected void NextButton_Click(object sender, EventArgs e)
    {
        if (Page.IsValid)
        {

            if (statisticValue.Checked)
            {
                if (dimensionSel.DimensionName == null || dimensionSel.DimensionName.StartsWith("[Statistic]") == false)
                {
                    ScriptManager.RegisterStartupScript(this, typeof(NewReportWizard_Pareto), "ByValue", "alert('The Display Statistic Value Totals feature is only available when selecting the Statistic dimension.');", true);
                    return;
                }
            }

            //Set Page Values
			this.RepInfo.ParetoInfo.IncludeTotal = totalCheck.Checked;
			this.RepInfo.ParetoInfo.SplitByCell = splitByCell.Checked;
            this.RepInfo.ParetoInfo.ByStatisticValue = (statisticValue.Enabled && statisticValue.Checked);

			this.RepInfo.DimensionName = dimensionSel.DimensionName;
			this.RepInfo.DimensionMembers = dimensionSel.CheckedDimensionMembers;

            if (!string.IsNullOrEmpty(rateTypeList.SelectedValue))
                this.RepInfo.ProgressInfo.RateTypeId1 = (ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue);
            else
                this.RepInfo.ProgressInfo.RateTypeId1 = 0;

            this.RepInfo.ProgressInfo.Format1 = formatTypeList.SelectedValue;

            if (!String.IsNullOrEmpty(workLoadField.Text))
                this.RepInfo.ProgressInfo.WorkLoad1 = Convert.ToDecimal(workLoadField.Text.Trim());
            else
                this.RepInfo.ProgressInfo.WorkLoad1 = decimal.MinValue;

            if (!string.IsNullOrEmpty(groupingList.SelectedValue))
				this.RepInfo.ParetoInfo.GroupingId = (ReportHelper.DateGroupingEnum) Convert.ToInt32(groupingList.SelectedValue);

			if (!string.IsNullOrEmpty(this.maxColumnsField.Text))
				this.RepInfo.ParetoInfo.MaxColumns = Convert.ToInt32(maxColumnsField.Text);
			else
				this.RepInfo.ParetoInfo.MaxColumns = 0;

            this.RepInfo.MinYScale = this.minYScale.Text;
            this.RepInfo.MaxYScale = this.maxYScale.Text;

            Utility.SetReportInfoForTransfer(this.RepInfo);
            Response.Redirect("NewReportWizard_Format.aspx");
        }
    }
}
