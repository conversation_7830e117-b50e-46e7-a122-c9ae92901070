using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class DashReportInfo : ISerializable
{
	public int ReportId = 0;
	public bool DisableLegend = false;

	// Default constructor.
	public DashReportInfo() { }

	// Deserialization constructor.
	public DashReportInfo(SerializationInfo info, StreamingContext context)
	{
		ReportId = (int)info.GetValue("r", typeof(int));
		DisableLegend = (bool)info.GetValue("d", typeof(bool));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("r", ReportId);
		info.AddValue("d", DisableLegend);
	}
}

