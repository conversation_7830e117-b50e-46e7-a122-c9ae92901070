﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditQueryReport.aspx.cs" Inherits="EditQueryReport" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Query Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Edit Query Report</div>
					<div id="NewSnapshotArea" runat="server">
						<table border="0" cellpadding="0" cellspacing="13" width="100%">
							<tr>
								<td class="rowHeading" style="width:150px;">Report Type Name</td>
								<td>
									<asp:textbox id="reportTypeName" runat="server" width="250"></asp:textbox>
									<asp:requiredfieldvalidator id="v0" runat="server" controltovalidate="reportTypeName" errormessage="* Required"></asp:requiredfieldvalidator>
								</td>
							</tr>
                            <tr>
								<td class="rowHeading" style="width:150px;">Stored Procedure Name</td>
								<td>
									<asp:textbox id="storedProcName" runat="server" width="250"></asp:textbox>
									<asp:requiredfieldvalidator id="v1" runat="server" controltovalidate="storedProcName" errormessage="* Required"></asp:requiredfieldvalidator>
								</td>
							</tr>
                            <tr>
								<td class="rowHeading" style="width:150px;">Support Filter: Include Level 3 Notes</td>
								<td>
									<asp:checkbox id="allowFilterLevel3Notes" runat="server" />
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Support Filter: Field Type</td>
								<td>
                                    <asp:checkbox id="allowFilterFieldType" runat="server" />
								</td>
							</tr>
						</table>
						
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:80px;" class="leftPad" id="SaveBtnCell" runat="server"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
								<td style="width:90px;"><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to cancel without saving?');" href="QueryReports.aspx">Cancel</a></div></td>
								<td><div class="cancelButton"><asp:linkbutton runat="server" causesvalidation="false" visible="false" onclick="DeleteButton_Click" id="deleteBtn">Delete</asp:linkbutton></div></td>
							</tr>
						</table>
						<br /><br />
					</div>
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

