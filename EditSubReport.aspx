﻿<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="EditSubReport.aspx.cs" Inherits="EditSubReport" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<table width="100%" border="0" cellpadding="0" cellspacing="10">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Edit SubReport</td>
					<td class="widgetTop">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-top:20px;">
				<div class="rowHeading">SubReport Name:</div>
				<br />
				<asp:textbox id="subReportNameField" runat="server" cssclass="entryControl" style="width:250px;"></asp:textbox>
				<asp:requiredfieldvalidator id="val1" runat="server" display="dynamic" controltovalidate="subReportNameField" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
				<br />&nbsp;<br />
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save</asp:linkbutton></div></td>
						<td><div class="cancelButton"><a href="javascript:CloseRadWindow();">Cancel</a></div></td>
					</tr>
				</table>
				<br />&nbsp;<br />
			</div>
		</td>
	</tr>
</table>	

</asp:Content>

