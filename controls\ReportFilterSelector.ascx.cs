using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Microsoft.AnalysisServices.AdomdClient;

public partial class controls_ReportFilterSelector : System.Web.UI.UserControl
{
	private bool IsLegacy
	{
		get { if (this.ViewState["il"] == null) return false; else return (bool)this.ViewState["il"]; }
		set { this.ViewState["il"] = value; }
	}

	private bool ShowEditableTree
	{
		get { if (this.ViewState["set"] == null) return true; else return (bool)this.ViewState["set"]; }
		set { this.ViewState["set"] = value; }
	}

	private Dictionary<string, List<string>> CheckedDimensionMembers
	{
	    get { if (this.ViewState["cdm"] != null) return (Dictionary<string, List<string>>)this.ViewState["cdm"]; else return null; }
	    set { this.ViewState["cdm"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
        if (!this.IsPostBack)
        {
            dimensionCombo.Focus();
        }
		this.PreRender += new EventHandler(controls_DimensionSelector_PreRender);
	}

	private void controls_DimensionSelector_PreRender(object sender, EventArgs e)
	{
		if (this.ShowEditableTree)
		{
			memberTree.Visible = true;
			selectedItemsTree.Visible = false;
			this.clearAllBtn.Visible = true;
			this.checkAllBtn.Visible = true;
			this.editButton.Visible = false;
			this.viewSelectedButton.Visible = true;
			this.dimensionCombo.Visible = true;
		}
		else
		{
			memberTree.Visible = false;
			selectedItemsTree.Visible = true;
			this.clearAllBtn.Visible = false;
			this.checkAllBtn.Visible = false;
			this.editButton.Visible = true;
			this.viewSelectedButton.Visible = false;
			this.dimensionCombo.Visible = false;
		}
	}

	public void PopulateDimensions(bool isLegacy, string[] includedLevels)
	{
		this.IsLegacy = isLegacy;
		this.memberTree.Nodes.Clear();
		this.selectedItemsTree.Nodes.Clear();

		dimensionCombo.ClearSelection();
		dimensionCombo.Items.Clear();
		dimensionCombo.Text = null;
		dimensionCombo.Items.Add(new RadComboBoxItem("Select...", ""));

		if (includedLevels != null)
		{
			foreach (string levelName in includedLevels)
			{
				Level level = OLAPHelper.FindLevelByUniqueName(isLegacy, levelName);
				if (level != null)
					dimensionCombo.Items.Add(new RadComboBoxItem(level.ParentHierarchy.ParentDimension.Name, level.UniqueName));
			}
		}
	}

	private void UpdateSelectedValues(string DimUniqueName)
	{
		if (!string.IsNullOrEmpty(DimUniqueName))
		{
			List<string> tempList = new List<string>();

			if (this.CheckedDimensionMembers == null)
				this.CheckedDimensionMembers = new Dictionary<string, List<string>>();

			if (this.CheckedDimensionMembers.ContainsKey(DimUniqueName))
				tempList = this.CheckedDimensionMembers[DimUniqueName];

			//Iterate of all nodes, remove any previous values and add in all checked
			foreach (RadTreeNode node in memberTree.GetAllNodes())
			{
                bool isInList = tempList.Contains(UnescapeUniqueName(node.Value));
				if (isInList && !node.Checked)
					tempList.Remove(UnescapeUniqueName(node.Value));
                else if (node.Checked && !isInList)
					tempList.Add(UnescapeUniqueName(node.Value));
			}

			if (tempList.Count > 0)
			{
				if (this.CheckedDimensionMembers.ContainsKey(DimUniqueName))
					this.CheckedDimensionMembers[DimUniqueName] = tempList;
				else
					this.CheckedDimensionMembers.Add(DimUniqueName, tempList);
			}
		}
	}

	public Dictionary<string, List<string>> GetSelectedReportFilters()
	{
		UpdateSelectedValues(UnescapeUniqueName(dimensionCombo.SelectedValue));
		return this.CheckedDimensionMembers;
	}

	public void SetSelectedReportFilters(Dictionary<string, List<string>> selectedMembers)
	{
		this.CheckedDimensionMembers = selectedMembers;
		
		if (selectedMembers != null)
			this.ShowEditableTree = false;

		BuildViewTree();
	}

	protected void OnDimensionChanged(object sender, RadComboBoxSelectedIndexChangedEventArgs e)
	{
		this.ShowEditableTree = true;

		if (!string.IsNullOrEmpty(e.OldValue))
			UpdateSelectedValues(UnescapeUniqueName(e.OldValue));
		
		UpdateTreeDisplay();
	}

	public void UpdateTreeDisplay()
	{
		BuildEditTree();
		BuildViewTree();
	}

	private void BuildEditTree()
	{
		this.memberTree.Nodes.Clear();

		if (!string.IsNullOrEmpty(dimensionCombo.SelectedValue))
		{
			string unescapedDimName = UnescapeUniqueName(dimensionCombo.SelectedValue);

			try
			{
				Level level = OLAPHelper.FindLevelByUniqueName(this.IsLegacy, unescapedDimName);
				PopulateMembers(this.memberTree.Nodes, level);
			}
			catch
			{
				try
				{
					Hierarchy hier = OLAPHelper.FindHierarchyByUniqueName(this.IsLegacy, unescapedDimName);
					PopulateMembers(this.memberTree.Nodes, hier.Levels[1]);
				}
				catch
				{
					Dimension dim = OLAPHelper.FindDimensionByUniqueName(this.IsLegacy, unescapedDimName);
					PopulateMembers(this.memberTree.Nodes, dim.Hierarchies[0].Levels[1]);
				}
			}
		}
	}

	private Dimension FindDimensionFromName(string uniqueName)
	{
		Dimension retVal = null;

		if (!string.IsNullOrEmpty(uniqueName))
		{
			try
			{
				Level level = OLAPHelper.FindLevelByUniqueName(this.IsLegacy, uniqueName);
				retVal = level.ParentHierarchy.ParentDimension;
			}
			catch
			{
				try
				{
					Hierarchy hier = OLAPHelper.FindHierarchyByUniqueName(this.IsLegacy, uniqueName);
					retVal = hier.ParentDimension;
				}
				catch
				{
					retVal = OLAPHelper.FindDimensionByUniqueName(this.IsLegacy, uniqueName);
				}
			}
		}
		return retVal;
	}

	private void BuildViewTree()
	{
		this.selectedItemsTree.Nodes.Clear();


		// Iterate over dimensions
		foreach (RadComboBoxItem dimItem in dimensionCombo.Items)
		{
			string dimUniqueName = UnescapeUniqueName(dimItem.Value);
			Dimension dim = FindDimensionFromName(dimUniqueName);
			if (dim != null)
			{
				RadTreeNode node = new RadTreeNode();
				node = new RadTreeNode(dim.Name);
				node.Value = EscapeUniqueName(dim.UniqueName);
				node.Expanded = true;
				node.Checkable = false;
				selectedItemsTree.Nodes.Add(node);

				// Iterate over CheckedDimensionMembers for the current dimension
				if (this.CheckedDimensionMembers.ContainsKey(dimUniqueName))
				{
					List<string> members = this.CheckedDimensionMembers[dimUniqueName];
					foreach (string memUniqueName in members)
					{
						try
						{
							Member mem = OLAPHelper.FindMemberByUniqueName(this.IsLegacy, memUniqueName);
							AddToViewTree(mem, true);
						}
						catch
						{
							//do nothing, don't add to view tree, and don't blow up!
						}	
					}
				}

				// If none - add message
				if (node.Nodes.Count == 0)
				{
					RadTreeNode child = new RadTreeNode();
					child = new RadTreeNode("No filters selected");
					child.Expanded = true;
					child.Checkable = false;
					node.Nodes.Add(child);
				}
			}
		}
	}

	private RadTreeNode AddToViewTree(Member mem, bool ensureChecked)
	{
		RadTreeNode node = selectedItemsTree.FindNodeByValue(EscapeUniqueName(mem.UniqueName));
		if (node == null)
		{
			node = new RadTreeNode(mem.Name);
			node.Value = EscapeUniqueName(mem.UniqueName);
			node.Expanded = true;
			if (mem.Parent != null && mem.Parent.Type != MemberTypeEnum.All)
			{
				RadTreeNode parentNode = AddToViewTree(mem.Parent, false);
				parentNode.Nodes.Add(node);
			}
			else
			{
				RadTreeNode parentNode = selectedItemsTree.FindNodeByValue(EscapeUniqueName(mem.ParentLevel.ParentHierarchy.ParentDimension.UniqueName));
				if(parentNode != null)
					parentNode.Nodes.Add(node);
			}
		}

		if(ensureChecked)
			node.Checked = true;
	
		return node;
	}

    private void PopulateMembers(RadTreeNodeCollection nodes, Level level)
    {
		string dimUniqueName = UnescapeUniqueName(dimensionCombo.SelectedValue);
        foreach (Member mem in level.GetMembers())
        {
            RadTreeNode node = new RadTreeNode(mem.Name);
            node.Value = EscapeUniqueName(mem.UniqueName);

			if (this.CheckedDimensionMembers.ContainsKey(dimUniqueName))
			{
				List<string> memberList = this.CheckedDimensionMembers[dimUniqueName];
				if (memberList != null && memberList.Contains(mem.UniqueName))
					node.Checked = true;
			}

			nodes.Add(node);

            if (mem.ChildCount > 0)
				PopulateChildMembers(dimUniqueName, node.Nodes, mem);
        }
    }

	private void PopulateChildMembers(string dimUniqueName, RadTreeNodeCollection nodes, Member parent)
    {
		foreach (Member mem in parent.GetChildren())
        {
			RadTreeNode node = new RadTreeNode(mem.Name);
            node.Value = EscapeUniqueName(mem.UniqueName);

			if (this.CheckedDimensionMembers.ContainsKey(dimUniqueName))
			{
				List<string> memberList = this.CheckedDimensionMembers[dimUniqueName];
				if (memberList != null && memberList.Contains(mem.UniqueName))
					node.Checked = true;
			}

			bool hasRealChildren = false;
			foreach (Member child in mem.GetChildren())
			{
				if (string.Compare(child.Name, "Unspecified", true) != 0)
				{
					hasRealChildren = true;
					break;
				}
			}

			if (hasRealChildren)
				node.ExpandMode = TreeNodeExpandMode.ServerSideCallBack;

			nodes.Add(node);
        }
    }

	protected void Node_Expanded(object sender, RadTreeNodeEventArgs e)
	{
		string nodeUniqueName = UnescapeUniqueName(e.Node.Value);
		Member member = OLAPHelper.FindMemberByUniqueName(this.IsLegacy, nodeUniqueName);
		string dimUniqueName = UnescapeUniqueName(dimensionCombo.SelectedValue);
		PopulateChildMembers(dimUniqueName, e.Node.Nodes, member);

		//check previously selected nodes for newly loaded up load-on-demand items
        if (!string.IsNullOrEmpty(dimensionCombo.SelectedValue) && this.CheckedDimensionMembers != null && this.CheckedDimensionMembers.ContainsKey(dimensionCombo.SelectedValue))
		{
			foreach (string dimMember in this.CheckedDimensionMembers[dimensionCombo.SelectedValue])
			{
				if (!string.IsNullOrEmpty(dimMember))
				{
					foreach (Member childMember in member.GetChildren())
					{
						if (childMember.UniqueName.Equals(dimMember))
						{
							RadTreeNode node = memberTree.FindNodeByValue(EscapeUniqueName(dimMember));
							if (node != null)
								node.Checked = true;
						}
					}
				}
			}
		}
	}

    private string EscapeUniqueName(string origName)
    {
        if (string.IsNullOrEmpty(origName))
            return null;
        else
            return origName.Replace(".", "\\~1").Replace("[", "\\~2").Replace("]", "\\~3");
    }

    private string UnescapeUniqueName(string escapedName)
    {
        if (string.IsNullOrEmpty(escapedName))
            return null;
        else
            return escapedName.Replace("\\~3", "]").Replace("\\~2", "[").Replace("\\~1", ".");
    }

	protected void SelectAll_Click(object sender, EventArgs e)
	{
		foreach (RadTreeNode node in memberTree.GetAllNodes())
		{
			node.Checked = true;
		}
	}

	protected void ClearAll_Click(object sender, EventArgs e)
	{
		foreach (RadTreeNode node in memberTree.GetAllNodes())
			node.Checked = false;
	}

	protected void Edit_Click(object sender, EventArgs e)
	{
		this.ShowEditableTree = true;
		this.editButton.Visible = false;
		this.viewSelectedButton.Visible = true;

		this.clearAllBtn.Visible = true;
		this.checkAllBtn.Visible = true;
		this.dimensionCombo.Visible = true;
	}

	protected void ViewSelected_Click(object sender, EventArgs e)
	{
		UpdateSelectedValues(UnescapeUniqueName(dimensionCombo.SelectedValue));
		UpdateTreeDisplay();
		this.ShowEditableTree = false;
		this.editButton.Visible = true;
		this.viewSelectedButton.Visible = false;

		this.clearAllBtn.Visible = false;
		this.checkAllBtn.Visible = false;
		this.dimensionCombo.Visible = false;
	}

	protected void ClearDimension_Click(object sender, EventArgs e)
	{
        dimensionCombo.ClearSelection();
		dimensionCombo.Text = "";
		memberTree.Nodes.Clear();
		selectedItemsTree.Nodes.Clear();
	}
}
