using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class _Default : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			string pageName = "dashboard.aspx";

			//TBD Load User Preference of last viewed page...either Shared Dashboard or Custom User Dashboard
			DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadDashboard", Utility.GetUserName());
			if (ds.Tables[0] != null)
			{
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					if (DataFormatter.getBool(row, "DisplayPersonalized"))
						pageName = "mydashboard.aspx";
					else
						pageName = "dashboard.aspx";
				}
			}

			Response.Redirect("~/" + pageName);
		}
	}
}

