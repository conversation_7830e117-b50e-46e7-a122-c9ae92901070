using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;
using MathNet.Numerics.Distributions;

public class PRSTReport : BaseReport
{
    public PRSTReport(ReportInfo repInfo) : base(repInfo, false) { }

    private List<int> censoredCumOb = null;
    private List<int> uncensoredCumOb = null;
    private List<int> censoredCumCnt = null;
    private List<int> uncensoredCumCnt = null;
    private List<double> censoredAverages = null;
    private List<double> uncensoredAverages = null;

    private const int CUM_OBS_COL_INDEX = 1;
    private const int CUM_TRAN_MEDIA_COL_INDEX = 3;
    private const int AVG_COL_INDEX = 4;
    private const int BLOCK_END_COL_INDEX = 5;

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        string mdxSelect = null;
        string mdxCategory = null;
        string mdxFromWhere = null;

        mdxSetup.Append(string.Format("WITH {0}\r\n",
            OLAPHelper.BuildMdxFilteredTime(ReportHelper.DateGroupingEnum.BY_DATETIME, repInfo.StartDate, repInfo.EndDate, "FilteredTime", "[Measures].[Transaction Count] > 0")));

        mdxSetup.Append("SET Uncensored AS '{[Censoring].[Censoring]}'\r\n");
        mdxSetup.Append("SET Observations AS '{[Censoring].[Censoring].&[Valid]}'\r\n");
        mdxSetup.Append("MEMBER [Censoring].[Censoring].[Uncensored] AS 'Aggregate(Uncensored)'\r\n");
        mdxSetup.Append("MEMBER [Censoring].[Censoring].[Observations] AS 'Aggregate(Observations)'\r\n");

        if(!string.IsNullOrEmpty(repInfo.DimensionName) && repInfo.DimensionMembers != null && repInfo.DimensionMembers.Count > 0)
        {
            mdxSetup.Append("MEMBER [Observation Cnt] AS 'SUM(");
			mdxSetup.Append(OLAPHelper.BuildMdxLevelTuple(false, repInfo.DimensionName, repInfo.DimensionMembers, false));
            mdxSetup.Append(",[Measures].[Observation Count]) + 0', FORMAT_STRING = \"#,##0\"\r\n");
        } else {
            mdxSetup.Append("MEMBER [Observation Cnt] AS 'SUM([Measures].[Observation Count]) + 0', FORMAT_STRING = \"#,##0\"\r\n");
        }

        switch (repInfo.PRSTInfo.XaxisTypeId)
        {
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_MEDIA:
                mdxSelect = "SELECT CROSSJOIN({[Censoring].[Censoring].[Observations], [Censoring].[Censoring].[Uncensored] }, {[Observation Cnt], [Measures].[Media Count]}) on columns, ";
                break;

            case ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS:
            default:
                mdxSelect = "SELECT CROSSJOIN({[Censoring].[Censoring].[Observations], [Censoring].[Censoring].[Uncensored] }, {[Observation Cnt], [Measures].[Transaction Count]}) on columns, ";
                break;
        }


        mdxCategory = "NON EMPTY {FilteredTime} on rows ";

        mdxFromWhere = string.Format("FROM [Reporting] {0}",
                OLAPHelper.BuildMdxWhereTuples(repInfo.AttachedSessions, repInfo.ReportFilters, false, "WHERE ", true));

        string mdx = mdxSetup.ToString() + mdxSelect + mdxCategory + mdxFromWhere;

        ExecuteMdx(mdx);
        BuildAggregates();
        BuildGridDisplay();
    }

    // Calculate cumulative observations
    // Average transactions/media over the last 5 days with activity
    private void BuildAggregates()
    {
        censoredCumOb = new List<int>();
        uncensoredCumOb = new List<int>();
        censoredCumCnt = new List<int>();
        uncensoredCumCnt = new List<int>();
        censoredAverages = new List<double>();
        uncensoredAverages = new List<double>();

        List<double> censoredFive = new List<double>();
        List<double> uncensoredFive = new List<double>();
        TupleCollection colTuples = this.cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = this.cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = this.cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = this.cellSet.Axes[1].Set.Hierarchies;
        string lastDateUniqueName = null;

        for (int col = 0; col < colTuples.Count; col = col + (colTuples.Count / colHierarchies.Count)) // Should happen twice
        {
            for (int row = 0; row < rowTuples.Count; row++)
            {
                Cell cellA = this.cellSet.Cells[col, row]; // Observation Cnt
                Cell cellB = this.cellSet.Cells[col + 1, row]; // Transactions/Media Count

                int obs = (cellA.Value == null ? 0 : int.Parse(cellA.Value.ToString()));
                int cnt = (cellB.Value == null ? 0 : int.Parse(cellB.Value.ToString()));
                int cumCnt = cnt;

                // Calculate cumulative observations and transactions/media
                if (col == 0) // censored
                {
                    if (censoredCumOb.Count > 0)
                        obs += censoredCumOb[censoredCumOb.Count - 1];
                    if (censoredCumCnt.Count > 0)
                        cumCnt += censoredCumCnt[censoredCumCnt.Count - 1];

                    censoredCumOb.Add(obs);
                    censoredCumCnt.Add(cumCnt);
                }
                else
                {
                    if (uncensoredCumOb.Count > 0)
                        obs += uncensoredCumOb[uncensoredCumOb.Count - 1];
                    if (uncensoredCumCnt.Count > 0)
                        cumCnt += uncensoredCumCnt[uncensoredCumCnt.Count - 1];

                    uncensoredCumOb.Add(obs);
                    uncensoredCumCnt.Add(cumCnt);
                }

                DateTime memberDate = DateTime.ParseExact(rowTuples[row].Members[0].Caption.Substring(0, 16), "MM/dd/yyyy HH:mm", System.Globalization.DateTimeFormatInfo.CurrentInfo);
                string curDateUniqueName = memberDate.ToString("MM/dd/yyyy");
                if (col == 0) // censored
                {
					if (string.Compare(lastDateUniqueName, curDateUniqueName) == 0 && censoredFive.Count > 0)
					{
						censoredFive[censoredFive.Count - 1] += Convert.ToDouble(cnt);
					}
					else
					{
						censoredFive.Add(Convert.ToDouble(cnt));
						lastDateUniqueName = curDateUniqueName;
					}

                    if (censoredFive.Count > 5)
                        censoredFive.RemoveAt(0);
                }
                else
                {
					if (string.Compare(lastDateUniqueName, curDateUniqueName) == 0 && uncensoredFive.Count > 0)
					{
						uncensoredFive[uncensoredFive.Count - 1] += Convert.ToDouble(cnt);
					}
					else
					{
						uncensoredFive.Add(Convert.ToDouble(cnt));
						lastDateUniqueName = curDateUniqueName;
					}

                    if (uncensoredFive.Count > 5)
                        uncensoredFive.RemoveAt(0);
                }

                double subTotal = 0;
                if (col == 0) // censored
                {
                    foreach(double curCnt in censoredFive)
                        subTotal += curCnt;

                    if (censoredFive.Count > 0)
                        censoredAverages.Add(subTotal / censoredFive.Count);
                }
                else // uncensored
                {
                    foreach (double curCnt in uncensoredFive)
                        subTotal += curCnt;

                    if (uncensoredFive.Count > 0)
                        uncensoredAverages.Add(subTotal / uncensoredFive.Count);
                }
            }
        }
    }

    public override void BuildGridDisplay()
    {
        if (censoredCumOb == null)
            BuildAggregates();
        base.BuildGridDisplay();

        TupleCollection colTuples = this.cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = this.cellSet.Axes[0].Set.Hierarchies;

        this.ColumnDisplayHeaders[0].Insert(CUM_OBS_COL_INDEX, "Observations");
        this.ColumnDisplayHeaders[0].Insert(CUM_TRAN_MEDIA_COL_INDEX, "Observations");        
        this.ColumnDisplayHeaders[0].Insert(AVG_COL_INDEX, "Observations");
        this.ColumnDisplayHeaders[0].Insert(BLOCK_END_COL_INDEX + CUM_OBS_COL_INDEX, "Uncensored");
        this.ColumnDisplayHeaders[0].Insert(BLOCK_END_COL_INDEX + CUM_TRAN_MEDIA_COL_INDEX, "Uncensored");
        this.ColumnDisplayHeaders[0].Insert(BLOCK_END_COL_INDEX + AVG_COL_INDEX, "Uncensored");

        switch (this.repInfo.PRSTInfo.XaxisTypeId)
        {
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_MEDIA:
                this.ColumnDisplayHeaders[1].Insert(CUM_OBS_COL_INDEX, "Cumulative Observations");
                this.ColumnDisplayHeaders[1].Insert(CUM_TRAN_MEDIA_COL_INDEX, "Cumulative Media");
                this.ColumnDisplayHeaders[1].Insert(AVG_COL_INDEX, "Avg Media");
                this.ColumnDisplayHeaders[1].Insert(BLOCK_END_COL_INDEX + CUM_OBS_COL_INDEX, "Cumulative Observations");
                this.ColumnDisplayHeaders[1].Insert(BLOCK_END_COL_INDEX + CUM_TRAN_MEDIA_COL_INDEX, "Cumulative Media");
                this.ColumnDisplayHeaders[1].Insert(BLOCK_END_COL_INDEX + AVG_COL_INDEX, "Avg Media");
                break;

            case ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS:
            default:
                this.ColumnDisplayHeaders[1].Insert(CUM_OBS_COL_INDEX, "Cumulative Observations");
                this.ColumnDisplayHeaders[1].Insert(CUM_TRAN_MEDIA_COL_INDEX, "Cumulative Transactions");
                this.ColumnDisplayHeaders[1].Insert(AVG_COL_INDEX, "Avg Transactions");
                this.ColumnDisplayHeaders[1].Insert(BLOCK_END_COL_INDEX + CUM_OBS_COL_INDEX, "Cumulative Observations");
                this.ColumnDisplayHeaders[1].Insert(BLOCK_END_COL_INDEX + CUM_TRAN_MEDIA_COL_INDEX, "Cumulative Transactions");
                this.ColumnDisplayHeaders[1].Insert(BLOCK_END_COL_INDEX + AVG_COL_INDEX, "Avg Transactions");
                break;
        }

        for (int x = 0; x < this.RowDisplayData.Count && x < this.censoredAverages.Count && x < this.uncensoredAverages.Count
            && x < this.censoredCumOb.Count && x < this.uncensoredCumOb.Count; x++)
        {
            List<string> rowData = this.RowDisplayData[x];
            rowData.Insert(CUM_OBS_COL_INDEX, this.censoredCumOb[x].ToString("#,##0"));
            rowData.Insert(CUM_TRAN_MEDIA_COL_INDEX, this.censoredCumCnt[x].ToString("#,##0"));
            rowData.Insert(AVG_COL_INDEX, this.censoredAverages[x].ToString("#,##0.0"));
            rowData.Insert(BLOCK_END_COL_INDEX + CUM_OBS_COL_INDEX, this.uncensoredCumOb[x].ToString("#,##0"));
            rowData.Insert(BLOCK_END_COL_INDEX + CUM_TRAN_MEDIA_COL_INDEX, this.uncensoredCumCnt[x].ToString("#,##0"));
            rowData.Insert(BLOCK_END_COL_INDEX + AVG_COL_INDEX, this.uncensoredAverages[x].ToString("#,##0.0"));
        }        
    }

    public override void PopulateChart(Dundas.Charting.WebControl.Chart chart, bool includeToolTips)
    {
        if (censoredCumOb == null)
            BuildAggregates();

        TupleCollection colTuples = this.cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = this.cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = this.cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = this.cellSet.Axes[1].Set.Hierarchies;

        string xAxisLabelPart = null;
        switch (repInfo.PRSTInfo.XaxisTypeId)
        {
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_MEDIA:
                xAxisLabelPart = "Media";
                break;
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS:
            default:
                xAxisLabelPart = "Transactions";
                break;
        }

        Dundas.Charting.WebControl.Axis axisX = chart.ChartAreas[0].AxisX;
        Dundas.Charting.WebControl.Axis axisY = chart.ChartAreas[0].AxisY;

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
            priorSeries.Add(series.Name, series);

        chart.Series.Clear();

        double maxTran = 0;
        double maxObs = 0;
        double maxObsOverall = 0;
        double prodTot = 0;
        double xsqrTot = 0;
        double ysqrTot = 0;
        double pointCnt = 0;

        int r = 15; //constant upper limit of reject line
        double Tconstant = 20.59923461; //default value * 0.00001
        double T = 0;
        double a = 1;
        double b = 1;
        double c = 1;
        double A = 0;
        double B = 0;
        double delta = 0;
        double alpha = 0;
        double beta = 0;
        double m0 = 0;
        double m1 = 0;
        bool testFailed = false;
        bool testSucceeded = false;
        double trendX = 0;
        double trendY = 0;

        const double DEFAULT_DISCRIMINATION_RATIO = 1.5;
        const double DEFAULT_RISK_PERCENT = 0.1;

        // Calculate PRST accept/reject limits
        if (repInfo.PRSTInfo.MTBFSpec > 0 &&  (repInfo.PRSTInfo.CustomizeRatioRisk == false || repInfo.PRSTInfo.DiscriminationRatio > 0))
        {
            delta = (double)(repInfo.PRSTInfo.CustomizeRatioRisk ? (double)repInfo.PRSTInfo.DiscriminationRatio : DEFAULT_DISCRIMINATION_RATIO); //default 1.5
            m0 = (double)repInfo.PRSTInfo.MTBFSpec; //default 100,000
            m1 = m0 / delta; //default 66,666.66
            alpha = (repInfo.PRSTInfo.CustomizeRatioRisk ? (double)repInfo.PRSTInfo.ProducersRisk : DEFAULT_RISK_PERCENT); //default 0.1
            beta = (repInfo.PRSTInfo.CustomizeRatioRisk ? (double)repInfo.PRSTInfo.ConsumersRisk : DEFAULT_RISK_PERCENT); //default 0.1

            //***** NOTE: All values calculated by provided Excel spreadsheet named 'PRST Formula Calculations.xlsx'. File saved in Client Download folder.
            if (delta == 1.5) {
                if (alpha == 0.1) {
                    r = 41;
                    Tconstant = 4955679.747;
                }
                else if (alpha == 0.2) {
                    r = 18;
                    Tconstant = 2155122.097;
                }
                else if (alpha == 0.3) {
                    r = 7;
                    Tconstant = 811610.8291;
                }
            }
            else if (delta == 2) {
                if (alpha == 0.1) {
                    r = 15; 
                    Tconstant = 2059923.461;
                }
                else if (alpha == 0.2) {
                    r = 7;
                    Tconstant = 946732.7987;
                }
                else if (alpha == 0.3) {
                    r = 3;
                    Tconstant = 382755.1588;
                }
            }
            else if (delta == 3) {
                if (alpha == 0.1) {
                    r = 6; 
                    Tconstant = 945569.4089;
                }
                if (alpha == 0.2) {
                    r = 3;
                    Tconstant = 460513.2608;
                }
            }
            else if (delta == 5) {
                if (alpha == 0.1) {
                    r = 3; 
                    Tconstant = 551032.6641;
                }
            }
            Tconstant = Tconstant * 0.00001; //unknown why Excel file states large values for T, but Dave's code uses much smaller number, but this fixes the calculation.

            T = m0 * Tconstant / delta; //default 1,373,282.3689778, || d=1.5 default = 3,303,786.498
            A = ((delta + 1) * (1 - beta)) / (2 * delta * alpha); //default 7.5
            B = beta / (1 - alpha); //default 0.111111111
            a = Math.Log(B) / Math.Log(delta); //default -5.4190225817
            b = ((1 / m1) - (1 / m0)) / Math.Log(delta); //default 1.2331517311882153E-05
            c = Math.Log(A) / Math.Log(delta); //4.969362296
        }

        // Build chart series data
        for (int col = 0; col < colTuples.Count; col = col + (colTuples.Count / colHierarchies.Count))
        {
            Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];

            Series series = null;
            string seriesName = colTuple.Members[0].Caption; // Observations or Uncensored

            if (priorSeries.ContainsKey(seriesName))
            {
                series = priorSeries[seriesName];
                priorSeries.Remove(series.Name);
                series.Points.Clear();
            }
            else
            {
                series = new Series(seriesName);
                series["DrawingStyle"] = "Cylinder";
				series.Type = SeriesChartType.StepLine;
            }

            for (int row = 0; row < rowTuples.Count; row++)
            {
                double YVal = 0;
                double XVal = 0;
                if (col == 0)
                {
                    YVal = this.censoredCumOb[row];
                    XVal = this.censoredCumCnt[row];
                }
                else
                {
                    YVal = this.uncensoredCumOb[row];
                    XVal = this.uncensoredCumCnt[row];
                }

                // Only total based on the observation series, not the uncensored series
                if (col == 0)
                {
                    pointCnt++;
                    maxTran = (maxTran < XVal ? XVal : maxTran);
                    maxObs = (maxObs < YVal ? YVal : maxObs);
                    prodTot += YVal * XVal;
                    xsqrTot += XVal * XVal;
                    ysqrTot += YVal * YVal;

                    // Test for success or failure
                    if (!testFailed && !testSucceeded)
                    {
                        double testFailY = (maxTran * b) + c;
                        // Test for failure
                        if (maxObs > r || maxObs > testFailY) {
                            testFailed = true;
                        }
                        else {
                            // Test for success
                            double testAcceptX = (maxObs - a) / b;
                            if (maxTran > T || maxTran > testAcceptX)
                                testSucceeded = true;
                        }
                    }
                }

                maxObsOverall = (maxObsOverall < YVal ? YVal : maxObsOverall);

                DataPoint dp = new DataPoint(XVal, YVal);
                if (includeToolTips) {
                    dp.ToolTip = string.Format("{0}\r\nObservations: {1}\r\n{2}: {3}", series.Name, YVal.ToString("#,##0"), xAxisLabelPart, XVal.ToString("#,##0"));
                }
    
                series.Points.Add(dp);
            }
			series.BorderWidth = 4;
			series.Color = Color.DarkOrange;

            if (string.Compare(series.Name, "Uncensored") == 0)
            {
				if (!repInfo.PRSTInfo.IncludeUncensoredSeries)
                    continue;

				//ensure Uncensored series is a different color than censored line
				series.BorderWidth = 2;
				series.BorderStyle = ChartDashStyle.Dot;
				series.Color = Color.BlueViolet;
            }

            chart.Series.Add(series);
        }

        chart.Annotations.Clear();
        // Calculate trend line
        if (pointCnt > 0 && maxObs > 0 && maxTran > 0 && repInfo.PRSTInfo.IncludeTrendline)
        {
            double m = (prodTot - (maxObs * maxTran)) / (xsqrTot - (maxTran * maxTran));
            double trend_b = (maxObs - (m * maxTran)) / pointCnt;

            trendX = maxTran;
            trendY = m * trendX + trend_b;
        }

        // Ensure data is visible on chart
        axisX.Minimum = 0;
        axisY.Minimum = 0;

        axisX.Maximum = RoundAxis(Math.Max(Math.Max(maxTran, T), trendX), 100000);
        axisY.Maximum = RoundAxis(Math.Max(Math.Max((repInfo.PRSTInfo.IncludeUncensoredSeries ? maxObsOverall : maxObs), r), trendY), 10);

	    if((repInfo.PRSTInfo.IncludeUncensoredSeries ? maxObsOverall : maxObs) <= 0)
    	{
            Series acceptRejectSeries = new Series("Accept/Reject");
            acceptRejectSeries.ShowInLegend = false;
            acceptRejectSeries.Points.AddXY(T, r);
            acceptRejectSeries.Points[0].Color = Color.FromArgb(0, 0, 0, 0);
            chart.Series.Add(acceptRejectSeries);
	    }

        ResetAxisMarks(axisX);
        ResetAxisMarks(axisY);

    	axisX.LabelStyle.Interval = axisX.Maximum / 5;
    	axisX.Interval = axisX.Maximum / 5;

        chart.ChartAreas[0].ReCalc();

        // Make some margin around the orgin (about 1%)
        axisX.Minimum = 0 - (axisX.Maximum * .005);
        axisX.LabelStyle.IntervalOffset = 0 - axisX.Minimum;
        axisY.Minimum = 0 - (axisY.Maximum * .005);
        axisY.LabelStyle.IntervalOffset = 0 - axisY.Minimum;

        double accX0 = (0 - a) / b;
        double accY1 = a + (b * T);
        double chiSquareLineMinX = 0;
        double chiSquareLineMaxX = T;
        double chiSquareLineMinY = 0;
        double chiSquareLineMaxY = 0;

        // Build reject and accept lines
        string daysToCompleteMessage = null;
        if (repInfo.PRSTInfo.MTBFSpec > 0 && (repInfo.PRSTInfo.CustomizeRatioRisk == false
            || (repInfo.PRSTInfo.DiscriminationRatio > 0 && repInfo.PRSTInfo.ProducersRisk > 0 && repInfo.PRSTInfo.ProducersRisk != 1 && repInfo.PRSTInfo.ConsumersRisk > 0)))
        {
            // Graph lines for chart boundries
            Color accpetLineColor = Color.Green;
            Color accpetLineTailColor = Color.Green;

            if (this.repInfo.PRSTInfo.AllInOneFormat) {
                accpetLineColor = Color.Blue;
                accpetLineTailColor = Color.Red;

                //CALCULATE THE INVERSE CHI SQUARED VALUE
                double initialDegreeFreedom = 2;
                double probability = 1 - beta; //right tail table of Chi Squared
                ChiSquared chi1 = new ChiSquared(initialDegreeFreedom);
                double chiSqInverseVal = chi1.InverseCumulativeDistribution(probability);

                int numUnits = this.repInfo.PRSTInfo.NumberOfUnits > 0 ? this.repInfo.PRSTInfo.NumberOfUnits : 1;
                
                //chiLineMinX - Maximum value of the 3 values below
                double minAcceptValue = (double)this.repInfo.PRSTInfo.MTBFSpec * numUnits;
                double minPRSTValue = accX0;
                double chiSquareMinValue = (double)this.repInfo.PRSTInfo.MTBFSpec / 2 * chiSqInverseVal;
                
                chiSquareLineMinX = minAcceptValue;
                if (minPRSTValue > chiSquareLineMinX) {
                    chiSquareLineMinX = minPRSTValue;
                }
                if (chiSquareMinValue > chiSquareLineMinX) {
                    chiSquareLineMinX = chiSquareMinValue;
                }

                //loop through Chi-Square calculations and find Chi Square lin Min/Max Y values, 
                //Min is found when Chi-Square value is greater than our Minimum X value for Chi-Squared line
                //Max is the Degrees of Freedom value when ChiSquare line intersects PRST-Y (represented by T)
                double tempY = 0;
                double tempDegreesFreedom = 0;
                int curIteration = 0;
                bool minYFound = false;
                while (tempY < T && curIteration < 50) {
                    try {
                        curIteration++; //keep track of max iterations so we dont create an infinte loop in strange scenarios
                        tempDegreesFreedom += 1;

                        ChiSquared tempChiSq = new ChiSquared(2 * tempDegreesFreedom + 2);
                        tempY = m0 / 2 * tempChiSq.InverseCumulativeDistribution(probability);

                        //once we have gone past chi square line, round down
                        if (!minYFound && tempY > chiSquareLineMinX) {
                            chiSquareLineMinY = tempDegreesFreedom - 1;
                            minYFound = true;
                        }
                    }
                    catch {
                        //throw new ApplicationException("deg: " + varDegreeFreedom + " maxY: " + chiSquareMaxY);
                    }
                }
                //need to back up 1 because we went past the Chi Line value at T.
                chiSquareLineMaxY = tempDegreesFreedom - 1;

                //calculate the slope of the line so we can determine the starting point for the Blue PRST line
                //formula of a line: y = mx + b
                double prstLineX1 = accX0;
                double prstLineY1 = 0;
                double prstLineX2 = T;
                double prstLineY2 = accY1;
                double prstLine_rise = prstLineY2 - prstLineY1;
                double prstLine_run = prstLineX2 - prstLineX1;
                double prstLine_M = prstLine_rise / prstLine_run; //calculate the slope of the line
                double prstLine_B = prstLineY1 - (prstLine_M * prstLineX1); //calculate the Y intercept of the line

                //Y point at which blue line starts when Y value is above Green Y value
                //Calculate the Y coordinate of the PRST line, based on the X coordinate on that line. (X coordinate comes from the Chi Squared line calcuation)
                double prstLine_Y = prstLine_M * chiSquareLineMinX + prstLine_B;

                //if our PRST line Y value is less than ChiSquare, then use that value to start our ChiSquare line also
                if (prstLine_Y < chiSquareLineMinY)
                    chiSquareLineMinY = prstLine_Y;                

                StringBuilder debugData = new StringBuilder();
                debugData.AppendFormat("chiSquareMinValue: {0}\r\n", chiSquareMinValue);
                debugData.AppendFormat("chiLineLineMinX: {0}\r\n", chiSquareLineMinX);
                debugData.AppendFormat("chiSquareLineMinY: {0}\r\n", chiSquareLineMinY);
                debugData.AppendFormat("chiSquareLineMaxY: {0}\r\n", chiSquareLineMaxY);
                //debugData.AppendFormat("varDegreeFreedom: {0}\r\n", varDegreeFreedom);
                debugData.AppendFormat("T: {0}\r\n", T);
                debugData.AppendFormat("accX0: {0}\r\n", Math.Round(accX0, 2).ToString());
                debugData.AppendFormat("accY1: {0}\r\n", Math.Round(accY1, 2).ToString());
                debugData.AppendFormat("x1: {0}\r\n", prstLineX1.ToString());
                debugData.AppendFormat("y1: {0}\r\n", prstLineY1.ToString());
                debugData.AppendFormat("x2: {0}\r\n", prstLineX2.ToString());
                debugData.AppendFormat("y2: {0}\r\n", prstLineY2.ToString());
                debugData.AppendFormat("rise: {0}\r\n", prstLine_rise.ToString());
                debugData.AppendFormat("run: {0}\r\n", prstLine_run.ToString());
                debugData.AppendFormat("lineM: {0}\r\n", prstLine_M.ToString());
                debugData.AppendFormat("lineB: {0}\r\n", prstLine_B.ToString());
                debugData.AppendFormat("lineY: {0}\r\n", prstLine_Y.ToString());
                //throw new ApplicationException(debugData.ToString());

                //if the min value is where the PRST starts, then we just need 1 line, otherwise 2
                if (prstLine_Y > 0) {
                    //Y point at which blue line starts when Y value is above Green Y value
                    //prstLine_Y = prstLine_M * chiLineMinX + prstLine_B;
                    
                    // Starting vertical line (Blue)
                    SixSigma.AddLineAnnotation(axisX, axisY, chiSquareLineMinX, chiSquareLineMinY, 0, prstLine_Y - chiSquareLineMinY, Color.Blue, chart);
                    // Accept Line (Blue)
                    SixSigma.AddLineAnnotation(axisX, axisY, chiSquareLineMinX, prstLine_Y, chiSquareLineMaxX - chiSquareLineMinX, accY1 - prstLine_Y, accpetLineColor, chart);
                }
                else {
                    //throw new ApplicationException(debugData.ToString());

                    // Accept Line (Blue)
                    SixSigma.AddLineAnnotation(axisX, axisY, chiSquareLineMinX, chiSquareLineMinY, chiSquareLineMaxX - chiSquareLineMinX, accY1 - chiSquareLineMinY, accpetLineColor, chart);
                }

                // Chi Square Starting vertical line
                SixSigma.AddLineAnnotation(axisX, axisY, chiSquareLineMinX, 0, 0, chiSquareLineMinY, Color.Green, chart);
                // Chi Square Line (Green)
                SixSigma.AddLineAnnotation(axisX, axisY, chiSquareLineMinX, chiSquareLineMinY, chiSquareLineMaxX - chiSquareLineMinX, chiSquareLineMaxY - chiSquareLineMinY, Color.Green, chart);
                // Chi Square Ending vertical line
                SixSigma.AddLineAnnotation(axisX, axisY, chiSquareLineMaxX, chiSquareLineMaxY, 0, accY1 - chiSquareLineMaxY, Color.DarkOrange, chart);

                //PINK PRST LINE - TBD REMOVE
                //SixSigma.AddLineAnnotation(axisX, axisY, accX0, 0, T - accX0, accY1, Color.Pink, chart);
                //CHI SQUARE LINE - TBD REMOVE
                //SixSigma.AddLineAnnotation(axisX, axisY, chiSquareMinValue, 0, chiSquareLineMaxX - chiSquareMinValue, varDegreeFreedom, Color.Brown, chart);
            }
            else {
                // Accept Line (Green)
                SixSigma.AddLineAnnotation(axisX, axisY, accX0, 0, T - accX0, accY1, accpetLineColor, chart);
            }

            // Accept (Tail) Line
            SixSigma.AddLineAnnotation(axisX, axisY, T, accY1, 0, r - accY1, accpetLineTailColor, chart);

            // Reject Line
            double rejX1 = (r - c) / b;
            SixSigma.AddLineAnnotation(axisX, axisY, 0, c, rejX1, r - c, Color.Red, chart);
            SixSigma.AddLineAnnotation(axisX, axisY, rejX1, r, T - rejX1, 0, Color.Red, chart);

            //StringBuilder debugData = new StringBuilder();
            //debugData.AppendFormat("Accept Line 1: ({0}, {1}), ({2}, {3})\r\n", Math.Round(accX0, 2).ToString(), "0", Math.Round((T - accX0), 2).ToString(), Math.Round(accY1, 2).ToString());
            //debugData.AppendFormat("Accept Line 2: ({0}, {1}), ({2}, {3})\r\n\r\n", Math.Round(T, 2).ToString(), Math.Round(accY1, 2).ToString(), "0", Math.Round((r - accY1), 2).ToString());
            //debugData.AppendFormat("Reject Line 1: ({0}, {1}), ({2}, {3})\r\n", "0", Math.Round(c, 2).ToString(), Math.Round(rejX1, 2).ToString(), Math.Round((r - c), 2).ToString());
            //debugData.AppendFormat("Reject Line 2: ({0}, {1}), ({2}, {3})\r\n\r\n", Math.Round(rejX1, 2).ToString(), r.ToString(), Math.Round((T - rejX1), 2).ToString(), "0");
            //debugData.AppendFormat("a: {0}\r\n", a.ToString());
            //debugData.AppendFormat("b: {0}\r\n", b.ToString());
            //debugData.AppendFormat("c: {0}\r\n", Math.Round(c, 2).ToString());
            //debugData.AppendFormat("r: {0}\r\n", r.ToString());
            //debugData.AppendFormat("Delta: {0}\r\n", Math.Round(delta, 2).ToString());
            //debugData.AppendFormat("T: {0}\r\n", Math.Round(T, 2).ToString());
            //debugData.AppendFormat("(T - rejX1): {0}\r\n", Math.Round((T - rejX1), 2).ToString());
            //debugData.AppendFormat("accX0: {0}\r\n", Math.Round(accX0, 2).ToString());
            //debugData.AppendFormat("accY1: {0}\r\n", Math.Round(accY1, 2).ToString());
            //debugData.AppendFormat("rejX1: {0}\r\n", Math.Round(rejX1, 2).ToString());
            //throw new ApplicationException(debugData.ToString());


            // Create days to complete message
            double lastAvg = 0;
            if (this.censoredAverages.Count > 0)
                lastAvg = this.censoredAverages[this.censoredAverages.Count - 1];

            if (lastAvg > 0)
            {
                if (testSucceeded) {
                    daysToCompleteMessage = "Test Complete - Accept";
                }
                else if (testFailed) {
                    daysToCompleteMessage = "Test Complete - Reject";
                }
                else {
                    double daysCnt = 0;
                    double testX = (maxObs - a) / b;
                    while ((maxTran + (daysCnt * lastAvg)) < testX)
                        daysCnt++;

                    if (daysCnt <= 0)
                        daysToCompleteMessage = "Test Complete - Accept";
                    else
                        daysToCompleteMessage = "Days To Complete = " + daysCnt.ToString();
                }
            }
        }

        // Show trend line
        if (pointCnt > 0 && maxObs > 0 && maxTran > 0 && repInfo.PRSTInfo.IncludeTrendline)
        {
            SixSigma.AddLineAnnotation(axisX, axisY, 0, 0, trendX, trendY, Color.Black, chart);
        }

        chart.Titles["Title1"].Text = repInfo.ChartTitle;

        if (string.IsNullOrEmpty(axisY.Title))
        {
            axisY.Title = "Observations";
            axisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
        }

        axisX.Title = "Cumulative " + xAxisLabelPart;
        axisX.TitleFont = new Font("Arial", 10, FontStyle.Bold);

        // Add text annotations
        DateTime curRollTime;
        if (DateTime.Now.Minute < 10)
            curRollTime = DateTime.Today.AddHours(DateTime.Now.Hour - 1).AddMinutes(30);
        else if (DateTime.Now.Minute > 40)
            curRollTime = DateTime.Today.AddHours(DateTime.Now.Hour).AddMinutes(30);
        else
            curRollTime = DateTime.Today.AddHours(DateTime.Now.Hour);

        string dateRangeStr = null;
		if (DateTime.MinValue.Equals(repInfo.StartDate))
		{
			if (DateTime.MinValue.Equals(repInfo.EndDate) || repInfo.EndDate.CompareTo(DateTime.Today) > 0)
				dateRangeStr = "Data through " + curRollTime.ToString("MM/dd/yyyy   hh:mm tt");
			else
				dateRangeStr = "Data through " + repInfo.EndDate.ToString("MM/dd/yyyy");
		}
		else
		{
			if (DateTime.MinValue.Equals(repInfo.EndDate) || repInfo.EndDate.CompareTo(DateTime.Today) > 0)
				dateRangeStr = "From " + repInfo.StartDate.ToString("MM/dd/yyyy") + " to " + curRollTime.ToString("MM/dd/yyyy   hh:mm tt");
			else
				dateRangeStr = "From " + repInfo.StartDate.ToString("MM/dd/yyyy") + " to " + repInfo.EndDate.ToString("MM/dd/yyyy");
		}

        if (maxObsOverall > maxObs)
        {
            int diff = (int)(maxObsOverall - maxObs);
            SixSigma.AddTextAnnotation(string.Format("{0} observation{1} censored", diff, (diff > 1 ? "s were" : " was")), axisX, axisY,
                axisX.Maximum - (axisX.Maximum / 4),
                axisY.Minimum - (axisY.Maximum / 6),
                Color.Red, new Font("Arial", 8, FontStyle.Regular), chart);
        }

        StringBuilder annotationText = new StringBuilder();
        if (!this.repInfo.PRSTInfo.AllInOneFormat) {
            annotationText.AppendLine(daysToCompleteMessage);
        }
        else {
            annotationText.AppendLine("");
        }
        annotationText.AppendLine(dateRangeStr);
        annotationText.AppendLine("MTBF: " + repInfo.PRSTInfo.MTBFSpec.ToString("#,#"));
        annotationText.AppendLine(xAxisLabelPart + ": " + maxTran.ToString("#,#"));

        SixSigma.AddTextAnnotation(annotationText.ToString(), axisX, axisY,
            axisX.Minimum - (axisX.Maximum / 8),
            axisY.Minimum - (axisY.Maximum / 8),
            Color.Black, new Font("Arial", 8, FontStyle.Regular), chart);

        //reject note
        SixSigma.AddTextAnnotation("Reject", axisX, axisY,
            axisX.Minimum + (axisX.Maximum / 12),
            axisY.Maximum - (axisY.Maximum / 12),
            Color.Red, new Font("Arial", 8, FontStyle.Regular), chart);

        //accept note
        SixSigma.AddTextAnnotation("Accept", axisX, axisY,
            axisX.Maximum - (axisX.Maximum / 6),
            axisY.Minimum + (axisY.Maximum / 6),
            Color.Green, new Font("Arial", 8, FontStyle.Regular), chart);

        if (this.repInfo.PRSTInfo.AllInOneFormat) {
            ////continue testing note
            double continueTextX = axisX.Minimum + (axisX.Maximum * 0.43);
            double continueTextY = axisY.Minimum + (axisY.Maximum / 2);
            SixSigma.AddTextAnnotation("Continue", axisX, axisY, continueTextX, continueTextY, Color.Black, new Font("Arial", 7, FontStyle.Regular), chart);

            //Goto Pilot Note
            double pilotTextX = T * 0.82;
            double pilotTextY = chiSquareLineMaxY + ((accY1 - chiSquareLineMaxY) / 5);
            SixSigma.AddTextAnnotation("Goto Pilot", axisX, axisY, pilotTextX, pilotTextY, Color.Blue, new Font("Arial", 7, FontStyle.Regular), chart);

            double riskTextOffset = (accY1 * 0.03);

            //Risk 10% note
            SixSigma.AddTextAnnotation(string.Format("Risk={0}%", (beta * 100).ToString()), axisX, axisY, 
                T, chiSquareLineMaxY + riskTextOffset, Color.Black, new Font("Arial", 7, FontStyle.Regular), chart);

            //Risk 33% note
            double riskPercent = (1 - 1 / delta) * 100;
            SixSigma.AddTextAnnotation(string.Format("Risk={0}% for d={1}", Math.Round(riskPercent, 1), delta.ToString()), axisX, axisY, 
                T, accY1 + riskTextOffset, Color.Black, new Font("Arial", 7, FontStyle.Regular), chart);

            //Risk 99% note - constant value of 99
            SixSigma.AddTextAnnotation("Risk=99%", axisX, axisY, T, accY1 + (r - accY1) + riskTextOffset, Color.Black, new Font("Arial", 7, FontStyle.Regular), chart);
        }

        SetChartTitle(chart, "SpaceHolder", " ", Docking.Bottom, ContentAlignment.MiddleCenter, new Font("Arial", 8, FontStyle.Regular));
    }

}
