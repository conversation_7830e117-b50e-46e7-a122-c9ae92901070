using System;
using System.Web.UI;

public partial class NewReportWizard_PRST : System.Web.UI.Page
{
    private string[] SupressedDimensions = new string[] { "Cell", "Session", "Statistic", "Measures", "Time", "Test Session", "Censoring", "Test Location", "Event", "Event Status", "Event Reason", "Device",
        "Metric", "Metric Distribution", "Metric Values", "Engineering Field", "Engineering Distribution", "Engineering Values", "Engineering Note Number", "Engineering Index Number", 
		"Distribution", "Distribution Values", "Setting", "Setting 2", "Setting 3", "Sensor", "File Number", "Media Number", "Command Number" };

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			this.RepInfo = Utility.GetReportInfoFromTransfer();
			if (this.RepInfo == null)
				Response.Redirect("popuperror.aspx");

			reportLabel.Text = this.RepInfo.ReportName;
			reportTypeLabel.Text = this.RepInfo.ReportTypeName; 
			
            xaxisList.SelectedValue = ((int)this.RepInfo.PRSTInfo.XaxisTypeId).ToString();

			trendlineCheck.Checked = this.RepInfo.PRSTInfo.IncludeTrendline;
			uncensoredSeriesCheck.Checked = this.RepInfo.PRSTInfo.IncludeUncensoredSeries;
			customizeCheck.Checked = this.RepInfo.PRSTInfo.CustomizeRatioRisk;
			
			if (this.RepInfo.PRSTInfo.MTBFSpec != Decimal.MinValue)
				mtbfSpecField.Text = Math.Round(this.RepInfo.PRSTInfo.MTBFSpec, 0).ToString();

			if (this.RepInfo.PRSTInfo.CustomizeRatioRisk)
			{
				if (this.RepInfo.PRSTInfo.DiscriminationRatio != Decimal.MinValue)
					discrimRatioField.SelectedValue = this.RepInfo.PRSTInfo.DiscriminationRatio.ToString();
                if (this.RepInfo.PRSTInfo.ProducersRisk != Decimal.MinValue) {
                    producerRiskField.SelectedValue = this.RepInfo.PRSTInfo.ProducersRisk.ToString();
                    producerRiskValue.Value = this.RepInfo.PRSTInfo.ProducersRisk.ToString();
                }
				//if (this.RepInfo.PRSTInfo.ConsumersRisk != Decimal.MinValue)
					//consumerRiskField.Text = this.RepInfo.PRSTInfo.ConsumersRisk.ToString();

				this.discrimRatioField.Enabled = true;
				this.producerRiskField.Enabled = true;
				//this.consumerRiskField.Enabled = true;
			}

            if (this.RepInfo.PRSTInfo.AllInOneFormat) {
                this.allInOneFormat.Checked = this.RepInfo.PRSTInfo.AllInOneFormat;
                if (this.RepInfo.PRSTInfo.NumberOfUnits > int.MinValue) {
                    this.numberOfUnits.Text = Convert.ToString(this.RepInfo.PRSTInfo.NumberOfUnits);
                }
            }

			if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
				this.dimensionSel.PopulateDimensions(true, SupressedDimensions);
			else
				this.dimensionSel.PopulateDimensions(false, SupressedDimensions);

			this.dimensionSel.DimensionName = this.RepInfo.DimensionName;
			this.dimensionSel.CheckedDimensionMembers = this.RepInfo.DimensionMembers;
			this.dimensionSel.UpdateTreeDisplay();
			dimensionSel.Focus();
		}
    }

	protected void NextButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			//Set Page Values
			this.RepInfo.DimensionName = dimensionSel.DimensionName;
			this.RepInfo.DimensionMembers = dimensionSel.CheckedDimensionMembers;

			this.RepInfo.PRSTInfo.IncludeTrendline = trendlineCheck.Checked;
			this.RepInfo.PRSTInfo.IncludeUncensoredSeries = uncensoredSeriesCheck.Checked;
			this.RepInfo.PRSTInfo.CustomizeRatioRisk = customizeCheck.Checked;

			this.RepInfo.PRSTInfo.XaxisTypeId = (ReportHelper.XAxisTypeEnum)Convert.ToInt32(xaxisList.SelectedValue);
			this.RepInfo.PRSTInfo.MTBFSpec = Convert.ToDecimal(mtbfSpecField.Text.Trim());

			if (customizeCheck.Checked)
			{
				this.RepInfo.PRSTInfo.DiscriminationRatio = Convert.ToDecimal(discrimRatioField.SelectedValue);
				this.RepInfo.PRSTInfo.ProducersRisk = Convert.ToDecimal(producerRiskValue.Value);
				this.RepInfo.PRSTInfo.ConsumersRisk = Convert.ToDecimal(producerRiskValue.Value); //no longer offering separate values, both risk factor values are the same. //Convert.ToDecimal(consumerRiskField.Text.Trim());
            }
			else
			{
				this.RepInfo.PRSTInfo.DiscriminationRatio = decimal.MinValue;
                this.RepInfo.PRSTInfo.ProducersRisk = decimal.MinValue;
                this.RepInfo.PRSTInfo.ConsumersRisk = decimal.MinValue;
			}

            this.RepInfo.PRSTInfo.AllInOneFormat = this.allInOneFormat.Checked;
            if (this.allInOneFormat.Checked) {
                this.RepInfo.PRSTInfo.NumberOfUnits = Convert.ToInt32(this.numberOfUnits.Text);
            }
            else {
                this.RepInfo.PRSTInfo.NumberOfUnits = 0;
            }            

            Utility.SetReportInfoForTransfer(this.RepInfo);
			Response.Redirect("NewReportWizard_Format.aspx");
		}
	}
}
