﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QueueServiceClient;

public partial class SourceControl : System.Web.UI.Page
{
	public Dictionary<string, string> RegisteredFiles
	{
		get { if (this.ViewState["rf"] != null) return (Dictionary<string, string>)this.ViewState["rf"]; else return null; }
		set { this.ViewState["rf"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
			LoadFileList();
    }

	private void LoadFileList()
	{
		this.RegisteredFiles = SourceControlClient.GetRegisteredFiles();

		registeredFilesRep.DataSource = this.RegisteredFiles.Keys;
		registeredFilesRep.DataBind();
	}

	protected void DownloadBtn_Click(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null)
		{
			foreach (string fileName in this.RegisteredFiles.Keys)
			{
				if (string.Compare(fileName, e.CommandArgument.ToString(), false) == 0)
				{
					byte[] fileData = SourceControlClient.GetFileData(this.RegisteredFiles[fileName]);
					if (fileData != null && fileData.Length > 0)
					{
						Response.ClearContent();
						Response.ClearHeaders();
						Response.ContentType = "application/octet-stream";
						Response.AppendHeader("content-disposition", "attachment; filename=" + fileName + ";");
						Response.BinaryWrite(fileData);
						Response.End();
					}
					break;
				}
			}
		}
	}

	protected void RemoveBtn_Click(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null)
		{
			SourceControlClient.RemoveFile(e.CommandArgument.ToString(), "Administrator", "File Deleted from Admin Interface");
		}
		LoadFileList();
	}
}