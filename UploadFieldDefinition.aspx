﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="UploadFieldDefinition.aspx.cs" Inherits="UploadFieldDefinition" %>

<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">
 	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Upload XML</td>
						<td class="widgetTop" style="width: 30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align: right;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									
									<td style="width: 10px;">
										&nbsp;</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<div class="widget">
					<div class="title">AMI Field Definitions XML File</div>
					<table width="100%" border="0" cellpadding="0" cellspacing="14">
						<tr>
							<td style="width:150px;">Device Type:</td>
							<td>
								<asp:dropdownlist id="deviceTypeIdList" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true">
									<asp:listitem text="Select..." value=""></asp:listitem>
								</asp:dropdownlist>
								<asp:requiredfieldvalidator id="v3" runat="server" controltovalidate="deviceTypeIdList" display="Dynamic" errormessage="* Required"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td style="width:150px;">AMI Version No:</td>
							<td>
								<asp:textbox id="amiVersion" runat="server"></asp:textbox>
								<asp:requiredfieldvalidator id="v2" runat="server" controltovalidate="amiVersion" display="Dynamic" errormessage="* Required"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td style="width:150px;">File:</td>
							<td>
								<asp:fileupload id="fileUploadCntrl" width="220" runat="server" />
								<asp:requiredfieldvalidator id="v" runat="server" controltovalidate="fileUploadCntrl" display="Dynamic" errormessage="* Required"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td colspan="2">
								<div class="goButton"><asp:linkbutton runat="server" id="upload" onclick="UploadBtn_Click">Upload</asp:linkbutton></div>
							</td>
						</tr>
						<tr>
							<td colspan="2"><b><asp:label id="successLbl" visible="false" runat="server">Your file has been uploaded successfully.</asp:label></b></td>
						</tr>
					</table>
				</div>
			</td>
		</tr>
	</table>
</asp:Content>

