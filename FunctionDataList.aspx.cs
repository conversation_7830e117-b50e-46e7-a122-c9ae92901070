using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class FunctionDataList : System.Web.UI.Page
{
    public int CellId
    {
        get { if (this.ViewState["c"] != null) return (int)this.ViewState["c"]; else return 0; }
        set { this.ViewState["c"] = value; }
    }

    public int SessionId
    {
        get { if (this.ViewState["s"] != null) return (int)this.ViewState["s"]; else return 0; }
        set { this.ViewState["s"] = value; }
    }

    public int TransactionId
    {
        get { if (this.ViewState["t"] != null) return (int)this.ViewState["t"]; else return 0; }
        set { this.ViewState["t"] = value; }
    }

    public int NodeNumber
    {
        get { if (this.ViewState["n"] != null) return (int)this.ViewState["n"]; else return 0; }
        set { this.ViewState["n"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!string.IsNullOrEmpty(Request.Params["c"]) && !string.IsNullOrEmpty(Request.Params["s"]) && !string.IsNullOrEmpty(Request.Params["t"]))
        {
            this.CellId = Convert.ToInt32(Request.Params["c"]);
            this.SessionId = Convert.ToInt32(Request.Params["s"]);
            this.TransactionId = Convert.ToInt32(Request.Params["t"]);
            this.NodeNumber = Convert.ToInt32(Request.Params["n"]);

            LoadData();
        }
    }

    private void LoadData()
    {
        DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFunctionDataValues", this.CellId, this.SessionId, this.TransactionId, this.NodeNumber);

        ListRepeater.DataSource = ds;
        ListRepeater.DataBind();
    }
}
