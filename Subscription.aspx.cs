using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Subscription : System.Web.UI.Page
{
	public string UserName
	{
		get { if (this.ViewState["u"] != null) return (string)this.ViewState["u"]; else return Utility.GetUserName(); }
		set { this.ViewState["u"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			sessionList.DataSource = Utility.GetActiveSessionsList();
			sessionList.DataBind();

			LoadSubscriptionSettings();
		}
    }

	private void LoadSubscriptionSettings()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadUserSubscription", UserName);
		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				this.allActiveCheck.Checked = !DataFormatter.getBool(row, "FilterSessions");
				if (this.allActiveCheck.Checked)
					this.sessionList.Enabled = false;
				else
					this.sessionList.Enabled = true;

				this.generalSessionsCheck.Checked = DataFormatter.getBool(row, "ReceiveGeneral");
				this.receiveEmailCheck.Checked = DataFormatter.getBool(row, "ReceiveEmailCopy");
				this.observCheck.Checked = DataFormatter.getBool(row, "ReceiveObservations");
				
				if (DataFormatter.getBool(row, "UrgentOnly"))
					this.messageTypeList.SelectedValue = "1";
				else
					this.messageTypeList.SelectedValue = "0";

				this.emailAddressBox.Text = DataFormatter.getString(row, "EmailAddress");
				EmailCheck_Changed(null, null);
			}
		}

		if (ds.Tables[1] != null)
		{
			if (!this.allActiveCheck.Checked)
			{
				foreach (DataRow row in ds.Tables[1].Rows)
				{
					if (this.sessionList.Items.FindByValue(DataFormatter.getInt32(row, "SessionId").ToString()) != null)
						((ListItem)this.sessionList.Items.FindByValue(DataFormatter.getInt32(row, "SessionId").ToString())).Selected = true;
				}
			}
		}
	}

	protected void AllSessionsCheck_Changed(object sender, EventArgs e)
	{
		if (this.allActiveCheck.Checked)
		{
			foreach (ListItem item in sessionList.Items)
			{
				item.Selected = false;
			}
			sessionList.Enabled = false;
		}
		else
		{
			sessionList.Enabled = true;
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			bool urgentOnly = false;

			if (this.messageTypeList.SelectedValue.Equals("1"))
				urgentOnly = true;

			//remove any old sessions subscriptions
			SqlHelper.ExecuteNonQuery("RPT_DeleteSubscriptionSessions", this.UserName);

			SqlHelper.ExecuteNonQuery("RPT_UpdateSubscription", this.UserName, !this.allActiveCheck.Checked, this.generalSessionsCheck.Checked, 
				observCheck.Checked, urgentOnly, this.receiveEmailCheck.Checked, this.emailAddressBox.Text.Trim(), DateTime.Now);

			//insert individual sessions
			if (this.allActiveCheck.Checked == false)
			{
				foreach (ListItem item in sessionList.Items)
				{
					if (item.Selected)
						SqlHelper.ExecuteNonQuery("RPT_InsertSubscriptionSession", this.UserName, item.Value);
				}
			}

			//close the window
			string closeScript = "<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.forms[0].submit();\r\n</script>";
			Page.ClientScript.RegisterStartupScript(GetType(), "CloseScript", closeScript);
		}
	}

	protected void EmailCheck_Changed(object sender, EventArgs e)
	{
		this.emailAddressBox.Visible = this.receiveEmailCheck.Checked;
		this.EmailFormatVal.Visible = this.receiveEmailCheck.Checked;
		this.EmailReqVal.Visible = this.receiveEmailCheck.Checked;
	}
}
