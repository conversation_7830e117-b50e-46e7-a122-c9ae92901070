<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Setup.aspx.cs" Inherits="NewReportWizard_Setup" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 1 of 4 &nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title">Step 1: Report Setup</div>
					<div class="rowHeading">Report Name:</div>
					<br />
					<asp:textbox id="reportNameField" runat="server" width="300" cssclass="entryControl"></asp:textbox>
					<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="reportNameField" errormessage="*" display="dynamic" cssclass="error"></asp:requiredfieldvalidator>
					<br /><br />
					<div class="rowHeading">Report Type:</div>
					<br />
					<asp:dropdownlist id="reportTypeList" runat="server" cssclass="entryControl" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" onselectedindexchanged="ReportTypeList_SelectedIndexChanged">
						<asp:listitem text="Select..." value=""></asp:listitem>
					</asp:dropdownlist>
					<asp:requiredfieldvalidator id="v2" runat="server" controltovalidate="reportTypeList" errormessage="*" display="dynamic" cssclass="error"></asp:requiredfieldvalidator>
					<br />&nbsp;<br />&nbsp;<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

