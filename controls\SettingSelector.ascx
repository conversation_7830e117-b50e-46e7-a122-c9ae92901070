<%@ Control Language="C#" AutoEventWireup="true" CodeFile="SettingSelector.ascx.cs" Inherits="controls_SettingSelector" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<script type="text/javascript" language="javascript">
    function checkAllNodes(val)
	{
		var treeView = $find("<%= settingFieldsTree.ClientID %>");

		var nodes = treeView.get_allNodes();
		if (nodes.length > 0)
		{ 
			for (var i = 0; i < nodes.length; i++)
			{
				nodes[i].set_checked(val);
			}
		} 
	}	
    function nodeChecked(sender, args)
    {
		var curNode = args.get_node();
        var childNode = null;
        var childNodes = curNode.get_nodes(); 
        var subNodes = null;
        
		if (curNode.get_checked() == true)
		{
			//expand and check all chidren
			curNode.set_expanded(true);

			if (childNodes.get_count() > 0)
			{
				for (var i = 0; i < childNodes.get_count(); i++)
				{
				    childNode = childNodes.getNode(i);
					childNode.set_checked(true);
					subNodes = childNode.get_nodes();
			        if (subNodes.get_count() > 0)
			        {
				        for (var j = 0; j < subNodes.get_count(); j++)
				        {
					        subNodes.getNode(j).set_checked(true);
				        }
			        }						
				}
			}
		}
		else
		{
			//uncheck children
			if (childNodes.get_count() > 0)
			{
				for (var i = 0; i < childNodes.get_count(); i++)
				{
				    childNode = childNodes.getNode(i);
					childNode.set_checked(false);
					subNodes = childNode.get_nodes();
			        if (subNodes.get_count() > 0)
			        {
				        for (var j = 0; j < subNodes.get_count(); j++)
				        {
					        subNodes.getNode(j).set_checked(false);
				        }
			        }						
				}
			}
		}
    }
    function unselectNode(sender, args)
    {
        args.get_node().set_selected(false);        
    }
</script>

<telerik:radcodeblock id="codeBlockCntrl" runat="server">
	<div>
	   <table border="0" cellpadding="0" cellspacing="0">
			<tr>
				<td valign="top" style="padding-left:1px;">				
					<div style="height: 170px; width:278px; border: solid 1px #999999; overflow:auto;" >
						<telerik:RadTreeView id="settingFieldsTree" runat="Server" checkboxes="true" onclientnodeclicked="unselectNode" onclientnodechecked="nodeChecked" 
							EnableDragAndDrop="false" EnableDragAndDropBetweenNodes="false" Skin="WebBlue"></telerik:RadTreeView>
					</div>
				</td>
			</tr>
		</table>
	</div>
	
	<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
		<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
	</telerik:RadAjaxLoadingPanel>
</telerik:radcodeblock>
