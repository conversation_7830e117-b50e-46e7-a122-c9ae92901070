using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Entity : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				EntityGrid.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}

		this.EntityNavBar.ItemChangedEvent += new CommandEventHandler(EntityNavBar_ItemChangedEvent);
		
		LoadEntityType();
		BindGridData();
	}

	public void RebindData(object sender, EventArgs e)
	{
		//do nothing, data rebound on every page load.
	}

	void EntityNavBar_ItemChangedEvent(object sender, CommandEventArgs e)
	{
		EntityGrid.CurrentPageIndex = 0;
		LoadEntityType();

		BindGridData();
	}

	protected void EntityGrid_OnItemDataBound(object sender, Telerik.Web.UI.GridItemEventArgs e)
	{
		if (e.Item != null && e.Item.DataItem != null)
		{
			Label trackingLbl = null;

			if (e.Item.FindControl("TrackingStatusLabel") != null)
				trackingLbl = (Label)e.Item.FindControl("TrackingStatusLabel");

			if (trackingLbl != null)
			{
				string trackAsSetting = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsSetting", "true", "false", "");
				string trackAsStatistic = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsStatistic", "true", "false", "");
				string trackAsMediaCount = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsMediaCount", "true", "false", "");

				if (DataFormatter.FormatBool(e.Item.DataItem, "DisableTrackingRequested", "true", "false", "").Equals("true"))
				{
					trackingLbl.Text = "<b>Disabling</b>";
				}
				else
				{
					if (Convert.ToBoolean(trackAsSetting) || Convert.ToBoolean(trackAsStatistic) || Convert.ToBoolean(trackAsMediaCount))
						trackingLbl.Text = "<span style=\"color:#666666;font-weight:bold;\">Enabled</span>";
					else
						trackingLbl.Text = "<span style=\"color:#aaaaaa;\">Disabled</span>";
				}
			}
		}
	}

	private void LoadEntityType()
	{
		switch (this.EntityNavBar.EntityTypeId)
		{
			case DieboldConstants.STATUS_ENTITY_KEY:
				this.EntityNameLabel.Text = "Status Entity";
				break;
			case DieboldConstants.METRIC_ENTITY_KEY:
				this.EntityNameLabel.Text = "Metric Entity";
				break;
			case DieboldConstants.INFO_ENTITY_KEY:
				this.EntityNameLabel.Text = "Info Entity";
				break;
			case DieboldConstants.RESULT_DATA_KEY:
				this.EntityNameLabel.Text = "Result Data";
				break;
			default:
				this.EntityNameLabel.Text = "Entity";
				break;
		}
	}

	private void BindGridData()
	{
		if (!string.IsNullOrEmpty(this.EntityNavBar.EntityTypeId))
            EntityGrid.DataSource = SqlHelper.ExecuteDataset("RPT_LoadFieldsByTypes", this.EntityNavBar.DeviceTypeId, this.EntityNavBar.EntityTypeId);
		else
            EntityGrid.DataSource = SqlHelper.ExecuteDataset("RPT_LoadFieldsByTypes", this.EntityNavBar.DeviceTypeId, "");

        EntityGrid.DataBind();
	}
}
