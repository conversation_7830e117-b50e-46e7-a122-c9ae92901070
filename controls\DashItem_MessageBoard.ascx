<%@ Control Language="C#" AutoEventWireup="true" CodeFile="DashItem_MessageBoard.ascx.cs" Inherits="controls_DashItem_MessageBoard" %>
<%@ Register TagPrefix="telerik" Namespace="Telerik.Web.UI" Assembly="Telerik.Web.UI" %>
<link href="../style.css" rel="stylesheet" type="text/css" />

<telerik:RadAjaxManagerProxy ID="RadAjaxManagerProxy1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="SaveButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="CancelButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="HidEditButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:RadAjaxManagerProxy>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<telerik:RadWindowManager id="RadWindowManager1" runat="server" height="500" width="400" ></telerik:RadWindowManager>

<telerik:radcodeblock id="panelCodeBlock" runat="server">
	<asp:panel id="DisplayPanel" runat="server">
		<div class="title" style="padding-left:10px;padding-bottom:2px;">Messages</div>
		<asp:repeater id="MessagesRepeater" runat="server" enableviewstate="true">
			<headertemplate><table style="padding-top:2px;width:100%;" border="0" cellpadding="0" cellspacing="0" class="body"></headertemplate>
			<itemtemplate>
				<tr>
					<td style="width:40px;padding-top:12px;"><center><img runat="server" visible='<%# ((MessageInfo)Container.DataItem).IsUrgent %>' src="~/images/alert.gif" width="20" height="20" alt="" /></center></td>
					<td style="padding-top:12px;">
						<b><%# DataFormatter.FormatDate(((MessageInfo)Container.DataItem).LastModified, "M/dd/yy h:mmtt", "")%></b><br />						
						<%# ((MessageInfo)Container.DataItem).MessageBody.Length > 90 ? string.Format("{0}<a style=\"cursor:pointer;\" onclick=\"window.radopen('popupmessage.aspx?m={1}'); return false;\">More</a>", DataFormatter.TruncateString(((MessageInfo)Container.DataItem).MessageBody, 87, 90, false), ((MessageInfo)Container.DataItem).MessageId) : ((MessageInfo)Container.DataItem).MessageBody %>
					</td>
				</tr>
			</itemtemplate>
			<footertemplate></table></footertemplate>
		</asp:repeater>
		<br />
		<table border="0" cellpadding="0" cellspacing="0" class="widget">
			<tr>
				<td style="width:90px;" class="leftPad"><div class="goButton"><asp:linkbutton style="padding-left:14px;" runat="server" onclick="ViewAll_Click" id="viewAllButton" causesvalidation="false">View All</asp:linkbutton></div></td>
			</tr>
		</table>
	</asp:panel>

	<asp:panel id="SettingsPanel" runat="server" visible="false" cssclass="body">
		<div class="title" style="padding-left:10px;padding-bottom:5px;padding-top:0px;">Edit Settings</div>
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td class="rowHeading">Number of Messages:</td>
				</tr>
				<tr>
					<td style="padding:10px 0px 10px 0px;">
						<asp:dropdownlist id="NumMessagesList" cssclass="entryControl" runat="server">
							<asp:listitem text="1" value="1"></asp:listitem>
							<asp:listitem text="2" value="2"></asp:listitem>
							<asp:listitem text="3" value="3"></asp:listitem>
							<asp:listitem text="4" value="4"></asp:listitem>
							<asp:listitem text="5" value="5"></asp:listitem>
							<asp:listitem text="6" value="6"></asp:listitem>
							<asp:listitem text="7" value="7"></asp:listitem>
							<asp:listitem text="8" value="8"></asp:listitem>
							<asp:listitem text="9" value="9"></asp:listitem>
							<asp:listitem text="10" selected="true" value="10"></asp:listitem>
						</asp:dropdownlist>
					</td>
				</tr>
			</table>
			<br />
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td style="width:90px;" class="leftPad"><div class="goButton"><asp:linkbutton style="padding-left:14px;" runat="server" onclick="SaveSettings_Click" id="SaveButton" validationgroup="settingsPanel">Save</asp:linkbutton></div></td>
					<td><div class="cancelButton"><asp:linkbutton runat="server" style="padding-left:14px;" onclick="CancelSettings_Click" id="CancelButton" causesvalidation="false">Cancel</asp:linkbutton></div></td>
				</tr>
			</table>
			<br />
	</asp:panel>
	<asp:button runat="server" onclick="EditSettings_Click" id="HidEditButton" style="display:none;" usesubmitbehavior="false" causesvalidation="false"></asp:button>
</telerik:radcodeblock>