<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Reprocess.aspx.cs" Inherits="Reprocess" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    function ToggleStartDatePopup() { $find("<%= startDateField.ClientID %>").showPopup(); }  
	function ToggleEndDatePopup() { $find("<%= endDateField.ClientID %>").showPopup(); }
		
    function ToggleStartTimePopup()
    {
        var picker = $find("<%= startTimeField.ClientID %>");
        picker.showTimePopup();
    }  
    
    function ToggleEndTimePopup()
    {
        var picker = $find("<%= endTimeField.ClientID %>");
        picker.showTimePopup();
    }       
</script>

<asp:panel id="DefaultPanel" runat="server">
<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Reprocess Data</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">
				<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
					<AjaxSettings>
						<telerik:AjaxSetting AjaxControlID="deviceTypeList">
							<UpdatedControls>
								<telerik:AjaxUpdatedControl ControlID="devicesList" LoadingPanelID="LoadingPanel1">
								</telerik:AjaxUpdatedControl>
							</UpdatedControls>
						</telerik:AjaxSetting>
					</AjaxSettings>
				</telerik:RadAjaxManager>
				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr style="padding-top:10px;">
						<td style="width:50%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>
									<td style="width:130px;" class="rowHeading">Uploader Version:</td>
									<td>
										<asp:radiobuttonlist id="uploaderVersion" runat="server" autopostback="true" onselectedindexchanged="uploaderVersionRadioChanged">
											<asp:listitem text="1.0 - 1.22" value="1"></asp:listitem> 
											<asp:listitem text="2.0 or greater" value="2" selected="True"></asp:listitem> 
										</asp:radiobuttonlist>
									</td>
								</tr>
								<tr id="SessionRow" runat="server">	
									<td style="width:130px;" class="rowHeading">Session:</td>
									<td>
									    <asp:listbox id="sessionList" selectionmode="multiple" runat="server" width="400px" rows="6" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:listbox>
									    <asp:requiredfieldvalidator id="vs" runat="server" controltovalidate="sessionList" errormessage="* Select at least one session" display="Dynamic" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr id="DeviceTypeRow" runat="server" visible="false">	
									<td style="width:130px;" class="rowHeading">Device Type:</td>
									<td>
									    <telerik:radcombobox id="deviceTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" autopostback="true"
                                             onselectedindexchanged="DevceTypeList_SelectedIndexChanged" causesvalidation="false">
                                        </telerik:radcombobox>
									</td>
								</tr>
								<tr id="DeviceRow" runat="server" visible="false">	
									<td style="width:130px;" class="rowHeading">
										Device:
										<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
											<asp:Image id="Image1" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
										</telerik:RadAjaxLoadingPanel>
									</td>
									<td>
										<asp:listbox id="devicesList" rows="6" width="250" datatextfield="Name" datavaluefield="Code" runat="server" selectionmode="multiple"></asp:listbox>
										<asp:requiredfieldvalidator id="va" runat="server" controltovalidate="devicesList" display="dynamic" errormessage="* Select at least one device" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Start Date:</td>
									<td>
										<telerik:RadDatePicker id="startDateField" runat="server" style="padding-top:1px;" width="80">        
											<calendar skin="Default2006" showrowheaders="false"></calendar>       
											<DatePopupButton Visible="False"></DatePopupButton>
											<DateInput onclick="ToggleStartDatePopup()"></DateInput>                           	                                             
										</telerik:RadDatePicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator2" runat="server" enableclientscript="false" controltovalidate="startDateField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<telerik:radtimepicker id="startTimeField" runat="server" timepopupbutton-visible="false" width="80">
											<dateinput onclick="ToggleStartTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
											<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
										</telerik:radtimepicker>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">End Date:</td>
									<td>
										<telerik:RadDatePicker id="endDateField" runat="server" style="padding-top:1px;" width="80">        
											<calendar skin="Default2006" showrowheaders="false"></calendar>       
											<DatePopupButton Visible="False"></DatePopupButton>
											<DateInput onclick="ToggleEndDatePopup()"></DateInput>                           	                                             
										</telerik:RadDatePicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator4" runat="server" enableclientscript="false" controltovalidate="endDateField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<telerik:radtimepicker id="endTimeField" runat="server" timepopupbutton-visible="false" width="80">
											<dateinput onclick="ToggleEndTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
											<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
										</telerik:radtimepicker>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Files:</td>
									<td>
										<asp:checkbox id="includeEngFile" runat="server" text="Include Engineering Files" /><br />
										<asp:checkbox id="includeXMLFile" runat="server" text="Include RDTool XML Files" /><br />
										<asp:customvalidator onservervalidate="EnsureFileTypes" id="val" runat="server" display="dynamic" errormessage="* Select at least one file type to include" cssclass="error"></asp:customvalidator>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<asp:label id="ErrorLabel" visible="false" runat="server" cssclass="error entryControl">There was a problem submitting the files for reprocessing.<br /><br /></asp:label>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>	
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="executeButton" onclick="ExecuteButton_Click">Save</asp:linkbutton></div></td>
						<td style="width:120px;"><div class="cancelButton"><asp:linkbutton runat="server" id="cancelButton" onclick="CancelButton_Click" causesvalidation="false">Cancel</asp:linkbutton></div></td>
						<td>&nbsp;</td>
					</tr>
				</table>
				<br />
				<br />
			</div>
		</td>
	</tr>
</table>
</asp:panel>

</asp:Content>

