using System;
using System.Collections.Generic;
using System.Text;

public class VersionNumber : IComparable
{
	private static char[] DOT_DELIM = { '.' };
	public static readonly VersionNumber ZeroVersion = new VersionNumber(0, 0, 0, 0);
	public static readonly VersionNumber HotFixVersion = new VersionNumber(2, 3, 0, 0);
	public static readonly VersionNumber MaxVersion = new VersionNumber(Convert.ToUInt16(0xFFFF), Convert.ToUInt16(0xFFFF), Convert.ToUInt16(0xFFFF), Convert.ToUInt16(0xFFFF));

	public UInt16 MajorVersion = 0;
	public UInt16 MinorVersion = 0;
	public UInt16 DeviceVersion = 0;
	public UInt16 BuildVersion = 0;

	public VersionNumber()
	{
	}

	public VersionNumber(UInt16 majorVersion, UInt16 minorVersion, UInt16 deviceVersion, UInt16 buildVersion)
	{
		this.MajorVersion = majorVersion;
		this.MinorVersion = minorVersion;
		this.DeviceVersion = deviceVersion;
		this.BuildVersion = buildVersion;
	}
	
	public static string ConverToString(Int64 versionNumber)
	{
		UInt16 majorVersion = Convert.ToUInt16((versionNumber >> 48) & 0xFFFF);
		UInt16 minorVersion = Convert.ToUInt16((versionNumber >> 32) & 0xFFFF);
		UInt16 deviceVersion = Convert.ToUInt16((versionNumber >> 16) & 0xFFFF);
		UInt16 buildVersion = Convert.ToUInt16(versionNumber & 0xFFFF);

		if (majorVersion == 0 && minorVersion == 0 && deviceVersion == 0)
			return buildVersion.ToString();
		else
			return string.Format("{0}.{1}.{2}.{3}", majorVersion, minorVersion, deviceVersion, buildVersion);
	}

	public static Int64 ConverToInt64(string versionNumberString)
	{
		UInt16 majorVersion = 0;
		UInt16 minorVersion = 0;
		UInt16 deviceVersion = 0;
		UInt16 buildVersion = 0;

		if (!string.IsNullOrEmpty(versionNumberString))
		{
			string[] versionStrArr = versionNumberString.Split(DOT_DELIM);
			if (versionStrArr != null)
			{
				if (versionStrArr.Length >= 1)
					UInt16.TryParse(versionStrArr[versionStrArr.Length - 1], out buildVersion);

				if (versionStrArr.Length >= 2)
					UInt16.TryParse(versionStrArr[versionStrArr.Length - 2], out deviceVersion);

				if (versionStrArr.Length >= 3)
					UInt16.TryParse(versionStrArr[versionStrArr.Length - 3], out minorVersion);

				if (versionStrArr.Length >= 4)
					UInt16.TryParse(versionStrArr[versionStrArr.Length - 4], out majorVersion);
			}
		}

		return (0x1000000000000 * majorVersion) + (0x100000000 * minorVersion) + (0x10000 * deviceVersion) + buildVersion;
	}

	public static VersionNumber ParseDotNotation(string versionNumberString)
	{
		VersionNumber retVal = VersionNumber.ZeroVersion;

		if (!string.IsNullOrEmpty(versionNumberString))
		{
			if (string.Compare(versionNumberString, "0") == 0)
				retVal = VersionNumber.ZeroVersion;
			else if (string.Compare(versionNumberString, "0.0.0.0") == 0)
				retVal = VersionNumber.HotFixVersion;
			else
			{
				retVal = new VersionNumber(0, 0, 0, 0);
				string[] versionStrArr = versionNumberString.Split(DOT_DELIM);
				if (versionStrArr != null)
				{
					if (versionStrArr.Length >= 1)
						UInt16.TryParse(versionStrArr[versionStrArr.Length - 1], out retVal.BuildVersion);

					if (versionStrArr.Length >= 2)
						UInt16.TryParse(versionStrArr[versionStrArr.Length - 2], out retVal.DeviceVersion);

					if (versionStrArr.Length >= 3)
						UInt16.TryParse(versionStrArr[versionStrArr.Length - 3], out retVal.MinorVersion);

					if (versionStrArr.Length >= 4)
						UInt16.TryParse(versionStrArr[versionStrArr.Length - 4], out retVal.MajorVersion);
				}
			}
		}

		return retVal;
	}

	public static VersionNumber ParseInt64(Int64 versionNumber)
	{
		UInt16 majorVersion = Convert.ToUInt16((versionNumber >> 48) & 0xFFFF);
		UInt16 minorVersion = Convert.ToUInt16((versionNumber >> 32) & 0xFFFF);
		UInt16 deviceVersion = Convert.ToUInt16((versionNumber >> 16) & 0xFFFF);
		UInt16 buildVersion = Convert.ToUInt16(versionNumber & 0xFFFF);

		return new VersionNumber(majorVersion, minorVersion, deviceVersion, buildVersion);
	}

	public int CompareTo(object obj)
	{
		int retVal = 0;
		if (obj is VersionNumber)
		{
			VersionNumber verObj = (VersionNumber)obj;
			retVal = (this.MajorVersion - verObj.MajorVersion);
			if (retVal == 0)
				retVal = (this.MinorVersion - verObj.MinorVersion);
			if (retVal == 0)
				retVal = (this.DeviceVersion - verObj.DeviceVersion);
			if (retVal == 0)
				retVal = (this.BuildVersion - verObj.BuildVersion);
		}
		else
		{
			throw new InvalidCastException(string.Format("Object of type '{0}' can not be cast to QueueService.VersionNumber.", (obj == null ? "null" : obj.GetType().FullName)));
		}

		return retVal;

	}

	public override string ToString()
	{
		if (this.MajorVersion == 0 && this.MinorVersion == 0 && this.DeviceVersion == 0)
			return this.BuildVersion.ToString();
		else
			return string.Format("{0}.{1}.{2}.{3}", this.MajorVersion, this.MinorVersion, this.DeviceVersion, this.BuildVersion);
	}

	public Int32 ToInt32()
	{
		return (0x10000 * this.DeviceVersion) + this.BuildVersion;
	}

	public Int64 ToInt64()
	{
		return (0x1000000000000 * this.MajorVersion) + (0x100000000 * this.MinorVersion) + (0x10000 * this.DeviceVersion) + this.BuildVersion;
	}

}
