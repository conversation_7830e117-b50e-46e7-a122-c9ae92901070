<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditDevice.aspx.cs" Inherits="Popup_EditDevice" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Device</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Add/Edit Device</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="14" width="100%">
						<tr>
							<td class="rowHeading" style="width:150px;">Device Type Id</td>
							<td>
								<asp:dropdownlist id="deviceTypeList" runat="server" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" autopostback="true" onselectedindexchanged="deviceTypeList_SelectedIndexChanged"></asp:dropdownlist>
								<asp:requiredfieldvalidator id="val" runat="server" errormessage="* Required" controltovalidate="deviceTypeList" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Serial Number</td>
							<td>
								<asp:textbox runat="server" width="230" id="serialNumberField"></asp:textbox>
								<asp:requiredfieldvalidator id="val1" runat="server" errormessage="* Required" controltovalidate="serialNumberField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
								<asp:label id="serialError" visible="false" runat="server" style="color:#c00;">Serial numbers cannot contain '#' characters.</asp:label>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Device Status</td>
							<td>
								<asp:dropdownlist id="statusList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist>
							</td>
						</tr>
                        <tr runat="server" id="productLineRow" visible="false">
							<td class="rowHeading" style="width:150px;">Product Line</td>
							<td>
								<asp:dropdownlist id="productLineList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server">
                                    <asp:listitem text="Select..." value=""></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
                        <tr runat="server" id="familyLineRow" visible="false">
							<td class="rowHeading" style="width:150px;">Family Line</td>
							<td>
								<asp:dropdownlist id="familyLineList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server">
                                    <asp:listitem text="Select..." value=""></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
                        <tr runat="server" id="modelTypeRow" visible="false">
							<td class="rowHeading" style="width:150px;">Model Type</td>
							<td>
								<asp:dropdownlist id="modelTypeList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server">
                                    <asp:listitem text="Select..." value=""></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />A device already exists with the specified serial number.<br /></div>
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

