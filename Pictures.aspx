<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Pictures.aspx.cs" Inherits="Pictures" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10" style="background-color:#5b5551;">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Observation Pictures</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;"></td>
					</tr>
				</table>				
				<div class="widget" id="ViewPicturesDiv" runat="server">
					<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding:10px 10px 10px 0px; border-bottom:solid 1px #cccccc;">
						<tr>
							<td style="width:200px;">
								<asp:label id="label" runat="server" cssclass="entryControl">Picture: </asp:label>
								<select id="PictureList" style="width:100px;" runat="server" class="entryControl" onchange="ChangePicture(this.selectedIndex, this.options[this.selectedIndex].value);"></select>
							</td>
							<td style="width:130px;">
								&nbsp;<%--<div class="goButton"><asp:linkbutton id="UploadButton" runat="server" onclick="ShowUploadButton_Click" causesvalidation="false">Upload Pictures</asp:linkbutton></div>--%>
							</td>
							<td style="padding-left:60px;">
								<img src="images/buttons/minus.png" alt="Zoom Out" border="0" onclick="ZoomOut();" />&nbsp;<img id="ZoomStrip" src="images/zoom.png" width="150" alt="" border="0" />&nbsp;<img src="images/buttons/add.png" alt="Zoom In" border="0" onclick="ZoomIn();" /><img id="MagMarker" style="display:none; position:absolute;" src="images/buttons/solid.png" alt="" border="0"  />
							</td>
						</tr>
					</table>
					<table width="100%" border="0" cellpadding="0" cellspacing="14" onresize="resizeView()">
						<tr>
							<td style="border:solid 1px #000000;">
								<div id="ViewPort" ondblclick="ZoomIn();" style="width: 100%; height: 400px; position:relative; border: solid 1px #0000ff; overflow:hidden;"><img id="CurrentPicture" style="position:absolute; left:0px; top:0px;" src="images/spacer.png" border="0" onerror="DisplayNoImage();" onabort="DisplayNoImage();" /></div>
							</td>
						</tr>
					</table>
				</div>
				<div class="widget" id="ErrorDiv" style="display:none;" runat="server">
					<table width="80%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td>
								<div class="title">
									Unable to identify the associated observation record.<br /><br />
									If you are in the process of creating a new observation record, make sure it is saved before trying to upload pictures.<br /><br />
								</div>
							</td>
						</tr>
					</table>
				</div>
				<div class="widget" id="NoPicturesDiv" style="display:none;" runat="server">
					<table width="80%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td>
								<div class="title">
									No pictures could be found.
									<asp:label id="errorLbl" runat="server"></asp:label>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</td>
		</tr>
	</table>		
    <script type="text/javascript" language="javascript">
        var origWidth = 0;	    
        var origHeight = 0;	    
        var viewPortWidth = 650;
        var viewPortHeight = 400;
        var viewPortX = 0;
        var viewPortY = 0;
        var mag = 1;
        var minMag = 1;
        var maxMag = 10;
        var pictureListIdx = -1;
        var dragObject = null;
        var mouseOffset = null;
        var imgCache = new Array();
        var picList = document.getElementById('<%= this.PictureList.ClientID %>');
        var pic = document.getElementById('CurrentPicture');
        var vp = document.getElementById('ViewPort');
        var zs = document.getElementById('ZoomStrip');
        var mm = document.getElementById('MagMarker');
	    
	    function OnPictureLoad()
	    {
	        var idx = picList.selectedIndex;
	        if(idx >= 0)
	        {
    	        var newPictureUrl = picList.options[idx].value;
                origWidth = imgCache[idx].width;
                origHeight = imgCache[idx].height;
    	        
                var widthMag = 1;
                if(origWidth && origWidth > 0)
                    widthMag = viewPortWidth / origWidth;

                var heightMag = 1;
                if(origHeight && origHeight > 0)
                    heightMag = viewPortHeight / origHeight;

                if(widthMag < heightMag)
                    mag = widthMag;
                else
                    mag = heightMag;

                minMag = mag;

                viewPortX = 0;
                viewPortY = 0;

                pic.src=newPictureUrl;
                FormatPicture();
	        }
	    }
	    
	    function ChangePicture(idx, newPictureUrl)
	    {
	        DisplayLoading();
	        
	        imgCache[idx] = new Image();
	        imgCache[idx].onload = OnPictureLoad;
	        imgCache[idx].onerror = DisplayNoImage;
	        imgCache[idx].onabort = DisplayNoImage;	        
	        imgCache[idx].src = newPictureUrl;
	    }
	    
	    function EnsureViewPortLimits()
	    {
	        var imgWidthHalf = parseInt((origWidth * mag)/2, 10);
	        var imgHeightHalf = parseInt((origHeight * mag)/2, 10);

            var minVisibleX = (viewPortWidth / 2);
            var minVisibleY = (viewPortHeight / 2);
            var negViewX = (viewPortX * -1);
            var negViewY = (viewPortY * -1);

            if(negViewX > 0 && negViewX > imgWidthHalf)
                viewPortX = 0 - imgWidthHalf;
            if(negViewX < 0 && negViewX < (0-imgWidthHalf))
                viewPortX = imgWidthHalf;
            if(negViewY > 0 && negViewY > imgHeightHalf)
                viewPortY = 0 - imgHeightHalf;
            if(negViewY < 0 && negViewY < (0-imgHeightHalf))
                viewPortY = imgHeightHalf;
	    }
	    
	    function ZoomIn(factor)
	    {
	        if(!factor || factor==0)
	            factor = 1.5;

            ZoomTo(mag * factor);
	    }

	    function ZoomOut(factor)
	    {
	        if(!factor || factor==0)
	            factor = 1.5;
	            
            ZoomTo(mag / factor);
	    }
	    
	    function ZoomTo(newMag)
	    {
	        if(!newMag || newMag==0)
	            newMag = mag;

   	        if(minMag > newMag)
   	            newMag = minMag;
   	        if(maxMag < newMag)
   	            newMag = maxMag;
     
	        var factor = mag / newMag;

            mag = newMag;
            viewPortX = parseInt((viewPortX / factor), 10);
            viewPortY = parseInt((viewPortY / factor), 10);
	        FormatPicture();
	    }

	    function DisplayLoading()
        {
            pic.src='images/loading.gif';
	        pic.style.width = '130px';
	        pic.style.height = '60px';

	        pic.style.clip = 'rect(0px ' + viewPortWidth + 'px ' + viewPortHeight + 'px 0px)';
	        pic.style.left = ((viewPortWidth / 2) - 65) + 'px';
	        pic.style.top = ((viewPortHeight / 2) - 30) + 'px';
        }

        function DisplayNoImage()
        {
            pic.src='images/noImage.jpg';
	        pic.style.width = '100px';
	        pic.style.height = '133px';

	        pic.style.clip = 'rect(0px ' + viewPortWidth + 'px ' + viewPortHeight + 'px 0px)';
	        pic.style.left = ((viewPortWidth / 2) - 50) + 'px';
	        pic.style.top = ((viewPortHeight / 2) - 68) + 'px';
        }
        
	    function FormatPicture()
	    {
            EnsureViewPortLimits();
            var leftMargin = parseInt((viewPortWidth - parseInt(origWidth * mag, 10)) / 2);
            var topMargin = parseInt((viewPortHeight - parseInt(origHeight * mag, 10)) / 2);
            
	        pic.style.clip = 'rect(' + (viewPortY - topMargin) + 'px ' + (viewPortX - leftMargin + viewPortWidth) + 'px ' 
	            + (viewPortY - topMargin + viewPortHeight) + 'px ' + (viewPortX - leftMargin) + 'px)';

	        pic.style.width = parseInt(origWidth * mag, 10) + 'px';
	        pic.style.height = parseInt(origHeight * mag, 10) + 'px';

	        pic.style.left = ((viewPortX * -1) + leftMargin) + 'px';
	        pic.style.top = ((viewPortY * -1) + topMargin) + 'px';
	        
	        if(!zs)
	        	zs = document.getElementById('ZoomStrip');
	        zsPos = getPosition(zs);
            mm.style.left=(zsPos.x - 8 + parseInt(mag * zs.width / maxMag)) + "px";
            mm.style.top=zsPos.y + "px";
            mm.style.display='';
	    }
	    
        function mouseCoords(ev){
	        if(ev.pageX || ev.pageY){
		        return {x:ev.pageX, y:ev.pageY};
	        }
	        return {
		        x:ev.clientX + document.body.scrollLeft - document.body.clientLeft,
		        y:ev.clientY + document.body.scrollTop  - document.body.clientTop
	        };
        }

        function makeClickable(object){
	        object.onmousedown = function(){
		        dragObject = this;
	        }
        }

        function makeDraggable(item){
	        if(!item) return;
	        item.onmousedown = function(ev){
		        dragObject  = this;
		        mouseOffset = getMouseOffset(this, ev);
		        return false;
	        }
        }

        function mouseUp(ev){
	        dragObject = null;
        }

        function getMouseOffset(target, ev)
        {
	        ev = ev || window.event;

	        var docPos    = getPosition(target);
	        var mousePos  = mouseCoords(ev);
	        return {x:mousePos.x - docPos.x, y:mousePos.y - docPos.y, vx:viewPortX, vy:viewPortY};
        }

        function getPosition(a)	// Find recursive offsetLeft/offsetTop
        {
	        var left = 0;
	        var top  = 0;

	        while(a!=null)
	        {
		        if(a.id=='ViewPort')
    		        break;
		        left+=a["offsetLeft"];
		        top+=a["offsetTop"];
		        a=a.offsetParent;
	        }

	        return {x:left, y:top};
        }

        function mouseMove(ev){
	        ev = ev || window.event;
	        var mousePos = mouseCoords(ev);

	        if(dragObject){
                viewPortX = (mouseOffset.vx + mouseOffset.x - mousePos.x);
                viewPortY = (mouseOffset.vy + mouseOffset.y - mousePos.y);
                FormatPicture();
		        return false;
	        }
        }
        
        function mouseWheel(event){
            var delta = 0;
            if (!event) /* For IE. */
                event = window.event;
        
            if (event.wheelDelta) {
                delta = event.wheelDelta/120;
            } 
            
            if (delta && delta < 0)
                ZoomOut(1.05);
            else if (delta && delta > 0)
                ZoomIn(1.05);

            if (event.preventDefault)
                    event.preventDefault();
	        event.returnValue = false;
        }

        function resizeView()
        {
            var vp = document.getElementById('ViewPort');
            viewPortWidth = vp.clientWidth;
            viewPortHeight = parseInt(viewPortWidth*4/7,10);
            vp.style.height= viewPortHeight + 'px';
            FormatPicture();
        }
        
        vp.onmouseup = mouseUp;
        vp.onmousemove = mouseMove;
        vp.onmouseout = mouseUp;
        vp.onmousewheel = mouseWheel;
        makeDraggable(vp);
        window.onmousewheel = document.onmousewheel = mouseWheel;

        resizeView();
        if(picList.selectedIndex >= 0)
		    ChangePicture(picList.selectedIndex, picList.options[picList.selectedIndex].value);

    </script>
</asp:Content>
