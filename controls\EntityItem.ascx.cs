using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class controls_EntityItem : System.Web.UI.UserControl
{
	public event CommandEventHandler ItemChangedEvent;

	public bool ShowEntityList
	{
		get { if (this.ViewState["iv"] != null) return (bool)this.ViewState["iv"]; else return true; }
		set { this.ViewState["iv"] = value; }
	}

	public bool ShowDeviceList
	{
		get { if (this.ViewState["sd"] != null) return (bool)this.ViewState["sd"]; else return true; }
		set { this.ViewState["sd"] = value; }
	}

	public string DeviceTypeId
	{
		get { return (string)this.ViewState["d"]; }
		set { this.ViewState["d"] = value; }
	}

	public string EntityTypeId
	{
		get { return (string)this.ViewState["e"]; }
		set { this.ViewState["e"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]))
				this.DeviceTypeId = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.ENTITY_KEY]))
				this.EntityTypeId = Request.Params[DieboldConstants.ENTITY_KEY];

			DeviceTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
			DeviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
			DeviceTypeList.DataBind();

			EntityList.Items.Add(new RadComboBoxItem("All", ""));
			EntityList.Items.Add(new RadComboBoxItem("Status Entity", DieboldConstants.STATUS_ENTITY_KEY));
			EntityList.Items.Add(new RadComboBoxItem("Metric Entity", DieboldConstants.METRIC_ENTITY_KEY));
			EntityList.Items.Add(new RadComboBoxItem("Info Entity", DieboldConstants.INFO_ENTITY_KEY));
			EntityList.Items.Add(new RadComboBoxItem("Result Data", DieboldConstants.RESULT_DATA_KEY));

			if (!string.IsNullOrEmpty(this.DeviceTypeId) && this.DeviceTypeList.Items.FindItemByValue(this.DeviceTypeId) != null)
			{
				this.DeviceTypeList.SelectedValue = this.DeviceTypeId;
				DeviceTypeList_SelectedIndexChanged(null, null);
			}

			if (!string.IsNullOrEmpty(this.EntityTypeId) && this.EntityList.Items.FindItemByValue(this.EntityTypeId) != null)
				this.EntityList.SelectedValue = this.EntityTypeId;
		}

		if (!ShowEntityList)
			EntityTypeSelection.Attributes.Add("style", "display:none;");

		if (!ShowDeviceList)
			DeviceTypeSelection.Attributes.Add("style", "display:none;");
	}

	protected void DeviceTypeList_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(DeviceTypeList.SelectedValue))
			this.DeviceTypeId = DeviceTypeList.SelectedValue;

			
		if (ItemChangedEvent != null && !string.IsNullOrEmpty(this.DeviceTypeList.SelectedValue))
			ItemChangedEvent(this.DeviceTypeList.ClientID, new CommandEventArgs(DieboldConstants.COMMAND_DEVICE_TYPE_CHANGED, this.DeviceTypeList.SelectedValue));
	}

	protected void EntityList_SelectedIndexChanged(object sender, EventArgs e)
	{
		this.EntityTypeId = this.EntityList.SelectedValue;

		if (ItemChangedEvent != null)
			ItemChangedEvent(this.EntityList.ClientID, new CommandEventArgs(DieboldConstants.COMMAND_ENTITY_TYPE_CHANGED, this.EntityList.SelectedValue));
	}
}
