﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;

public partial class controls_RelativeDate : System.Web.UI.UserControl
{
	public DateTime FixedDate
	{
		get
		{
			if (StartTypeFixed.Checked && startDateField.SelectedDate.HasValue)
				return (DateTime)startDateField.SelectedDate;
			else
				return DateTime.MinValue;
		}
		set
		{
			if (value != DateTime.MinValue)
			{
				startDateField.SelectedDate = value;
				StartTypeFixed.Checked = true;
				
				if (this.FixedDate != DateTime.MinValue)
					startDateField.SelectedDate = value.Date;

				fixedDiv.Attributes.Add("style", "display:block");
				RelativeStartList.Attributes.Add("style", "display:none");
			}
		}
	}

	public int RelativeTimeId
	{
		get
		{
			if (StartTypeRelative.Checked && RelativeStartList.SelectedIndex > 0)
				return Convert.ToInt32(RelativeStartList.SelectedValue);
			else
				return 0;
		}
		set
		{
			if (RelativeStartList.DataSource == null)
			{
				RelativeStartList.DataSource = Utility.GetRelativeTimePurgeList();
				RelativeStartList.DataBind();
			}

			if (RelativeStartList.Items.FindByValue(Convert.ToString(value)) != null)
				RelativeStartList.SelectedValue = Convert.ToString(value);
			else
				RelativeStartList.SelectedIndex = -1;

			if (value != 0)
			{
				this.StartTypeRelative.Checked = true;
				
				fixedDiv.Attributes.Add("style", "display:none");
				RelativeStartList.Attributes.Add("style", "display:block");
			}
		}
	}

	protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (RelativeStartList.DataSource == null)
			{
				RelativeStartList.DataSource = Utility.GetRelativeTimePurgeList();
				RelativeStartList.DataBind();
			}

			if (this.FixedDate == DateTime.MinValue)
				this.StartTypeRelative.Checked = true;
			else
				this.StartTypeFixed.Checked = true;
		}
		this.StartTypeFixed.Attributes.Add("onclick", string.Format("showFixed{0}();", this.ClientID));
		this.StartTypeRelative.Attributes.Add("onclick", string.Format("showRelative{0}();", this.ClientID));
		startDateField.DateInput.Attributes.Add("onclick", string.Format("ToggleStartDatePopup{0}();", this.ClientID));
	}
}
