using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public class DieboldConstants
{
    public const string DEVICE_FILTER_DIMENSION_NAME = "[Device].[Type - Device].[Type]";
    public const string DASHBOARD_MASTER_NAME = "MASTER";
    public const int DEFAULT_CACHE_MINUTES = 15;

    public const int DEFAULT_REPORT_CACHE_MINUTES = 2 * 60;
    public const int DISTRIBUTION_REPORT_CACHE_MINUTES = 24 * 60;

	public const string SESSION_INCLUDE_INACTIVE_KEY = "sii";

    public const string SNAPSHOT_ID_KEY = "snp";
    public const string REPORT_ID_KEY = "r";
    public const string SUBREPORT_ID_KEY = "s";
    public const string FIELD_ID_KEY = "fld";
	public const string FIELD_OPTION_ID_KEY = "opt";
	public const string EXTENSION_KEY = "ext";
	public const string ENGINEERING_KEY = "eng";
	public const string DEVICE_ID_KEY = "dvi";
	public const string CELL_ID_KEY = "cel";
	public const string SESSION_ID_KEY = "ses";
    public const string JIRA_SYNC_ID_KEY = "jsi";
    public const string DEVICE_TYPE_KEY = "dev";
	public const string DEVICE_TYPE_NAME_KEY = "dtn";
	public const string IS_ACTIVE_KEY = "iak";
	public const string IS_TERMINAL_TYPE = "itt";
	public const string EVENT_TYPE_KEY = "evt";
	public const string EVENT_TYPE_NAME_KEY = "etn";
	public const string ENTITY_KEY = "ent";
	public const string OPERATOR_ID_KEY = "ope";
	public const string FAILURE_TYPE_ID_KEY = "fti";
	public const string FAILURE_LOCATION_ID_KEY = "fli";
	public const string INVESTIGATION_AREA_ID_KEY = "iai";
	public const string DISCIPLINE_ID_KEY = "did";
	public const string TRIAGE_TYPE_ID_KEY = "tti";
	public const string SOLUTION_STATE_ID_KEY = "ssi";
	public const string TEST_LOCATION_ID_KEY = "tli";
	public const string SCHEDULED_REPORT_ID_KEY = "scr";
    public const string QUERY_REPORT_ID_KEY = "qri";
    public const string SERIAL_NUMBER_KEY = "sn";
	public const string PRODUCT_LINES_KEY = "pls";
	public const string FAMILY_LINES_KEY = "flk";
	public const string TEST_TYPE_KEY = "ttk";
	public const string MODEL_TYPE_KEY = "mtk";
	public const string TRAN_DATE_KEY = "td";
	public const string FILE_NAME_KEY = "fn";
	public const string FILE_IS_DATA_KEY = "fid";
	public const string FILE_IS_IMAGE_KEY = "fii";
	public const string TRAN_ID_KEY = "tid";
	public const string OBSERVATION_ID_KEY = "oid";
	public const string STATUS_ENTITY_KEY = "2";
	public const string METRIC_ENTITY_KEY = "4";
	public const string INFO_ENTITY_KEY = "3";
	public const string RESULT_DATA_KEY = "5";
	public const string ENGINEERING_DATA_KEY = "6";
	public const string COMMAND_DEVICE_TYPE_CHANGED = "DEVICE_CHANGED";
	public const string COMMAND_EVENT_TYPE_CHANGED = "EVENT_CHANGED";
	public const string COMMAND_ENTITY_TYPE_CHANGED = "ENTITY_CHANGED";

	public const string ADV_SEARCH_CELL = "CEL";
	public const string ADV_SEARCH_CELL_NAME = "CEN";
	public const string ADV_SEARCH_COMMAND_NUMBER = "CNM";
	public const string ADV_SEARCH_DEVICE = "DEV";
	public const string ADV_SEARCH_DEVICE_NAME = "DVN";
	public const string ADV_SEARCH_DEVICE_TYPE = "DET";
	public const string ADV_SEARCH_DEVICE_TYPE_NAME = "DEN";
	public const string ADV_SEARCH_DISCIPLINE = "DIS";
	public const string ADV_SEARCH_DISCIPLINE_NAME = "DIN";
	public const string ADV_SEARCH_END_DATE = "EDA";
	public const string ADV_SEARCH_EVENT = "EVT";
	public const string ADV_SEARCH_EVENT_NAME = "EVN";
	public const string ADV_SEARCH_FAILURE_LOCATION = "FAL";
	public const string ADV_SEARCH_FAILURE_LOCATION_NAME = "FLN"; 
	public const string ADV_SEARCH_FAILURE_TYPE = "FAI"; 
	public const string ADV_SEARCH_FAILURE_TYPE_NAME = "FAN";
	public const string ADV_SEARCH_FILE_NUMBER = "FNM";
	public const string ADV_SEARCH_INVESTIGATION_AREA = "INV";
	public const string ADV_SEARCH_INVESTIGATION_AREA_NAME = "INN";
	public const string ADV_SEARCH_MAX_QUANTIZED_VALUE = "MAQ";
	public const string ADV_SEARCH_MIN_QUANTIZED_VALUE = "MIQ";
	public const string ADV_SEARCH_MEDIA_NUMBER = "MNM";
	public const string ADV_SEARCH_MODULE_TYPE = "MOD";
	public const string ADV_SEARCH_MODULE_TYPE_NAME = "MTN";
	public const string ADV_SEARCH_OBSERVATIONS_ONLY = "OBO";
	public const string ADV_SEARCH_OPERATOR = "OPP";
	public const string ADV_SEARCH_OPERATOR_NAME = "OPN";
	public const string ADV_SEARCH_OWNER = "OWR";
	public const string ADV_SEARCH_OWNER_NAME = "OWN";
	public const string ADV_SEARCH_PROJECTED_END_DATE = "PED";
	public const string ADV_SEARCH_PROJECTED_START_DATE = "PSD";
	public const string ADV_SEARCH_SESSION = "SES"; 
	public const string ADV_SEARCH_SESSION_NAME = "SEN";
	public const string ADV_SEARCH_START_DATE = "SDA";
	public const string ADV_SEARCH_SOLUTION_STATE = "SOS";
	public const string ADV_SEARCH_SOLUTION_STATE_NAME = "SON";
	
	public const string ADV_SEARCH_SETTING = "SET"; //int list for query
	public const string ADV_SEARCH_SETTING_NAME = "SSN"; //name list for display
	public const string ADV_SEARCH_SETTING_TREE = "SST"; //session object list for tree

	public const string ADV_SEARCH_SETTING2 = "SET2"; //int list for query
	public const string ADV_SEARCH_SETTING_NAME2 = "SSN2"; //name list for display
	public const string ADV_SEARCH_SETTING_TREE2 = "SST2"; //session object list for tree

	public const string ADV_SEARCH_SETTING3 = "SET3"; //int list for query
	public const string ADV_SEARCH_SETTING_NAME3 = "SSN3"; //name list for display
	public const string ADV_SEARCH_SETTING_TREE3 = "SST3"; //session object list for tree

	public const string ADV_SEARCH_STATISTIC_FIELDS = "STF"; //field int list for query
	public const string ADV_SEARCH_STATISTIC_FIELD_OPTIONS = "STO"; //field option int list for query
	public const string ADV_SEARCH_STATISTIC_NAME = "STN"; //name list for display
	public const string ADV_SEARCH_STATISTIC_TREE = "STT"; //session object list for tree

	public const string ADV_SEARCH_TRANSACTION_END = "TRE";
	public const string ADV_SEARCH_TRANSACTION_START = "TRS";
	public const string ADV_SEARCH_TRIAGE_TYPE = "TRT";
	public const string ADV_SEARCH_TRIAGE_TYPE_NAME = "TRN";
	public const string ADV_SEARCH_TEXT = "TXT";
}

