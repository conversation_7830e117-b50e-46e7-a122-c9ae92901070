using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class CreateFolderPopup : System.Web.UI.Page
{
	public bool LibraryIsOpener
	{
		get { if (this.ViewState["l"] != null) return (bool)this.ViewState["l"]; else return false; }
		set { this.ViewState["l"] = value; }
	}

	public bool IsShared
	{
		get { if (this.ViewState["i"] != null) return (bool)this.ViewState["i"]; else return false; }
		set { this.ViewState["i"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!string.IsNullOrEmpty(Request.Params["lib"]))
			this.LibraryIsOpener = Convert.ToBoolean(Request.Params["lib"]);

		if (!string.IsNullOrEmpty(Request.Params["i"]))
			this.IsShared = Convert.ToBoolean(Request.Params["i"]);

		folderNameField.Focus();
    }

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			string userName = null;

			if (!this.IsShared)
				userName = Utility.GetUserName();

			SqlHelper.ExecuteNonQuery("RPT_InsertFolder", folderNameField.Text.Trim(), userName);

			string closeScript = null;
			if (LibraryIsOpener)
				closeScript = "<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.location.href='reportlibrary.aspx';\r\n</script>";				
			else
				closeScript = "<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.forms[0].submit();\r\n</script>";

			Page.ClientScript.RegisterStartupScript(typeof(CreateFolderPopup), "CloseScript", closeScript);
		}
	}

	protected void CheckName(object sender, ServerValidateEventArgs e)
	{
		string userName = null;
		if (!this.IsShared)
			userName = Utility.GetUserName();

		if (Convert.ToInt32(SqlHelper.ExecuteScalar("RPT_CheckFolderNameCollision", folderNameField.Text.Trim(), userName)) == 0)
			e.IsValid = true;
		else
			e.IsValid = false;
	}
}
