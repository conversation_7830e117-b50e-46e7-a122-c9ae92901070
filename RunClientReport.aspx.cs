using System;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Web.UI;
using Telerik.Web.UI;
using Dundas.Olap.Data;
using Dundas.Olap.WebUIControls;

public partial class RunClientReport : System.Web.UI.Page
{
	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

    //private OlapManager.DataAxisDimensionsChangedEventHandler changeHandler;
    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

            if (this.RepInfo == null)
                Response.Redirect("~/reportlibrary.aspx");

            //require user to be admin to edit or save reports that are associated with sessions
			if (this.RepInfo.FolderId == 0 && this.RepInfo.ReportId != 0)
			{
				bool isAdmin = Utility.IsUserAdmin();
				EditBtnDiv.Visible = isAdmin;
				SaveBtnDiv.Visible = isAdmin;
			}
			
            bool firstBuild = string.IsNullOrEmpty(this.RepInfo.ReportData);

			if (this.RepInfo.ReportTypeId.Equals(ReportHelper.ReportTypeEnum.GENERAL))
			{
				AdomdNetDataProvider1.ConnectionString = ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString;
				this.OlapClient1.OlapManager.CurrentCubeName = "Reporting"; 
			}
			else
			{
				AdomdNetDataProvider1.ConnectionString = ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString;
				this.OlapClient1.OlapManager.CurrentCubeName = "Transactions";
			}

            if (!firstBuild)
				ReportHelper.ConvertStringToReports(this.OlapClient1.OlapManager, this.RepInfo.ReportData);
                
            switch (this.RepInfo.ReportTypeId)
            {
                case ReportHelper.ReportTypeEnum.GENERAL:
					ReportHelper.BuildGeneralReport(false, this.OlapClient1.OlapManager, this.RepInfo, firstBuild);
					break;
				case ReportHelper.ReportTypeEnum.LEGACY_GENERAL:
					ReportHelper.BuildGeneralReport(true, this.OlapClient1.OlapManager, this.RepInfo, firstBuild);
                    break;
				default:
                    throw new ApplicationException("Unsupported report type - " + this.RepInfo.ReportTypeId.ToString());
            }
            this.OlapClient1.OlapChart.OlapDataPopulated += new OlapChartCore.OlapDataPopulatedEventHandler(OlapChart_OlapDataPopulated);
            reportNameLabel.Text = this.RepInfo.ReportName;
        }
    }

    private void OlapChart_OlapDataPopulated(object sender, EventArgs e)
    {
        switch (this.RepInfo.PrebuildType)
        {
            case ReportHelper.PrebuiltReportsEnum.SUMMARY_TRANSACTION_VOLUME:
            case ReportHelper.PrebuiltReportsEnum.SUMMARY_MEDIA_VOLUME:
            case ReportHelper.PrebuiltReportsEnum.SUMMARY_OBSERVATION_VOLUME:
                foreach (Series series in this.OlapClient1.OlapChart.Series)
                    series.Type = SeriesChartType.Line;
                break;
        }
        this.RepInfo.PrebuildType = ReportHelper.PrebuiltReportsEnum.UNSPECIFIED;
    }

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		//this.RepInfo.ChartTitle = this.OlapClient1.OlapManager.CurrentOlapReport.DataTitleFormat;
        this.RepInfo.ReportData = ReportHelper.ConvertReportsToString(this.OlapClient1.OlapManager);
        UpdateAttachedSessions();
        Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "SaveReportWindow")
				win.VisibleOnPageLoad = true;
		}
	}

    private void UpdateAttachedSessions()
    {
        // Update selected sessions
		DimensionDescriptor sessionDesc = null;

		if (this.RepInfo.ReportTypeId.Equals(ReportHelper.ReportTypeEnum.LEGACY_GENERAL))
			sessionDesc = ReportHelper.FindDescriptor(this.OlapClient1.OlapManager.CurrentOlapReport, "Test Session");
		else
			sessionDesc = ReportHelper.FindDescriptor(this.OlapClient1.OlapManager.CurrentOlapReport, "Session");

        this.RepInfo.AttachedSessions = new List<SessionInfo>();
        bool includeAllActive = false;
        bool includeAllClosed = false;
        ICollection activeSessionList = Utility.GetActiveSessionsList();
        ICollection fullSessionList = Utility.GetSessionsList();
        if (sessionDesc != null)
        {
            foreach (DimensionMemberDescriptor memb in sessionDesc.Members)
            {
                int pos = memb.MemberUniqueName.IndexOf("&") + 2;
                if (string.Compare("Active", memb.MemberUniqueName.Substring(pos, memb.MemberUniqueName.Length - pos - 1), true) == 0)
                    includeAllActive = true;
                else if (string.Compare("Closed", memb.MemberUniqueName.Substring(pos, memb.MemberUniqueName.Length - pos - 1), true) == 0)
                    includeAllClosed = true;
                else
                {
                    SessionInfo sess = new SessionInfo();
                    sess.SessionId = Int32.Parse(memb.MemberUniqueName.Substring(pos, memb.MemberUniqueName.Length - pos - 1));
                    sess.SessionName = memb.MemberName;
                    this.RepInfo.AttachedSessions.Add(sess);

                    // Assumes that the active and inactive nodes occur before any itemized sessions
                    if (isSessionInList(activeSessionList, sess.SessionId))
                        includeAllActive = false;
                    else
                        includeAllClosed = false;
                }
            }

            if (includeAllActive)
            {
                foreach (TypeCodeEntry entry in activeSessionList)
                {
                    SessionInfo sess = new SessionInfo();
                    sess.SessionId = Int32.Parse(entry.Code);
                    sess.SessionName = entry.Name;
                    this.RepInfo.AttachedSessions.Add(sess);
                }
            }
            if (includeAllClosed)
            {
                foreach (TypeCodeEntry entry in fullSessionList)
                {
                    if (isSessionInList(activeSessionList, Int32.Parse(entry.Code)) == false)
                    {
                        SessionInfo sess = new SessionInfo();
                        sess.SessionId = Int32.Parse(entry.Code);
                        sess.SessionName = entry.Name;
                        this.RepInfo.AttachedSessions.Add(sess);
                    }
                }
            }
        }
    }

    private bool isSessionInList(ICollection sessionList, int targetId)
    {
        bool retVal = false;
        string targetIdStr = targetId.ToString();
        foreach (TypeCodeEntry entry in sessionList)
        {
            if (string.Compare(entry.Code, targetIdStr) == 0)
            {
                retVal = true;
                break;
            }
        }
        return retVal;
    }

	protected void EditButton_Click(object sender, EventArgs e)
	{
		//this.RepInfo.ChartTitle = this.OlapClient1.OlapManager.CurrentOlapReport.DataTitleFormat;
		this.RepInfo.ReportData = ReportHelper.ConvertReportsToString(this.OlapClient1.OlapManager);
        UpdateAttachedSessions();
        Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "EditWindow")
			{
				win.NavigateUrl = this.RepInfo.WizardPageName;
				win.VisibleOnPageLoad = true;
			}
		}
	}
}
