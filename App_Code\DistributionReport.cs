using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class DistributionReport : BaseReport
{
    public override bool IsStale
    {
        get
        {
            return ((this.cellSet == null) || (DateTime.Now.AddMinutes(0 - DieboldConstants.DISTRIBUTION_REPORT_CACHE_MINUTES) >= repInfo.LastSaveDate));
        }
    }

    protected SubReportInfo subRepInfo = null;

    public DistributionReport(ReportInfo repInfo, SubReportInfo subRepInfo)
        : base(repInfo, false) 
    {
        HideRowHeaders = true;
        this.subRepInfo = subRepInfo;
    }

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        StringBuilder mdxSelect = new StringBuilder();
        string mdxCategory = null;
        string mdxFromWhere = null;

        List<string> dimMembers = repInfo.DimensionMembers;
        if (subRepInfo != null && subRepInfo.DimensionMembers.Count > 0)
            dimMembers = subRepInfo.DimensionMembers;

        List<SessionInfo> sessions = repInfo.AttachedSessions;
        if (subRepInfo != null && subRepInfo.AttachedSessions.Count > 0)
            sessions = subRepInfo.AttachedSessions;

        Dictionary<string, List<string>> reportFilters = repInfo.ReportFilters;
        if (subRepInfo != null && subRepInfo.DeviceFilters.Count > 0)
        {
            reportFilters = new Dictionary<string, List<string>>();
            foreach (string key in repInfo.ReportFilters.Keys)
            {
                if (string.Compare(key, DieboldConstants.DEVICE_FILTER_DIMENSION_NAME, true) == 0)
                    reportFilters.Add(key, subRepInfo.DeviceFilters);
                else
                    reportFilters.Add(key, repInfo.ReportFilters[key]);
            }
        }

        if (repInfo.DimensionName != null && repInfo.DimensionName.StartsWith("[Distribution]"))
        {
            mdxSetup.Append("WITH MEMBER [Distribution Value] AS 'IIF(([Distribution Values].[QuantizedValue],[Measures].[Frequency])>0, [Distribution Values].[QuantizedValue].CurrentMember.MemberValue, NULL) '\r\n");

            mdxSelect.Append("SELECT {[Distribution Value], [Measures].[Frequency]} on columns, \r\n");

            mdxCategory = "NON EMPTY [Distribution Values].[QuantizedValue].[QuantizedValue] on rows \r\n";
        }
        else
        {
            throw new ApplicationException("Unsupported distribution dimension - '" + repInfo.DimensionName + "'.");
        }

        mdxFromWhere = string.Format("FROM [Reporting] WHERE ({0}, {1}{2})", 
            OLAPHelper.BuildMdxFilteredTime(ReportHelper.DateGroupingEnum.BY_DATETIME, repInfo.StartDate, repInfo.EndDate, null, null),
            OLAPHelper.BuildMdxLevelTuple(false, repInfo.DimensionName, dimMembers, false),
            OLAPHelper.BuildMdxWhereTuples(sessions, reportFilters, false, ", ", false));

        string mdx = mdxSetup.ToString() + mdxSelect.ToString() + mdxCategory + mdxFromWhere;

        ExecuteMdx(mdx);
        BuildGridDisplay();
    }

	public override void PopulateChart(Chart chart, bool includeToolTips)
	{
        List<DataPoint> histData = new List<DataPoint>();
        TupleCollection colTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[1].Set.Hierarchies;

        Dundas.Charting.WebControl.Axis axisX = chart.ChartAreas[0].AxisX;
        Dundas.Charting.WebControl.Axis axisY = chart.ChartAreas[0].AxisY;
        Dundas.Charting.WebControl.Axis axisY2 = chart.ChartAreas[0].AxisY2;

        double totValueFreq = 0;
        double sampleSize = 0;
        double mean = 0;
        double stdDev = 1;
        double skewness = 0;
        double kurtosis = 0;
        double usl = double.MaxValue;
        double lsl = double.MinValue;
        double specFailures = 0;
        double DPM = 0;
        double Cp = 0;
        double Cpk = 0;
        double minX = 0;
        double maxX = 0;
        double maxHistY = 0;
        double maxDistY = 0;
		bool isNonNormal = false;

        if (!decimal.MinValue.Equals(this.repInfo.DistributionInfo.UpperSpecLimit))
            usl = (double)this.repInfo.DistributionInfo.UpperSpecLimit;
        if (!decimal.MinValue.Equals(this.repInfo.DistributionInfo.LowerSpecLimit))
            lsl = (double)this.repInfo.DistributionInfo.LowerSpecLimit;

        bool hasSpecLimits = (!double.MinValue.Equals(lsl) || !double.MaxValue.Equals(usl));

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
            priorSeries.Add(series.Name, series);

        chart.Series.Clear();
        chart.Legends[0].Enabled = false;

        double filterMin = double.MinValue;
        if(!decimal.MinValue.Equals(this.repInfo.DistributionInfo.FilterStartValue) && !decimal.MaxValue.Equals(this.repInfo.DistributionInfo.FilterStartValue))
            filterMin = Convert.ToDouble(this.repInfo.DistributionInfo.FilterStartValue);

        double filterMax = double.MaxValue;
        if(!decimal.MinValue.Equals(this.repInfo.DistributionInfo.FilterEndValue) && !decimal.MaxValue.Equals(this.repInfo.DistributionInfo.FilterEndValue))
            filterMax = Convert.ToDouble(this.repInfo.DistributionInfo.FilterEndValue);

        for (int col = 0; (col + 1) < colTuples.Count; col = col + 2)
        {
            for (int row = 0; row < rowTuples.Count; row++)
            {
                Cell valueCell = cellSet.Cells[col, row];
                Cell freqCell = cellSet.Cells[col + 1, row];

                double value = (valueCell.Value != null) ? double.Parse(valueCell.Value.ToString()) : 0;
                double freq = (freqCell.Value != null) ? double.Parse(freqCell.Value.ToString()) : 0;

                if (value >= filterMin && value <= filterMax)
                {
                    histData.Add(new DataPoint(value, freq));
                    totValueFreq += (value * freq);
                    sampleSize += freq;
                    if (value < lsl || value > usl)
                        specFailures += freq;
                }
            }
        }

        // Calculate Mean and DPM
        mean = (sampleSize > 0 ? (totValueFreq / sampleSize) : 0);
        DPM = (sampleSize > 0 ? ((specFailures / sampleSize) * 1000000) : 0);

        // Calculate standard deviation
        if (sampleSize > 1)
        {
            stdDev = 0;
            foreach (DataPoint point in histData)
                stdDev += Math.Pow(point.XValue - mean, 2) * point.YValues[0];
            stdDev = Math.Sqrt(Math.Abs(stdDev / (sampleSize - 1)));
        }

        // Calculate skewness
        if (sampleSize > 0 && stdDev > 0)
        {
            foreach (DataPoint point in histData)
                skewness += Math.Pow((point.XValue - mean) / stdDev, 3) * point.YValues[0];
            skewness = skewness / sampleSize;
        }

        // Calculate kurtosis
        if (sampleSize > 0 && stdDev > 0)
        {
            foreach (DataPoint point in histData)
                kurtosis += Math.Pow(point.XValue - mean, 4) * point.YValues[0];
            kurtosis = (kurtosis / (sampleSize * stdDev * stdDev * stdDev * stdDev)) - 3;
        }

		// Calculate KSNormality
		if (sampleSize >= 10 && stdDev > 0)
		{
			KSNormality norm = new KSNormality();
			double maxDStatValue = 0;
			double criticalValue = 1.36 / Math.Sqrt(sampleSize);
			int i = 0;

			foreach (DataPoint point in histData)
			{
				double dStatValue = 0;
				double yI = (point.XValue - mean) / stdDev;
				double ksValue = norm.FindKSNormality(yI);

				int i1 = i + 1;
				int i2 = i + Convert.ToInt32(point.YValues[0]);

				double a = (i1 / sampleSize) - ksValue;
				double b = ksValue - ((i2 - 1) / sampleSize);

				if (a > b)
					dStatValue = a;
				else
					dStatValue = b;

				if (dStatValue > maxDStatValue)
					maxDStatValue = dStatValue;

				i += Convert.ToInt32(point.YValues[0]);
			}

			if (maxDStatValue >= criticalValue)
				isNonNormal = true;
		}
		
        // Calculate Lp', Up', and M'
        PearsonCurves pearson = new PearsonCurves();
        double lp = pearson.LookupLp(kurtosis, skewness);
        double up = pearson.LookupUp(kurtosis, skewness);
        double m = pearson.LookupM(kurtosis, skewness);

        bool invalidPearson = (double.MinValue.Equals(lp) || double.MinValue.Equals(up) || double.MinValue.Equals(m));
        if (!invalidPearson)
        {
			lp = mean - (stdDev * lp);
            up = mean + (stdDev * up);
            m = mean + (stdDev * m);
        }

        // Calculate Cp and Cpk
        if (!invalidPearson && hasSpecLimits)
        {
            if (!double.MinValue.Equals(lsl) && !double.MaxValue.Equals(usl))
                Cp = (usl - lsl) / (up - lp);

            if (!double.MinValue.Equals(lsl) && !double.MaxValue.Equals(usl))
            {
                double CpL = (m - lsl) / (m - lp);
                double CpU = (usl - m) / (up - m);
                Cpk = (CpL < CpU ? CpL : CpU);
            }
            else if (!double.MinValue.Equals(lsl))
            {
                Cpk = (m - lsl) / (m - lp);
            }
            else if (!double.MaxValue.Equals(usl))
            {
                Cpk = (usl - m) / (up - m);
            }
        }
        else if (invalidPearson && stdDev > 0)
        {
            if (!double.MinValue.Equals(lsl) && !double.MaxValue.Equals(usl))
                Cp = (usl - lsl) / (6 * stdDev);

            if (!double.MinValue.Equals(lsl) && !double.MaxValue.Equals(usl))
            {
                double CpL = (mean - lsl) / (3 * stdDev);
                double CpU = (usl - mean) / (3 * stdDev);
                Cpk = (CpL < CpU ? CpL : CpU);
            }
            else if (!double.MinValue.Equals(lsl))
            {
                Cpk = (mean - lsl) / (3 * stdDev);
            }
            else if (!double.MaxValue.Equals(usl))
            {
                Cpk = (usl - mean) / (3 * stdDev);
            }
        }

        minX = mean;
        maxX = mean;

        if (repInfo.DistributionInfo.ShowNormalDistribution)
        {
            // Calculate coefficient
            double coef = 1.0 / (stdDev * Math.Sqrt(2 * Math.PI));
            // Fill data points with values from Normal distribution

            Series distSeries = null;
            if (priorSeries.ContainsKey("Distribution"))
            {
                distSeries = priorSeries["Distribution"];
                priorSeries.Remove(distSeries.Name);
                distSeries.Points.Clear();
            }
            else
            {
                distSeries = new Series("Distribution");
                distSeries.Type = SeriesChartType.Area;
                
            }

            Color defectColor = Color.Red;
            if (repInfo.DistributionInfo.ShowHistogram)
            {
                distSeries.YAxisType = AxisType.Secondary;
                distSeries.Color = Color.FromArgb(100, 0x41, 0x8c, 0xf0);
                defectColor = Color.FromArgb(100, Color.Red);
            }
            else
            {
                distSeries.YAxisType = AxisType.Primary;
                distSeries.Color = Color.FromArgb(0x41, 0x8c, 0xf0);
            }

            double lowerX = mean - (stdDev * 5);
            double upperX = mean + (stdDev * 5);

            if (Math.Abs(upperX - lowerX) == 0)
            {
                lowerX = mean - 5;
                upperX = mean + 5;
            }

            for (double xVal = lowerX; xVal <= upperX; xVal = xVal + ((upperX - lowerX) / 100))
            {
                double yVal = coef * Math.Exp((xVal - mean) * (xVal - mean) / -(2 * stdDev * stdDev));

                DataPoint dp = new DataPoint(xVal, yVal);

                maxDistY = (maxDistY > dp.GetValueY(0)) ? maxDistY : dp.GetValueY(0);

                minX = (minX < dp.XValue) ? minX : dp.XValue;
                maxX = (maxX > dp.XValue) ? maxX : dp.XValue;

                if (dp.XValue < lsl || dp.XValue > usl)
                    dp.Color = defectColor;

                distSeries.Points.Add(dp);
            }

            chart.Series.Add(distSeries);
        }

        if (repInfo.DistributionInfo.ShowHistogram)
        {
            Series histSeries = null;
            if (priorSeries.ContainsKey("Histogram"))
            {
                histSeries = priorSeries["Histogram"];
                priorSeries.Remove(histSeries.Name);
                histSeries.Points.Clear();
            }
            else
            {
                histSeries = new Series("Histogram");
                histSeries["DrawingStyle"] = "Cylinder";
                histSeries.BorderColor = Color.FromArgb(26, 59, 105);
                histSeries.ShadowOffset = 2;

                histSeries.Type = SeriesChartType.Column;
            }

            foreach (DataPoint point in histData)
            {
                DataPoint dp = new DataPoint(point.XValue, point.GetValueY(0));

                maxHistY = (maxHistY > dp.GetValueY(0)) ? maxHistY : dp.GetValueY(0);

                minX = (minX < dp.XValue) ? minX : dp.XValue;
                maxX = (maxX > dp.XValue) ? maxX : dp.XValue;

                if (dp.XValue < lsl || dp.XValue > usl)
                    dp.Color = Color.Red;

                if (includeToolTips)
                    dp.ToolTip = string.Format("Value: {0}\r\nCount: {1}", dp.XValue.ToString("#,##0.####"), dp.GetValueY(0).ToString("#,##0"));

                histSeries.Points.Add(dp);
            }

            chart.Series.Add(histSeries);
        }

        if(this.subRepInfo != null)
            chart.Titles["Title1"].Text = this.subRepInfo.SubReportName;
        else
            chart.Titles["Title1"].Text = this.repInfo.ChartTitle;

        axisX.LabelStyle.Format = "#,##0.##";
        axisY.LabelStyle.Format = "#,##0.##";
        axisY2.LabelStyle.Format = "#,##0.##";

		if (this.repInfo.DistributionInfo.XAxisStartValue != Decimal.MinValue)
			axisX.Minimum = Convert.ToDouble(this.repInfo.DistributionInfo.XAxisStartValue);
		else
			axisX.Minimum = minX;

		if (this.repInfo.DistributionInfo.XAxisEndValue != Decimal.MinValue)
			axisX.Maximum = Convert.ToDouble(this.repInfo.DistributionInfo.XAxisEndValue);
		else
			axisX.Maximum = maxX;
        
		axisY.Minimum = 0;
        axisY2.Minimum = 0;

        if (repInfo.DistributionInfo.ShowHistogram && repInfo.DistributionInfo.ShowNormalDistribution)
        {
            axisY.Maximum = RoundAxis(maxHistY, 100);

            if (maxDistY < 0.1)
                axisY2.Maximum = 0.1;
            else if (maxDistY < 0.2)
                axisY2.Maximum = 0.2;
            else if (maxDistY < 0.4)
                axisY2.Maximum = 0.4;
            else if (maxDistY < 0.5)
                axisY2.Maximum = 0.5;
            else if (maxDistY < 0.8)
                axisY2.Maximum = 0.8;
            else if (maxDistY <= 1)
                axisY2.Maximum = 1;
            else
                axisY2.Maximum = RoundAxis(maxDistY, 1);
        }
        else if (repInfo.DistributionInfo.ShowHistogram)
        {
            axisY.Maximum = RoundAxis(maxHistY, 100);
            axisY2.Maximum = axisY.Maximum;
        }
        else
        {
            if (maxDistY < 0.1)
                axisY.Maximum = 0.1;
            else if (maxDistY < 0.2)
                axisY.Maximum = 0.2;
            else if (maxDistY < 0.4)
                axisY.Maximum = 0.4;
            else if (maxDistY < 0.5)
                axisY.Maximum = 0.5;
            else if (maxDistY < 0.8)
                axisY.Maximum = 0.8;
            else if (maxDistY <= 1)
                axisY.Maximum = 1;
            else
                axisY.Maximum = RoundAxis(maxDistY, 1);

            axisY2.Maximum = axisY.Maximum;
        }


        ResetAxisMarks(axisY);
        ResetAxisMarks(axisY2);
        ResetAxisMarks(axisX);

        chart.ChartAreas[0].ReCalc();

        // Build annotations
        StringBuilder annotationText = new StringBuilder();

        annotationText.Append(string.Format("Mean = {0}\r\n", mean.ToString("#,##0.##")));
        annotationText.Append(string.Format("StdDev = {0}\r\n", stdDev.ToString("#,##0.##")));
        if (!decimal.MinValue.Equals(repInfo.DistributionInfo.UpperSpecLimit))
            annotationText.Append(string.Format("USL = {0}\r\n", repInfo.DistributionInfo.UpperSpecLimit.ToString("#,##0.##")));
        if (!decimal.MinValue.Equals(repInfo.DistributionInfo.LowerSpecLimit))
            annotationText.Append(string.Format("LSL = {0}\r\n", repInfo.DistributionInfo.LowerSpecLimit.ToString("#,##0.##")));

        if ((!invalidPearson || stdDev > 0) && hasSpecLimits)
        {
            annotationText.Append(string.Format("Sigma Level = {0}\r\n", (3 * Cpk).ToString("#,##0.####")));
            annotationText.Append(string.Format("Cpk = {0}\r\n", Cpk.ToString("#,##0.####")));
            if(!double.MinValue.Equals(lsl) && !double.MaxValue.Equals(usl))
                annotationText.Append(string.Format("Cp = {0}\r\n", Cp.ToString("#,##0.####")));
        }

        annotationText.Append(string.Format("DPM = {0}\r\n", DPM.ToString("#,##0")));
        annotationText.Append(string.Format("N = {0}\r\n", sampleSize.ToString("#,##0.##")));

        // Show annotations
        chart.Annotations.Clear();
        SixSigma.AddTextAnnotation(annotationText.ToString(), axisX, axisY, axisX.Minimum + ((axisX.Maximum-axisX.Minimum) / 50), axisY.Maximum - (axisY.Maximum / 25),
            Color.Black, new Font("Arial", 8, FontStyle.Bold), chart);

		if (isNonNormal && repInfo.DistributionInfo.ShowNormalDistribution)
		{
			SixSigma.AddTextAnnotation("The data is non-normal.  Note that the calculated metrics displayed are correct.  \r\nHowever the normal distribution graph displayed is not a true representation of the \r\nprocess. Please refer to the histogram of the actual data for an accurate display.",
				axisX, axisY, axisX.Minimum + ((axisX.Maximum - axisX.Minimum) / 4), axisY.Maximum - (axisY.Maximum / 25), Color.Red, new Font("Arial", 8, FontStyle.Bold), chart);
		}
		else if (invalidPearson && stdDev <= 0 && hasSpecLimits)
        {
            SixSigma.AddTextAnnotation("A problem was identified with the data.  Therefore \r\ncapability information cannot be calculated at this \r\ntime.  Problem resolution is in progress.",
                axisX, axisY, axisX.Minimum + ((axisX.Maximum - axisX.Minimum) / 4), axisY.Maximum - (axisY.Maximum / 25), Color.Red, new Font("Arial", 8, FontStyle.Bold), chart);
        }

        SetChartTitle(chart, "FootNote", "Calculations are based on formulas which can be utilized for both Normal and Non-Normal Data. \r\n"
            + "Traditional formulas are only accurate for data which has a Normal Distribution.", Docking.Bottom, 
            ContentAlignment.BottomCenter, new Font("Arial", 7, FontStyle.Regular), Color.Black);
    }
}
