using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public class LibrarySource
{
	public static DataSet GetSessions(bool includePastSessions, string userName) { return GetSessions(includePastSessions, userName, null); }
	public static DataSet GetSessions(bool includePastSessions, string userName, string filteredSessionList)
	{
		string owner = null;
		string sessionsList = null;

		if (!string.IsNullOrEmpty(userName))
			owner = userName;

		if (!string.IsNullOrEmpty(filteredSessionList))
			sessionsList = filteredSessionList;

		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetLibrary_Sessions", includePastSessions, owner, sessionsList);
        ds.Relations.Add("Children", ds.Tables[0].Columns["SessionId"], ds.Tables[1].Columns["SessionId"]);
        DataColumn[] parentCols = new DataColumn[2];
        parentCols[0] = ds.Tables[1].Columns["SessionId"];
        parentCols[1] = ds.Tables[1].Columns["ReportId"];
        DataColumn[] childCols = new DataColumn[2];
        childCols[0] = ds.Tables[2].Columns["SessionId"];
        childCols[1] = ds.Tables[2].Columns["ReportId"];
        ds.Relations.Add("SubReports", parentCols, childCols);
        
        return ds;
	}

	public static DataSet GetMyLibrary(bool includePastSessions, string userName)
	{
		string owner = null;

		if (!string.IsNullOrEmpty(userName))
			owner = userName;

		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetLibrary_Personal", includePastSessions, owner);
		ds.Relations.Add("Children", ds.Tables[0].Columns["FolderId"], ds.Tables[1].Columns["FolderId"]);
        DataColumn[] parentCols = new DataColumn[2];
        parentCols[0] = ds.Tables[1].Columns["FolderId"];
        parentCols[1] = ds.Tables[1].Columns["ReportId"];
        DataColumn[] childCols = new DataColumn[2];
        childCols[0] = ds.Tables[2].Columns["FolderId"];
        childCols[1] = ds.Tables[2].Columns["ReportId"];
        ds.Relations.Add("SubReports", parentCols, childCols);
        
        return ds;
	}

	public static DataSet GetSharedLibrary(bool includePastSessions)
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetLibrary_Shared", includePastSessions);
		ds.Relations.Add("Children", ds.Tables[0].Columns["FolderId"], ds.Tables[1].Columns["FolderId"]);
        DataColumn[] parentCols = new DataColumn[2];
        parentCols[0] = ds.Tables[1].Columns["FolderId"];
        parentCols[1] = ds.Tables[1].Columns["ReportId"];
        DataColumn[] childCols = new DataColumn[2];
        childCols[0] = ds.Tables[2].Columns["FolderId"];
        childCols[1] = ds.Tables[2].Columns["ReportId"];
        ds.Relations.Add("SubReports", parentCols, childCols);
        
        return ds;
	}
}
