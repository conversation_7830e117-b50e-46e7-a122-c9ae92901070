using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditFieldOption : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string FieldId
	{
		get { return (string)this.ViewState["f"]; }
		set { this.ViewState["f"] = value; }
	}

	public string FieldOptionId
	{
		get { return (string)this.ViewState["fo"]; }
		set { this.ViewState["fo"] = value; }
	}

	public string RedirectionUrl
	{
		get { return (string)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	public bool IsDisableTrackingRequested
	{
		get { if (this.ViewState["id"] != null) return (bool)this.ViewState["id"]; else return false; }
		set { this.ViewState["id"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (Request.UrlReferrer != null)
				this.RedirectionUrl = Request.UrlReferrer.PathAndQuery;

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FIELD_ID_KEY]))
				this.FieldId = Request.Params[DieboldConstants.FIELD_ID_KEY];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FIELD_OPTION_ID_KEY]))
				this.FieldOptionId = Request.Params[DieboldConstants.FIELD_OPTION_ID_KEY];

			if (!string.IsNullOrEmpty(this.FieldOptionId))
			{
				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFieldOption", this.FieldOptionId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.rdToolNameField.Enabled = false;
					this.rdToolNameField.Text = DataFormatter.getString(row, "RDToolName");
					this.displayNameField.Text = DataFormatter.getString(row, "DisplayName");

					if (DataFormatter.getBool(row, "ExcludeRequested"))
						this.IsDisableTrackingRequested = true;

					if (!DataFormatter.getBool(row, "ExcludeFromTracking"))
					{
						this.TrackDataDiv.Visible = false;
						this.DisableTrackingDiv.Visible = true;
					}

                    if (DataFormatter.getBool(row, "IsActiveForManualConfigDLL"))
                        this.isManualConfig.Checked = true;
                }
			}
			else if (!string.IsNullOrEmpty(this.FieldId))
			{
				this.IncludeTrackingCheck.Visible = true;
				this.TrackDataDiv.Visible = false;
				this.DisableTrackingDiv.Visible = false;
			}

			if (this.IsDisableTrackingRequested)
			{
				this.PendingDisableDiv.Visible = true;
				this.IncludeTrackingCheck.Visible = false;
				this.TrackDataDiv.Visible = false;
				this.DisableTrackingDiv.Visible = false;
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(this.FieldOptionId))
			SqlHelper.ExecuteNonQuery("RPT_UpdateFieldOption", this.FieldOptionId, displayNameField.Text.Trim(), this.TrackDataDiv.Visible, this.IsDisableTrackingRequested, this.isManualConfig.Checked);
		else
            SqlHelper.ExecuteNonQuery("RPT_InsertFieldOption", this.FieldId, rdToolNameField.Text.Trim(), displayNameField.Text.Trim(), !IncludeTrackingCheck.Checked, this.isManualConfig.Checked);

		if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
			ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("CloseRadWindow(); GetRadWindow().BrowserWindow.location.href='{0}';", this.RedirectionUrl + "&page=" + this.PageNo), true);
		else
			ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("CloseRadWindow(); GetRadWindow().BrowserWindow.location.href='{0}';", this.RedirectionUrl), true);
	}

	protected void CancelButton_Click(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
			ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("CloseRadWindow(); GetRadWindow().BrowserWindow.location.href='{0}';", this.RedirectionUrl + "&page=" + this.PageNo), true);
		else
			ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("CloseRadWindow(); GetRadWindow().BrowserWindow.location.href='{0}';", this.RedirectionUrl), true);
	}

	protected void TrackDataButton_Click(object sender, EventArgs e)
	{
		this.TrackDataDiv.Visible = false;
		this.DisableTrackingDiv.Visible = true;
	}

	protected void DisableTrackingButton_Click(object sender, EventArgs e)
	{
		SqlHelper.ExecuteNonQuery("RPT_DisableFieldOptionTracking", this.FieldOptionId);

		this.PendingDisableDiv.Visible = true;
		this.TrackDataDiv.Visible = false;
		this.DisableTrackingDiv.Visible = false;
	}
}
