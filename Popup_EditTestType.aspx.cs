using System;
using System.Data;
using System.Web.UI;

public partial class Popup_EditTestType : System.Web.UI.Page {
	public string PageNo {
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string itemId {
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e) {
		if (!Page.IsPostBack) {
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.TEST_TYPE_KEY])) {
				this.itemId = Request.Params[DieboldConstants.TEST_TYPE_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTestType", this.itemId);
				foreach (DataRow row in ds.Tables[0].Rows) {
					this.nameField.Text = DataFormatter.getString(row, "TestType");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e) {
		int result = 1;

		if (string.IsNullOrEmpty(this.itemId)) {
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertTestType", this.nameField.Text);
		}
		else {
			SqlHelper.ExecuteNonQuery("RPT_UpdateTestType", Convert.ToInt32(this.itemId), this.nameField.Text, this.isActive.Checked);
		}

		if (result == 0) {
			this.ErrorLabel.Visible = true;
		}
		else {
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditTestType.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditTestType.aspx';CloseRadWindow(); ", true);
		}
	}
}
