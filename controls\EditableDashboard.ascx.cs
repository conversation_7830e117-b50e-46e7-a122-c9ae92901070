using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using System.IO;

public partial class controls_EditableDashboard : System.Web.UI.UserControl
{
	public string UserName
	{
		get { if (this.ViewState["u"] != null) return (string)this.ViewState["u"]; else return Utility.GetUserName(); }
		set { this.ViewState["u"] = value; }
	}
	public bool IsAdminMode
	{
		get { if (this.ViewState["i"] != null) return (bool)this.ViewState["i"]; else return false; }
		set { this.ViewState["i"] = value; }
	}

	private List<DockState> CurrentDockStates
	{
		get
		{
            string key = "EditableDashboard";
            List<DockState> currentDockStates = (List<DockState>)HttpContext.Current.Items[key];
            if (currentDockStates == null)
            {
                currentDockStates = new List<DockState>();
                object dashState = null;
                if (IsAdminMode)
                    dashState = SqlHelper.ExecuteScalar("RPT_LoadDashboardState", DieboldConstants.DASHBOARD_MASTER_NAME);
                else
                    dashState = SqlHelper.ExecuteScalar("RPT_LoadDashboardState", UserName);

                if (dashState != null)
                {
                    string[] dockStates = dashState.ToString().Split('|');
                    foreach (string serializedState in dockStates)
                    {
                        if (!string.IsNullOrEmpty(serializedState))
                        {
                            DockState state = DockState.Deserialize(serializedState);
                            currentDockStates.Add(state);
                        }
                    }
                }

                HttpContext.Current.Items[key] = currentDockStates;
            }
            return currentDockStates;
		}
		set
		{
            StringBuilder builder = new StringBuilder();
			foreach (DockState curState in value)
			{
				string serializedState = curState.ToString() + "|";
				builder.Append(serializedState).ToString();
			}
			if (IsAdminMode)
				SqlHelper.ExecuteNonQuery("RPT_UpdateDashboard", DieboldConstants.DASHBOARD_MASTER_NAME, builder.ToString());
			else
				SqlHelper.ExecuteNonQuery("RPT_UpdateDashboard", UserName, builder.ToString());

            string key = "EditableDashboard";
            this.Session[key] = value;
        }
	}

    protected void Page_Init(object sender, EventArgs e)
    {
        //Recreate the docks in order to ensure their proper operation
        for (int i = 0; i < CurrentDockStates.Count; i++)
        {
            if (CurrentDockStates[i].Closed == false)
            {
                RadDock dock = CreateRadDockFromState(CurrentDockStates[i]);
                dock.Skin = "Diebold";
                dock.EnableEmbeddedSkins = false;
                // The RadDockLayout control will automatically move the RadDock
                // controls to their corresponding zone in the LoadDockLayout
                // event (see below).
                RadDockLayout1.Controls.Add(dock);

                //We want to save the dock state every time a dock is moved.
                CreateSaveStateTrigger(dock);
                //Load the selected widget
                LoadWidget(dock);
            }
        }
    }
    
    protected void Page_Load(object sender, EventArgs e)
	{
        this.Page.PreRender += new EventHandler(Page_PreRender);
        if (!IsPostBack)
		{
			if (IsAdminMode)
			{
				if (!Utility.IsUserAdmin())
					Response.Redirect("Unauthorized.aspx");
			}

			AdminModeDiv.Visible = IsAdminMode;
			AdminModeDiv2.Visible = IsAdminMode;
			NormalModeDiv.Visible = !IsAdminMode;
			NormalModeDiv2.Visible = !IsAdminMode;
		}
    }

    private void Page_PreRender(object sender, EventArgs e)
    {
        foreach (RadDock dock in RadDockLayout1.RegisteredDocks)
        {
            foreach (Control cntrl in dock.ContentContainer.Controls)
                if (cntrl is IDashboardItem)
                    ((IDashboardItem)cntrl).DisplayContent();
        }
    }

	protected void RadDockLayout1_LoadDockLayout(object sender, DockLayoutEventArgs e)
	{
		//Populate the event args with the state information. The RadDockLayout control
		// will automatically move the docks according that information.
		foreach (DockState state in CurrentDockStates)
		{
			e.Positions[state.UniqueName] = state.DockZoneID;
			e.Indices[state.UniqueName] = state.Index;
		}
	}

	protected void RadDockLayout1_SaveDockLayout(object sender, DockLayoutEventArgs e)
	{
		//Save the dock state in the session. This will enable us 
		// to recreate the dock in the next Page_Init. 
		CurrentDockStates = RadDockLayout1.GetRegisteredDocksState();
	}

	private RadDock CreateRadDockFromState(DockState state)
	{
		RadDock dock = new RadDock();
		dock.Skin = "Diebold";
		dock.DockMode = DockMode.Docked;
		dock.EnableEmbeddedSkins = false;
		dock.ID = string.Format("RadDock{0}", state.UniqueName);
		dock.ApplyState(state);

		dock.Command += new DockCommandEventHandler(dock_Command);
		DockCloseCommand clsCmd = new DockCloseCommand();
		clsCmd.Text = "remove";
		dock.Commands.Add(clsCmd);

		DockCommand cmd1 = new DockCommand();
		cmd1.Text = "Edit";
		cmd1.AutoPostBack = false;
		dock.Commands.Add(cmd1);

		return dock;
	}

	private RadDock CreateRadDock()
	{
		int docksCount = CurrentDockStates.Count;

		RadDock dock = new RadDock();
		dock.Skin = "Diebold";
		dock.EnableEmbeddedSkins = false;
		dock.UniqueName = Guid.NewGuid().ToString();
		dock.ID = string.Format("RadDock{0}", dock.UniqueName);
		dock.Title = widgetList.SelectedItem.Text;
		dock.Text = string.Format("Added at {0}", DateTime.Now);
		dock.Width = Unit.Pixel(300);

		dock.Command += new DockCommandEventHandler(dock_Command);
		DockCloseCommand clsCmd = new DockCloseCommand();
		clsCmd.Text = "remove";
		dock.Commands.Add(clsCmd);
		
		DockCommand cmd1 = new DockCommand();
		cmd1.Text = "Edit";
		cmd1.AutoPostBack = false;
		dock.Commands.Add(cmd1);

		return dock;
	}

	void dock_Command(object sender, DockCommandEventArgs e)
	{
		if (e.Command.Name == "Close")
		{
			ScriptManager.RegisterStartupScript(UpdatePanel1, this.GetType(), "RemoveDock", string.Format(@"function _removeDock() {{
					Sys.Application.remove_load(_removeDock); $find('{0}').undock(); $get('{1}').appendChild($get('{0}'));
					$find('{0}').doPostBack('DockPositionChanged');}};Sys.Application.add_load(_removeDock);", ((RadDock)sender).ClientID, UpdatePanel1.ClientID), true);

			Guid cntlGuid = new Guid(((RadDock)sender).UniqueName);
			string ctrlXML = null;

			if (this.IsAdminMode)
				ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", cntlGuid, DieboldConstants.DASHBOARD_MASTER_NAME));
			else
				ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", cntlGuid, this.UserName));

			//If the dashboard item being removed is an image type, delete the associated file from the hard drive.
			try
			{
				DashImageInfo dashInfo = null;

				if (!string.IsNullOrEmpty(ctrlXML))
					dashInfo = (DashImageInfo)XmlSerializable.LoadFromXmlText(typeof(DashImageInfo), ctrlXML);

				if (dashInfo != null)
				{
					string fullName = Server.MapPath(ConfigurationManager.AppSettings["DashboardImagesPath"]) + dashInfo.FileName;

					if (File.Exists(fullName))
						File.Delete(fullName);
				}
			}
			catch
			{
				//ignore errors
			}

			// Remove the Dashboard Item record
			SqlHelper.ExecuteNonQuery("RPT_DeleteDashboardItem", cntlGuid);
		}
		else if (e.Command.Name == "Custom")
		{
			RadDock dock = (RadDock)sender;
			Control itemCntrl = dock.ContentContainer.FindControl(dock.ID + "_widget");
			if (itemCntrl is IDashboardItem)
			{
				IDashboardItem item = (IDashboardItem)itemCntrl;
				item.DisplayEdit();

	//			ScriptManager.RegisterStartupScript(UpdatePanel1, this.GetType(), "Refresh", string.Format(@"{0}.click();", RefreshButton.ClientID), true);
			}
		}
	}

	private void CreateSaveStateTrigger(RadDock dock)
	{
		//Ensure that the RadDock control will initiate postback when its position changes on the client or 
		//any of the commands is clicked. Using the trigger we will "ajaxify" that postback.
		dock.AutoPostBack = true;
		dock.CommandsAutoPostBack = true;

		AsyncPostBackTrigger saveStateTrigger = new AsyncPostBackTrigger();
		saveStateTrigger.ControlID = dock.ID;
		saveStateTrigger.EventName = "DockPositionChanged";
		UpdatePanel1.Triggers.Add(saveStateTrigger);

		saveStateTrigger = new AsyncPostBackTrigger();
		saveStateTrigger.ControlID = dock.ID;
		saveStateTrigger.EventName = "Command";
		UpdatePanel1.Triggers.Add(saveStateTrigger);
	}

	private void LoadWidget(RadDock dock)
	{
		if (string.IsNullOrEmpty(dock.Tag))
		{
			return;
		}
		Control widget = LoadControl(dock.Tag);
		widget.ID = dock.ID + "_widget";
        dock.ContentContainer.Controls.Add(widget);
        if (widget is IDashboardItem)
			((IDashboardItem)widget).Initialize(new Guid(dock.UniqueName), this.IsAdminMode);
	}

	protected void ButtonAddDock_Click(object sender, EventArgs e)
	{
		RadDock dock = CreateRadDock();
		dock.Skin = "Diebold";
		dock.DockMode = DockMode.Docked;
		dock.EnableEmbeddedSkins = false;
		//In order to optimize the execution speed we are adding the dock to a hidden update panel and then 
		//register a script which will move it to RadDockZone1 after the AJAX request completes.
		UpdatePanel1.ContentTemplateContainer.Controls.AddAt(0, dock);

        ScriptManager.RegisterStartupScript(dock, this.GetType(), "AddDock", string.Format(@"function _addDock() {{Sys.Application.remove_load(_addDock); $find('{1}').dock($find('{0}'), 0);
				$find('{0}').doPostBack('DockPositionChanged');}}; Sys.Application.add_load(_addDock);", dock.ClientID, RadDockZone1.ClientID), true);

		//Right now the RadDock control is not docked. When we try to save its state later, the DockZoneID will be empty. To workaround this problem we will 
		// set the AutoPostBack property of the RadDock control to true and will attach an AsyncPostBackTrigger for the DockPositionChanged client-side
		// event. This will initiate second AJAX request in order to save the state AFTER the dock was docked in RadDockZone1.
		CreateSaveStateTrigger(dock);

		//Load the selected widget in the RadDock control
		dock.Tag = widgetList.SelectedValue;
		LoadWidget(dock);
	}
}
