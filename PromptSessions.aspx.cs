using System;
using System.Data;
using System.Configuration;
using System.Collections.Generic;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class PromptSessions : System.Web.UI.Page
{
	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (Request.QueryString[DieboldConstants.REPORT_ID_KEY] != null)
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

			if (this.RepInfo == null)
				Response.Redirect("popuperror.aspx");

			sessionsList.DataSource = Utility.GetActiveSessionsList();
			sessionsList.DataBind();
		}
    }

	protected void RunButton_Click(object sender, EventArgs e)
	{
		this.DefaultPanel.Visible = false;
		this.RedirectingPanel.Visible = true;

		//Save Sessions
		this.RepInfo.AttachedSessions = new List<SessionInfo>();
		SessionInfo curSession = null;
		foreach (ListItem item in sessionsList.Items)
		{
			if (item.Selected)
			{
				curSession = new SessionInfo();
				curSession.SessionId = Convert.ToInt32(item.Value);
				curSession.SessionName = item.Text;

				this.RepInfo.AttachedSessions.Add(curSession);

				//Save to report
				if (saveSessionsCheck.Checked)
				{
					SqlHelper.ExecuteNonQuery("RPT_InsertReportSession", this.RepInfo.ReportId, curSession.SessionId);
				}
			}
		}

		//Update report prompt field
		if (saveSessionsCheck.Checked)
		{
			SqlHelper.ExecuteNonQuery("RPT_UpdateReportRemovePrompt", this.RepInfo.ReportId);
		}

		Utility.SetReportInfoForTransfer(this.RepInfo);

		//Close the window and redirect parent to the Run Report screen.
		string closeScript = "<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.location.href='" + this.RepInfo.RunPageName +"';\r\n</script>";
		Page.ClientScript.RegisterStartupScript(typeof(PromptSessions), "CloseScript", closeScript);
	}
}
