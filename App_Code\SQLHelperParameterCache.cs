using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public sealed class SqlHelperParameterCache
{
	// Fields
	private static Hashtable paramCache = Hashtable.Synchronized(new Hashtable());

	// Methods
	private SqlHelperParameterCache()
	{
	}

	public static void CacheParameterSet(string connectionString, string commandText, params SqlParameter[] commandParameters)
	{
		string hashKey = connectionString + ":" + commandText;
		paramCache[hashKey] = commandParameters;
	}

	private static SqlParameter[] CloneParameters(SqlParameter[] originalParameters)
	{
		short j = (short)(originalParameters.Length - 1);
		SqlParameter[] clonedParameters = new SqlParameter[j + 1];
		short _Vb_t_i2_0 = j;
		for (short i = 0; i <= _Vb_t_i2_0; i = (short)(i + 1))
		{
			clonedParameters[i] = (SqlParameter)((ICloneable)originalParameters[i]).Clone();
		}
		return clonedParameters;
	}

	private static SqlParameter[] DiscoverSpParameterSet(string connectionString, string spName, bool includeReturnValueParameter, params object[] parameterValues)
	{
		SqlParameter[] discoveredParameters;
		SqlConnection cn = new SqlConnection(connectionString);
		SqlCommand cmd = new SqlCommand(spName, cn);
		try
		{
			cn.Open();
			cmd.CommandType = CommandType.StoredProcedure;
			SqlCommandBuilder.DeriveParameters(cmd);
			if (!includeReturnValueParameter)
			{
				cmd.Parameters.RemoveAt(0);
			}
			discoveredParameters = new SqlParameter[(cmd.Parameters.Count - 1) + 1];
			cmd.Parameters.CopyTo((Array)discoveredParameters, 0);
		}
		finally
		{
			cmd.Dispose();
			cn.Dispose();
		}
		return discoveredParameters;
	}

	public static SqlParameter[] GetCachedParameterSet(string connectionString, string commandText)
	{
		string hashKey = connectionString + ":" + commandText;
		SqlParameter[] cachedParameters = (SqlParameter[])paramCache[hashKey];
		if (cachedParameters == null)
		{
			return null;
		}
		return CloneParameters(cachedParameters);
	}

	public static SqlParameter[] GetSpParameterSet(string connectionString, string spName)
	{
		return GetSpParameterSet(connectionString, spName, false);
	}

	public static SqlParameter[] GetSpParameterSet(string connectionString, string spName, bool includeReturnValueParameter)
	{
		string hashKey = connectionString + ":" + spName + (includeReturnValueParameter?":include ReturnValue Parameter":"");
		SqlParameter[] cachedParameters = (SqlParameter[])paramCache[hashKey];
		if (cachedParameters == null)
		{
			paramCache[hashKey] = DiscoverSpParameterSet(connectionString, spName, includeReturnValueParameter, new object[0]);
			cachedParameters = (SqlParameter[])paramCache[hashKey];
		}
		return CloneParameters(cachedParameters);
	}
}