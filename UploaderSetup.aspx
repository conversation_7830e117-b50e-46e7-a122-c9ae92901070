﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="UploaderSetup.aspx.cs" Inherits="UploaderSetup" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
 	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Uploader Setup</td>
						<td class="widgetTop" style="width: 30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align: right;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="width: 10px;">
										&nbsp;</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<div class="widget">
					<div class="title">Installing the RDTool Uploader</div>
					
					<p style="padding-left:14px; font-size:14px; line-height:22px;">
						1. If a version of the Uploader prior to version 2.0 is already installed, uninstall it using Add/Remove Programs from the Control Panel.<br />
						2. Download the zipped <a style="padding:0px; font-size:13px; font-weight:bold;" href="ClientDownload/Uploader_Latest.zip">Uploader Installer</a> to a directory on the test cell.<br />
						3. Unzip the downloaded file, and double click or run the Setup.exe.<br />
						4. A reboot may be required if the installation of Microsoft .NET is required. If it is, Setup.exe should be run again after the reboot.<br />
						5. Follow the on-screen instructions to complete the installation<br />
						6. If more help is needed, consult the <a style="padding:0px; font-size:13px; font-weight:bold;" href="ClientDownload/Uploader.pdf">Uploader Guide</a>
							or the <a style="padding:0px; font-size:13px; font-weight:bold;" href="ClientDownload/UploaderInstallApp.pdf">Install Guide</a>.<br />
					</p>
					<p style="padding-left:14px; font-size:14px; line-height:22px; padding-top:10px;">
						<b style="font-size:13px;">Installing the Uploader Extension:</b><br /><br />
						1. Download the zipped <a style="padding:0px; font-size:13px; font-weight:bold;" href="ClientDownload/UploaderExtension.zip">UploaderExtension.dll</a><br />
						2. Unzip the downloaded file and place in the RDTool directory (commonly located at: C:\Program Files\Diebold\AMI\RDTool)<br />
						3. Within the same RDTool directory, locate the Extension.xml file and open it for editing.<br />
						4. Copy and paste the code from the box below into the Extension.xml file at the proper location, save and close the file when complete.<br />
						5. If more help is needed, consult the <a style="padding:0px; font-size:13px; font-weight:bold;" href="ClientDownload/UploaderExtension.pdf">Uploader Extension document</a>.<br />
					</p>
					
<asp:textbox runat="server" textmode="MultiLine" rows="20" readonly="true" style="margin:0 0 30px 14px; background-color:#e5e5e5; padding:5px; width:740px; text-align:left;">
&lt;command name="StartTransaction" module="UploaderExtension.dll" type="D"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;EngineeringOutDirectory&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.256s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;C:\RDToolEngineeringZips&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;CellId&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;$cellid&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;Session&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;$sessionid&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;DeviceId&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;AFD&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;TXN_NUM&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;$trx&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;SerialNumber&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;123&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;IncludeAMITrace&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.4s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;MaxWaitingTransactions&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%l&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;5&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;output-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;numFiles&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%l&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/output-parameter&gt;
&nbsp;&nbsp;&lt;/command&gt;
&nbsp;&nbsp;&lt;command name="EndTransaction" module="UploaderExtension.dll" type="D"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;EngineeringOutDirectory&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.256s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;C:\RDToolEngineeringZips&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;CellId&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;$cellid&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;Session&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;$sessionid&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;DeviceId&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;AFD&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;TXN_NUM&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;$trx&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;SerialNumber&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;123&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;IncludeAMITrace&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.4s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;MaxWaitingTransactions&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%l&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;5&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;output-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;numFiles&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%l&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/output-parameter&gt;
&nbsp;&nbsp;&lt;/command&gt;
&nbsp;&nbsp;&lt;command name="CaptureEngineeringFiles" module="UploaderExtension.dll" type="D"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;EngineeringOutDirectory&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.256s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;C:\RDToolEngineeringZips&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;DbdDfsDirectory&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.256s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;C:\Program Files\Diebold\AMI\LOG&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;CellId&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;Session&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;DeviceId&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;AFD&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;TXN_NUM&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;SerialNumber&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.64s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;IncludeAMITrace&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.4s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;1&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;control&gt;edit-box&lt;/control&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;Compression Level (0 = none, 1 = low, 2 = medium, 3 = max)&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%.4s&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;value&gt;3&lt;/value&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/input-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;output-parameter&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;name&gt;numFiles&lt;/name&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;type&gt;%l&lt;/type&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/output-parameter&gt;
&nbsp;&nbsp;&lt;/command&gt;
</asp:textbox>
				</div>
			</td>
		</tr>
	</table>
</asp:Content>

