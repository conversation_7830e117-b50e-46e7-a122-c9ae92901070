using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditScheduledReport : System.Web.UI.Page
{
	public int ScheduledReportId
	{
		get { if (this.ViewState["sr"] != null) return (int)this.ViewState["sr"]; else return 0; }
		set { this.ViewState["sr"] = value; }
	}

	public int ReportId
	{
		get { if (this.ViewState["r"] != null) return (int)this.ViewState["r"]; else return 0; }
		set { this.ViewState["r"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		deleteBtn.Attributes.Add("onclick", "return confirm('Are you sure you wish to delete this scheduled report?');");

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SCHEDULED_REPORT_ID_KEY]))
				ScheduledReportId = Convert.ToInt32(Request.QueryString[DieboldConstants.SCHEDULED_REPORT_ID_KEY]);
			
			if (!string.IsNullOrEmpty(Request.Params["r"]))
				ReportId = Convert.ToInt32(Request.QueryString["r"]);

			if (ScheduledReportId > 0 || ReportId > 0)
				LoadScheduledReport();

			if (this.ScheduledReportId > 0)
				deleteBtn.Visible = true;
		}
	}

	private void LoadScheduledReport()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadScheduledReport", this.ScheduledReportId, this.ReportId);
		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows) //only loads one row.
			{
				this.ScheduledReportId = DataFormatter.getInt32(row, "ScheduledReportId");
				this.ReportId = DataFormatter.getInt32(row, "ReportId");
				reportName.Text = DataFormatter.getString(row, "ReportName");
				rptNameRow.Visible = true;

				if (DataFormatter.getDateTime(row, "NextScheduledDate") != DateTime.MinValue)
					timeField.SelectedDate = DataFormatter.getDateTime(row, "NextScheduledDate");

				scheduleList.Items[0].Selected = DataFormatter.getBool(row, "Sunday");
				scheduleList.Items[1].Selected = DataFormatter.getBool(row, "Monday");
				scheduleList.Items[2].Selected = DataFormatter.getBool(row, "Tuesday");
				scheduleList.Items[3].Selected = DataFormatter.getBool(row, "Wednesday");
				scheduleList.Items[4].Selected = DataFormatter.getBool(row, "Thursday");
				scheduleList.Items[5].Selected = DataFormatter.getBool(row, "Friday");
				scheduleList.Items[6].Selected = DataFormatter.getBool(row, "Saturday");

				if (DataFormatter.getBool(row, "Landscape"))
					formatList.SelectedIndex = 1;

				if (!string.IsNullOrEmpty(DataFormatter.getString(row, "ChartScale")))
					chartScaleList.SelectedValue = DataFormatter.getString(row, "ChartScale");

				if (!string.IsNullOrEmpty(DataFormatter.getString(row, "DataScale")))
					dataScaleList.SelectedValue = DataFormatter.getString(row, "DataScale");

				dataSeparate.Checked = DataFormatter.getBool(row, "DataSeparate");
				groupReportsCheck.Checked = DataFormatter.getBool(row, "GroupReports");

				if (!string.IsNullOrEmpty(DataFormatter.getString(row, "EmailList")))
				{
					emailAddressBox.Text = DataFormatter.getString(row, "EmailList");
					receiveEmailCheck.Checked = true;
					EmailCheck_Changed(null, null);
				}

				if (!string.IsNullOrEmpty(DataFormatter.getString(row, "PrinterPath")))
				{
					printerPath.Text = DataFormatter.getString(row, "PrinterPath");
					printCheck.Checked = true;
					numCopies.Text = DataFormatter.getInt32(row, "NumCopies").ToString();
					PrintCheck_Changed(null, null);
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		object emailAddresses = null;
		object printPath = null;
		object selectedTime = null;
		int copies = 1;

		if (!string.IsNullOrEmpty(numCopies.Text.Trim()))
			copies = Convert.ToInt32(numCopies.Text.Trim());

		if (receiveEmailCheck.Checked && !string.IsNullOrEmpty(emailAddressBox.Text.Trim()))
			emailAddresses = emailAddressBox.Text.Trim().Replace(" ", "");

		if (printCheck.Checked && !string.IsNullOrEmpty(printerPath.Text.Trim()))
			printPath = printerPath.Text.Trim();

		if (timeField.SelectedDate != null)
			selectedTime = CalculateNextScheduledReportDate((DateTime)timeField.SelectedDate);
		
		SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReport",
			this.ScheduledReportId, this.ReportId, selectedTime, scheduleList.Items[0].Selected,
			scheduleList.Items[1].Selected, scheduleList.Items[2].Selected, scheduleList.Items[3].Selected, scheduleList.Items[4].Selected,
			scheduleList.Items[5].Selected, scheduleList.Items[6].Selected, (formatList.SelectedIndex == 1), chartScaleList.SelectedValue,
			dataScaleList.SelectedValue, dataSeparate.Checked, emailAddresses, printPath, copies, groupReportsCheck.Checked);

		ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "CloseRadWindow();", true);
	}

	protected void DeleteButton_Click(object sender, EventArgs e)
	{
		SqlHelper.ExecuteNonQuery("RPT_DeleteScheduledReport", this.ScheduledReportId);
		ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "CloseRadWindow(); GetRadWindow().BrowserWindow.document.location.href='ReportLibrary.aspx';", true);
	}

	protected void EmailCheck_Changed(object sender, EventArgs e)
	{
		this.emailDiv.Visible = this.receiveEmailCheck.Checked;
	}

	protected void PrintCheck_Changed(object sender, EventArgs e)
	{
		this.printDiv.Visible = this.printCheck.Checked;
	}

	private DateTime CalculateNextScheduledReportDate(DateTime scheduledDate)
	{
		bool runSunday = scheduleList.Items[0].Selected;
		bool runMonday = scheduleList.Items[1].Selected;
		bool runTuesday = scheduleList.Items[2].Selected;
		bool runWednesday = scheduleList.Items[3].Selected;
		bool runThursday = scheduleList.Items[4].Selected;
		bool runFriday = scheduleList.Items[5].Selected;
		bool runSaturday = scheduleList.Items[6].Selected;
		
		double dayOffset = 0;

		DateTime curDate = DateTime.Now;
		if (scheduledDate > curDate)
		{
			//remove current time
			curDate = curDate.AddHours(-curDate.Hour);
			curDate = curDate.AddMinutes(-curDate.Minute);
			curDate = curDate.AddSeconds(-curDate.Second);
			curDate = curDate.AddMilliseconds(-curDate.Millisecond);
			
			//reset to scheduled time, round to hours & minutes
			curDate = curDate.AddHours(scheduledDate.Hour);
			curDate = curDate.AddMinutes(scheduledDate.Minute);
			
			scheduledDate = curDate;
		}

		//if time is passed for today, start looking for the next schedule date tomorrow
		if (scheduledDate.TimeOfDay < DateTime.Now.TimeOfDay)
			dayOffset = 1;

		bool dateFound = false;
		while (!dateFound && dayOffset < 14) 
		{
			switch (scheduledDate.AddDays(dayOffset).DayOfWeek)
			{
				case DayOfWeek.Sunday:
					if (runSunday)
						dateFound = true;
					break;
				case DayOfWeek.Monday:
					if (runMonday)
						dateFound = true;
					break;
				case DayOfWeek.Tuesday:
					if (runTuesday)
						dateFound = true;
					break;
				case DayOfWeek.Wednesday:
					if (runWednesday)
						dateFound = true;
					break;
				case DayOfWeek.Thursday:
					if (runThursday)
						dateFound = true;
					break;
				case DayOfWeek.Friday:
					if (runFriday)
						dateFound = true;
					break;
				case DayOfWeek.Saturday:
					if (runSaturday)
						dateFound = true;
					break;
			}

			if (!dateFound)
				dayOffset++;
		}

		return scheduledDate.AddDays(dayOffset);
	}
}
