using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class EditDevices : System.Web.UI.Page
{
	public bool IncludeInactive
	{
		get { if (this.Session[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY] != null) return (bool)this.Session[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY]; else return false; }
		set { this.Session[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		this.EntityNavBar.ItemChangedEvent += new CommandEventHandler(EntityNavBar_ItemChangedEvent);
		this.AddButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditDevice.aspx?{0}={1}', null); return false;", DieboldConstants.DEVICE_TYPE_KEY, this.EntityNavBar.DeviceTypeId));
		
		if (!Page.IsPostBack)
		{
			ManageClosedDisplay();

			if (!string.IsNullOrEmpty(Request.Params["page"]))
				DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}

		BindData();
	}

	void EntityNavBar_ItemChangedEvent(object sender, CommandEventArgs e)
	{
		DataGrid1.CurrentPageIndex = 0;
		this.AddButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditDevice.aspx?{0}={1}', null); return false;", DieboldConstants.DEVICE_TYPE_KEY, this.EntityNavBar.DeviceTypeId));
		
		BindData();
	}

	private void ManageClosedDisplay()
	{
		if (this.IncludeInactive)
		{
			this.includeInactiveLink.Text = "Exclude Inactive";
			IncludeInactiveToggle.Attributes.Add("class", "cancelButtonTop");
		}
		else
		{
			this.includeInactiveLink.Text = "Include Inactive";
			IncludeInactiveToggle.Attributes.Add("class", "addButtonTop");
		}
	}

	protected void IncludeInactive_Toggle(object sender, EventArgs e)
	{
		this.IncludeInactive = !this.IncludeInactive;
		ManageClosedDisplay();
		BindData();
	}

	private void BindData()
	{
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_DevicesByDeviceType", this.EntityNavBar.DeviceTypeId, this.IncludeInactive);
		DataGrid1.DataBind();
	}
}
