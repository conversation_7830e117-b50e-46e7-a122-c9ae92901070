<%@ Control Language="C#" AutoEventWireup="true" CodeFile="EntityItem.ascx.cs" Inherits="controls_EntityItem" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<table border="0" cellpadding="0" cellspacing="0">
	<tr>
		<td id="DeviceTypeSelection" runat="server">
			<div class="title">Device Type</div>
			<telerik:radcombobox id="DeviceTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
				 appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" autopostback="true"
				 onselectedindexchanged="DeviceTypeList_SelectedIndexChanged" causesvalidation="false">
			</telerik:radcombobox>
		</td>
		<td id="EntityTypeSelection" runat="server">
			<div class="title">Entity Type</div>
			<telerik:radcombobox id="EntityList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                 appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" autopostback="true"
                 onselectedindexchanged="EntityList_SelectedIndexChanged">
            </telerik:radcombobox>
		</td>
	</tr>	
</table>