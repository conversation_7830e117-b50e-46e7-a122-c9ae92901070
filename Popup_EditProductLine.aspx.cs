using System;
using System.Data;
using System.Web.UI;

public partial class Popup_EditProductLine : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string ProductLineId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.PRODUCT_LINES_KEY]))
			{
				this.ProductLineId = Request.Params[DieboldConstants.PRODUCT_LINES_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetProductLine", this.ProductLineId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.ProductLineField.Text = DataFormatter.getString(row, "ProductLine");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(ProductLineId)) {
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertProductLine", this.ProductLineField.Text);
		}
		else {
			SqlHelper.ExecuteNonQuery("RPT_UpdateProductLine", Convert.ToInt32(this.ProductLineId), this.ProductLineField.Text, this.isActive.Checked);
		}

		if (result == 0) {
			this.ErrorLabel.Visible = true;
		}
		else {
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditProductLines.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditProductLines.aspx';CloseRadWindow(); ", true);
		}
	}
}
