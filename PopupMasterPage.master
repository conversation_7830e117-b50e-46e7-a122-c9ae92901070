<%@ Master Language="C#" AutoEventWireup="true" CodeFile="PopupMasterPage.master.cs" Inherits="PopupMasterPage" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Diebold Online Reporting</title>
    <link href="style.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" language="javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.4.0/jquery.min.js"></script>
    <script type="text/javascript" language="javascript">
	function CloseRadWindow()
    {
	    var oWindow = GetRadWindow();			
	    oWindow.close();
    } 
    function GetRadWindow()
    {
	    var oWindow = null;
	    if (window.radWindow)
	        oWindow = window.radWindow;
	    else if (window.frameElement.radWindow)
	        oWindow = window.frameElement.radWindow;
	    return oWindow;
    }
</script>
</head>
<body style="margin: 0px 0px 0px 0px; background-color:#5b5551;">
    <form id="form1" runat="server">
    <div>
		<asp:scriptmanager id="Scriptmanager1" runat="server"></asp:scriptmanager>
        <asp:contentplaceholder id="BodyContent" runat="server">
        </asp:contentplaceholder>
    </div>
    </form>
</body>
</html>
