using System;
using System.Collections;
using System.Runtime.Serialization;

[Serializable()]
public class ParetoInfo : ISerializable
{
	public bool IncludeTotal = false;
    public ReportHelper.DateGroupingEnum GroupingId = ReportHelper.DateGroupingEnum.BY_MONTH;
	public int MaxColumns = 0;
    public bool SplitByCell = false;
    public bool ByStatisticValue = false;

	// Default constructor.
	public ParetoInfo() { }

	// Deserialization constructor.
	public ParetoInfo(SerializationInfo info, StreamingContext context)
	{
		IncludeTotal = (bool)info.GetValue("it", typeof(bool));
        GroupingId = (ReportHelper.DateGroupingEnum)info.GetValue("g", typeof(int));
		MaxColumns = (int)info.GetValue("max", typeof(int));
		SplitByCell = (bool)info.GetValue("s", typeof(bool));
        ByStatisticValue = (bool)info.GetValue("v", typeof(bool));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("it", IncludeTotal);
		info.AddValue("g", (int)GroupingId);
		info.AddValue("max", (int)MaxColumns);
        info.AddValue("s", SplitByCell);
        info.AddValue("v", ByStatisticValue);
    }
}
