<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ReportLibrary.aspx.cs" Inherits="ReportLibrary" smartnavigation="false" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="c" ContentPlaceHolderID="BodyContent" Runat="Server">

	<script language="javascript" type="text/javascript">
	    var sessionList = new Array(<%= BuildSessionsJSON() %>);
	    var myList = new Array(<%= BuildMyReportsJSON() %>);
	    var sharedList = new Array(<%= BuildSharedReportsJSON() %>);
	    var subReportExpandList = '<%= Session["subReportExpandList"] %>';
	    
	    function containerRowClick(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
            var id = $(this).attr('sessId');
	        var hasReportList = ($('.report', $(this)).size() > 0);
	        var hasChildren = false;
            var shouldExpand = ($('.expandButton', $(this)).size() > 0);
            var shouldCollapse = ($('.collapseButton', $(this)).size() > 0) || ($('.repeaterTreeItemSelected', $(this)).size() > 0);
            if(!shouldExpand && !shouldCollapse)
                shouldExpand = true;

            var section = $('#sessionList');
            var lst = sessionList;
            var options = (shouldExpand? {sessionSelectedFolder: id} : {sessionSelectedFolder: '0'});
            var isSess = true;
            var delOn = null;
            var delOff = null;
            if(!id) {
                id = $(this).attr('fldId');
                section = $('#myList');
                lst = myList;
                options = (shouldExpand? {mySelectedFolder: id} : {mySelectedFolder: '0'});
                isSess = false;
                delOn = $('#MyReportDeleteFolder');
                delOff = $('#MyReportDeleteFolder2');
            }
            if(!id) {
                id = $(this).attr('shrId');
                section = $('#sharedList');
                lst = sharedList;
                options = (shouldExpand? {sharedSelectedFolder: id} : {sharedSelectedFolder: '0'});
                isSess = false;
                delOn = $('#SharedReportDeleteFolder');
                delOff = $('#SharedReportDeleteFolder2');
            }
                        
            var grp = null;
            $.each(lst, function(index, item) {
                if(item.id == id) {
                    grp = item;
                    return false;
                }
            });
            if(grp) 
                hasChildren = (grp.reports && grp.reports.length > 0);

            if(!hasReportList) {
                if(hasChildren) {
                    var tbl = $('<table class="report" width="100%" cellpadding="0" cellspacing="0"></table>').hide().appendTo($(this));
                    var isAlt = false;
    	            $.each(grp.reports, function(index, item) {
                        buildReportRow(item, tbl, isAlt, isSess);
                        isAlt = !isAlt;
                    });
                }
            }
            
            if(shouldExpand) {
                $('.repeaterTreeItemSelected', section).removeClass('repeaterTreeItemSelected');
                $('.collapseButton', section).removeClass('collapseButton').addClass('expandButton');
                $('.report', section).hide();
                if(hasChildren) {
                    $('.expandButton', $(this)).removeClass('expandButton').addClass('collapseButton');
                    if(delOn)
                        delOn.hide();
                    if(delOff)
                        delOff.show();
                } else {
                    if(delOn)
                        delOn.show();
                    if(delOff)
                        delOff.hide();
                }
	            $(this).children('div:first').addClass('repeaterTreeItemSelected');
                $('.report', $(this)).show();
            } else {
	            $('.collapseButton', $(this)).removeClass('collapseButton').addClass('expandButton');
	            $('.repeaterTreeItemSelected', $(this)).removeClass('repeaterTreeItemSelected');
                $('.report', $(this)).hide();
                if(delOn)
                    delOn.hide();
                if(delOff)
                    delOff.show();
            }
			$('<div></div>').load('ReportLibrary.aspx', options);
            return false;
	    }
	    function buildSessionRow(item) {
	        var newItem = $('<div style="padding:3px 0 3px 0px;"></div>').attr('sessId', item.id).click(containerRowClick);
	        var padDiv = $('<div style="padding-left:14px;"></div>').hover(function() { $(this).css('background-color', '#bcdaf2'); }, function() { $(this).css('background-color', ''); }).appendTo(newItem);
	        var btnClass = 'expandButton';
	        if(item.reports && item.reports.length == 0)
	            btnClass = 'noChildrenButton';
	        $('<div style="padding-left:14px;"></div>').addClass(btnClass).text(item.name).appendTo(padDiv);
	        return newItem;
	    }
	    function buildFolderRow(item, isShared) {
	        var newItem = $('<div style="padding:3px 0 3px 0px;"></div>').click(containerRowClick);
	        if(isShared)
	            newItem.attr('shrId', item.id);
            else
	            newItem.attr('fldId', item.id);
	        var padDiv = $('<div style="padding-left:14px;"></div>').hover(function() { $(this).css('background-color', '#bcdaf2'); }, function() { $(this).css('background-color', ''); }).appendTo(newItem);
	        var btnClass = 'expandButton';
	        if(item.reports && item.reports.length == 0)
	            btnClass = 'noChildrenButton';
	        $('<div style="padding-left:14px;"></div>').addClass(btnClass).text(item.name).appendTo(padDiv);
	        return newItem;
	    }
	    function runReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
	        $('#reportId').val($(this).attr('rptId'));
	        $('#action').val('run');
	        document.forms[0].submit();
            return false;
	    }
	    function copyReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
    	    window.radopen('savereport.aspx?r=' + $(this).attr('rptId'), null);
    	    return false;
	    }
	    function deleteReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
	        if(confirm('Are you sure you wish to delete this report?')) {
	            $('#reportId').val($(this).attr('rptId'));
	            $('#action').val('delete');
	            document.forms[0].submit();
	        }
	        return false;
	    }
	    function deleteMyFolder(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
            var id = $('.repeaterTreeItemSelected', '#myList').parents('div:first').attr('fldId');
            if(id && id > 0) {
	            if(confirm('Are you sure you wish to delete the selected folder?')) {
	                $('#reportId').val(id);
	                $('#action').val('deleteMyFolder');
	                document.forms[0].submit();
	            }
	        }
	        return false;
	    }
	    function deleteSharedFolder(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
            var id = $('.repeaterTreeItemSelected', '#sharedList').parents('div:first').attr('shrId');
            if(id && id > 0) {
	            if(confirm('Are you sure you wish to delete the selected folder?')) {
	                $('#reportId').val(id);
	                $('#action').val('deleteSharedFolder');
	                document.forms[0].submit();
	            }
	        }
	        return false;
	    }
	    function editReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
    	    window.radopen('NewReportWizard_Setup.aspx?r=' + $(this).attr('rptId'), null);
    	    return false;
	    }
	    function snapReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
    	    window.radopen('ReportSnapshot.aspx?r=' + $(this).attr('rptId'), null);
    	    return false;
	    }
	    function toggleReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
            var parentRow = $(this).parents('tr:first');
            var shouldExpand = ($('.expandSubButton', parentRow).size() > 0);
            var shouldCollapse = ($('.collapseSubButton', parentRow).size() > 0);
            if(!shouldExpand && !shouldCollapse)
                shouldExpand = true;

            if(shouldExpand) {
                $('.expandSubButton', parentRow).removeClass('expandSubButton').addClass('collapseSubButton');
                parentRow.nextUntil('[rptId!=' + $(this).attr('rptId') + ']').show();
                subReportExpandList = subReportExpandList + ',' + $(this).attr('rptId');
            } else {
	            $('.collapseSubButton', parentRow).removeClass('collapseSubButton').addClass('expandSubButton');
                parentRow.nextUntil('[rptId!=' + $(this).attr('rptId') + ']').hide();
                if(subReportExpandList) {
                    var regObj = new RegExp(',' + $(this).attr('rptId'),'g');
                    subReportExpandList = subReportExpandList.replace(regObj, '');
                    if(!subReportExpandList || subReportExpandList.length == 0)
                        subReportExpandList = ',0';
                }
            }
			$('<div></div>').load('ReportLibrary.aspx', {subReportExpandList: subReportExpandList});
    	    return false;
	    }
	    function buildReportRow(item, parentItem, isAlt, isSession) {
	        var allowEdit = (!isSession) || <%= (Utility.IsUserAdmin()?"true":"false") %>;
            var rowItem = $('<tr></tr>');
            var cellClass = (isAlt?'repeaterSubItemAlt':'repeaterSubItem');
            var btnClass = 'expandSubButton';
	        if(item.sub && item.sub.length == 0)
	            btnClass = 'noChildrenSubButton';
	        rowItem.appendTo(parentItem);
	        $('<td style="width:5px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
	        $('<td style="padding-left:0px;padding-right:0px;width:14px;"></td>').addClass(cellClass).addClass(btnClass).attr('rptId', item.id).click(toggleReport).appendTo(rowItem);
	        $('<td style="padding-left:0px;"></td>').addClass(cellClass).append($('<a class="reportLibraryLink" style="cursor:pointer;padding-left:0px;"></a>').text(item.name).attr('title', item.name).attr('rptId', item.id).click(runReport)).appendTo(rowItem);
	        $('<td style="width:160px;"></td>').addClass(cellClass).text(item.type).appendTo(rowItem);
	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Run</a>').attr('rptId', item.id).click(runReport))).appendTo(rowItem);
	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Copy</a>').attr('rptId', item.id).click(copyReport))).appendTo(rowItem);
            if(allowEdit) {
    	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Delete</a>').attr('rptId', item.id).click(deleteReport))).appendTo(rowItem);
    	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Edit</a>').attr('rptId', item.id).click(editReport))).appendTo(rowItem);
            } else {
    	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
    	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
            }
            if(item.snap && item.snap > 0) {
    	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Snapshots</a>').attr('rptId', item.id).click(snapReport))).appendTo(rowItem);
            } else {
    	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
            }

            $.each(item.sub, function(subindex, subitem) {
                buildSubReportRow(subitem, parentItem, isAlt, isSession, item.id);
            });
	    }
	    
	    function runSubReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
	        $('#reportId').val($(this).attr('rptId'));
	        $('#subReportId').val($(this).attr('subId'));
	        $('#action').val('run');
	        document.forms[0].submit();
	        return false;
	    }
	    function editSubReport(e) {
            if(e) {
                e.stopPropagation();
                e.preventDefault();
            }
            var editWin = window.radopen('EditSubReport.aspx?s=' + $(this).attr('subId'), null);
            editWin.SetSize(350, 250);
            return false;
	    }
	    function buildSubReportRow(item, parentItem, isAlt, isSession, rptId) {
	        var allowEdit = (!isSession) || <%= (Utility.IsUserAdmin()?"true":"false") %>;
            var cellClass = (isAlt?'repeaterSubItemAlt':'repeaterSubItem');
            var rowItem = $('<tr></tr>').attr('rptId', rptId);
	        rowItem.hide().appendTo(parentItem);
	        $('<td style="width:5px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
	        $('<td style="padding-left:0px;padding-right:0px;width:14px;"></td>').addClass(cellClass).appendTo(rowItem);
	        $('<td style="padding-left:0px;"></td>').addClass(cellClass).append($('<a class="reportLibraryLink" style="cursor:pointer;"></a>').text(item.name).attr('title', item.name).attr('rptId', rptId).attr('subId', item.id).click(runSubReport)).appendTo(rowItem);
	        $('<td style="width:160px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Run</a>').attr('rptId', rptId).attr('subId', item.id).click(runSubReport))).appendTo(rowItem);
	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
            if(allowEdit) {
    	        $('<td style="width:20px;"></td>').addClass(cellClass).append($('<div class="goButton" style="padding-bottom:0px;"></div>').append($('<a class="reportLibraryLink" style="cursor:pointer;">Edit</a>').attr('subId', item.id).click(editSubReport))).appendTo(rowItem);
            } else {
    	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
            }
	        $('<td style="width:20px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
        }
        
		$(function() {
			
	        $.each(sessionList, function(index, item) {
                $(buildSessionRow(item)).appendTo('#sessionList');
            });
            var sessId = '<%= Session["sessionSelectedFolder"] %>';
            if(sessId && sessId.length > 0)
                $('div[sessId=' + sessId + ']', '#sessionList').click();
                
	        $.each(myList, function(index, item) {
                $(buildFolderRow(item, false)).appendTo('#myList');
            });
            var fldId = '<%= Session["mySelectedFolder"] %>';
            if(fldId && fldId.length > 0)
                $('div[fldId=' + fldId + ']', '#myList').click();

	        $.each(sharedList, function(index, item) {
                $(buildFolderRow(item, true)).appendTo('#sharedList');
            });
            var shrId = '<%= Session["sharedSelectedFolder"] %>';
            if(shrId && shrId.length > 0)
                $('div[shrId=' + shrId + ']', '#sharedList').click();

            var subArr = subReportExpandList.split(',');
            if(subArr && subArr.length > 0) {
	            $.each(subArr, function(index, item) {
                    $('.expandSubButton[rptId=' + item + ']').click();
                });
            }

            $('#deleteMyFolder').click(deleteMyFolder);
            $('#deleteSharedFolder').click(deleteSharedFolder);
		});
	</script>
	
	<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="720" reloadonshow="true" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows><telerik:radwindow runat="server" ID="SaveReportWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Title="" Height="600" Width="800" ></telerik:radwindow></windows>
		<windows><telerik:radwindow runat="server" ID="CreateFolder" VisibleOnPageLoad="false" openerelementid="<%# newFolder.ClientID %>" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="createfolderpopup.aspx?i=false&lib=true" Height="250" Width="300" ></telerik:radwindow></windows>
		<windows><telerik:radwindow runat="server" ID="CreateSharedFolder" VisibleOnPageLoad="false" openerelementid="<%# newSharedFolder.ClientID %>" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="createfolderpopup.aspx?i=true&lib=true" Height="250" Width="300" ></telerik:radwindow></windows>
		<windows><telerik:radwindow runat="server" ID="EditWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Title="" Height="680" Width="800" ></telerik:radwindow></windows>
		<windows><telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="promptsessions.aspx" Title="" Height="400" Width="600" ></telerik:radwindow></windows>
	</telerik:radwindowmanager>
                    
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Report Library</td>
						<td class="widgetTop" style="width:30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;"><table cellpadding="0" cellspacing="0" border="0"><tr><td><div id="PastSessionToggleButton" runat="server" class="addButtonTop"><asp:linkbutton runat="server" id="pastSeriesLink" onclick="IncludePastSessions_Toggle">Include Closed Sessions</asp:linkbutton></div></td></tr></table></td>
					</tr>
				</table>				
				<div class="widget" style="padding-top:10px;">
					
				<table border="0" cellpadding="0" cellspacing="0" style="padding-left:10px;">
					<tr>
						<td style="width:115px;"><div class="goButton" style="padding-bottom:15px;"><asp:linkbutton runat="server" id="NewReportButton" onclientclick="window.radopen('NewReportWizard_Setup.aspx', null);return false;">New Report</asp:linkbutton></div></td>
						<%--<td style="width:150px;"><div class="goButton" style="padding-bottom:15px;"><asp:linkbutton runat="server" id="scheduledReportsBtn" postbackurl="ScheduledReports.aspx">Schedule Reports</asp:linkbutton></div></td>--%>
					</tr>
				</table>

			    <asp:objectdatasource id="SharedLibrarySource" runat="server" typename="LibrarySource" selectmethod="GetSharedLibrary">
				    <selectparameters>
						<asp:controlparameter controlid="hidIncludeClosed" propertyname="Value" name="includePastSessions" type="boolean" />
				    </selectparameters>
			    </asp:objectdatasource>


			    <div id="sessionList">
				    <div class="rowHeading" style="padding: 8px 0px 8px 14px;"><asp:label id="sessionsLabel" runat="server">Active Sessions:</asp:label></div>	
			    </div>
				    
				<div id="myList" class="widget" style="padding-top:15px;">
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="rowHeading" style="width:30%;">My Report Library:</td>
							<td class="rowHeading" style="width:40%;">&nbsp;</td>
							<td class="rowHeading" style="width:30%; text-align:right;">
								<table cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td><div id="Div5" runat="server" class="addButton"><asp:linkbutton runat="server" id="newFolder">New Folder</asp:linkbutton></div></td>
										<td>
											<div id="MyReportDeleteFolder" class="cancelButton" style="display:none;"><a id="deleteMyFolder">Delete</a></div>
											<div id="MyReportDeleteFolder2" style="font-size:10px;color:#999999; padding-left:14px;padding-right:10px;">Delete</div>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</div>
				
				<div id="sharedList" class="widget" style="padding-top:15px;">
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="rowHeading" style="width:30%;">Shared Report Library:</td>
							<td class="rowHeading" style="width:40%;">&nbsp;</td>
							<td class="rowHeading" style="width:30%; text-align:right;">
								<table cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td><div id="Div7" runat="server" class="addButton"><asp:linkbutton runat="server" id="newSharedFolder">New Folder</asp:linkbutton></div></td>
										<td>
											<div id="SharedReportDeleteFolder" class="cancelButton" style="display:none;"><a id="deleteSharedFolder">Delete</a></div>
											<div id="SharedReportDeleteFolder2" style="font-size:10px; color:#999999; padding-left:14px;padding-right:10px;">Delete</div>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
				</div>
				</div>
			</td>
		</tr>
	</table>	
	
	<asp:hiddenfield id="hidIncludeClosed" runat="server" value="false" />
	
	<input type="hidden" id="reportId" name="reportId" />
	<input type="hidden" id="subReportId" name="subReportId" />
	<input type="hidden" id="action" name="action" />
</asp:Content>
