<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DundasOlapDataProvider</name>
    </assembly>
    <members>
        <member name="T:Dundas.Olap.Data.DimensionMemberDescriptor">
            <summary>
            Represents a single member in the <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.#ctor(System.String)">
            <summary>
            DimensionMemberDescriptor object constructor.
            </summary>
            <param name="memberName">
            Member name.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.#ctor(System.String,System.String)">
            <summary>
            DimensionMemberDescriptor object constructor.
            </summary>
            <param name="memberName">
            Member name.
            </param>
            <param name="memberUniqueName">
            Member unique name.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.Clone">
            <summary>
            Creates an exact copy of an object.
            </summary>
            <returns>
            a <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptor"/> object wich is an exact copy of current object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.ToString">
            <summary>
            string representation
            </summary>
            <returns>string</returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.op_Equality(Dundas.Olap.Data.DimensionMemberDescriptor,Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Checks if two DimensionMemberDescriptor objects are equal.
            </summary>
            <param name="left">
            Object to the left of the operator.
            </param>
            <param name="right">
            Object to the right of the operator.
            </param>
            <returns>
            <b>True</b> if two DimensionMemberDescriptor objects are equal; otherwise <b>false</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.op_Inequality(Dundas.Olap.Data.DimensionMemberDescriptor,Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Checks if two DimensionMemberDescriptor objects are not equal.
            </summary>
            <param name="left">
            Object to the left of the operator.
            </param>
            <param name="right">
            Object to the right of the operator.
            </param>
            <returns>
            <b>True</b> if two DimensionMemberDescriptor objects are not equal; otherwise <b>false</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.GetHashCode">
            <summary>
            Returns a hash code of the object.
            </summary>
            <returns>
            An <b>integer</b> that contains the hash code of the object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptor.Equals(System.Object)">
            <summary>
            Checks if the specified object equals this object.
            </summary>
            <param name="obj">
            Object to the test with.
            </param>
            <returns>
            <b>True</b> if specified object is equal to this object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMemberDescriptor.IsParentMember">
            <summary>
            Indicates that the member is a parent member
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMemberDescriptor.MemberName">
            <summary>
            Gets or sets the member name.
            </summary>
            <value>
            A <b>string</b> that contains the member name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMemberDescriptor.MemberUniqueName">
            <summary>
            Gets or sets the member unique name.
            </summary>
            <value>
            A <b>string</b> that contains the member unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMemberDescriptor.Properties">
            <summary>
            Gets dimension member descriptor dynamic property collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic member descriptor properties.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionMembersRange">
            <summary>
            Range class for slicer axis
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMembersRange.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMembersRange.#ctor(Dundas.Olap.Data.DimensionMemberDescriptor,Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Creates an instance of member range
            </summary>
            <param name="left">left member</param>
            <param name="right">right member</param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMembersRange.IsValid">
            <summary>
            Checks if the range contains valid members
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMembersRange.Clone">
            <summary>
            Creates an exact copy of an object.
            </summary>
            <returns>
            A <see cref="T:Dundas.Olap.Data.DimensionMembersRange"/> object wich is an exact copy of current object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMembersRange.Left">
            <summary>
            Gets or sets range left member
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMembersRange.Right">
            <summary>
            Gets or sets range right member
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMembersRange.ParentDimensionDescriptor">
            <summary>
            Parent dimension
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionMemberDescriptorCollection">
            <summary>
            Represents a collection of the <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptor"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Remove(Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Removes the specified <b>DimensionMemberDescriptor</b> from the collection.
            </summary>
            <param name="memberDescriptor">
            <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptor"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Add(Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Adds a <b>DimensionMemberDescriptor</b> to the end of the collection.
            </summary>
            <param name="memberDescriptor">
            <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptor"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Add(System.String)">
            <summary>
            Adds a <b>DimensionMemberDescriptor</b> that references 
            the <see cref="T:Dundas.Olap.Data.Member"/> by its specified name. 
            </summary>
            <param name="memberName">
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> name.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Add(System.String,System.String)">
            <summary>
            Adds a <b>DimensionMemberDescriptor</b> that references the 
            <see cref="T:Dundas.Olap.Data.Member"/> by its specified name and its unique name.
            </summary>
            <param name="memberName">
            Member name.
            </param>
            <param name="memberUniqueName">
            Member unique name.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Insert(System.Int32,Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Inserts a <b>DimensionMemberDescriptor</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="memberDescriptor">
            <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptor"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Contains(Dundas.Olap.Data.DimensionMemberDescriptor)">
            <summary>
            Determines whether a <b>DimensionMemberDescriptor</b> is in the <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptorCollection"/>.
            </summary>
            <param name="value">The <b>DimensionMemberDescriptor</b> to locate in the <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptorCollection"/>. </param>
            <returns>true if item is found in the <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptorCollection"/>; otherwise, false.</returns>
            <remarks>The result is determined not by object references but by values. <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptor"/> implements a equal (==) operator</remarks>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.AddRange(Dundas.Olap.Data.DimensionMemberDescriptorCollection)">
            <summary>
            Appends a collection
            </summary>
            <param name="dimensionMemberDescriptorCollection"></param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Sort(Dundas.Olap.Data.MemberSortOrder,Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Sorts member descriptors of the collection using a specified order.
            </summary>
            <param name="sortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration member that specifies the sorting order.
            </param>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used for member sorting.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Dispose">
            <summary>
            Dispose the object
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMemberDescriptorCollection.Item(System.Int32)">
            <summary>
            Gets the <b>DimensionMemberDescriptor</b> by index.
            </summary>
            <value>
            Returns a <b>DimensionMemberDescriptor</b> from the collection specified by index.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionMemberRangeCollection">
            <summary>
            Represents a <b>Member</b> object collection.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.#ctor(System.Object)">
            <summary>
            MemberCollection object constructor.
            </summary>
            <param name="parent">Owner of the collection.</param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.Remove(Dundas.Olap.Data.DimensionMembersRange)">
            <summary>
            Removes the given <b>Member</b> from the collection.
            </summary>
            <param name="member">
            A <b>Member</b> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.Add(Dundas.Olap.Data.DimensionMembersRange)">
            <summary>
            Adds a <b>Member</b> to the end of the collection.
            </summary>
            <param name="member">
            A <b>Member</b> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.Insert(System.Int32,Dundas.Olap.Data.DimensionMembersRange)">
            <summary>
            Inserts a <b>Member</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="member">
            A <b>Member</b> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the Object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionMemberRangeCollection.OnNewItem(System.Object)">
            <summary>
            New Object in the collection.
            </summary>
            <param name="newItem">
            New collection Object.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionMemberRangeCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Member</b> object by index.
            </summary>
            <value>
            Returns a <b>Member</b> from the collection specified by index.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.MemberSortOrder">
            <summary>
            Dimension members sorting order enumeration.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.MemberSortOrder.None">
            <summary>
            No sorting.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.MemberSortOrder.Ascending">
            <summary>
            Ascending sorting order.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.MemberSortOrder.Descending">
            <summary>
            Descending sorting order.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionType">
            <summary>
            Dimension type enumeration.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Accounts">
            <summary>
            Describes a dimension that contains an account structure.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.BillOfMaterials">
            <summary>
            Describes a dimension that represents a material/component breakdown.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Channel">
            <summary>
            Describes a dimension that contains information about a distribution channel.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Currency">
            <summary>
            Describes a dimension that contains currency information.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Customers">
            <summary>
            Describes a dimension that contains customer information.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Geography">
            <summary>
            Describes a dimension that contains a geographical hierarchy.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Measure">
            <summary>
            Describes a dimension that contains measures.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Organization">
            <summary>
            Describes a dimension that represents the reporting structure of an organization.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Other">
            <summary>
            Describes a dimension of the default dimension type, which is used for dimensions that are not time dimensions or measure dimensions.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Products">
            <summary>
            Describes a dimension that contains product information. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Promotion">
            <summary>
            Describes a dimension that contains information about marketing and advertising promotions.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Quantitative">
            <summary>
            Describes a dimension that contains quantitative elements.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Rates">
            <summary>
            Describes a dimension that contains different types of rates.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Scenario">
            <summary>
            Describes a dimension that contains different business scenarios.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Time">
            <summary>
            Indicates that a dimension refers to time.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Unknown">
            <summary>
            Describes a dimension with an unknown type.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionType.Utility">
            <summary>
            Describes a dimension that contains only calculated members.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Dimension">
            <summary>
            Represents a single dimension inside the <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object. 
            </summary>
            <remarks>
            Each <b>Dimension</b> contains a collection of <see cref="P:Dundas.Olap.Data.Dimension.Hierarchies"/>.
            Each <b>Hierarchy</b> contains a collection of <see cref="P:Dundas.Olap.Data.Hierarchy.Levels"/>.
            Each <see cref="T:Dundas.Olap.Data.Level"/> contains a collection of <see cref="P:Dundas.Olap.Data.Level.Members"/>.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Dimension.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Dimension.GetDefaultHierarchy">
            <summary>
            Gets <b>Dimension</b> default hierarchy.
            </summary>
            <returns>
            Dimension default <b>Hierarchy</b> or <b>null</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Dimension.GetDefaultLevel">
            <summary>
            Gets <b>Dimension</b> default <see cref="T:Dundas.Olap.Data.Level"/> from the default hierarchy.
            </summary>
            <returns>
            Default <see cref="T:Dundas.Olap.Data.Level"/> from the default <b>Hierarchy</b> or <b>null</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Dimension.ToString">
            <summary>
            Returns a string that represents the <b>Dimension</b> object.
            </summary>
            <returns>
            A string that represents the <b>Dimension</b> object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>Dimension</b> is visible in 
            the end user interface.
            </summary>
            <value>
            <b>True</b> if dimension is visible to the end user.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.DimensionType">
            <summary>
            Gets or sets <b>Dimension</b> type.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.Dimension.DimensionType"/>
            <value>
            A <see cref="P:Dundas.Olap.Data.Dimension.DimensionType"/> enumeration value that identifies dimension type.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.DefaultHierarchyName">
            <summary>
            Gets or sets <b>Dimension</b> default hierarchy name.
            </summary>
            <value>
            A <b>string</b> value that represents dimension default hierarchy name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.Name">
            <summary>
            Gets or sets <b>Dimension</b> name.
            </summary>
            <value>
            A <b>string</b> value that represents dimension name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.UniqueName">
            <summary>
            Gets or sets the <b>Dimension</b> unique name.
            </summary>
            <value>
            A <b>string</b> value that represents dimension unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.Caption">
            <summary>
            Gets or sets the <b>Dimension</b> caption.
            </summary>
            <value>
            A <b>string</b> value that represents the dimension caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.Description">
            <summary>
            Gets or sets <b>Dimension</b> description.
            </summary>
            <value>
            A <b>string</b> value that represents dimension description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.ParentCubeDataSchema">
            <summary>
            Gets or sets the cube data schema which this <b>Dimension</b> belongs to.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.CubeDataSchema"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object that represents the data schema this
            dimension belongs to.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.Hierarchies">
            <summary>
            Gets a collection of <b>Dimension</b> hierarchies. 
            </summary>
            <seealso cref="T:Dundas.Olap.Data.HierarchyCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Hierarchy"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.HierarchyCollection"/> object that represents a collection of
            hierarchies in the dimension.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Dimension.Properties">
            <summary>
            Gets <b>Dimension</b> collection of dynamic properties.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.PropertyCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Property"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic dimension properties.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionCollection">
            <summary>
            Represents collection of the <see cref="T:Dundas.Olap.Data.Dimension"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.#ctor(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Object constructor.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.CubeDataSchema"/>
            <param name="parentCubeDataSchema">
            Owner of the collection.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.Remove(Dundas.Olap.Data.Dimension)">
            <summary>
            Removes the given <b>Dimension</b> from the collection.
            </summary>
            <param name="dimension">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.Add(Dundas.Olap.Data.Dimension)">
            <summary>
            Adds a <b>Dimension</b> to the end of the collection.
            </summary>
            <param name="dimension">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.Insert(System.Int32,Dundas.Olap.Data.Dimension)">
            <summary>
            Inserts a <b>Dimension</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="dimension">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Newly inserted object.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the object which was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.OnNewItem(System.Object,System.Int32)">
            <summary>
            New object in the collection.
            </summary>
            <param name="newItem">
            New collection object.
            </param>
            <param name="index">
            New collection object index.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.FindByName(System.String)">
            <summary>
            Finds <b>Dimension</b> by its name.
            </summary>
            <param name="name">
            Dimension name.
            </param>
            <returns><see cref="T:Dundas.Olap.Data.Dimension"/> object or <b>null</b> if not found.</returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionCollection.FindByUniqueName(System.String)">
            <summary>
            Finds <b>Dimension</b> by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension unique name.
            </param>
            <returns><see cref="T:Dundas.Olap.Data.Dimension"/> object or <b>null</b> if not found.</returns>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Dimension</b> by index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Member">
            <summary>
            Represents a single member inside the dimension hierarchy.
            </summary>
            <remarks>
            Each <see cref="T:Dundas.Olap.Data.Level"/> has an associated collection of <see cref="P:Dundas.Olap.Data.Level.Members"/>.
            <p>Each Member may have a collection of <see cref="P:Dundas.Olap.Data.Member.ChildMembers"/>.</p>
            <p>Each Member may have a <see cref="P:Dundas.Olap.Data.Member.ParentMember"/>.</p>
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Member.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Member.GetFilteredAndSortedChildMemberList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.AxisDescriptor)">
            <summary>
            Gets sorted and filtered collection of child <b>Member</b>.
            </summary>
            <remarks>
            This method returns a list of child members. Only <see cref="P:Dundas.Olap.Data.Member.Visible"/> 
            members are returned. <b>Members</b> are sorted alphabetically based on the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            property of the associated <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </remarks>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object is used to define the sorting and filtering rules.
            </param>
            <param name="axisDescriptor">
            A <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object is used to define the axis descriptor used for the sorting and filtering rules.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of child members.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Member.GetFilteredAndSortedChildMemberList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.MemberSortOrder,System.Int64,System.Int64)">
            <summary>
            Gets a filtered and sorted list of child <b>Members</b>.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object is used to define the sorting and filtering rules.
            </param>
            <param name="memberSortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration value that defines the sorting order of the members.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of child members.
            </returns>
            <remarks>
            This method returns a list of child members. Only <see cref="P:Dundas.Olap.Data.Member.Visible"/> 
            members are returned. Members are sorted alphabetically based on the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            property of the associated <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Member.GetFilteredAndSortedChildMemberList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.MemberSortOrder)">
            <summary>
            Gets a filtered and sorted list of child <b>Members</b>.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object is used to define the sorting and filtering rules.
            </param>
            <param name="memberSortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration value that defines the sorting order of the members.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of child members.
            </returns>
            <remarks>
            This method returns a list of child members. Only <see cref="P:Dundas.Olap.Data.Member.Visible"/> 
            members are returned. Members are sorted alphabetically based on the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            property of the associated <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Member.GetDimension">
            <summary>
            Gets the dimension that this <b>Member</b> object belongs to.
            </summary>
            <returns>
            A <see cref="T:Dundas.Olap.Data.Dimension"/> object or <b>null</b> if cannot be found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Member.GetDimension(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Gets the dimension that this <b>Member</b> object belongs to.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to find member dimension.
            </param>		
            /// <returns>
            A <see cref="T:Dundas.Olap.Data.Dimension"/> object or <b>null</b> if cannot be found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Member.ToString">
            <summary>
            Returns a string that represents the current <b>Member</b>.
            </summary>
            <returns>
            A <b>string</b> that represents current object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Member.GetFullCaption(Dundas.Olap.Data.CubeDataSchema,System.Boolean)">
            <summary>
            Gets the caption of the <b>Member</b> using all parent captions as a prefix.
            </summary>
            <remarks>
            This method returns a full member caption which will include the captions of 
            all parent members. For example, member "[Q1]" will have a full name 
            of "[1997].[Q1]".
            </remarks>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to find a member's parent members.
            </param>
            <param name="forceGetParent">
            Indicates if the parent information MUST be loaded, even if round-trips to 
            the data source will be required.
            </param>
            <returns>
            A <b>string</b> that contains member full caption.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Member.ResetChildInfo">
            <summary>
            Resets children member collection
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.Member.DrilledDown">
            <summary>
            Gets or sets a flag that indicates whether no children immediately follow the <b>Member</b> on the axis.
            </summary>
            <remarks>
            This property can only be used when a <b>Member</b> is returned in the <see cref="T:Dundas.Olap.Data.CellSet"/> 
            after the call to the <b>ExecuteCellSet</b> method.
            </remarks>
            <value>
            Returns <b>true</b> if there are no child members of the current <b>Member</b> on the axis; 
            Returns <b>false</b> if there are one or more child members of the current <b>Member</b> on the axis.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>Member</b> is visible in 
            the end user interface.
            </summary>
            <value>
            Returns <b>true</b> if the <b>Member</b> is visible to the end user.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.LevelDepth">
            <summary>
            Gets or sets the <b>Member</b> level ordinal position.
            </summary>
            <value>
            A <b>long</b> value that is the ordinal position of the member's level in the hierarchy.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.LevelUniqueName">
            <summary>
            Gets or sets the <b>Member</b> level's unique name.
            </summary>
            <value>
            A <b>String</b> that contains the level's unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.Name">
            <summary>
            Gets or sets the <b>Member</b> name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>Member</b> name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.UniqueName">
            <summary>
            Gets or sets the <b>Member</b> unique name.
            </summary>
            <value>
            A <b>String</b> that contains the level unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.Caption">
            <summary>
            Gets or sets the <b>Member</b> caption.
            </summary>
            <value>
            A <b>String</b> that contains the level caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.Description">
            <summary>
            Gets or sets the <b>Member</b> description.
            </summary>
            <value>
            A <b>String</b> that contains the level description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.ParentLevel">
            <summary>
            Gets or sets the dimension level which this <b>Member</b> belongs to.
            </summary>
            <remarks>
            Accessing this property may require a round-trip to the data source. 
            </remarks>
            <value>
            A <see cref="T:Dundas.Olap.Data.Level"/> object which this member belongs to.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.ParentMember">
            <summary>
            Gets or sets the parent <b>Member</b> that contains this <b>Member</b> object.
            </summary>
            <remarks>
            Accessing this property may require a round-trip to the data source. 
            You can check if the round-trip is required using the <see cref="P:Dundas.Olap.Data.Member.ParentOnDemandLoaded"/> property.
            </remarks>
            <value>
            A parent <b>Member</b> object or <b>null</b> for the top-level members.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.HasChildMembers">
            <summary>
            Gets or sets a flag that indicates whether or not this <b>Member</b> has children.
            </summary>
            <remarks>
            This property can only be used when a <b>Member</b> is returned in the <see cref="T:Dundas.Olap.Data.CellSet"/> 
            after the call to the <b>ExecuteCellSet</b> method.
            </remarks>
            <value>
            Returns <b>true</b> if member has child members; <b>false</b> otherwise.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.MembersOnDemandLoaded">
            <summary>
            Gets or sets a flag that indicates that the child <b>Member</b> collection is pre-populated with members.
            </summary>
            <remarks>
            If this property is set to <b>false</b> and the <see cref="P:Dundas.Olap.Data.Member.ChildMembers"/>
            collection of the <b>Member</b> is accessed, a round trip to the data provider will be required. If the property
            is set to <b>true</b>, the member list is already loaded and saved in the <b>Member</b>. 
            </remarks>
            <value>
            Returns <b>true</b> if the child member collection is already loaded.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.ParentOnDemandLoaded">
            <summary>
            Gets a flag that indicates that the parent member is loaded from the data source.
            </summary>
            <remarks>
            If this property is set to <b>false</b> and the <see cref="P:Dundas.Olap.Data.Member.ParentMember"/>
            property of the <b>Member</b> is accessed, a round trip to the data provider will be required. If the property
            is set to <b>true</b>, the parent member object is already loaded and saved. 
            </remarks>
            <value>
            Returns <b>true</b> if the parent member is already loaded.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.ChildMembers">
            <summary>
            Gets a collection of the child <b>Members</b>.
            </summary>
            <remarks>
            Accessing this property may require a round-trip to the data source. This can be verified by using 
            the <see cref="P:Dundas.Olap.Data.Member.MembersOnDemandLoaded"/> property.
            </remarks>
            <value>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that contains a child <b>Member</b>. <b>Null</b> may be
            returned if no child members exist.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.Properties">
            <summary>
            Gets <b>Member</b> dynamic property collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic <b>Member</b> properties.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Member.IsTopLevelMember">
            <summary>
            Gets or sets a flag indicating this member is a top level member.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.MemberCollection">
            <summary>
            Represents a <b>Member</b> object collection.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.#ctor(System.Object)">
            <summary>
            MemberCollection object constructor.
            </summary>
            <param name="parent">Owner of the collection.</param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.Remove(Dundas.Olap.Data.Member)">
            <summary>
            Removes the given <b>Member</b> from the collection.
            </summary>
            <param name="member">
            A <b>Member</b> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.Add(Dundas.Olap.Data.Member)">
            <summary>
            Adds a <b>Member</b> to the end of the collection.
            </summary>
            <param name="member">
            A <b>Member</b> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.Insert(System.Int32,Dundas.Olap.Data.Member)">
            <summary>
            Inserts a <b>Member</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="member">
            A <b>Member</b> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.AddRange(Dundas.Olap.Data.MemberCollection)">
            <summary>
            Add collection of members to current
            </summary>
            <param name="memberCollection">members to be added</param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the Object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.OnNewItem(System.Object)">
            <summary>
            New Object in the collection.
            </summary>
            <param name="newItem">
            New collection Object.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.FindByName(System.String)">
            <summary>
            Finds a dimension <b>Member</b> by its name.
            </summary>
            <param name="name">
            Dimension member name.
            </param>
            <returns>
            Dimension <b>Member</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.FindByHashCode(System.Int32)">
            <summary>
            Finds a dimension <b>Member</b> by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension member unique name.
            </param>
            <returns>
            Dimension <b>Member</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.FindByUniqueName(System.String)">
            <summary>
            Finds a dimension <b>Member</b> by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension member unique name.
            </param>
            <returns>
            Dimension <b>Member</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.Sort(Dundas.Olap.Data.MemberSortOrder,Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Sorts members of the collection using a specified order.
            </summary>
            <param name="sortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration member that specifies the sorting order.
            </param>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used for member sorting.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberCollection.SortWithParents(Dundas.Olap.Data.MemberSortOrder,Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Sorts members of the collection using a specified order.
            </summary>
            <param name="sortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration member that specifies the sorting order.
            </param>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used for member sorting.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.MemberCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Member</b> object by index.
            </summary>
            <value>
            Returns a <b>Member</b> from the collection specified by index.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.MemberComparer">
            <summary>
            A comparer object for the <see cref="T:Dundas.Olap.Data.Member"/> and <see cref="T:Dundas.Olap.Data.Measure"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MemberComparer.#ctor">
            <summary>
            Default constructor is unavailable.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MemberComparer.#ctor(Dundas.Olap.Data.MemberSortOrder,Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            MemberComparer object constructor.
            </summary>
            <param name="sortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration member that specifies sorting order.
            </param>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used for member sorting.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberComparer.Compare(System.Object,System.Object)">
            <summary>
            Compares two <see cref="T:Dundas.Olap.Data.Member"/>  or <see cref="T:Dundas.Olap.Data.Measure"/>objects and 
            returns a value indicating whether one is less than, equal to or greater 
            than the other.
            </summary>
            <param name="x">
            The first Member or Measure object to compare.
            </param>
            <param name="y">
            The second Member or Measure object to compare.
            </param>
            <returns>
            Zero if objects are equal; value more than zero if x greater than y; value less than zero if x less than y;
            </returns>
        </member>
        <member name="T:Dundas.Olap.Data.MemberParentComparer">
            <summary>
            Comparer for parents
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MemberParentComparer.#ctor(Dundas.Olap.Data.MemberSortOrder,Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            MemberComparer object constructor.
            </summary>
            <param name="sortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration member that specifies sorting order.
            </param>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used for member sorting.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberParentComparer.Compare(System.Object,System.Object)">
            <summary>
            Compares members with parents.
            </summary>
            <param name="x">object1</param>
            <param name="y">object2</param>
            <returns>comparation result.</returns>
        </member>
        <member name="T:Dundas.Olap.Data.NoNameExpandableObjectConverter">
            <summary>
            Data point attributes converter
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.NoNameExpandableObjectConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Overrides the ConvertTo method of TypeConverter.
            </summary>
            <param name="context">Descriptor context.</param>
            <param name="culture">Culture information.</param>
            <param name="value">Value to convert.</param>
            <param name="destinationType">Convertion destination type.</param>
            <returns>Converted object.</returns>
        </member>
        <member name="T:Dundas.Olap.Data.CellSetPaging">
            <summary>
            CellSet paging management
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CellSetPaging.GetPageCount(System.Int32)">
            <summary>
            Gets page count for given axis
            </summary>
            <param name="axisNo">axis number</param>
            <returns>number of pages or 0 if not enabled</returns>
        </member>
        <member name="P:Dundas.Olap.Data.CellSetPaging.PageSize">
            <summary>
            
            </summary>
        </member>
        <member name="E:Dundas.Olap.Data.CellSetPaging.PageNumberUpdated">
            <summary>
            Raises when page number is changed.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.CellSetSize">
            <summary>
            Cell set size.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.CellSetSize.CategoricalCellSetCount">
            <summary>
            Number of tuples on categorical axis.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.CellSetSize.SeriesCellSetCount">
            <summary>
            Number of tuples on series axis.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Cell">
            <summary>
            Represents a single cell in the <see cref="T:Dundas.Olap.Data.CellSet"/>.
            <seealso cref="T:Dundas.Olap.Data.CellSet"/>
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Cell.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Cell.#ctor(System.Object,System.String)">
            <summary>
            <b>Cell</b> object constructor.
            </summary>
            <param name="value">
            Cell value.
            </param>
            <param name="formattedValue">
            Cell formated value.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.Cell.Value">
            <summary>
            Gets a <b>Cell</b> value.
            </summary>
            <value>
            An <b>object</b> that represents the cell value.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Cell.FormattedValue">
            <summary>
            Gets a formatted <b>Cell</b> value.
            </summary>
            <value>
            A <b>string</b> that represents a formatted cell value.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Cell.Properties">
            <summary>
            Gets <b>Cell</b> dynamic properties.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.PropertyCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Property"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a cell's collection
            of dynamic properties.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.MeasureItemBase">
            <summary>
            Base class for measure-related items
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureItemBase.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>Measure</b> is visible in 
            the end user interface.
            </summary>
            <value>
            <b>True</b> if <b>Measure</b> is visible to the end user.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureItemBase.Name">
            <summary>
            Gets or sets the <b>Measure</b> name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>Measure</b> name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureItemBase.Caption">
            <summary>
            Gets or sets a <b>Measure</b> caption.
            </summary>
            <value>
            A <b>String</b> that contains a <b>Measure</b> caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureItemBase.Description">
            <summary>
            Gets or sets the <b>Measure</b> description.
            </summary>
            <value>
            A <b>String</b> that contains the <b>Measure</b> description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureItemBase.UniqueName">
            <summary>
            Gets or sets the <b>Measure</b> unique name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>Measure</b> unique name.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.MeasureBase">
            <summary>
            Base class for measures and KPIs
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureBase.ToString">
            <summary>
            Returns a string that represents this object.
            </summary>
            <returns>
            A <b>string</b> that represents this object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureBase.GetParentName">
            <summary>
            Returns folder name for the measure.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureBase.Dispose">
            <summary>
            Dispose the object
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureBase.Dispose(System.Boolean)">
            <summary>
            Disposing the object
            </summary>
            <param name="disposing">flag indicates that the object is disposing</param>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureBase.ParentCubeName">
            <summary>
            Gets or sets the parent cube name.
            </summary>
            <value>
            A <b>String</b> that contains the parent cube name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureBase.ParentCubeDataSchema">
            <summary>
            Gets or sets the parent cube data schema object.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> that contains parent cube data schema.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureBase.GroupName">
            <summary>
            Gets or sets the <b>Measure</b> group name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>Measure</b> group name.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.Measure">
            <summary>
            Represents a measure inside the <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object. 
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Measure.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.Measure.Units">
            <summary>
            Gets or sets the <b>Measure</b> unit's name.
            </summary>
            <value>
            A <b>String</b> that contains <b>Measure</b> unit's name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Measure.NumericPrecision">
            <summary>
            Gets or sets the <b>Measure</b> numeric precision.
            </summary>
            <value>
            An <b>integer</b> that contains the <b>Measure</b> numeric precision.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Measure.NumericScale">
            <summary>
            Gets or sets the <b>Measure</b> numeric scale.
            </summary>
            <value>
            An <b>integer</b> that contains the <b>Measure</b> numeric scale.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Measure.Properties">
            <summary>
            Gets a <b>Measure</b> dynamic property collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic <b>Measure</b> properties.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.MeasureCollection">
            <summary>
            Represents a collection of <see cref="T:Dundas.Olap.Data.Measure"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.#ctor(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            MeasureCollection object constructor.
            </summary>
            <param name="parentCubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> that owns the collection.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.Remove(Dundas.Olap.Data.Measure)">
            <summary>
            Removes the given <b>Measure</b> from the collection.
            </summary>
            <param name="measure">
            <see cref="T:Dundas.Olap.Data.Measure"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.Add(Dundas.Olap.Data.Measure)">
            <summary>
            Adds a <b>Measure</b> to the end of the collection.
            </summary>
            <param name="measure">
            <see cref="T:Dundas.Olap.Data.Measure"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.Insert(System.Int32,Dundas.Olap.Data.Measure)">
            <summary>
            Inserts a <b>Measure</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="measure">
            <see cref="T:Dundas.Olap.Data.Measure"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the Object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.OnNewItem(System.Object,System.Int32)">
            <summary>
            New object in the collection.
            </summary>
            <param name="newItem">
            New collection Object.
            </param>
            <param name="index">
            New collection Object index.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.GetFilteredAndSortedMeasureList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.MemberSortOrder)">
            <summary>
            Gets a filtered and sorted list of <b>Measures</b>.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to define sorting and filtering rules.
            </param>
            <param name="memberSortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> object that defines the sort order (ascending or descending).
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MeasureCollection"/> object that represents a collection of measures.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.Sort(Dundas.Olap.Data.MemberSortOrder,Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Sorts <b>Measures</b> of the collection using a specified order.
            </summary>
            <param name="sortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration value that defines the sorting order.
            </param>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object that contains cube data schema.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.FindByName(System.String)">
            <summary>
            Finds a <b>Measure</b> by its name.
            </summary>
            <param name="name">
            The measure name.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.Measure"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MeasureCollection.FindByUniqueName(System.String)">
            <summary>
            Finds a <b>Measure</b> by its unique name.
            </summary>
            <param name="uniqueName">
            Measure unique name.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.Measure"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.MeasureCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Measure</b> by index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Kpi">
            <summary>
            Represents a KPI inside the <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object. 
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Kpi.GetParentName">
            <summary>
            Gets parent name or measure group name.
            </summary>
            <returns>parent name.</returns>
        </member>
        <member name="M:Dundas.Olap.Data.Kpi.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.Kpi.Parent">
            <summary>
            Parent KPI.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.Kpi.StatusGraphic">
            <summary>
            Status graphics.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.Kpi.TrendGraphic">
            <summary>
            Trend graphics.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.KpiCollection">
            <summary>
            Represents a collection of <see cref="T:Dundas.Olap.Data.Kpi"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.KpiCollection.FindByName(System.String)">
            <summary>
            Finds 
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.KpiCollection.FindByUniqueName(System.String)">
            <summary>
            Find kpi by unique name
            Name = [Kpis].[Name]
            </summary>
            <param name="uniqueName"></param>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.KpiCollection.GetKpiName(System.String)">
            <summary>
            Returns KPI name
            </summary>
            <param name="uniqueName"></param>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.KpiCollection.FindExpressionByName(System.String)">
            <summary>
            Returns KPI by name
            </summary>
            <param name="methodName"></param>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.KpiCollection.FindExpressionByHash(System.Int32)">
            <summary>
            Returns KPI by hash
            </summary>
            <param name="hashCode">Hash code.</param>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.KpiCollection.FindExpressionByMdx(System.String)">
            <summary>
            Returns KPI expression by mdx expression
            </summary>
            <param name="methodName"></param>
            <returns></returns>
        </member>
        <member name="T:Dundas.Olap.Data.KpiExpression">
            <summary>
            Represents a KPI expression inside the <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.KpiExpression.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.KpiExpression.#ctor(Dundas.Olap.Data.Kpi)">
            <summary>
            Constructor.
            </summary>
            <param name="parentKpi"></param>
        </member>
        <member name="M:Dundas.Olap.Data.KpiExpression.GetParentName">
            <summary>
            Parent KPI name
            </summary>
            <returns></returns>
        </member>
        <member name="M:Dundas.Olap.Data.KpiExpression.ToString">
            <summary>
            String representation
            </summary>
            <returns></returns>
        </member>
        <member name="P:Dundas.Olap.Data.KpiExpression.UniqueName">
            <summary>
            Unique name
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.KpiExpression.Expression">
            <summary>
            Expression
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.KpiExpression.Parent">
            <summary>
            Parent KPI
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Hierarchy">
            <summary>
            Represents a single hierarchy inside the <see cref="T:Dundas.Olap.Data.Dimension"/>.
            </summary>
            <remarks>
            <p>Each dimension may have one or more hierarchies.</p>
            <p>Each <b>Hierarchy</b> contains a collection of <see cref="P:Dundas.Olap.Data.Hierarchy.Levels"/>.</p>
            <p>Each <see cref="T:Dundas.Olap.Data.Level"/> contains a collection of child <see cref="P:Dundas.Olap.Data.Level.Members"/>.</p>
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Hierarchy.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Hierarchy.#ctor(System.Boolean)">
            <summary>
            Object constructor.
            </summary>
            <param name="isAttributeHierarchy"><b>True</b> if an attribute hierarchy is constructed.</param>
        </member>
        <member name="M:Dundas.Olap.Data.Hierarchy.GetDefaultLevel">
            <summary>
            Gets <b>Hierarchy</b> default level.
            </summary>
            <returns>
            Default hierarchy <see cref="T:Dundas.Olap.Data.Level"/> object or <b>null</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Hierarchy.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns>
            A <b>string</b> that represents the current object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>Hierarchy</b> is visible in 
            the end user interface.
            </summary>
            <value>
            <b>True</b> if the hierarchy is visible to the end user.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.Name">
            <summary>
            Gets or sets the <b>Hierarchy</b> name.
            </summary>
            <value>
            A <b>string</b> value that represents the hierarchy name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.UniqueName">
            <summary>
            Gets or sets the <b>Hierarchy</b> unique name.
            </summary>
            <value>
            A <b>string</b> value that represents the hierarchy unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.Caption">
            <summary>
            Gets or sets the <b>Hierarchy</b> caption.
            </summary>
            <value>
            A <b>string</b> value that represents the hierarchy caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.Description">
            <summary>
            Gets or sets the <b>Hierarchy</b> description.
            </summary>
            <value>
            A <b>string</b> value that represents the hierarchy description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.DefaultMemberUniqueName">
            <summary>
            Gets or sets the <b>Hierarchy</b> default member unique name.
            </summary>
            <value>
            A <b>string</b> value that represents the hierarchy default member unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.DefaultLevelName">
            <summary>
            Gets or sets the <b>Hierarchy</b> default level name.
            </summary>
            <value>
            A <b>string</b> value that represents the hierarchy default level name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.ParentDimension">
            <summary>
            Gets or sets the dimension that this <b>Hierarchy</b> belongs to.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.Dimension"/> object that this hierarchy belongs to.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.Levels">
            <summary>
            Gets a collection of hierarchy levels.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.LevelCollection"/> object that represents a collection of
            the <b>Hierarchy</b> levels.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.Properties">
            <summary>
            Gets a dimension <b>Hierarchy</b> property collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic hierarchy properties.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.DisplayFolder">
            <summary>
            Gets or sets the <b>Hierarchy</b> display folder name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>Hierarchy</b> display folder name.
            </value>
            <remarks>
            The <b>DisplayFolder</b> property contains the fully qualified name of the
            display folder in which hierarchy appears. It consists of one or many display 
            folder names that represent the path to the hierarchy . If the display folders 
            are nested, the property lists the name of each display folder the parent folder 
            contains, separated by backslash characters <b>'\'</b>. 
            
            If hierarchy do not have associated display folder, this property contains 
            an empty string.
            </remarks>
        </member>
        <member name="P:Dundas.Olap.Data.Hierarchy.IsAttributeHierarchy">
            <summary>
            Gets the flag that indicates if <b>Hierarchy</b> is an attribute hierarchy.
            </summary>
            <value>
            <b>True</b> if <b>Hierarchy</b> is an attribute hierarchy.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.HierarchyCollection">
            <summary>
            Represents a collection of <b>Hierarchy</b> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.#ctor(Dundas.Olap.Data.Dimension)">
            <summary>
            HierarchyCollection object constructor.
            </summary>
            <param name="parentDimension">
            A <see cref="T:Dundas.Olap.Data.Dimension"/> object that owns the collection.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.Remove(Dundas.Olap.Data.Hierarchy)">
            <summary>
            Removes the given <b>Hierarchy</b> from the collection.
            </summary>
            <param name="hierarchy">
            The <b>Hierarchy</b> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.Add(Dundas.Olap.Data.Hierarchy)">
            <summary>
            Adds a <b>Hierarchy</b> to the end of the collection.
            </summary>
            <param name="hierarchy">
            <b>Hierarchy</b> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.Insert(System.Int32,Dundas.Olap.Data.Hierarchy)">
            <summary>
            Inserts a <b>Hierarchy</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="hierarchy">
            <b>Hierarchy</b> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.OnNewItem(System.Object,System.Int32)">
            <summary>
            New Object in the collection.
            </summary>
            <param name="newItem">
            New collection object.
            </param>
            <param name="index">
            New collection object index.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.FindByName(System.String)">
            <summary>
            Finds a <b>Hierarchy</b> by its name.
            </summary>
            <param name="name">
            Dimension hierarchy name.
            </param>
            <returns>
            A <b>Hierarchy</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.HierarchyCollection.FindByUniqueName(System.String)">
            <summary>
            Finds a <b>Hierarchy</b> by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension hierarchy unique name.
            </param>
            <returns>
            A <b>Hierarchy</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.HierarchyCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Hierarchy</b> by index.
            </summary>
            <value>
            Returns a <b>Hierarchy</b> object specified by the index.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.NamedSet">
            <summary>
            Represents a NamedSet inside the <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object. 
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSet.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSet.#ctor(System.String)">
            <summary>
            Object constructor.
            </summary>
            <param name="name">
            <b>NamedSet</b> object name.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSet.#ctor(System.String,System.String)">
            <summary>
            Object constructor.
            </summary>
            <param name="name">
            <b>NamedSet</b> object name.
            </param>
            <param name="expression">
            <b>NamedSet</b> object expression.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSet.ToString">
            <summary>
            Returns a string that represents this object.
            </summary>
            <returns>
            A <b>string</b> that represents this object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>NamedSet</b> is visible in 
            the end user interface.
            </summary>
            <value>
            <b>True</b> if <b>NamedSet</b> is visible to the end user.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Name">
            <summary>
            Gets or sets the <b>NamedSet</b> name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>NamedSet</b> name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Expression">
            <summary>
            Gets or sets the <b>NamedSet</b> expression.
            </summary>
            <value>
            A <b>String</b> that contains the <b>NamedSet</b> expression.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Caption">
            <summary>
            Gets or sets a <b>NamedSet</b> caption.
            </summary>
            <value>
            A <b>String</b> that contains a <b>NamedSet</b> caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Description">
            <summary>
            Gets or sets the <b>NamedSet</b> description.
            </summary>
            <value>
            A <b>String</b> that contains the <b>NamedSet</b> description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Properties">
            <summary>
            Gets a <b>NamedSet</b> dynamic property collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic <b>NamedSet</b> properties.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.DisplayFolder">
            <summary>
            Gets or sets the <b>NamedSet</b> display folder name.
            </summary>
            <value>
            A <b>String</b> that contains the <b>NamedSet</b> display folder name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.Dimensions">
            <summary>
            Gets or sets the <b>NamedSet</b> dimensions.
            </summary>
            <value>
            A <b>String</b> that contains the comma delimited list of dimension 
            hierarchies unique names included in the <b>NamedSet</b>.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSet.ParentCubeDataSchema">
            <summary>
            Gets or sets the parent cube data schema object.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> that contains parent cube data schema.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.NamedSetCollection">
            <summary>
            Represents a collection of <see cref="T:Dundas.Olap.Data.NamedSet"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.#ctor(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            NamedSetCollection object constructor.
            </summary>
            <param name="parentCubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> that owns the collection.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.Remove(Dundas.Olap.Data.NamedSet)">
            <summary>
            Removes the given <b>NamedSet</b> from the collection.
            </summary>
            <param name="namedSet">
            <see cref="T:Dundas.Olap.Data.NamedSet"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.Add(Dundas.Olap.Data.NamedSet)">
            <summary>
            Adds a <b>NamedSet</b> to the end of the collection.
            </summary>
            <param name="namedSet">
            <see cref="T:Dundas.Olap.Data.NamedSet"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.Add(System.String)">
            <summary>
            Adds a <b>NamedSet</b> to the end of the collection.
            </summary>
            <param name="name">
            <see cref="T:Dundas.Olap.Data.NamedSet"/> object name.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.Add(System.String,System.String)">
            <summary>
            Adds a <b>NamedSet</b> to the end of the collection.
            </summary>
            <param name="name">
            <see cref="T:Dundas.Olap.Data.NamedSet"/> object name.
            </param>
            <param name="expression">
            <see cref="T:Dundas.Olap.Data.NamedSet"/> object expression.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.Insert(System.Int32,Dundas.Olap.Data.NamedSet)">
            <summary>
            Inserts a <b>NamedSet</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="namedSet">
            <see cref="T:Dundas.Olap.Data.NamedSet"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the Object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.OnNewItem(System.Object,System.Int32)">
            <summary>
            New object in the collection.
            </summary>
            <param name="newItem">
            New collection Object.
            </param>
            <param name="index">
            New collection Object index.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.NamedSetCollection.FindByName(System.String)">
            <summary>
            Finds a <b>NamedSet</b> by its name.
            </summary>
            <param name="name">
            The NamedSet name.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.NamedSet"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.NamedSetCollection.Item(System.Int32)">
            <summary>
            Gets a <b>NamedSet</b> by index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionDescriptor">
            <summary>
            Represents one or more dimensions on the axis used to 
            retrieve data from the data provider.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptor"/>
            <remarks>
            The <b>DimensionDescriptor</b> represents one or more dimensions in
            the <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object. To identify the dimension, its 
            name must be specified as the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.DimensionName"/> property. In 
            addition, default <see cref="P:Dundas.Olap.Data.DimensionDescriptor.LevelName"/> and <see cref="P:Dundas.Olap.Data.DimensionDescriptor.Members"/> collection
            may be provided. If the level is not specified, the first level of the dimension will 
            be used. If the member collection is not specified, all members of the level will
            be used.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.#ctor(System.String)">
            <summary>
            DimensionDescriptor object constructor.
            </summary>
            <param name="dimensionName">
            Dimension name.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.#ctor(System.String,System.String)">
            <summary>
            DimensionDescriptor object constructor.
            </summary>
            <param name="dimensionName">
            Dimension name.
            </param>
            <param name="levelName">
            Dimension level name.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.#ctor(System.String,System.String,System.String[])">
            <summary>
            DimensionDescriptor object constructor.
            </summary>
            <param name="dimensionName">
            Dimension name.
            </param>
            <param name="levelName">
            Dimension level name.
            </param>
            <param name="memberNames">
            A string array with dimension <see cref="T:Dundas.Olap.Data.Member"/> names this descriptor represents.
            </param>
        </member>
        <member name="F:Dundas.Olap.Data.DimensionDescriptor.membersRangeList">
            <summary>
            Current list of ranges.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.Clone">
            <summary>
            Creates an exact copy of an object.
            </summary>
            <returns>
            A <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object which is an exact copy of this onject.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.DrillUpLevel(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Drill level up.
            </summary>
            <param name="dataSchema">
            Data schema.
            </param>
            <returns>
            <b>True</b> if drill up operation was succesfull.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.DrillDownLevel(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Drill level down.
            </summary>
            <param name="dataSchema">
            Data schema.
            </param>
            <returns>
            <b>True</b> if drill down operation was succesfull.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.GetDimension(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Gets the Dimension object based on the descriptor dimension name.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to search for the the dimension.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.Dimension"/> object that is presented by this dimension
            descriptor.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.GetPrevNextLevelNames(Dundas.Olap.Data.CubeDataSchema,System.Boolean,System.String@,System.String@)">
            <summary>
            Gets previous and next level names based on the current <see cref="P:Dundas.Olap.Data.DimensionDescriptor.LevelName"/>
            of the <b>DimensionDescriptor</b>.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to search for the the dimension.
            </param>
            <param name="ignoreLevelTypeAll">
            <b>True</b> if the top level ("All") should be ignored.
            </param>
            <param name="levelDownName">
            Returns a <b>string</b> value that represents a level name below the current. 
            Empty string is returned if the level below does not exist.
            </param>
            <param name="levelUpName">
            Returns a <b>string</b> value that represents a level name above the current. 
            Empty string is returned if level above does not exist.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.LoadFromXmlNode(System.Xml.XmlNode)">
            <summary>
            Loads <b>DimensionDescriptor</b> information from the XML noide.
            </summary>
            <param name="xmlNode">
            An <see cref="T:System.Xml.XmlNode"/> object to load the data from.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.SaveAsXmlNode(System.Xml.XmlDocument)">
            <summary>
            Saves <b>DimensionDescriptor</b> information into the XML document.
            </summary>
            <param name="xmlDocument">
            An <see cref="T:System.Xml.XmlDocument"/> were the descriptor information should be saved.
            </param>
            <returns>
            An <see cref="T:System.Xml.XmlNode"/> object that was added to the XML document
            and contains descriptor information.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.GetMemberXML(System.Xml.XmlDocument,Dundas.Olap.Data.DimensionMemberDescriptor,System.String)">
            <summary>
            Returns XML node for given member.
            </summary>
            <param name="xmlDocument">Xml document</param>
            <param name="memberDescriptor">member decriptor</param>
            <returns>XML node.</returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.op_Equality(Dundas.Olap.Data.DimensionDescriptor,Dundas.Olap.Data.DimensionDescriptor)">
            <summary>
            Checks if two <b>DimensionDescriptor</b> objects are equal.
            </summary>
            <param name="left">
            Object to the left of the operator.
            </param>
            <param name="right">
            Object to the right of the operator.
            </param>
            <returns>
            <b>True</b> if two <b>DimensionDescriptor</b> objects are equal; otherwise <b>false</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.op_Inequality(Dundas.Olap.Data.DimensionDescriptor,Dundas.Olap.Data.DimensionDescriptor)">
            <summary>
            Checks if two <b>DimensionDescriptor</b> objects are not equal.
            </summary>
            <param name="left">
            Object to the left of the operator.
            </param>
            <param name="right">
            Object to the right of the operator.
            </param>
            <returns>
            <b>True</b> if two <b>DimensionDescriptor</b> objects are not equal; otherwise <b>false</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.GetHashCode">
            <summary>
            Returns a hash code of the object.
            </summary>
            <returns>
            An <b>integer</b> that contains the hash code of the object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptor.Equals(System.Object)">
            <summary>
            Checks if specified object equals this object.
            </summary>
            <param name="obj">
            Object to the test with.
            </param>
            <returns>
            <b>True</b> if specified object is equal to this object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.MembersRangeCollection">
            <summary>
            Gets the range list.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.IsNamedSetUsed">
            <summary>
            Gets a flag that indicates if <b>DimensionDescriptor</b> describes a <b>NamedSet</b>.
            </summary>
            <value>
            A <b>True</b> value if this dimension descriptor object is used to define a named set.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.NamedSetName">
            <summary>
            Gets or sets the Named Set name.
            </summary>
            <value>
            A <b>string</b> value that represents the Named Set name.
            </value>
            <remarks>
            Setting this property to the name of one of the registered NamedSet will
            associate this <b>DimensionDescriptor</b> object with that named set.
            DimensionName, LevelName and Members properties will be ignored in this case.
            </remarks>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder">
            <summary>
            Gets or sets dimension members sorting order.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            <value>
            A <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/> enumeration value that represents dimension 
            members sorting order.
            </value>
            <remarks>
            Setting this property will automatically clear the list of <see cref="P:Dundas.Olap.Data.DimensionDescriptor.Members"/>
            in this descriptor.
            </remarks>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.ShowAllParentLevels">
            <summary>
            Gets or sets a flag that indicates if the full dimension hierarchy
            should be displayed for this dimension in the UI controls.
            </summary>
            <value>
            A <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/> enumeration value that represents dimension 
            members sorting order.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.DimensionName">
            <summary>
            Gets or sets the dimension name.
            </summary>
            <value>
            A <b>string</b> value that represents the dimension name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.LevelName">
            <summary>
            Gets or sets the dimension default level name.
            </summary>
            <value>
            A <b>string</b> value that represents the dimension default level name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptor.Members">
            <summary>
            Gets the collection of dimension members.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.DimensionMemberDescriptorCollection"/> object that represents a 
            collection of dimension member descriptors.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.DimensionDescriptorCollection">
            <summary>
            Collection of the <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> objects.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.DimensionDescriptor"/>
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptor"/>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.Remove(Dundas.Olap.Data.DimensionDescriptor)">
            <summary>
            Removes the given <b>DimensionDescriptor</b> from the collection.
            </summary>
            <param name="dimensionDescriptor">
            <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.Add(Dundas.Olap.Data.DimensionDescriptor)">
            <summary>
            Adds a <b>DimensionDescriptor</b> to the end of the collection.
            </summary>
            <param name="dimensionDescriptor">
            <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.Add(System.String)">
            <summary>
            Adds a <b>DimensionDescriptor</b> to the end of the collection.
            </summary>
            <param name="dimensionName">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object name that this descriptor represents.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.Add(System.String,System.String)">
            <summary>
            Adds a <b>DimensionDescriptor</b> to the end of the collection.
            </summary>
            <param name="dimensionName">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object name that this descriptor represents.
            </param>
            <param name="levelName">
            <see cref="T:Dundas.Olap.Data.Level"/> object name that this descriptor represents.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.Add(System.String,System.String,System.String[])">
            <summary>
            Adds a <b>DimensionDescriptor</b> to the end of the collection.
            </summary>
            <param name="dimensionName">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object name that this descriptor represents.
            </param>
            <param name="levelName">
            <see cref="T:Dundas.Olap.Data.Level"/> object name that this descriptor represents.
            </param>
            <param name="memberNames">
            A string array with dimension <see cref="T:Dundas.Olap.Data.Member"/> names this descriptor represents.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.Insert(System.Int32,Dundas.Olap.Data.DimensionDescriptor)">
            <summary>
            Inserts a <b>DimensionDescriptor</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="dimensionDescriptor">
            <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.DimensionDescriptorCollection.FindByName(System.String)">
            <summary>
            Finds a <b>DimensionDescriptor</b> by dimension name.
            </summary>
            <param name="dimensionName">
            A <b>string</b> value with dimension name to find.
            </param>
            <returns>
            <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.DimensionDescriptorCollection.Item(System.Int32)">
            <summary>
            Gets <b>DimensionDescriptor</b> by index.
            </summary>
            <remarks>
            This collection indexer returns <b>DimensionDescriptor</b>
            object by its index.
            </remarks>
        </member>
        <member name="T:Dundas.Olap.Data.CellSet">
            <summary>
            Represents a cellset that is returned back from the multidimensional 
            data provider as a result of the query.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.AxisCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Axis"/>
            <seealso cref="T:Dundas.Olap.Data.Cell"/>
            <remarks>
            A <b>CellSet</b> is created when the <see cref="M:Dundas.Olap.Data.IDataProvider.ExecuteCellSet(System.String,Dundas.Olap.Data.CubeDataSchema,System.Boolean,Dundas.Olap.Data.AxisDescriptorCollection,Dundas.Olap.Data.AxisDescriptor)"/> method of the
            data provider is called.
            
            <p>The <see cref="P:Dundas.Olap.Data.CellSet.Axes"/> property represents a collection of <see cref="T:Dundas.Olap.Data.Axis"/> objects returned back in
            the cellset.</p>
            
            <p>Use the <see cref="M:Dundas.Olap.Data.CellSet.GetCell(System.Int32[])"/> method to retrieve individual <see cref="T:Dundas.Olap.Data.Cell"/> objects.</p>
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.CellSet.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CellSet.#ctor(Dundas.Olap.Data.IDataProvider)">
            <summary>
            CellSet object constructor.
            </summary>
            <param name="dataProvider">
            Data provider that creates the <b>CellSet</b>.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.CellSet.Initialize">
            <summary>
            Initializes cellset object.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CellSet.GetCell(System.Int32[])">
            <summary>
            Retrieves a single <see cref="T:Dundas.Olap.Data.Cell"/> object using the index for each 
            existing axis.
            </summary>
            <remarks>
            To uniquely identify the <b>Cell</b> an integer position (index) must 
            be provided for each axis in the <b>Cellset</b>.
            </remarks>
            <param name="indexes">
            Array of integer indexes that uniquely identify the cell.
            </param>
            <returns>
            Requested <see cref="T:Dundas.Olap.Data.Cell"/> object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.CellSet.Axes">
            <summary>
            Gets a <b>Cellset</b> axis collection.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.AxisCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Axis"/>
            <value>
            An <see cref="T:Dundas.Olap.Data.AxisCollection"/> object that represents a collection
            of axes in the cellset.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.Axis">
            <summary>
            Represents an axis in the multidimensional cellset.
            </summary>
            <remarks>
            An <b>Axis</b> represents a single query axis returned in the <see cref="T:Dundas.Olap.Data.CellSet"/>
            object. It contains a <see cref="P:Dundas.Olap.Data.Axis.TupleSet"/> property that contains a collection 
            of tuples (positions) along the axis.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Axis.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.Axis.Name">
            <summary>
            Gets or sets an <b>Axis</b> name.
            </summary>
            <value>
            A <b>string</b> that represents the axis name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Axis.TupleSet">
            <summary>
            Gets an <b>Axis</b> tuple set collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.TupleCollection"/> object that represents collection of axis tuples.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.AxisCollection">
            <summary>
            Represents a collection of axes in the multidimensional cellset.
            <seealso cref="T:Dundas.Olap.Data.Axis"/>
            </summary>
            <remarks>
            Multidimensional data is returned back from the data provider as a <see cref="T:Dundas.Olap.Data.CellSet"/>
            object. The <see cref="P:Dundas.Olap.Data.CellSet.Axes"/> property contains a collection of query axes 
            returned back in the cellset.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.AxisCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.AxisCollection.#ctor(Dundas.Olap.Data.CellSet)">
            <summary>
            Object constructor.
            </summary>
            <param name="parentCellSet">
            <see cref="T:Dundas.Olap.Data.CellSet"/> object this axis collection belongs to.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.AxisCollection.Remove(Dundas.Olap.Data.Axis)">
            <summary>
            Removes the specified <b>Axis</b> from the collection.
            </summary>
            <param name="axis">
            <see cref="T:Dundas.Olap.Data.Axis"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.AxisCollection.Add(Dundas.Olap.Data.Axis)">
            <summary>
            Adds an <b>Axis</b> to the end of the collection.
            </summary>
            <param name="axis">
            <see cref="T:Dundas.Olap.Data.Axis"/> object to add.
            </param>
            <returns>
            Index of the newly added axis object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.AxisCollection.Insert(System.Int32,Dundas.Olap.Data.Axis)">
            <summary>
            Inserts an <b>Axis</b> into the collection at the specified position.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="axis">
            <see cref="T:Dundas.Olap.Data.Axis"/> object to insert.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.AxisCollection.Item(System.Int32)">
            <summary>
            Gets an <b>Axis</b> object from the collection by index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.IDataProvider">
            <summary>
            Multidimensional data provider interface. An interface that must be 
            implemented by every OLAP data provider.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetCubesInfo">
            <summary>
            Gets the collection of <see cref="T:Dundas.Olap.Data.CubeInfo"/> objects from the data provider.  
            </summary>
            <seealso cref="T:Dundas.Olap.Data.CubeInfoCollection"/>
            <seealso cref="T:Dundas.Olap.Data.CubeInfo"/>
            <returns>
            Collection of <see cref="T:Dundas.Olap.Data.CubeInfo"/> objects.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetDataSchema(System.String)">
            <summary>
            Gets the <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object for the specified cube.
            </summary>
            <param name="cubeName">
            Cube name to get the data schema for.
            </param>
            <returns>
            Data schema for the specified cube name as <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetLevelMembers(Dundas.Olap.Data.Level)">
            <summary>
            Gets the <see cref="T:Dundas.Olap.Data.Level"/> child <see cref="T:Dundas.Olap.Data.Member"/> object collection.
            </summary>
            <param name="level">
            Dimension <see cref="T:Dundas.Olap.Data.Level"/> to get the members for.
            </param>
            <returns>
            Collection of child <see cref="T:Dundas.Olap.Data.Member"/> objects as <see cref="T:Dundas.Olap.Data.MemberCollection"/>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetChildMembers(Dundas.Olap.Data.Member)">
            <summary>
            Gets <see cref="T:Dundas.Olap.Data.Member"/> child <see cref="T:Dundas.Olap.Data.Member"/> object collection.
            </summary>
            <param name="member">
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> to get the child members for.
            </param>
            <returns>
            Collection of child <see cref="T:Dundas.Olap.Data.Member"/> objects as <see cref="T:Dundas.Olap.Data.MemberCollection"/>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetParentMember(Dundas.Olap.Data.Member)">
            <summary>
            Gets the parent <see cref="T:Dundas.Olap.Data.Member"/> of a specified member object. 
            </summary>
            <param name="member">
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> to get the parent member for.
            </param>
            <returns>
            Parent <see cref="T:Dundas.Olap.Data.Member"/> or <b>null</b> for the top-level members.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.ExecuteCellSet(System.String,Dundas.Olap.Data.CubeDataSchema,System.Boolean,Dundas.Olap.Data.AxisDescriptorCollection,Dundas.Olap.Data.AxisDescriptor)">
            <summary>
            Retrieves data from the multidimensional data provider.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.CellSet"/>
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptor"/>
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptorCollection"/>
            <param name="cubeName">
            A <b>string</b> value that represents the cube name to retrieve data from.
            </param>
            <param name="cubeDataSchema">
            Cube data schema.
            </param>
            <param name="nonEmptyCellsOnly">
            <b>True</b> if only non-empty cells should be retrieved.
            </param>
            <param name="requestedAxes">
            Requested axis descriptor collection as <see cref="T:Dundas.Olap.Data.AxisDescriptorCollection"/>.
            </param>
            <param name="slicerAxis">
            Slicer <see cref="T:Dundas.Olap.Data.AxisDescriptor"/>.
            </param>
            <returns>
            Multidimensional data as a <see cref="T:Dundas.Olap.Data.CellSet"/>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetCell(Dundas.Olap.Data.CellSet,System.Int32[])">
            <summary>
            Retrieves a single <see cref="T:Dundas.Olap.Data.Cell"/> from the <see cref="T:Dundas.Olap.Data.CellSet"/>.
            </summary>
            <remarks>
            To uniquely identify the <see cref="T:Dundas.Olap.Data.Cell"/> in the <see cref="T:Dundas.Olap.Data.CellSet"/>, an index
            must be specified for each <see cref="T:Dundas.Olap.Data.Axis"/> in the cellset. For example, if the 
            cellset has 2 axes, an array of 2 integers must be provided as a parameter to
            this method.
            </remarks>
            <param name="cellSet">
            <see cref="T:Dundas.Olap.Data.CellSet"/> object to get the cell from.
            </param>
            <param name="indexes">
            Array of integer indexes that uniquely identify the cell on each axis in the cellset.
            </param>
            <returns>
            Required <see cref="T:Dundas.Olap.Data.Cell"/> object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.Close">
            <summary>
            Closes the connection to the database.
            </summary>
            <remarks>
            It then releases the connection to the connection pool, or closes the connection if connection pooling is disabled.
            An application can call Close more than one time without generating an exception.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.Open">
            <summary>
            Opens the connection to the database.
            </summary>
            <remarks>
            This method can be called to ensure that connection with the database is established.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.GetAvailableCatalogNames">
            <summary>
            Gets an array of available catalog names.
            </summary>
            <remarks>
            <b>Null</b> is returned if data provider do not support multiple catalogs.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.IsTopLevelMember(Dundas.Olap.Data.Member)">
            <summary>
            Checks if the given member is top level member.
            </summary>
            <param name="member">member to be checked</param>
            <returns>true if member is top level</returns>
        </member>
        <member name="M:Dundas.Olap.Data.IDataProvider.ExecuteCellSet(System.String,Dundas.Olap.Data.CubeDataSchema,System.Boolean,Dundas.Olap.Data.AxisDescriptorCollection,Dundas.Olap.Data.AxisDescriptor,Dundas.Olap.Data.CellSetPaging,System.Collections.ArrayList)">
            <summary>
            Retrieves data from the multidimensional data provider.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.CellSet"/>
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptor"/>
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptorCollection"/>
            <param name="cubeName">
            A <b>string</b> value that represents the cube name to retrieve data from.
            </param>
            <param name="cubeDataSchema">
            Cube data schema.
            </param>
            <param name="nonEmptyCellsOnly">
            <b>True</b> if only non-empty cells should be retrieved.
            </param>
            <param name="requestedAxes">
            Requested axis descriptor collection as <see cref="T:Dundas.Olap.Data.AxisDescriptorCollection"/>.
            </param>
            <param name="slicerAxis">
            Slicer <see cref="T:Dundas.Olap.Data.AxisDescriptor"/>.
            </param>
            <returns>
            Multidimensional data as a <see cref="T:Dundas.Olap.Data.CellSet"/>.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.IDataProvider.SupportsMultipleSlicerMemberSelection">
            <summary>
            Checks if data provider supports selection of multiple dimension members on the 
            Slicer axis.
            </summary>
            <value>
            Returns <b>true</b> if multiple member selection is supported on the Slicer axis.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.IDataProvider.IsConnected">
            <summary>
            Checks if data provider is currently connected to the data source.
            </summary>
            <value>
            Returns <b>true</b> if data provider is currently connected.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.IDataProvider.SupportsMembersOnDemand">
            <summary>
            Gets the flag that indicates if the data provider supports 
            on-demand member retrieval.
            </summary>
            <value>
            Returns <b>true</b> if data provider supports on-demand retrieval of 
            the child members metadata.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.IDataProvider.SupportsCubes">
            <summary>
            Gets the flag that indicates if the data provider supports 
            multiple cubes.
            </summary>
            <value>
            Returns <b>true</b> if the data provider supports cubes.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.IDataProvider.CurrentCellSet">
            <summary>
            Gets the current cellset from the cube.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.CellSet"/> object that represents the latest data retrieved
            from the data source.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.IDataProvider.CatalogName">
            <summary>
            Gets current catalog name from the data provider.
            </summary>
            <value>
            A <b>string</b> value that contains current catalog name.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.FilterTopBottomType">
            <summary>
            Top/Bottom filter type.
            <seealso cref="T:Dundas.Olap.Data.MemberSortingAndFiltering"/>
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterTopBottomType.None">
            <summary>
            No filtering.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterTopBottomType.Top">
            <summary>
            Filtering top members.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterTopBottomType.Bottom">
            <summary>
            Filtering bottom members.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.FilterTopBottomValueType">
            <summary>
            Top/bottom filter value type.
            <seealso cref="T:Dundas.Olap.Data.MemberSortingAndFiltering"/>
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterTopBottomValueType.Items">
            <summary>
            Filtering top/bottom value defines how many top/bottom items to display.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterTopBottomValueType.Percent">
            <summary>
            Filtering top/bottom value defines the percentage of members to display.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.FilterCondition">
            <summary>
            Filter condition.
            <seealso cref="T:Dundas.Olap.Data.MemberSortingAndFiltering"/>
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.None">
            <summary>
            No Filtering
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.Equal">
            <summary>
            Equal.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.NotEqual">
            <summary>
            Not Equal.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.More">
            <summary>
            More.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.MoreOrEqual">
            <summary>
            More or Equal.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.Less">
            <summary>
            Less.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.LessOrEqual">
            <summary>
            Less or Equal.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.Between">
            <summary>
            Between. Uses 2 values.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.FilterCondition.NotBetween">
            <summary>
            Not Between. Uses 2 values.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.MemberSortingAndFiltering">
            <summary>
            Represents dimension member sorting and filtering rules in the 
            <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object.
            </summary>
            <remarks>
            <b>Filtering</b> allows the user to remove members that do not match the specified 
            criteria. Criteria is defined using the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterCondition"/>, <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterMeasure"/>, 
            <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue"/> and <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/> properties.
            
            <p><b>Top/bottom filtering</b> allows the user to remove all members except the specified 
            number of top/bottom members. The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType"/> property
            defines if top or bottom members are kept. The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue"/> 
            property defines the number or percentage of members kept depending on the 
            <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType"/> property. The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomBreaksHierarchy"/>
            property controls whether the hierarchy will be broken as a result of filtering.</p>
             
            <p><b>Sorting</b> allows the user to order members ascendingly or descendingly using one
            of the measure names. The order is defined by the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortOrder"/>
            property.  The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortMeasure"/> property defines the measure name.
            The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortBreaksHierarchy"/> property controls if the hierarchy will 
            be broken as a result of sorting.</p>
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.ValidateParameters(Dundas.Olap.Data.CubeDataSchema)">
            <summary>
            Validates sorting and filtering parameters.
            </summary>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object that represents current cube data schema.
            </param>
            <remarks>
            Function checks if valid measure names where provided in
            sorting and filtering parameters. Exception is thrown if
            any problem detected.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.ValidateMeasureName(Dundas.Olap.Data.CubeDataSchema,System.String,System.String)">
            <summary>
            Validates measure name.
            </summary>
            <param name="cubeDataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object that represents current cube data schema.
            </param>
            <param name="measureName">
            A <b>string</b> value that represents measure name to be validated.
            </param>
            <param name="propertyName">
            A <b>string</b> value that represents measure property name.
            </param>
            <remarks>
            Function checks if valid measure names where provided in
            sorting and filtering parameters. Exception is thrown if
            any problem detected.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.IsSortedOrFiltered">
            <summary>
            Checks if sorting or filtering is enabled.
            </summary>
            <returns>
            <b>True</b> if filtering or sorting is enabled.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.IsHierarchyBroken">
            <summary>
            Checks if members' hierarchy is broken as a result of filtering or sorting.
            </summary>
            <returns>
            <b>True</b> if members' hierarchy is broken as a result of filtering or sorting.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.Reset">
            <summary>
            Reset all sorting and filtering properties.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.Clone">
            <summary>
            Creates an exact copy of this object.
            </summary>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberSortingAndFiltering"/> object which is an exact copy of this object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.LoadFromXmlNode(System.Xml.XmlNode)">
            <summary>
            Loads <b>MemberSortingAndFiltering</b> object from the XML node.
            </summary>
            <param name="xmlNode">
            A <see cref="T:System.Xml.XmlNode"/> to load the object from.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.SaveAsXmlNode(System.Xml.XmlDocument,System.Xml.XmlNode)">
            <summary>
            Saves object into specified XML node.
            </summary>
            <param name="xmlDocument">
            A <see cref="T:System.Xml.XmlDocument"/> the object is saved to.
            </param>
            <param name="xmlNodeParent">
            A <see cref="T:System.Xml.XmlNode"/> the object is saved to.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.op_Equality(Dundas.Olap.Data.MemberSortingAndFiltering,Dundas.Olap.Data.MemberSortingAndFiltering)">
            <summary>
            Checks if two <b>MemberSortingAndFiltering</b> objects are equal.
            </summary>
            <param name="left">
            Object to the left of the operator.
            </param>
            <param name="right">
            Object to the right of the operator.
            </param>
            <returns>
            <b>True</b> if two <b>MemberSortingAndFiltering</b> objects are equal; otherwise <b>false</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.op_Inequality(Dundas.Olap.Data.MemberSortingAndFiltering,Dundas.Olap.Data.MemberSortingAndFiltering)">
            <summary>
            Checks if two <b>MemberSortingAndFiltering</b> objects are not equal.
            </summary>
            <param name="left">
            Object to the left of the operator.
            </param>
            <param name="right">
            Object to the right of the operator.
            </param>
            <returns>
            <b>True</b> if two <b>MemberSortingAndFiltering</b> objects are not equal; otherwise <b>false</b>.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.GetHashCode">
            <summary>
            Returns a hash code of the object.
            </summary>
            <returns>
            An <b>integer</b> that contains the hash code of the object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.MemberSortingAndFiltering.Equals(System.Object)">
            <summary>
            Checks if specified object equals this object.
            </summary>
            <param name="obj">
            Object to the test with.
            </param>
            <returns>
            <b>True</b> if specified object is equal to this object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomBreaksHierarchy">
            <summary>
            Gets or sets a flag that indicates if top/bottom filtering breaks
            dimension hierarchy.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomMeasure"/>
            <value>
            <b>True</b> if top/bottom filtering breaks dimension hierarchy.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType">
            <summary>
            Gets or sets top/bottom filtering type.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomMeasure"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType"/>
            <remarks>
            To enable top/bottom filtering, set this property to either <b>FilterTopBottomType.Top</b>
            or <b>FilterTopBottomType.Bottom</b> and specify the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomMeasure"/>
            property. The number or percent of top/bottom members shown can be controlled by using the
            <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue"/> and <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType"/> properties.
            </remarks>
            <value>
            A <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType"/> enumeration value that indicates if top, 
            bottom or no filtering is enabled.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue">
            <summary>
            Gets or sets the number or percentage of top/bottom members to be displayed.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomMeasure"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType"/>
            <remarks>
            Top/bottom filtering can filter using the member count or percentage of
            their values. The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType"/> property controls this
            behaviour.
            </remarks>
            <value>
            A <b>double</b> that contains either the number or percentage of top/bottom members.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType">
            <summary>
            Gets or sets if the top/bottom members are counted using the count or percentage.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomMeasure"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue"/>
            <remarks>
            Top/bottom filtering can filter using member count or percentage of
            their values based on this property. The <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue"/> property defines
            the actual number/percent of members shown.
            </remarks>
            <value>
            A <b>double</b> that contains either the number or the percentage of top/bottom members.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomMeasure">
            <summary>
            Gets or sets the measure name used for top/bottom filtering.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomType"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValue"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterTopBottomValueType"/>
            <value>
            A <b>string</b> that contains measure name used for filtering.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterCondition">
            <summary>
            Gets or sets a filtering condition.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterMeasure"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/>
            <remarks>
            Use this property to enable or disable filtering and to specify the filtering 
            condition. Use the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue"/> and <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/> properties
            to specify condition values. Most of the conditions (excluding Between and NotBetween) only use 
            one condition value.
            
            <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterMeasure"/> property must be set to measure name which is used 
            in the filter.
            </remarks>
            <value>
            A <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterCondition"/> enumeration value that indicates if filtering 
            is enabled and what filtering condition is used.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterMeasure">
            <summary>
            Gets or sets a measure name used to filter members.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterCondition"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/>
            <value>
            A <b>string</b> that contains a measure name used to filter members.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue">
            <summary>
            Gets or sets the first value of the filtering condition.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterCondition"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/>
            <remarks>
            Filtering conditions <b>Between</b> and <b>NotBetween</b> use 
            two values. Use the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/> property to
            specify the second value.
            </remarks>
            <value>
            A <b>double</b> that contains the first value of the filtering condition.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2">
            <summary>
            Gets or sets the second value of the filtering condition.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterCondition"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue2"/>
            <remarks>
            The second filtering value is only used in conjunction with the <b>Between</b> and <b>NotBetween</b> 
            conditions. For all other conditions, please use the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.FilterValue"/> property.
            </remarks>
            <value>
            A <b>double</b> that contains the second value of the filtering condition.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortOrder">
            <summary>
            Gets or sets the sorting order.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortMeasure"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortBreaksHierarchy"/>
            <remarks>
            Use this property to enable ascending or descending sorting of the members.
            A measure name must be specified using the <see cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortMeasure"/> property.
            </remarks>
            <value>
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration value that indicates if sorting 
            is enabled and what sorting order is used.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortBreaksHierarchy">
            <summary>
            Gets or sets a flag that indicates if sorting breaks the hierachy.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortMeasure"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortOrder"/>
            <value>
            <b>True</b> if sorting breaks dimension hierachy.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortMeasure">
            <summary>
            Gets or sets a measure name used to sort members.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortOrder"/>
            <seealso cref="P:Dundas.Olap.Data.MemberSortingAndFiltering.SortBreaksHierarchy"/>
            <value>
            A <b>string</b> that contains measure name that used to sort members.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.Property">
            <summary>
            Represents dynamic property of various multidimensional data elements.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Property.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Property.#ctor(System.String,System.Object)">
            <summary>
            Property object constructor.
            </summary>
            <param name="name">
            Property name.
            </param>
            <param name="value">
            Property value.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.Property.Name">
            <summary>
            Gets a <b>Property</b> name.
            </summary>
            <value>
            A <b>string</b> that contains a <b>Property</b> name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Property.Value">
            <summary>
            Gets a <b>Property</b> value.
            </summary>
            <value>
            An <b>object</b> that contains a <b>Property</b> value.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.PropertyCollection">
            <summary>
            Represents collection of the <see cref="T:Dundas.Olap.Data.Property"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.PropertyCollection.Remove(Dundas.Olap.Data.Property)">
            <summary>
            Removes the given <b>Property</b> from the collection.
            </summary>
            <param name="property">
            <see cref="T:Dundas.Olap.Data.Property"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.PropertyCollection.Add(Dundas.Olap.Data.Property)">
            <summary>
            Adds a <b>Property</b> to the end of the collection.
            </summary>
            <param name="property">
            <see cref="T:Dundas.Olap.Data.Property"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.PropertyCollection.Add(System.String,System.Object)">
            <summary>
            Adds a <b>Property</b> to the end of the collection.
            </summary>
            <param name="name">
            Property name.
            </param>
            <param name="value">
            Property value.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.PropertyCollection.Insert(System.Int32,Dundas.Olap.Data.Property)">
            <summary>
            Inserts a <b>Property</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="property">
            <see cref="T:Dundas.Olap.Data.Property"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.PropertyCollection.FindByName(System.String)">
            <summary>
            Finds a <b>Property</b> by its name.
            </summary>
            <param name="name">
            Property name.
            </param>
            <returns>
            <see cref="T:Dundas.Olap.Data.Property"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.PropertyCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Property</b> by index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.LevelType">
            <summary>
            Dimension level type enumeration.
            <seealso cref="P:Dundas.Olap.Data.Level.LevelType"/>
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Account">
            <summary>
            Indicates that a level exists within an account dimension. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.All">
            <summary>
            Indicates the top (All) level of a dimension (the one that pre-calculates all the members of all lower levels).
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.BomResource">
            <summary>
            Indicates that a level is part of a bill of materials dimension.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Calculated">
            <summary>
            Indicates that a level is calculated.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Channel">
            <summary>
            Indicates that a level exists within a distribution channel dimension.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Company">
            <summary>
            Indicates that a level contains information about a company. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.CurrencyDestination">
            <summary>
            Indicates that a level contains information about the resulting currency after a foreign exchange conversion. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.CurrencySource">
            <summary>
            Indicates that a level contains information about the starting currency before a foreign exchange conversion. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Customer">
            <summary>
            Indicates that a level contains information about an individual customer. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.CustomerGroup">
            <summary>
            Indicates that a level contains information about a customer group. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.CustomerHousehold">
            <summary>
            Indicates that a level contains information about an entire household.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoCity">
            <summary>
            Indicates that a level refers to a city name.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoContinent">
            <summary>
            Indicates that a level refers to a continent name.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoCountry">
            <summary>
            Indicates that a level refers to a country or region name.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoPoint">
            <summary>
            Indicates that a level refers to a location type that does not fit into the other geographical categories.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoPostalCode">
            <summary>
            Indicates that a level refers to a postal code.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoRegion">
            <summary>
            Indicates that a level refers to a custom-defined region.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.GeoStateOrProvince">
            <summary>
            Indicates that a level refers to a state or province name. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.OrgUnit">
            <summary>
            Indicates that a level refers to the name of a unit within a larger organization. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Person">
            <summary>
            Indicates that a level refers to an individual within a larger organization.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Product">
            <summary>
            Indicates that a level refers to an individual product.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.ProductGroup">
            <summary>
            Indicates that a level refers to a product group.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Promotion">
            <summary>
            Indicates that a level refers to a promotion.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Quantitative">
            <summary>
            Indicates that a level refers to a quantitative member within a quantitative dimension. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Regular">
            <summary>
            Indicates that the level is not related to time.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Representative">
            <summary>
             Indicates that a level refers to a sales representative. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Scenario">
            <summary>
            Indicates that a level refers to a scenario. 
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Time">
            <summary>
            Indicates that a level is related to time.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeDays">
            <summary>
            Indicates that a level refers to days.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeHalfYears">
            <summary>
            Indicates that a level refers to half-years.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeHours">
            <summary>
            Indicates that a level refers to hours.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeMinutes">
            <summary>
            Indicates that a level refers to minutes.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeMonths">
            <summary>
            Indicates that a level refers to months.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeQuarters">
            <summary>
            Indicates that a level refers to (calendar) quarters.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeSeconds">
            <summary>
            Indicates that a level refers to seconds.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeUndefined">
            <summary>
            Indicates that a level refers to an indeterminate or nonstandard measurement of time.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeWeeks">
            <summary>
            Indicates that a level refers to weeks.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.TimeYears">
            <summary>
            Indicates that a level refers to years.
            </summary>
        </member>
        <member name="F:Dundas.Olap.Data.LevelType.Utility">
            <summary>
            Indicates that a level exists in a utility dimension.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Level">
            <summary>
            Represents a single level inside the dimension <see cref="T:Dundas.Olap.Data.Hierarchy"/>.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.Dimension"/>
            <seealso cref="T:Dundas.Olap.Data.Hierarchy"/>
            <remarks>
            Each <b>Level</b> contains a collection of child <see cref="P:Dundas.Olap.Data.Level.Members"/>.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Level.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Level.GetFilteredAndSortedMemberList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.AxisDescriptor)">
            <summary>
            Gets a filtered and sorted list of level members.
            </summary>
            <remarks>
            This method returns a list of level members. Only <see cref="P:Dundas.Olap.Data.Member.Visible"/> 
            members are returned. Members are sorted alphabetically based on the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            property of the associated <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </remarks>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to define sorting and filtering rules.
            </param>
            <param name="axisDescriptor">
            A <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object used to define the axis descriptor used for the sorting and filtering rules.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of level members.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Level.GetFilteredAndSortedMemberList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.MemberSortOrder)">
            <summary>
            Gets a filtered and sorted list of level members.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to define sorting and filtering rules.
            </param>
            <param name="memberSortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration vale that defines the sorting order of the members.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of level members.
            </returns>
            <remarks>
            This method returns a list of level members. Only <see cref="P:Dundas.Olap.Data.Member.Visible"/> 
            members are returned. Members are sorted alphabetically based on the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            property of the associated <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Level.GetFilteredAndSortedMemberList(Dundas.Olap.Data.CubeDataSchema,Dundas.Olap.Data.MemberSortOrder,System.Int64,System.Int32)">
            <summary>
            Gets a filtered and sorted list of level members.
            </summary>
            <param name="dataSchema">
            A <see cref="T:Dundas.Olap.Data.CubeDataSchema"/> object used to define sorting and filtering rules.
            </param>
            <param name="memberSortOrder">
            A <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> enumeration vale that defines the sorting order of the members.
            </param>
            <returns>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of level members.
            </returns>
            <remarks>
            This method returns a list of level members. Only <see cref="P:Dundas.Olap.Data.Member.Visible"/> 
            members are returned. Members are sorted alphabetically based on the <see cref="P:Dundas.Olap.Data.DimensionDescriptor.MemberSortOrder"/>
            property of the associated <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> object.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.Level.ToString">
            <summary>
            Returns a string that represents the current object.
            </summary>
            <returns>
            A <b>string</b>b> that represents current object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Level.ToUniqueString">
            <summary>
            Returns a string that uniqly idetifies the current object.
            </summary>
            <returns>
            A <b>string</b> that uniqly idetifies current object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.Level.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>Level</b> is visible in 
            the end user interface.
            </summary>
            <value>
            <b>True</b> if the <b>Level</b> is visible to the end user.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.LevelType">
            <summary>
            Gets or sets the dimension <b>Level</b> type.
            </summary>
            <value>
            A <see cref="P:Dundas.Olap.Data.Level.LevelType"/> enumeration value that represents the <b>Level</b> type.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.Name">
            <summary>
            Gets or sets the dimension <b>Level</b> name.
            </summary>
            <value>
            A <b>string</b> value that represents the <b>Level</b> name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.UniqueName">
            <summary>
            Gets or sets the dimension <b>Level</b> unique name.
            </summary>
            <value>
            A <b>string</b> value that represents the <b>Level</b> unique name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.Caption">
            <summary>
            Gets or sets the dimension <b>Level</b> caption.
            </summary>
            <value>
            A <b>string</b> value that represents the level caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.Description">
            <summary>
            Gets or sets the dimension <b>Level</b> description.
            </summary>
            <value>
            A <b>string</b> value that represents <b>Level</b> description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.ParentHierarchy">
            <summary>
            Gets or sets the dimension Hierarchy which this <b>Level</b> belongs to.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.Hierarchy"/> object which this <b>Level</b> belongs to.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.MembersOnDemandLoaded">
            <summary>
            Gets or sets a flag that indicates that the members collection is pre-populated with members.
            </summary>
            <remarks>
            If this property is set to <b>false</b> and the <see cref="P:Dundas.Olap.Data.Level.Members"/>
            collection of the <b>Level</b> is accessed, a round trip to the data provider will be required. If this 
            property is set to <b>true</b>, the member list is already loaded and saved in the <b>Level</b>. 
            </remarks>
            <value>
            <b>True</b> if <b>Level</b> member collection is already loaded.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.Members">
            <summary>
            Gets a collection of the child members. 
            </summary>
            <remarks>
            Accessing this property may require a round-trip to the data source.
            If the <see cref="P:Dundas.Olap.Data.Level.MembersOnDemandLoaded"/> property is set to <b>true</b>, 
            the members collection is already pre-loaded and no round-trip will be 
            required.
            </remarks>
            <value>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that represents a collection of 
            level <see cref="T:Dundas.Olap.Data.Member"/> objects.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.Properties">
            <summary>
            Gets a dimension <b>Level</b> dynamic property collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection of
            dynamic <b>Level</b> properties.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Level.LevelDepth">
            <summary>
            Gets the <b>Level</b> depth in the hierarchy.
            </summary>
            <value>
            An <b>integer</b> value that represents the <b>Level</b> ordinal position
            in the <see cref="T:Dundas.Olap.Data.Hierarchy"/>.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.LevelCollection">
            <summary>
            Represents collection of the dimension <see cref="T:Dundas.Olap.Data.Level"/> objects.
            <seealso cref="T:Dundas.Olap.Data.Level"/>
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.#ctor(Dundas.Olap.Data.Hierarchy)">
            <summary>
            LevelCollection object constructor.
            </summary>
            <param name="parentHierarchy">
            A <see cref="T:Dundas.Olap.Data.Hierarchy"/> object that owns the collection.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.Remove(Dundas.Olap.Data.Level)">
            <summary>
            Removes the given <b>Level</b> from the collection.
            </summary>
            <param name="level">
            <see cref="T:Dundas.Olap.Data.Level"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.Add(Dundas.Olap.Data.Level)">
            <summary>
            Adds a <b>Level</b> to the end of the collection.
            </summary>
            <param name="level">
            <see cref="T:Dundas.Olap.Data.Level"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.Insert(System.Int32,Dundas.Olap.Data.Level)">
            <summary>
            Inserts a <b>Level</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="level">
            <see cref="T:Dundas.Olap.Data.Level"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Value of the object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.OnNewItem(System.Object,System.Int32)">
            <summary>
            New object in the collection.
            </summary>
            <param name="newItem">
            New collection object.
            </param>
            <param name="index">
            New collection object index.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.FindByName(System.String)">
            <summary>
            Finds a dimension <b>Level</b> by its name.
            </summary>
            <param name="name">
            Dimension <b>Level</b> name.
            </param>
            <returns>
            Dimension <b>Level</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.LevelCollection.FindByUniqueName(System.String)">
            <summary>
            Finds a dimension <b>Level</b> by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension level unique name.
            </param>
            <returns>
            Dimension <b>Level</b> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.LevelCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Level</b> object by index.
            </summary>
            <value>
            Returns a <see cref="T:Dundas.Olap.Data.Level"/> object from the collection specified by the index.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.CubeDataSchema">
            <summary>
            Represents the multidimensional cube data schema.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.CubeDataSchema.CubeInfo"/>
            <remarks>
            The <b>CubeDataSchema</b> represents the metadata of the cube. It contains information 
            about cube <see cref="P:Dundas.Olap.Data.CubeDataSchema.Dimensions"/> and <see cref="P:Dundas.Olap.Data.CubeDataSchema.Measures"/>.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.#ctor(Dundas.Olap.Data.IDataProvider)">
            <summary>
            The <b>CubeDataSchema</b> object constructor.
            <seealso cref="T:Dundas.Olap.Data.IDataProvider"/>
            </summary>
            <param name="dataProvider">
            Data provider that creates this data schema object.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.Initialize">
            <summary>
            Initializes cube data schema object.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindHierarchyByUniqueName(System.String)">
            <summary>
            Finds a dimension <see cref="T:Dundas.Olap.Data.Hierarchy"/> object by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension hierarchy unique name.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Hierarchy"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindHierarchyByName(System.String)">
            <summary>
            Finds a dimension <see cref="T:Dundas.Olap.Data.Hierarchy"/> object by its name.
            </summary>
            <param name="name">
            Dimension hierarchy name.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Hierarchy"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindLevelByUniqueName(System.String)">
            <summary>
            Finds a dimension <see cref="T:Dundas.Olap.Data.Level"/> object by its unique name.
            </summary>
            <param name="uniqueName">
            Dimension level unique name.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Level"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindLevelByName(System.String)">
            <summary>
            Finds a dimension <see cref="T:Dundas.Olap.Data.Level"/> by its name.
            </summary>
            <param name="name">
            Dimension level name.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Level"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindMemberByUniqueName(System.String,System.Boolean)">
            <summary>
            Finds dimension <see cref="T:Dundas.Olap.Data.Member"/> object by its unique name.
            </summary>
            <remarks>
            This method finds the <b>Member</b> by iterating through each member of each 
            Dimension, Hierarchy and Level. This operation may take a significant amount of 
            time, especially if the information about the <b>Level</b> members is not yet
            preloaded into the CubeDataSchema object. Set the <b>loadedMembersOnly</b> 
            property to <b>true</b> to search only in the levels that have their
            members already loaded. 
            </remarks>
            <param name="uniqueName">
            Dimension member unique name.
            </param>
            <param name="loadedMembersOnly">
            Indicates that a search is performed on the preloaded members only.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindMemberByUniqueName(System.String,System.String,System.Boolean)">
            <summary>
            Finds dimension <see cref="T:Dundas.Olap.Data.Member"/> object by its unique name.
            </summary>
            <remarks>
            This method finds the <b>Member</b> by iterating through each member in the
            specified <see cref="T:Dundas.Olap.Data.Level"/>. This operation may take a significant amount of  
            time, especially if information about the <b>Level</b> members is not yet
            preloaded into the CubeDataSchema object. Set the <b>loadedMembersOnly</b> 
            property to <b>true</b> to search only if the level has its members 
            already loaded. 
            </remarks>
            <param name="levelName">
            Member <see cref="T:Dundas.Olap.Data.Level"/> name.
            </param>
            <param name="uniqueName">
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> unique name.
            </param>
            <param name="loadedMembersOnly">
            Indicates that a search is performed on the preloaded members only.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindMemberByName(System.String,System.Boolean)">
            <summary>
            Finds dimension <see cref="T:Dundas.Olap.Data.Member"/> by its name.
            </summary>
            <remarks>
            This method finds the <b>Member</b> by iterating through each member of each 
            Dimension, Hierarchy and Level. This operation may take a significant amount of 
            time, especially if the information about the <b>Level</b> members is not yet
            preloaded into the CubeDataSchema object. Set the <b>loadedMembersOnly</b> 
            property to <b>true</b> to search only in the levels that have their
            members already loaded. 
            </remarks>
            <param name="name">
            Dimension member name.
            </param>
            <param name="loadedMembersOnly">
            Indicates that a search is performed on the preloaded members only.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindMemberByName(System.String,System.String,System.Boolean)">
            <summary>
            Finds dimension <see cref="T:Dundas.Olap.Data.Member"/> by its name.
            </summary>
            <remarks>
            This method finds the <b>Member</b> by iterating through each member in the
            specified <see cref="T:Dundas.Olap.Data.Level"/>. This operation may take a significant amount of  
            time, especially if information about the <b>Level</b> members is not yet
            preloaded into the CubeDataSchema object. Set the <b>loadedMembersOnly</b> 
            property to <b>true</b> to search only if the level has its members 
            already loaded. 
            </remarks>
            <param name="levelName">
            Member <see cref="T:Dundas.Olap.Data.Level"/> name.
            </param>
            <param name="name">
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> name.
            </param>
            <param name="loadedMembersOnly">
            Indicates that a search is performed on the preloaded members only.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeDataSchema.FindMemberByName(System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Finds dimension <see cref="T:Dundas.Olap.Data.Member"/> object by its unique name.
            </summary>
            <remarks>
            This method finds the <b>Member</b> by iterating through each member in the
            specified <see cref="T:Dundas.Olap.Data.Level"/>. This operation may take a significant amount of  
            time, especially if information about the <b>Level</b> members is not yet
            preloaded into the CubeDataSchema object. Set the <b>loadedMembersOnly</b> 
            property to <b>true</b> to search only if the level has its members 
            already loaded. 
            </remarks>
            <param name="levelName">
            Member <see cref="T:Dundas.Olap.Data.Level"/> name.
            </param>
            <param name="name">
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> unique name.
            </param>
            <param name="loadedMembersOnly">
            Indicates that a search is performed on the preloaded members only.
            </param>
            <param name="useUniqueName">
            Indicates that a unique member name is passed as a parameter to this function.
            </param>
            <returns>
            Dimension <see cref="T:Dundas.Olap.Data.Member"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.CubeInfo">
            <summary>
            Gets or sets multidimensional cube information.
            </summary>
            <seealso cref="P:Dundas.Olap.Data.CubeDataSchema.CubeInfo"/>
            <value>
            A <see cref="P:Dundas.Olap.Data.CubeDataSchema.CubeInfo"/> object that represents multidimensional cube information.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.Dimensions">
            <summary>
            Gets a collection of the cube dimensions.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.DimensionCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Dimension"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.DimensionCollection"/> object that represents a collection 
            of <see cref="T:Dundas.Olap.Data.Dimension"/> objects in the cube.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.Measures">
            <summary>
            Gets a collection of the cube measures.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.MeasureCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Measure"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.MeasureCollection"/> object that represents a collection 
            of <see cref="T:Dundas.Olap.Data.Measure"/> objects in the cube.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.NamedSets">
            <summary>
            Gets a collection of the cube named sets.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.NamedSetCollection"/>
            <seealso cref="T:Dundas.Olap.Data.NamedSet"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.NamedSetCollection"/> object that represents a collection 
            of <see cref="T:Dundas.Olap.Data.NamedSet"/> objects in the cube.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.DataProvider">
            <summary>
            Gets or sets the data provider that retrieved the schema.
            </summary>
            <value>
            An <see cref="T:Dundas.Olap.Data.IDataProvider"/> interface that represents the OLAP data
            provider used to retrieve the data schema.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.UniqueMemberNameTable">
            <summary>
            Gets or sets the lookup table used to quickly find members by unique name.
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.CubeDataSchema.UniqueLevelNameTable">
            <summary>
            Gets or sets the lookup table used to quickly find levels by unique name.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.CubeInfo">
            <summary>
            Represents multidimensional cube information.
            <seealso cref="T:Dundas.Olap.Data.CubeDataSchema"/>
            </summary>
            <remarks>
            A <b>CubeInfo</b> object contains metadata relating to a single cube.
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfo.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfo.#ctor(System.String,System.String,System.String)">
            <summary>
            <b>CubeInfo</b> object constructor.
            </summary>
            <param name="name">
            Cube name.
            </param>
            <param name="caption">
            Cube caption.
            </param>
            <param name="description">
            Cube description.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfo.ToString">
            <summary>
            Returns a string that represents the object.
            </summary>
            <returns>
            A string that represents a <b>CubeInfo</b> object.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.CubeInfo.Visible">
            <summary>
            Gets or sets a flag that indicates if the cube is visible in 
            the end user interface.
            </summary>
            <value>
            <b>True</b> if cube is visible to the end user, otherwise <b>false</b>.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeInfo.Name">
            <summary>
            Gets or sets a cube name.
            </summary>
            <value>
            A <b>string</b> value that represent cube name.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeInfo.Caption">
            <summary>
            Gets or sets a cube caption.
            </summary>
            <value>
            A <b>string</b> value that represent a cube caption.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeInfo.Description">
            <summary>
            Gets or sets a cube description.
            </summary>
            <value>
            A <b>string</b> value that represent a cube description.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.CubeInfo.Properties">
            <summary>
            Gets cube dynamic <see cref="T:Dundas.Olap.Data.Property"/> collection.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.PropertyCollection"/>
            <seealso cref="T:Dundas.Olap.Data.Property"/>
            <value>
            A <see cref="T:Dundas.Olap.Data.PropertyCollection"/> object that represents a collection
            of dynamic cube properties.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.CubeInfoCollection">
            <summary>
            Represents collection of <see cref="T:Dundas.Olap.Data.CubeInfo"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfoCollection.Remove(Dundas.Olap.Data.CubeInfo)">
            <summary>
            Removes the given <b>CubeInfo</b> object from the collection.
            </summary>
            <param name="cubeInfo">
            <see cref="T:Dundas.Olap.Data.CubeInfo"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfoCollection.Add(Dundas.Olap.Data.CubeInfo)">
            <summary>
            Adds a <b>CubeInfo</b> to the end of the collection.
            </summary>
            <param name="cubeInfo">
            <see cref="T:Dundas.Olap.Data.CubeInfo"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfoCollection.Insert(System.Int32,Dundas.Olap.Data.CubeInfo)">
            <summary>
            Inserts a <b>CubeInfo</b> into the collection at the specified index.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="cubeInfo">
            <see cref="T:Dundas.Olap.Data.CubeInfo"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.CubeInfoCollection.FindByName(System.String)">
            <summary>
            Finds a <b>CubeInfo</b> by its name.
            </summary>
            <param name="name">
            Cube name.
            </param>
            <returns>
            <see cref="T:Dundas.Olap.Data.CubeInfo"/> object or <b>null</b> if not found.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.CubeInfoCollection.Item(System.Int32)">
            <summary>
            Gets a <b>CubeInfo</b> object using integer index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.AxisDescriptor">
            <summary>
            Represents dimensions to be retrieved from the multidimensional
            data provider along a single data axis.
            </summary>
            <seealso cref="T:Dundas.Olap.Data.DimensionDescriptorCollection"/>
            <seealso cref="T:Dundas.Olap.Data.DimensionDescriptor"/>
            <seealso cref="T:Dundas.Olap.Data.MemberSortingAndFiltering"/>
            <remarks>
            An <b>AxisDescriptor</b> is used to define the query
            which is passed to the OLAP data provider. Each query may consist
            of one or more axis descriptors, plus one axis descriptor that is used
            for the slicing axis.
            
            <p>Each <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object contains a collection 
            of <see cref="T:Dundas.Olap.Data.DimensionDescriptor"/> objects that identify the
            dimensions along this axis.</p>
            </remarks>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.Clone">
            <summary>
            Creates an exact copy of the <b>AxisDescriptor</b> object.
            </summary>
            <returns>
            New <b>AxisDescriptor</b> object which is an exact copy this object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.IsSortingOrFilteringEnabled">
            <summary>
            Checks if sorting or filtering is enabled on the axis.
            </summary>
            <returns>
            Returns true if sorting or filtering is enabled.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.GetMembersSortOrder(Dundas.Olap.Data.Dimension)">
            <summary>
            Returns an alphabetically sorted order of the specified dimension 
            in the axis.
            </summary>
            <param name="dimension">
            <see cref="T:Dundas.Olap.Data.Dimension"/> object to get the member sorting order for.
            </param>
            <returns>
            <see cref="T:Dundas.Olap.Data.MemberSortOrder"/> value.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.LoadFromXmlNode(System.Xml.XmlNode)">
            <summary>
            Loads an <b>AxisDescriptor</b> object from the XML node.
            </summary>
            <param name="xmlNode">
            <see cref="T:System.Xml.XmlNode"/> object to load the data from.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.GetChildNodeByName(System.Xml.XmlNode,System.String)">
            <summary>
            Helper method that returns XML child node by name.
            </summary>
            <param name="xmlNode">
            <see cref="T:System.Xml.XmlNode"/> object to look in.
            </param>
            <param name="name">
            Name of the child node to look for.
            </param>
            <returns>
            Child XmlNode with specified name.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.SaveAsXmlNode(System.Xml.XmlDocument,System.Xml.XmlNode)">
            <summary>
            Saves an <b>AxisDescriptor</b> object as the XML node.
            </summary>
            <param name="xmlDocument">
            XML document where the object is saved.
            </param>
            <param name="xmlNodeAxisDescriptor">
            <see cref="T:System.Xml.XmlNode"/> object to save the object to.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptor.Dispose">
            <summary>
            Dispose the object
            </summary>
        </member>
        <member name="P:Dundas.Olap.Data.AxisDescriptor.SortingAndFiltering">
            <summary>
            Gets or sets the sorting and filtering rules for the members of this <b>AxisDescriptor</b>.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.MemberSortingAndFiltering"/> object that represents sorting and 
            filtering rules for the <b>AxisDescriptor</b>.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.AxisDescriptor.Dimensions">
            <summary>
            Gets a collection of axis dimension descriptors.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.DimensionDescriptorCollection"/> that represents a collection
            of <b>Axis</b> dimensions.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.AxisDescriptorCollection">
            <summary>
            Represents a collection of <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> objects.
            <seealso cref="T:Dundas.Olap.Data.AxisDescriptor"/>
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptorCollection.Remove(Dundas.Olap.Data.AxisDescriptor)">
            <summary>
            Removes the given <b>AxisDescriptor</b> from the collection.
            </summary>
            <param name="axisDescriptor">
            <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptorCollection.Add(Dundas.Olap.Data.AxisDescriptor)">
            <summary>
            Adds an <b>AxisDescriptor</b> to the end of the collection.
            </summary>
            <param name="axisDescriptor">
            <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.AxisDescriptorCollection.Insert(System.Int32,Dundas.Olap.Data.AxisDescriptor)">
            <summary>
            Inserts an <b>AxisDescriptor</b> into the collection at the specified index.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="axisDescriptor">
            <see cref="T:Dundas.Olap.Data.AxisDescriptor"/> object to insert.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.AxisDescriptorCollection.Item(System.Int32)">
            <summary>
            Gets an <b>AxisDescriptor</b> from the collection by index.
            </summary>
        </member>
        <member name="T:Dundas.Olap.Data.Tuple">
            <summary>
            Represents an ordered collection of members from different dimensions.
            <seealso cref="T:Dundas.Olap.Data.Member"/>
            </summary>
            <remarks>
            Tuples are used to represent positions along the <see cref="T:Dundas.Olap.Data.Axis"/> in
            the <see cref="T:Dundas.Olap.Data.CellSet"/> returned by the data provider.
            </remarks>    
        </member>
        <member name="M:Dundas.Olap.Data.Tuple.#ctor">
            <summary>
            Default object constructor.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.Tuple.ToString">
            <summary>
            Converts object to string.
            <seealso cref="M:Dundas.Olap.Data.Tuple.ToUniqueString"/>
            </summary>
            <returns>
            A <b>string</b> representation of this object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.Tuple.ToUniqueString">
            <summary>
            Converts object to a unique string.
            </summary>
            <returns>
            A <b>string</b> that uniquely identify the tuple.
            </returns>
        </member>
        <member name="P:Dundas.Olap.Data.Tuple.Visible">
            <summary>
            Gets or sets a flag that indicates if the <b>Tuple</b> in the <see cref="T:Dundas.Olap.Data.CellSet"/> is visible.
            </summary>
            <value>
            <b>True</b> if tuple is visible and should be processed.
            </value>
            <remarks>
            When a new <b>CellSet</b> is retrieved all the tuples are being processed by 
            the visualizer controls like <b>OlapChart</b>. If tuple is marked as non-visible 
            it should not be displayed in the chart.
            </remarks>
        </member>
        <member name="P:Dundas.Olap.Data.Tuple.OrdinalPosition">
            <summary>
            Gets an ordinal position of the <b>Tuple</b>.
            </summary>
            <value>
            An <b>integer</b> value that contains the <b>Tuple</b> ordinal position along
            the <see cref="T:Dundas.Olap.Data.Axis"/> in the cellset.
            </value>
        </member>
        <member name="P:Dundas.Olap.Data.Tuple.Members">
            <summary>
            Gets a <b>Tuple</b> member collection.
            </summary>
            <value>
            A <see cref="T:Dundas.Olap.Data.MemberCollection"/> object that contains collection 
            of <see cref="T:Dundas.Olap.Data.Member"/> objects.
            </value>
        </member>
        <member name="T:Dundas.Olap.Data.TupleCollection">
            <summary>
            Represents collection of <see cref="T:Dundas.Olap.Data.Tuple"/> objects.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.#ctor">
            <summary>
            Default constructor is unaccessible.
            </summary>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.#ctor(Dundas.Olap.Data.Axis)">
            <summary>
            TupleCollection object constructor.
            </summary>
            <param name="parentAxis">
            Parent <see cref="T:Dundas.Olap.Data.Axis"/> of the collection.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.Remove(Dundas.Olap.Data.Tuple)">
            <summary>
            Removes the specified <b>Tuple</b> from the collection.
            </summary>
            <param name="tuple">
            <see cref="T:Dundas.Olap.Data.Tuple"/> object to be removed.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.Add(Dundas.Olap.Data.Tuple)">
            <summary>
            Adds a <b>Tuple</b> to the end of the collection.
            </summary>
            <param name="tuple">
            <see cref="T:Dundas.Olap.Data.Tuple"/> object to add.
            </param>
            <returns>
            Index of the newly added object.
            </returns>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.Insert(System.Int32,Dundas.Olap.Data.Tuple)">
            <summary>
            Inserts a <b>Tuple</b> into the collection.
            </summary>
            <param name="index">
            Index to insert the object at.
            </param>
            <param name="tuple">
            <see cref="T:Dundas.Olap.Data.Tuple"/> object to insert.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.OnInsertComplete(System.Int32,System.Object)">
            <summary>
            New object inserted into the collection.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="value">
            Object value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
            <summary>
            Vale of the object was changed.
            </summary>
            <param name="index">
            Object index.
            </param>
            <param name="oldValue">
            Object old value.
            </param>
            <param name="newValue">
            Object new value.
            </param>
        </member>
        <member name="M:Dundas.Olap.Data.TupleCollection.OnNewItem(System.Int32,System.Object)">
            <summary>
            New object in the collection.
            </summary>
            <param name="index">
            New collection object index.
            </param>
            <param name="newItem">
            New collection object.
            </param>
        </member>
        <member name="P:Dundas.Olap.Data.TupleCollection.Item(System.Int32)">
            <summary>
            Gets a <b>Tuple</b> by index.
            </summary>
        </member>
    </members>
</doc>
