using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class ProgressInfo : ISerializable
{
	public bool Normalize = false;
	public bool SplitByCell = false;
	public bool AssumeOneFailure = false;
	public ReportHelper.DateGroupingEnum GroupingId = ReportHelper.DateGroupingEnum.BY_MONTH;
	
	public string SeriesName1 = null;
	public string SpecLabel1 = null;
	public decimal SpecRate1 = Decimal.MinValue;
	public decimal WorkLoad1 = Decimal.MinValue;
	public string Format1 = null;
	public ReportHelper.RateTypeEnum RateTypeId1 = ReportHelper.RateTypeEnum.PER_MILLION_MEDIA;
	public string DimensionName1 = null;
	public List<string> DimensionMembers1 = new List<string>();

	public string SeriesName2 = null;
	public string SpecLabel2 = null;
	public decimal SpecRate2 = Decimal.MinValue;
	public decimal WorkLoad2 = Decimal.MinValue;
	public string Format2 = null; 
	public ReportHelper.RateTypeEnum RateTypeId2 = ReportHelper.RateTypeEnum.PER_MILLION_MEDIA;
	public string DimensionName2 = null;
	public List<string> DimensionMembers2 = new List<string>();
	
	public string SeriesName3 = null;
	public string SpecLabel3 = null;
	public decimal SpecRate3 = Decimal.MinValue;
	public decimal WorkLoad3 = Decimal.MinValue;
	public string Format3 = null;
	public ReportHelper.RateTypeEnum RateTypeId3 = ReportHelper.RateTypeEnum.PER_MILLION_MEDIA;
	public string DimensionName3 = null;
	public List<string> DimensionMembers3 = new List<string>();
	
	public string SeriesName4 = null;
	public string SpecLabel4 = null;
	public decimal SpecRate4 = Decimal.MinValue;
	public decimal WorkLoad4 = Decimal.MinValue;
	public string Format4 = null;
	public ReportHelper.RateTypeEnum RateTypeId4 = ReportHelper.RateTypeEnum.PER_MILLION_MEDIA;
	public string DimensionName4 = null;
	public List<string> DimensionMembers4 = new List<string>();
	
	// Default constructor.
	public ProgressInfo() { }

	// Deserialization constructor.
	public ProgressInfo(SerializationInfo info, StreamingContext context)
	{
		Normalize = (bool)info.GetValue("norm", typeof(bool));
		SplitByCell = (bool)info.GetValue("split", typeof(bool));
		AssumeOneFailure = (bool)info.GetValue("assumeOne", typeof(bool)); 
		GroupingId = (ReportHelper.DateGroupingEnum)info.GetValue("g", typeof(int));

		SeriesName1 = (string)info.GetValue("seriesName1", typeof(string));
		SpecLabel1 = (string)info.GetValue("specLbl1", typeof(string));
		SpecRate1 = (decimal)info.GetValue("spr1", typeof(decimal));
		WorkLoad1 = (decimal)info.GetValue("wrk1", typeof(decimal));
		Format1 = (string)info.GetValue("f1", typeof(string));
		RateTypeId1 = (ReportHelper.RateTypeEnum)info.GetValue("rate1", typeof(int));
		DimensionName1 = (string)info.GetValue("dimName1", typeof(string));
		DimensionMembers1 = (List<string>)info.GetValue("dimMem1", typeof(List<string>));

		SeriesName2 = (string)info.GetValue("seriesName2", typeof(string));
		SpecLabel2 = (string)info.GetValue("specLbl2", typeof(string));
		SpecRate2 = (decimal)info.GetValue("spr2", typeof(decimal));
		WorkLoad2 = (decimal)info.GetValue("wrk2", typeof(decimal));
		Format2 = (string)info.GetValue("f2", typeof(string)); 
		RateTypeId2 = (ReportHelper.RateTypeEnum)info.GetValue("rate2", typeof(int));
		DimensionName2 = (string)info.GetValue("dimName2", typeof(string));
		DimensionMembers2 = (List<string>)info.GetValue("dimMem2", typeof(List<string>));

		SeriesName3 = (string)info.GetValue("seriesName3", typeof(string));
		SpecLabel3 = (string)info.GetValue("specLbl3", typeof(string));
		SpecRate3 = (decimal)info.GetValue("spr3", typeof(decimal));
		WorkLoad3 = (decimal)info.GetValue("wrk3", typeof(decimal));
		Format3 = (string)info.GetValue("f3", typeof(string)); 
		RateTypeId3 = (ReportHelper.RateTypeEnum)info.GetValue("rate3", typeof(int));
		DimensionName3 = (string)info.GetValue("dimName3", typeof(string));
		DimensionMembers3 = (List<string>)info.GetValue("dimMem3", typeof(List<string>));

		SeriesName4 = (string)info.GetValue("seriesName4", typeof(string));
		SpecLabel4 = (string)info.GetValue("specLbl4", typeof(string));
		SpecRate4 = (decimal)info.GetValue("spr4", typeof(decimal));
		WorkLoad4 = (decimal)info.GetValue("wrk4", typeof(decimal));
		Format4 = (string)info.GetValue("f4", typeof(string)); 
		RateTypeId4 = (ReportHelper.RateTypeEnum)info.GetValue("rate4", typeof(int));
		DimensionName4 = (string)info.GetValue("dimName4", typeof(string));
		DimensionMembers4 = (List<string>)info.GetValue("dimMem4", typeof(List<string>));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("norm", Normalize);
		info.AddValue("split", SplitByCell);
		info.AddValue("assumeOne", AssumeOneFailure);
		info.AddValue("g", (int)GroupingId);

		info.AddValue("seriesName1", SeriesName1);
		info.AddValue("specLbl1", SpecLabel1);
		info.AddValue("spr1", SpecRate1);
		info.AddValue("wrk1", WorkLoad1);
		info.AddValue("f1", Format1);
		info.AddValue("rate1", (int)RateTypeId1);
		info.AddValue("dimName1", DimensionName1);
		info.AddValue("dimMem1", DimensionMembers1);

		info.AddValue("seriesName2", SeriesName2);
		info.AddValue("specLbl2", SpecLabel2);
		info.AddValue("spr2", SpecRate2);
		info.AddValue("wrk2", WorkLoad2);
		info.AddValue("f2", Format2);
		info.AddValue("rate2", (int)RateTypeId2);
		info.AddValue("dimName2", DimensionName2);
		info.AddValue("dimMem2", DimensionMembers2);

		info.AddValue("seriesName3", SeriesName3);
		info.AddValue("specLbl3", SpecLabel3);
		info.AddValue("spr3", SpecRate3);
		info.AddValue("wrk3", WorkLoad3);
		info.AddValue("f3", Format3);
		info.AddValue("rate3", (int)RateTypeId3);
		info.AddValue("dimName3", DimensionName3);
		info.AddValue("dimMem3", DimensionMembers3);

		info.AddValue("seriesName4", SeriesName4);
		info.AddValue("specLbl4", SpecLabel4);
		info.AddValue("spr4", SpecRate4);
		info.AddValue("wrk4", WorkLoad4);
		info.AddValue("f4", Format4);
		info.AddValue("rate4", (int)RateTypeId4);
		info.AddValue("dimName4", DimensionName4);
		info.AddValue("dimMem4", DimensionMembers4);
	}
}