using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class WeeklySummaryInfo : ISerializable
{
	public bool FilterSessions = false;
	public List<int> IncludedSessions = new List<int>();

	// Default constructor.
	public WeeklySummaryInfo() { }

	// Deserialization constructor.
	public WeeklySummaryInfo(SerializationInfo info, StreamingContext context)
	{
		FilterSessions = (bool)info.GetValue("fs", typeof(bool));
		IncludedSessions = (List<int>)info.GetValue("is", typeof(List<int>));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("fs", FilterSessions);
		info.AddValue("is", IncludedSessions);
	}
}
