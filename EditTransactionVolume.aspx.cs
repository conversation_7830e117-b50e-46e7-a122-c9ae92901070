using System;
using System.Data;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class EditTransactionVolume : System.Web.UI.Page
{
	public int CellId
	{
		get { if (this.ViewState["c"] != null) return (int)this.ViewState["c"]; else return 0; }
		set { this.ViewState["c"] = value; }
	}

	public DateTime TransactionDate
	{
		get { if (this.ViewState["t"] != null) return (DateTime)this.ViewState["t"]; else return DateTime.MinValue; }
		set { this.ViewState["t"] = value; }
	}

    public int EffectiveConfigId
    {
        get { if (this.ViewState["eci"] != null) return (int)this.ViewState["eci"]; else return 0; }
        set { this.ViewState["eci"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (!Utility.IsUserAdmin())
				Response.Redirect("Unauthorized.aspx");

			sessionList.Items.Add(new ListItem("Select...", ""));
			sessionList.DataSource = Utility.GetManualVolumeSessionList();
			sessionList.DataBind(); 
			
			cellList.Items.Add(new ListItem("Select...", ""));
			cellList.DataSource = Utility.GetCellList();
			cellList.DataBind();

			deviceTypeList.Items.Add(new ListItem("Select...", ""));
			deviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
			deviceTypeList.DataBind();

			deviceList.Items.Add(new ListItem("Select...", ""));
			deviceList.Enabled = false;

            this.dateField.SelectedDate = DateTime.Today.AddDays(-1);
            this.timeField.SelectedDate = Convert.ToDateTime("11:59:59 PM");

			if (!string.IsNullOrEmpty((string)HttpContext.Current.Items["SessionId"]))
			{
				if (this.sessionList.Items.FindByValue((string)HttpContext.Current.Items["SessionId"]) != null)
					this.sessionList.SelectedValue = (string)HttpContext.Current.Items["SessionId"];

				this.sessionList.Enabled = false;
			}

			if (!string.IsNullOrEmpty((string)HttpContext.Current.Items["CellId"]))
				this.CellId = Convert.ToInt32(HttpContext.Current.Items["CellId"]);

			if ((string)HttpContext.Current.Items["TranDate"] != null)
				this.TransactionDate = Convert.ToDateTime((string)HttpContext.Current.Items["TranDate"]);

            if (!string.IsNullOrEmpty(sessionList.SelectedValue) && this.CellId != 0 && this.TransactionDate != DateTime.MinValue)
            {
                LoadTransactionVolume();
                transactionCountField.Focus();
            }
            else
            {
                cellList.Focus();
            }
		}
    }

	private void LoadTransactionVolume()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadTransactionVolume", Convert.ToInt32(sessionList.SelectedValue), this.CellId, this.TransactionDate);
        if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
        {
            DataRow row = ds.Tables[0].Rows[0];

            if (this.cellList.Items.FindByValue(DataFormatter.getInt32(row, "CellId").ToString()) != null)
                this.cellList.SelectedValue = DataFormatter.getInt32(row, "CellId").ToString();

            if (DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "") != null)
            {
                this.dateField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "")));
                this.timeField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "hh:mm:ss tt", "")));
            }

			if (DataFormatter.getInt32(row, "DeviceTypeId") > 0)
			{
				if (this.deviceTypeList.Items.FindByValue(DataFormatter.getInt32(row, "DeviceTypeId").ToString()) != null)
				{
					this.deviceTypeList.SelectedValue = DataFormatter.getInt32(row, "DeviceTypeId").ToString();
					DeviceType_IndexChanged(null, null);
				}

				if (DataFormatter.getInt32(row, "DeviceId") > 0)
				{
					if (this.deviceList.Items.FindByValue(DataFormatter.getInt32(row, "DeviceId").ToString()) == null)
						this.deviceList.Items.Add(new ListItem(DataFormatter.getInt32(row, "DeviceId").ToString() + " - " + DataFormatter.getString(row, "SerialNumber"), DataFormatter.getInt32(row, "DeviceId").ToString()));
						
					this.deviceList.SelectedValue = DataFormatter.getInt32(row, "DeviceId").ToString();
                    Device_IndexChanged(null, null);
				}
				deviceList.Enabled = true;
			}

			this.transactionCountField.Text = DataFormatter.getInt32(row, "CumTranCount").ToString();
            this.mediaCountField.Text = DataFormatter.getInt32(row, "CumMediaCount").ToString();
            this.EffectiveConfigId = DataFormatter.getInt32(row, "EffectiveConfigId");
        }
	}

	protected void DeviceType_IndexChanged(object sender, EventArgs e)
	{
		deviceList.Items.Clear();
		deviceList.Items.Add(new ListItem("Select...", ""));

		if (!string.IsNullOrEmpty(deviceTypeList.SelectedValue))
		{
			deviceList.Enabled = true;
			deviceList.DataSource = Utility.GetDevicesList(Convert.ToInt32(deviceTypeList.SelectedValue));
			deviceList.DataBind();
		}
		else
		{
			deviceList.Enabled = false;
		}
	}

    protected void Device_IndexChanged(object sender, EventArgs e)
    {
        this.configSettingsData.Value = null;
        if (!string.IsNullOrEmpty(deviceList.SelectedValue))
        {
            int deviceId = Convert.ToInt32(deviceList.SelectedValue);
            // Get selections for device
            if (this.CellId != 0)
            {
                // If editing, pull from effective config - if no effective config pull from tranVolumeSetting
                if (this.EffectiveConfigId > 0)
                {
                    DataSet ds = SqlHelper.ExecuteDataset("RPT_GetConfigFieldOptions", this.EffectiveConfigId);
                    bool isFirst = true;
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        if (isFirst)
                            isFirst = false;
                        else
                            this.configSettingsData.Value = this.configSettingsData.Value + "~";

                        this.configSettingsData.Value = this.configSettingsData.Value + string.Format("{0}/{1}", DataFormatter.getInt32(row, "FieldId"), DataFormatter.getInt32(row, "FieldOptionId"));
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(this.sessionList.SelectedValue))
                    {
                        TimeSpan time = new TimeSpan(((DateTime)timeField.SelectedDate).Hour, ((DateTime)timeField.SelectedDate).Minute, ((DateTime)timeField.SelectedDate).Second);
                        DateTime transactionDate = ((DateTime)dateField.SelectedDate).Add(time);
                        int sessionId = Convert.ToInt32(this.sessionList.SelectedValue);
                        DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTranVolumeSettings", sessionId, this.CellId, transactionDate);
                        bool isFirst = true;
                        foreach (DataRow row in ds.Tables[0].Rows)
                        {
                            if (isFirst)
                                isFirst = false;
                            else
                                this.configSettingsData.Value = this.configSettingsData.Value + "~";

                            this.configSettingsData.Value = this.configSettingsData.Value + string.Format("{0}/{1}", DataFormatter.getInt32(row, "FieldId"), DataFormatter.getInt32(row, "FieldOptionId"));
                        }
                    }
                }
            }
            else
            {
                // If new pull from device setting
                DataSet ds = SqlHelper.ExecuteDataset("RPT_GetDeviceSettings", deviceId);
                bool isFirst = true;
                foreach (DataRow row in ds.Tables[0].Rows)
                {
                    if (isFirst)
                        isFirst = false;
                    else
                        this.configSettingsData.Value = this.configSettingsData.Value + "~";

                    this.configSettingsData.Value = this.configSettingsData.Value + string.Format("{0}/{1}", DataFormatter.getInt32(row, "FieldId"), DataFormatter.getInt32(row, "FieldOptionId"));
                }
            }
        }
    }

    protected string BuildFieldJSON()
    {
        StringBuilder retVal = new StringBuilder();

        DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadExtensionFieldsByLibrary", "");
        ds.Relations.Add("FieldOptions", ds.Tables[0].Columns["FieldId"], ds.Tables[1].Columns["FieldId"]);
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            if (DataFormatter.getInt32(row, "FieldStatusId") != 2)
            {
                if (retVal.Length > 0)
                    retVal.Append(',');

                retVal.Append(string.Format("{{ 'Id':'{0}', 'Name':'{1}', 'Options': [", DataFormatter.Format(row, "FieldId"), Utility.FormatJsonString(DataFormatter.getString(row, "LibraryName")) + ": " + Utility.FormatJsonString(DataFormatter.getString(row, "RDToolName"))));

                bool isFirst = true;
                foreach (DataRow subRow in row.GetChildRows("FieldOptions"))
                {
                    if (DataFormatter.getBool(subRow, "IsActiveForManualConfigDLL"))
                    {
                        if (isFirst)
                            isFirst = false;
                        else
                            retVal.Append(',');
                        retVal.Append(string.Format("{{ 'Id':'{0}', 'Name':'{1}' }}", DataFormatter.Format(subRow, "FieldOptionId"), Utility.FormatJsonString(DataFormatter.getString(subRow, "DisplayName"))));
                    }
                }

                retVal.Append("] }");
            }
        }

        retVal.Insert(0, "[");
        retVal.Append("]");

        return retVal.ToString();
    }

    protected void SaveButton_Click(object sender, EventArgs e)
	{
		ErrorDiv.Visible = ErrorDiv1.Visible = false; 
		if (Page.IsValid)
		{
			TimeSpan time = new TimeSpan(((DateTime)timeField.SelectedDate).Hour, ((DateTime)timeField.SelectedDate).Minute, ((DateTime)timeField.SelectedDate).Second);
			DateTime transactionDate = ((DateTime)dateField.SelectedDate).Add(time);

			//Verify that the CumTranCount is valid between the previous and next dated observation or manual transaction volume entries.
			string errorReason = Utility.VerifyCumulativeValues(null, transactionDate, this.cellList.SelectedValue, this.sessionList.SelectedValue, this.transactionCountField.Text, this.mediaCountField.Text);
			if (!string.IsNullOrEmpty(errorReason))
			{
				ErrorMessage.Text = ErrorMessage1.Text = errorReason;
				ErrorDiv.Visible = ErrorDiv1.Visible = true;
			}
			else
			{
				object deviceId = null;
				if (!string.IsNullOrEmpty(deviceList.SelectedValue))
					deviceId = Convert.ToInt32(deviceList.SelectedValue);
				if (this.CellId != 0)
				{
					//Update
					int success = SqlHelper.ExecuteNonQuery("RPT_UpdateTransactionVolume",
								this.sessionList.SelectedValue, this.CellId, this.TransactionDate, this.cellList.SelectedValue, transactionDate, this.transactionCountField.Text.Trim(), this.mediaCountField.Text.Trim(), deviceId);
				}
				else
				{
					//Insert New
					SqlHelper.ExecuteNonQuery("RPT_InsertTransactionVolume",
								this.sessionList.SelectedValue, this.cellList.SelectedValue, transactionDate, this.transactionCountField.Text.Trim(), this.mediaCountField.Text.Trim(), deviceId);
                    this.CellId = Convert.ToInt32(this.cellList.SelectedValue);
                }

                // Save settings
                SqlHelper.ExecuteNonQuery("RPT_DeleteTranVolumeSettings", this.sessionList.SelectedValue, this.CellId, transactionDate);
                if (!string.IsNullOrEmpty(this.configSettingsData.Value))
                {
                    string[] arr = this.configSettingsData.Value.Split(new char[] { '~' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (string pair in arr)
                    {
                        string[] pairArr = pair.Split(new char[] { '/' }, StringSplitOptions.RemoveEmptyEntries);
                        if (pairArr.Length == 2 && !string.IsNullOrEmpty(pairArr[1]))
                        {
                            SqlHelper.ExecuteNonQuery("RPT_InsertTranVolumeSetting", this.sessionList.SelectedValue, this.CellId, transactionDate, Convert.ToInt32(pairArr[1]));
                        }
                    }
                }
                if (deviceId != null && (int)deviceId > 0)
                    SqlHelper.ExecuteNonQuery("RPT_UpdateDeviceSettingsFromVolume", (int)deviceId, this.sessionList.SelectedValue, this.CellId, transactionDate);

                if (this.CellId > 0) {
                    Response.Redirect(string.Format("TransactionVolume.aspx?s={0}&c={1}", sessionList.SelectedValue, this.CellId));
                }
                else {
                    Response.Redirect("TransactionVolume.aspx?s=" + sessionList.SelectedValue);
                }
			}
		}
	}

	protected void CancelButton_Click(object sender, EventArgs e)
	{
        if (this.CellId > 0) {
            Response.Redirect(string.Format("TransactionVolume.aspx?s={0}&c={1}", sessionList.SelectedValue, this.CellId));
        }
        else {
            Response.Redirect("TransactionVolume.aspx?s=" + sessionList.SelectedValue);
        }
    }

	protected void ValidateTransactionCount(object sender, ServerValidateEventArgs e)
	{
		if (Convert.ToInt32(transactionCountField.Text) > 0)
			e.IsValid = true;
		else
			e.IsValid = false;
	}

	protected void ValidateMediaCount(object sender, ServerValidateEventArgs e)
	{
		if (Convert.ToInt32(mediaCountField.Text) >= 0)
			e.IsValid = true;
		else
			e.IsValid = false;
	}
}
