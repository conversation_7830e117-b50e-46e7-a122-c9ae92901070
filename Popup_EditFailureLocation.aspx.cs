using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditFailureLocation : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string FailureLocationId
	{
		get { return (string)this.ViewState["f"]; }
		set { this.ViewState["f"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			deviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
			deviceTypeList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]))
			{
				this.deviceTypeList.SelectedValue = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];
				this.deviceTypeList.Enabled = false;
			}

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FAILURE_LOCATION_ID_KEY]))
			{
				this.FailureLocationId = Request.Params[DieboldConstants.FAILURE_LOCATION_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFailureLocation", this.FailureLocationId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.deviceTypeList.SelectedValue = DataFormatter.getInt32(row, "DeviceTypeId").ToString();
					this.failureLocationNameField.Text = DataFormatter.getString(row, "FailureLocationName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(FailureLocationId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertFailureLocation", this.failureLocationNameField.Text, Convert.ToInt32(this.deviceTypeList.SelectedValue));
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateFailureLocation", Convert.ToInt32(this.FailureLocationId), this.failureLocationNameField.Text, this.isActive.Checked);

		if (result == 0)
		{
			this.ErrorLabel.Visible = true;
		}
		else
		{
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditFailureLocation.aspx?{0}={1}&page={2}';CloseRadWindow(); ", DieboldConstants.DEVICE_TYPE_KEY, this.deviceTypeList.SelectedValue, this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditFailureLocation.aspx?{0}={1}';CloseRadWindow(); ", DieboldConstants.DEVICE_TYPE_KEY, this.deviceTypeList.SelectedValue), true);
		}
	}
}
