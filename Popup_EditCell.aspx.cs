using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditCell : System.Web.UI.Page
{
	public string CellId
	{
		get { return (string)this.ViewState["c"]; }
		set { this.ViewState["c"] = value; }
	}

	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			statusList.DataSource = Utility.GetCellStatusList();
			statusList.DataBind();

			testLocationList.DataSource = Utility.GetTestLocationList();
			testLocationList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.CELL_ID_KEY]))
			{
				this.CellId = Request.Params[DieboldConstants.CELL_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetCell", this.CellId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.cellNameField.Text = DataFormatter.getString(row, "CellName");
					this.testLocationList.SelectedValue = DataFormatter.getInt32(row, "TestLocationId").ToString();
					this.statusList.SelectedValue = DataFormatter.getInt32(row, "CellStatusId").ToString();

                    this.createdBy.Text = DataFormatter.getString(row, "CreatedBy");
                    if (DataFormatter.getDateTime(row, "CreatedDate") != DateTime.MinValue)
                        this.createdDate.Text = DataFormatter.getDateTime(row, "CreatedDate").ToShortDateString();

                    this.lastModifiedBy.Text = DataFormatter.getString(row, "LastModifiedBy");
                    if (DataFormatter.getDateTime(row, "LastModifiedDate") != DateTime.MinValue)
                        this.lastModifiedDate.Text = DataFormatter.getDateTime(row, "LastModifiedDate").ToShortDateString();
                }
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
        int result = 1;

		if (string.IsNullOrEmpty(this.CellId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertCell", this.cellNameField.Text, this.statusList.SelectedValue, this.testLocationList.SelectedValue, Utility.GetUserName());
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateCell", this.CellId, this.cellNameField.Text, this.statusList.SelectedValue, this.testLocationList.SelectedValue, Utility.GetUserName());

		if (result == 0)
		{
			this.ErrorLabel.Visible = true;
		}
		else
		{
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditCells.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditCells.aspx';CloseRadWindow(); ", true);
		}
	}
}
