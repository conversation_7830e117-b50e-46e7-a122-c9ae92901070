﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Web;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class QueryReport : BaseReport
{
	public QueryReport(ReportInfo repInfo) : base(repInfo, false) { }

	public override void LoadData()
	{
		DataSet ds = null;
		
		string startDate = null;
		string endDate = null;
		string minTran = null;
		string maxTran = null;

		if (this.repInfo.StartDate != DateTime.MinValue)
			startDate = this.repInfo.StartDate.ToString("MM/dd/yyyy hh:mm:ss tt");

		if (this.repInfo.EndDate != DateTime.MinValue)
			endDate = this.repInfo.EndDate.ToString("MM/dd/yyyy hh:mm:ss tt");

		if (this.repInfo.QueryInfo.MinTranId > 0)
			minTran = Convert.ToString(this.repInfo.QueryInfo.MinTranId);

		if (this.repInfo.QueryInfo.MaxTranId > 0)
			maxTran = Convert.ToString(this.repInfo.QueryInfo.MaxTranId);

		switch (this.repInfo.ReportTypeId)
		{
			case ReportHelper.ReportTypeEnum.ENA:
				ds = SqlHelper.ExecuteDataset("CQT_ENA_Report", this.repInfo.AttachedSessions[0].SessionId, this.repInfo.CellFilters[0].CellId, startDate, endDate, minTran, maxTran, this.repInfo.QueryInfo.AcceptLevel3Notes);
				break;
			case ReportHelper.ReportTypeEnum.ECRM:
				ds = SqlHelper.ExecuteDataset("CQT_ECRM_Report", this.repInfo.AttachedSessions[0].SessionId, this.repInfo.CellFilters[0].CellId, startDate, endDate, minTran, maxTran);
				break;
			case ReportHelper.ReportTypeEnum.AFD:
				ds = SqlHelper.ExecuteDataset("CQT_AFD_Report", this.repInfo.AttachedSessions[0].SessionId, this.repInfo.CellFilters[0].CellId, startDate, endDate, minTran, maxTran);
				break;
            case ReportHelper.ReportTypeEnum.AFD_CONFIGURATION_MANAGER:
                ds = SqlHelper.ExecuteDataset("CQT_AFD_Configuration_Manager_Report", this.repInfo.AttachedSessions[0].SessionId, this.repInfo.CellFilters[0].CellId, startDate, endDate, minTran, maxTran);
                break;
            case ReportHelper.ReportTypeEnum.TBT:
				ds = SqlHelper.ExecuteDataset("CQT_TBT_Report", this.repInfo.AttachedSessions[0].SessionId, this.repInfo.CellFilters[0].CellId, startDate, endDate, minTran, maxTran);
				break;
			case ReportHelper.ReportTypeEnum.STATISTICS:
				ds = SqlHelper.ExecuteDataset("CQT_Statistics_Report", this.repInfo.AttachedSessions[0].SessionId, this.repInfo.CellFilters[0].CellId, startDate, endDate, minTran, maxTran, this.repInfo.QueryInfo.FieldTypeId);
				break;
			case ReportHelper.ReportTypeEnum.BUBBLE:
				object sessionId1 = this.repInfo.AttachedSessions[0].SessionId;
				object sessionId2 = null;
				object sessionId3 = null;
				object sessionId4 = null;

				if (this.repInfo.AttachedSessions.Count > 1)
					sessionId2 = this.repInfo.AttachedSessions[1].SessionId;
				if (this.repInfo.AttachedSessions.Count > 2)
					sessionId3 = this.repInfo.AttachedSessions[2].SessionId;
				if (this.repInfo.AttachedSessions.Count > 3)
					sessionId4 = this.repInfo.AttachedSessions[3].SessionId;

				ds = SqlHelper.ExecuteDataset("CQT_Bubble_Report", sessionId1, sessionId2, sessionId3, sessionId4, startDate, endDate, minTran, maxTran);
				break;
		}
		this.dataSet = ds;

		BuildGridDisplay();
	}

	public override void BuildGridDisplay()
	{
		this.ColumnDisplayHeaders = new List<List<string>>();		
		this.RowDisplayHeaders = new List<List<string>>();
		this.RowDisplayData = new List<List<string>>();

	
		if (this.dataSet != null && this.dataSet.Tables.Count != 0)
		{
			List<string> colList = new List<string>();
			this.ColumnDisplayHeaders.Add(colList);
			foreach (DataColumn col in this.dataSet.Tables[0].Columns)
				colList.Add(col.ColumnName);

			foreach (DataRow row in this.dataSet.Tables[0].Rows)
			{
				List<string> rowData = new List<string>();

				foreach (DataColumn col in this.dataSet.Tables[0].Columns)
				{
					if (row.IsNull(col))
						rowData.Add("&nbsp;");
					else if (col.DataType == typeof(decimal))
						rowData.Add(((decimal)row[col]).ToString("0.#"));
					else
						rowData.Add(row[col].ToString());
				}

				this.RowDisplayData.Add(rowData);
			}
		}
	}

	public override void PopulateChart(Chart chart, bool includeToolTips)
	{
	}
}