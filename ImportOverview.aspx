<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ImportOverview.aspx.cs" Inherits="ImportOverview" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="dcwc" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
	
	<script type="text/javascript">
		function ToggleStartDatePopup() { showDate($find("<%= startDateField.ClientID %>")); }  
		function ToggleStartTimePopup() { showTime($find("<%= startTimeField.ClientID %>")); }  
		function ToggleEndDatePopup() { showDate($find("<%= endDateField.ClientID %>")); }
		function ToggleEndTimePopup() { showTime($find("<%= endTimeField.ClientID %>")); }  
		function showDate(picker) { picker.showPopup(); }
		function showTime(picker) { picker.showTimePopup(); }       
	    
		var openedRow = null;
		function RowClick(sender, eventArgs)
		{
			curRowClicked = eventArgs.get_itemIndexHierarchical();
			if(openedRow && curRowClicked != openedRow)
			{    
				$find("<%= ErrorGrid.MasterTableView.ClientID %>").updateItem(openedRow); //colapse previous row
				$find("<%= ErrorGrid.MasterTableView.ClientID %>").editItem(curRowClicked); //expand row
				openedRow = curRowClicked;
			}
			else if (openedRow)
			{
				$find("<%= ErrorGrid.MasterTableView.ClientID %>").updateItem(openedRow); //colapse previous row
				openedRow = null;
			}
			else
			{
				openedRow = eventArgs.get_itemIndexHierarchical();
				$find("<%= ErrorGrid.MasterTableView.ClientID %>").editItem(openedRow); //expand row
			}
		}
         
        var tableView = null;
		function pageLoad(sender, args)
		{
			tableView = $find("<%= ErrorGrid.ClientID %>").get_masterTableView();
		}
		function changePage(argument)
		{
			tableView.page(argument);
		}
		function RadNumericTextBox1_ValueChanged(sender, args)
		{
			tableView.page(sender.get_value());
		}
	</script>
	
	<asp:panel id="DefaultPanel" runat="server">
		<table width="100%" border="0" cellpadding="0" cellspacing="15">
			<tr>
				<td>
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="widgetTitle">Import Statistics</td>
							<td class="widgetTop" style="width:30%;">&nbsp;</td>
							<td class="widgetTop" style="text-align:right;">&nbsp;</td>
						</tr>
					</table>				
					<div class="widget">
						
						<div class="title">Recent Activity</div>
						<table border="0" cellpadding="0" cellspacing="0" style="width: 100%;">
							<tr>
								<td colspan="3" class="rowHeading">Date Range:</td>
							</tr>
							<tr>
								<td style="width:225px;" style="padding:5px 0px 5px 0px;">
									<telerik:RadDatePicker id="startDateField"  Runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" cssclass="entryControl" style="padding-top:1px;" Width="110px">        
										<calendar skin="Default2006" showrowheaders="false"></calendar>       
										<DatePopupButton Visible="False"></DatePopupButton>
										<DateInput onclick="ToggleStartDatePopup()"></DateInput>                           	                                             
									</telerik:RadDatePicker>
									<telerik:radtimepicker id="startTimeField" runat="server" timepopupbutton-visible="false" width="80">
									    <dateinput onclick="ToggleStartTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
									    <TimeView Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
								    </telerik:radtimepicker>
								</td>
								<td style="width:225px;" style="padding:5px 0px 5px 0px;">
									<telerik:RadDatePicker id="endDateField"  Runat="server" cssclass="entryControl" style="padding-top:1px;" Width="110px">        
										<calendar skin="Default2006" showrowheaders="false"></calendar>       
										<DatePopupButton Visible="False"></DatePopupButton>
										<DateInput onclick="ToggleEndDatePopup()"></DateInput>                           	                                             
									</telerik:RadDatePicker>
									<telerik:radtimepicker id="endTimeField" runat="server" timepopupbutton-visible="false" width="80">
									    <dateinput onclick="ToggleEndTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
									    <TimeView Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
								    </telerik:radtimepicker>
								</td>
								<td>
									<div class="goButton"><asp:linkbutton runat="server" id="loadStatsButton" onclick="LoadStatistics_Click">Load Statistics</asp:linkbutton></div></td>
								<td>
									&nbsp;</td>
							</tr>
						</table>
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td class="cellHeading" style="width:34%;">Database Records:</td>
								<td class="cellHeading" style="width:33%;">File Count:</td>
								<td class="cellHeading" style="width:33%;">File Volume:</td>
							</tr>
							<tr>
								<td style="width:34%;padding-top:5px;">
									<center>
										<dcwc:Chart ID="databaseChart" runat="server" Width="253px" Palette="Dundas" Height="140px" BackColor="White" BorderLineStyle="Solid"
											RenderType="ImageTag" ImageType="jpeg" MapEnabled="false">
											<ChartAreas>
												<dcwc:ChartArea Name="Transactions" BackColor="White" BorderColor="26, 59, 105" BorderStyle="NotSet" ShadowOffset="0">
													<AxisY>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisY>
													<AxisX>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisX>
													<AxisX2>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisX2>
													<AxisY2>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisY2>
												</dcwc:ChartArea>
											</ChartAreas>
											<Titles>
												<dcwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></dcwc:Title>
											</Titles>
											<Legends>
												<dcwc:Legend Enabled="False" BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
												</dcwc:Legend>
											</Legends>
											<UI>
												<Toolbar Enabled="False"></Toolbar>
												<ContextMenu Enabled="False"></ContextMenu>
											</UI>
											<BorderSkin SkinStyle="None" />
										</dcwc:Chart>
									</center>
								</td>
								<td style="width:33%;padding-top:5px;">
									<center>
										<dcwc:Chart ID="fileCountChart" runat="server" Width="253px" Palette="Dundas" Height="140px" BackColor="White" BorderLineStyle="Solid"
											RenderType="ImageTag" ImageType="jpeg" MapEnabled="false">
											<ChartAreas>
												<dcwc:ChartArea Name="Transactions" BackColor="White" BorderColor="26, 59, 105" BorderStyle="NotSet" ShadowOffset="0">
													<AxisY>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisY>
													<AxisX>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisX>
													<AxisX2>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisX2>
													<AxisY2>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisY2>
												</dcwc:ChartArea>
											</ChartAreas>
											<Titles>
												<dcwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></dcwc:Title>
											</Titles>
											<Legends>
												<dcwc:Legend Enabled="False" BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
												</dcwc:Legend>
											</Legends>
											<UI>
												<Toolbar Enabled="False"></Toolbar>
												<ContextMenu Enabled="False"></ContextMenu>
											</UI>
											<BorderSkin SkinStyle="None" />
										</dcwc:Chart>
									</center>
								</td>
								<td style="width:33%;padding-top:5px;">
									<center>
										<dcwc:Chart ID="fileVolumeChart" runat="server" Width="253px" Palette="Dundas" Height="140px" BackColor="White" BorderLineStyle="Solid"
											RenderType="ImageTag" ImageType="jpeg" MapEnabled="false">
											<ChartAreas>
												<dcwc:ChartArea Name="Transactions" BackColor="White" BorderColor="26, 59, 105" BorderStyle="NotSet" ShadowOffset="0">
													<AxisY>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisY>
													<AxisX>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisX>
													<AxisX2>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisX2>
													<AxisY2>
														<MajorGrid LineColor="Silver" Enabled="false" />
														<MinorGrid LineColor="Silver" Enabled="false" />
													</AxisY2>
												</dcwc:ChartArea>
											</ChartAreas>
											<Titles>
												<dcwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></dcwc:Title>
											</Titles>
											<Legends>
												<dcwc:Legend Enabled="False" BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
												</dcwc:Legend>
											</Legends>
											<UI>
												<Toolbar Enabled="False"></Toolbar>
												<ContextMenu Enabled="False"></ContextMenu>
											</UI>
											<BorderSkin SkinStyle="None" />
										</dcwc:Chart>	
									</center>
								</td>
							</tr>
							<tr id="NoMessagesRow" runat="server" visible="false">
								<td colspan="3" style="padding:10px 10px 10px 14px;">
									<asp:label id="label1" runat="server">No recent file imports were found.</asp:label>
								</td>
							</tr>
						</table>
						
						<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:30%;padding-bottom:0px;padding-top:0px;" class="title"><center><asp:Label ID="DatabaseCountLabel" runat="server"></asp:Label></center></td>
								<td style="width:30%;padding-bottom:0px;padding-top:0px;" class="title"><center><asp:Label ID="FileCountLabel" runat="server"></asp:Label></center></td>
								<td style="width:30%;padding-bottom:0px;padding-top:0px;" class="title"><center><asp:Label ID="FileVolumeLabel" runat="server"></asp:Label></center></td>
							</tr>
							<%--<tr>
								<td style="width:30%;font-size:10px;"><center><asp:linkbutton ID="TranLink" runat="server" CausesValidation="false">Database Records</asp:linkbutton></center></td>
								<td style="width:30%;font-size:10px;"><center><asp:linkbutton ID="MediaLink" runat="server" CausesValidation="false">File Count</asp:linkbutton></center></td>
								<td style="width:30%;font-size:10px;"><center><asp:linkbutton ID="ObsrvLink" runat="server" CausesValidation="false">File Volume</asp:linkbutton></center></td>
							</tr>--%>
						</table>
						<br />
						
						<br />
						<table cellpadding="0" cellspacing="0" width="100%" border="0">
							<tr>
								
								<td class="cellHeading" style="width:180px;">Jobs Pending: <asp:label id="jobsPendingLabel" runat="server"></asp:label></td>
								<td class="cellHeading" style="width:180px;">Jobs Processing: <asp:label id="jobsProcessingLabel" runat="server"></asp:label></td>
								<td class="cellHeading">&nbsp;</td>
							</tr>
						</table>
						<div class="title">Recent Errors</div>
						
						<table width="100%" border="0" cellspacing="0" cellpadding="0" style="padding:0px 0px 5px 14px;">
							<tr>
								<td style="width:130px;"><div class="goButton"><asp:linkbutton runat="server" id="ReprocessAllBtn" onclick="ReprocessAllBtn_Click">Reprocess All</asp:linkbutton></div></td>
								<td><div class="goButton"><asp:linkbutton runat="server" id="ClearAllBtn" onclick="ClearAllBtn_Click">Clear All</asp:linkbutton></div></td>
							</tr>
						</table>
									
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td>
									<asp:objectdatasource id="QueueServiceSource" runat="server" typename="QueueServiceSource" selectmethod="GetErrorList">
									</asp:objectdatasource>
									
									<telerik:radgrid id="ErrorGrid" allowmultirowselection="false" allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="25" allowpaging="True" 
										autogeneratecolumns="False" datasourceid="QueueServiceSource" showstatusbar="false" autogenerateeditcolumn="false" allowsorting="true">
										
										<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
										<headerstyle cssclass="gridHeading" />
										<itemstyle cssclass="repeaterItem" />
										<alternatingitemstyle cssclass="repeaterItemAlt" />
										<edititemstyle cssclass="gridItemSelected" />
										<clientsettings enablerowhoverstyle="true" resizing-allowcolumnresize="true">
											<selecting allowrowselect="true" />
											<clientevents onrowclick="RowClick" />
										</clientsettings>

										<mastertableview commanditemdisplay="None" datakeynames="ErrorDate" datasourceid="QueueServiceSource" editmode="EditForms" autogeneratecolumns="False" width="100%">
											<columns>
												<telerik:gridtemplatecolumn headertext="Error Date" sortexpression="ErrorDate" uniquename="ErrorDate">
													<itemtemplate>
														<div class="expandCollapseButton">&nbsp;&nbsp;&nbsp;&nbsp;<%# DataFormatter.FormatDate(Container.DataItem, "ErrorDate", "MM/dd/yyyy hh:mm tt", "")%></div>
													</itemtemplate>
												</telerik:gridtemplatecolumn>
												<telerik:gridboundcolumn datafield="FileName" headertext="File Name" sortexpression="FileName" uniquename="FileName">
													<itemstyle width="70%" />
												</telerik:gridboundcolumn>
												<%--<telerik:gridtemplatecolumn headertext="Error" sortexpression="ErrorText" uniquename="ErrorText">
													<headerstyle width="55%" />
													<itemtemplate>
														<%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "ErrorText", "", true, false), 55, 65, true)%>&nbsp;
													</itemtemplate>
												</telerik:gridtemplatecolumn>--%>
											</columns>
											
											<PagerTemplate>
												<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
													<span style="float: right; padding-top:4px;">
														Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
														of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
														items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
														to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
														of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
														<p style="margin: 0px; padding: 0px;">
														<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
														&nbsp;&nbsp;
														<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
														&nbsp;&nbsp;
															<span style="vertical-align: middle;">Page:</span>
															<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
																Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
																runat="server">
																<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
																<NumberFormat DecimalDigits="0" />
															</telerik:RadNumericTextBox>
															<span style="vertical-align: middle;">of
																<%# DataBinder.Eval(Container, "Paging.PageCount")%>
															</span>
														&nbsp;&nbsp;
														<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
														&nbsp;&nbsp;
														<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
													</p>
												</asp:Panel>
											</PagerTemplate>

											<editformsettings editformtype="template">
												<formtemplate>
													<table width="100%" cellpadding="0" cellspacing="0" style="border: solid 10px #9e9995;">
															<tr>
																<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">Error Text</td>
																<td style="padding:10px;">
																	<%# GetValue(DataFormatter.Format(Container.DataItem, "FileName")) %>
																</td>
															</tr>
															<tr>
																<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">Download File</td>
																<td style="padding: 0px 0px 0px 10px;">
																	<div class="goButton"><asp:hyperlink runat="server" id="link" target="_blank" navigateurl='<%# "DownloadFile.aspx?fn=" + Server.UrlEncode(DataFormatter.Format(Container.DataItem, "FileName")) %>'><%# DataFormatter.Format(Container.DataItem, "FileName")%></asp:hyperlink></div>
																</td>
															</tr>
															<tr>
																<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">Remove Error</td>
																<td style="padding: 0px 0px 0px 10px;">
																	<div class="goButton"><asp:linkbutton runat="server" id="transactionButton" onclientclick="return confirm('Are you sure you wish to clear this error?');" oncommand="ClearFile_Command" commandargument='<%# DataFormatter.Format(Container.DataItem, "FileName") %>'>Clear</asp:linkbutton></div>
																</td>
															</tr>
															<tr>
																<td style="width: 150px;" class="rowHeading">Reprocess File</td>
																<td style="padding: 0px 0px 0px 10px;">
																	<div class="goButton"><asp:linkbutton runat="server" id="Linkbutton1" oncommand="ReprocessFile_Command" commandargument='<%# DataFormatter.Format(Container.DataItem, "FileName") %>'>Reprocess</asp:linkbutton></div>
																</td>
															</tr>
														</table>
												</formtemplate>
											</editformsettings>
										</mastertableview>
									</telerik:radgrid>
									
									<telerik:radajaxmanager id="RadAjaxManager1" runat="server">
										<ajaxsettings>
											<telerik:ajaxsetting ajaxcontrolid="ErrorGrid">
												<updatedcontrols>
													<telerik:ajaxupdatedcontrol controlid="ErrorGrid" loadingpanelid="RadAjaxLoadingPanel1" />
													<telerik:ajaxupdatedcontrol controlid="jobsPendingLabel" loadingpanelid="RadAjaxLoadingPanel1" />
													<telerik:ajaxupdatedcontrol controlid="jobsProcessingLabel" loadingpanelid="RadAjaxLoadingPanel1" />
												</updatedcontrols>
											</telerik:ajaxsetting>
										</ajaxsettings>
									</telerik:radajaxmanager>
									<telerik:radajaxloadingpanel id="RadAjaxLoadingPanel1" runat="server" height="75px" width="75px" transparency="50">
										<img alt="Loading..." src='<%= RadAjaxLoadingPanel.GetWebResourceUrl(Page, "Telerik.Web.UI.Skins.Default.Ajax.loading.gif") %>' style="border: 0;" />
									</telerik:radajaxloadingpanel>
								</td>
							</tr>
						</table>
					</div>
				</td>
			</tr>
		</table>
	</asp:panel>
</asp:Content>


