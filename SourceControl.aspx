﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="SourceControl.aspx.cs" Inherits="SourceControl" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="b" ContentPlaceHolderId="BodyContent" Runat="Server">
	
	<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="550" reloadonshow="true" width="700" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows><telerik:radwindow runat="server" ID="EditWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Title="" Height="550" Width="700" ></telerik:radwindow></windows>
	</telerik:radwindowmanager>

	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">RDTool Source Control</td>
						<td class="widgetTop" style="width: 30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align: right;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="width: 10px;">
										&nbsp;</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<div class="widget">
					<div class="title">Registered Files</div>
					<div style="padding:0px 14px; font-size:12px;">
						
						<asp:repeater id="registeredFilesRep" runat="server">
							<headertemplate>
								<table width="100%" border="0" cellpadding="10">
							</headertemplate>
							<itemtemplate>
								<tr>
									<td class="repeaterItemAlt" style="line-height:20px;"><%# Container.DataItem %></td>
									<td class="repeaterItemAlt"><div class='goButton'><%# "<a href=\"#\" onclick=\"window.radopen('FileHistory.aspx?f=" + Container.DataItem + "', null);return false;\">View File History</a>"%></div></td>
									<td class="repeaterItemAlt"><div class='goButton'><asp:linkbutton runat="server" id="downloadBtn" oncommand="DownloadBtn_Click" commandargument="<%# Container.DataItem %>">Download File</asp:linkbutton></div></td>
									<td class="repeaterItemAlt"><div class='cancelButton'><asp:linkbutton runat="server" id="removeBtn" onclientclick="return confirm('Are you sure you wish to remove this file from the source control system?')" oncommand="RemoveBtn_Click" commandargument="<%# Container.DataItem %>">Remove</asp:linkbutton></div></td>
								</tr>
							</itemtemplate>
							<alternatingitemtemplate>
								<tr>
									<td class="repeaterItem" style="line-height:20px;"><%# Container.DataItem %></td>
									<td class="repeaterItem"><div class='goButton'><%# "<a href=\"#\" onclick=\"window.radopen('FileHistory.aspx?f=" + Container.DataItem + "', null);return false;\">View File History</a>"%></div></td>
									<td class="repeaterItem"><div class='goButton'><asp:linkbutton runat="server" id="downloadBtn" oncommand="DownloadBtn_Click" commandargument="<%# Container.DataItem %>">Download File</asp:linkbutton></div></td>
									<td class="repeaterItem"><div class='cancelButton'><asp:linkbutton runat="server" id="removeBtn" onclientclick="return confirm('Are you sure you wish to remove this file from the source control system?')" oncommand="RemoveBtn_Click" commandargument="<%# Container.DataItem %>">Remove</asp:linkbutton></div></td>		
								</tr>
							</alternatingitemtemplate>
							<footertemplate>
							</table>
						</footertemplate>
						</asp:repeater>
					</div>
					<br /><br />
				</div>
			</td>
		</tr>
	</table>
	<br /><br />
</asp:Content>

