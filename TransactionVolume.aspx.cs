using System;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class TransactionVolume : System.Web.UI.Page
{
	public bool IsUserAdmin
	{
		get { if (this.ViewState["ia"] != null) return (bool)this.ViewState["ia"]; else return Utility.IsUserAdmin(); }
		set { this.ViewState["ia"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		RadGrid1.DataBind(); //behavior issue...grid throws javascript error if no bind here.

		if (!Page.IsPostBack)
		{
			sessionList.Items.Add(new ListItem("Select...", ""));
			sessionList.DataSource = Utility.GetManualVolumeSessionList();
			sessionList.DataBind();

			cellList.Items.Add(new ListItem("Select...", ""));
			cellList.DataSource = Utility.GetCellList();
			cellList.DataBind();

			if (Request.Params["s"] != null && sessionList.Items.FindByValue(Request.Params["s"]) != null)
			{
				sessionList.SelectedValue = Request.Params["s"];
			}
            if (Request.Params["c"] != null && cellList.Items.FindByValue(Request.Params["c"]) != null) {
                cellList.SelectedValue = Request.Params["c"];
            }

            if (!this.IsUserAdmin)
				AddButtonDiv.Visible = false;
		}
		BindData();
    }

	private void BindData()
	{
		object cellId = null;
		if (!string.IsNullOrEmpty(cellList.SelectedValue))
			cellId = Convert.ToInt32(cellList.SelectedValue);

		if (!string.IsNullOrEmpty(sessionList.SelectedValue))
		{
			RadGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetTransactionVolumeBySession", Convert.ToInt32(sessionList.SelectedValue), cellId);
			RadGrid1.DataBind();
		}
	}

	protected void AddButton_Click(object sender, EventArgs e)
	{
		HttpContext.Current.Items["SessionId"] = sessionList.SelectedValue;
		Server.Transfer("EditTransactionVolume.aspx");
	}

	protected void EditButton_Click(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null && e.CommandName != null)
		{
			HttpContext.Current.Items["SessionId"] = sessionList.SelectedValue;
			HttpContext.Current.Items["CellId"] = e.CommandName;
			HttpContext.Current.Items["TranDate"] = e.CommandArgument;

			Server.Transfer("EditTransactionVolume.aspx");
		}
	}

	protected void DeleteButton_Click(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null && e.CommandName != null)
		{
			SqlHelper.ExecuteNonQuery("RPT_DeleteTransactionVolume", this.sessionList.SelectedValue, e.CommandName, e.CommandArgument);
			BindData();
		}
	}
	
}
