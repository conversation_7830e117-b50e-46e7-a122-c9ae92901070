using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class LegacyParetoReport : BaseReport
{
	public LegacyParetoReport(ReportInfo repInfo) : base(repInfo, true) { }

    public override void InitializeChart(Chart chart, string pageOrControlName)
    {
        base.InitializeChart(chart, pageOrControlName);
        chart.Customize += new CustomizeEventHandler(chart_Customize);
    }

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        string mdxSelect = null;
        string mdxCategory = null;
        string mdxFromWhere = null;
        string timeHier = OLAPHelper.GetTimeHier(repInfo.ParetoInfo.GroupingId);
        DateTime startDate = repInfo.StartDate;
        DateTime endDate = repInfo.EndDate;
        string countMeasurePart = null;
        bool splitByCell = this.repInfo.ParetoInfo.SplitByCell;

        if (repInfo.DimensionName.StartsWith("[Event]."))
            countMeasurePart = "Event";
        else if (repInfo.DimensionName.StartsWith("[Event Status]."))
            countMeasurePart = "Status";
        else if (repInfo.DimensionName.StartsWith("[Event Reason]."))
            countMeasurePart = "Reason";
        else
            countMeasurePart = "Observation";

        mdxSetup.Append(string.Format("WITH {0}\r\n", OLAPHelper.BuildMdxFilteredTime(repInfo.ParetoInfo.GroupingId, startDate, endDate, "FilteredTime")));

        mdxSetup.Append(string.Format("MEMBER [Time].[{0}].[Total] AS '0', FORMAT_STRING = \"#,##0\"\r\n", timeHier, timeHier));

        mdxSetup.Append(string.Format("MEMBER [{0} Cnt] AS 'IIF([Time].[{1}].CurrentMember IS [Time].[{2}].[Total], SUM(FilteredTime, [Measures].[{3} Cnt]),",
            countMeasurePart, timeHier, timeHier, countMeasurePart));

        if (DateTime.MinValue.Equals(startDate) && DateTime.MinValue.Equals(endDate))
            mdxSetup.Append(string.Format("SUM(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]), [Measures].[{2} Count]))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, countMeasurePart));
        else if (!DateTime.MinValue.Equals(startDate) && DateTime.MinValue.Equals(endDate))
            mdxSetup.Append(string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue >= CDate(\"{3}\")), [Measures].[{4} Count]))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, timeHier, startDate.ToString("yyyy-MM-dd HH:mm:ss"), countMeasurePart));
        else if (DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            mdxSetup.Append(string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue < CDate(\"{3}\")), [Measures].[{4} Count]))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, timeHier, endDate.ToString("yyyy-MM-dd HH:mm:ss"), countMeasurePart));
        else // (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            mdxSetup.Append(string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue >= CDate(\"{3}\") AND [Time].[{4}].CurrentMember.MemberValue < CDate(\"{5}\")), [Measures].[{6} Count]))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, timeHier, startDate.ToString("yyyy-MM-dd HH:mm:ss"), timeHier, endDate.ToString("yyyy-MM-dd HH:mm:ss"), countMeasurePart));

        mdxSelect = string.Format("SELECT NON EMPTY {{[Time].[{0}].[Total], FilteredTime}} on columns, \r\n", timeHier);

        if (splitByCell)
        {
            string cellList = OLAPHelper.BuildMdxCellTuple(true, repInfo.CellFilters);
            if (string.IsNullOrEmpty(cellList))
                cellList = "[Test Location].[Location-Cell].[Cell]";
            mdxSetup.Append(string.Format("SET FilteredCells AS 'Filter({0}, SUM(FilteredTime, [Measures].[{1} Cnt]) > 0)'\r\n", cellList, countMeasurePart));

            mdxCategory = string.Format("NON EMPTY (Order({0}, [Measures].[{1} Cnt], BDESC), FilteredCells) on rows \r\n",
                OLAPHelper.BuildMdxLevelTuple(true, repInfo.DimensionName, repInfo.DimensionMembers, false), countMeasurePart);
        }
        else
        {
            mdxCategory = string.Format("NON EMPTY Order({0}, [Measures].[{1} Cnt], BDESC) on rows \r\n",
				OLAPHelper.BuildMdxLevelTuple(true, repInfo.DimensionName, repInfo.DimensionMembers, false), countMeasurePart);
        }

        if (splitByCell)
        {
            if (repInfo.AttachedSessions != null && repInfo.AttachedSessions.Count > 0)
                mdxFromWhere = string.Format("FROM [Transactions] WHERE ([Measures].[{0} Cnt], {1})", countMeasurePart, OLAPHelper.BuildMdxWhereTuples(true, repInfo.AttachedSessions, null, null));
            else
                mdxFromWhere = string.Format("FROM [Transactions] ");
        }
        else
        {
            if ((repInfo.AttachedSessions != null && repInfo.AttachedSessions.Count > 0) || (repInfo.CellFilters != null && repInfo.CellFilters.Count > 0))
                mdxFromWhere = string.Format("FROM [Transactions] WHERE ([Measures].[{0} Cnt], {1})", countMeasurePart, OLAPHelper.BuildMdxWhereTuples(true, repInfo.AttachedSessions, repInfo.CellFilters, null));
            else
                mdxFromWhere = string.Format("FROM [Transactions] ");
        }

        string mdx = mdxSetup.ToString() + mdxSelect + mdxCategory + mdxFromWhere;

        ExecuteMdx(mdx);
        BuildGridDisplay();
    }

    public override void PopulateChart(Chart chart, bool includeToolTips)
    {
        TupleCollection rowTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection colTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[1].Set.Hierarchies;
        double maxY = 0;

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
            priorSeries.Add(series.Name, series);

        chart.Series.Clear();

        if (this.repInfo.ParetoInfo.SplitByCell)
        {
            // Series per Cell and Date Grouping
            // StackedGroupName matching Date Grouping
            // Must have a point for each category, cell and date grouping combination
            List<string> categoryList = new List<string>();
            List<string> cellList = new List<string>();
            List<string> legendList = new List<string>();
            Dictionary<string, int> columnLookup = new Dictionary<string, int>();
            Dictionary<string, double> maxLookup = new Dictionary<string, double>();

            for (int col = 0; col < colTuples.Count; col++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
                string catName = colTuple.Members[0].Caption;
                if (!categoryList.Contains(catName))
                {
                    if (repInfo.ParetoInfo.MaxColumns == 0 || categoryList.Count < repInfo.ParetoInfo.MaxColumns)
                        categoryList.Add(catName);
                }

                string cellName = colTuple.Members[1].Caption;
                if (!cellList.Contains(cellName))
                    cellList.Add(cellName);

                columnLookup.Add(string.Format("{0}~{1}", catName, cellName), col);
            }

            for (int row = 0; row < rowTuples.Count; row++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];
                string dateGroup = rowTuple.Members[0].Caption;

                foreach (string cellName in cellList)
                {
                    Series series = null;
                    string seriesName = cellName;

                    if (priorSeries.ContainsKey(seriesName))
                    {
                        series = priorSeries[seriesName];
                        priorSeries.Remove(series.Name);
                        series.Points.Clear();
                    }
                    else
                    {
                        series = new Series(seriesName);
                        series["DrawingStyle"] = "Cylinder";
                        series.BorderColor = Color.FromArgb(26, 59, 105);
                        series.ShadowOffset = 2;
                        series.Type = SeriesChartType.StackedColumn;
                    }

                    if (string.Compare(dateGroup, "Total") == 0)
                    {
                        if (!repInfo.ParetoInfo.IncludeTotal)
                            continue;
                    }

                    series["StackedGroupName"] = dateGroup;
                    if (legendList.Contains(cellName))
                    {
                        series.Name = string.Format("{0}~{1}", cellName, row);
                        series.ShowInLegend = false;                        
                    }
                    else
                    {
                        series.ShowInLegend = true;
                        legendList.Add(cellName);
                    }

                    foreach (string category in categoryList)
                    {
                        if (categoryList.Contains(category))
                        {
                            DataPoint dp = null;
                            string formattedValue = null;
                            string colKey = string.Format("{0}~{1}", category, cellName);
                            string maxKey = string.Format("{0}~{1}", category, dateGroup);
                            if (columnLookup.ContainsKey(colKey))
                            {
                                Cell cell = cellSet.Cells[row, columnLookup[colKey]];
                                object valObj = cell.Value;
                                formattedValue = cell.FormattedValue;

                                if (valObj == null)
                                    dp = new DataPoint(0, 0);
                                else
                                    dp = new DataPoint(0, double.Parse(cell.Value.ToString()));
                            }
                            else
                            {
                                dp = new DataPoint(0, 0);
                                formattedValue = "0";
                            }

                            if (!maxLookup.ContainsKey(maxKey))
                                maxLookup.Add(maxKey, 0.0);
                            maxLookup[maxKey] += dp.GetValueY(0);

                            dp.AxisLabel = string.Format("{0}", category);
                            if (includeToolTips)
                                dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}\r\n{3}", formattedValue, category, cellName, dateGroup);

                            series.Points.Add(dp);
                        }
                    }
                    
                    chart.Series.Add(series);
                }
            }

            maxY = 0.0;
            foreach (double curMax in maxLookup.Values)
            {
                maxY = (maxY > curMax) ? maxY : curMax;
            }
        }
        else
        {
            // Build chart series data
            for (int row = 0; row < rowTuples.Count; row++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];

                Series series = null;
                string seriesName = rowTuple.Members[0].Caption;

                if (priorSeries.ContainsKey(seriesName))
                {
                    series = priorSeries[seriesName];
                    priorSeries.Remove(series.Name);
                    series.Points.Clear();
                }
                else
                {
                    series = new Series(seriesName);
                    series["DrawingStyle"] = "Cylinder";
                    series.BorderColor = Color.FromArgb(26, 59, 105);
                    series.ShadowOffset = 2;

                    series.Type = SeriesChartType.Column;
                }

                if (string.Compare(series.Name, "Total") == 0)
                {
                    if (!repInfo.ParetoInfo.IncludeTotal)
                        continue;
                }

                for (int col = 0; col < colTuples.Count; col++)
                {
                    if (repInfo.ParetoInfo.MaxColumns == 0 || col < repInfo.ParetoInfo.MaxColumns)
                    {
                        Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
                        Cell cell = cellSet.Cells[row, col];

                        object valObj = cell.Value;
                        DataPoint dp = null;
                        if (valObj == null)
                            dp = new DataPoint(0, 0);
                        else
                            dp = new DataPoint(0, double.Parse(cell.Value.ToString())); ;

                        maxY = (maxY > dp.GetValueY(0)) ? maxY : dp.GetValueY(0);
                        dp.AxisLabel = string.Format("{0}", colTuple.Members[0].Caption);

                        if (includeToolTips)
                            dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}", cell.FormattedValue, colTuple.Members[0].Caption, series.Name);

                        series.Points.Add(dp);
                    }
                    else
                    {
                        break;
                    }
                }

                chart.Series.Add(series);
            }
        }

        chart.ChartAreas[0].AxisY.Minimum = 0;
        chart.ChartAreas[0].AxisY.Maximum = RoundAxis(maxY, 100);

		chart.ChartAreas[0].AxisX.Interval = 1;
        chart.ChartAreas[0].AxisX.Margin = true;

        ResetAxisMarks(chart.ChartAreas[0].AxisY);

        chart.Titles["Title1"].Text = repInfo.ChartTitle;

        if (string.IsNullOrEmpty(chart.ChartAreas[0].AxisY.Title))
        {
            chart.ChartAreas[0].AxisY.Title = "Occurrences";
            chart.ChartAreas[0].AxisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
        }
    }

    private void chart_Customize(Chart chart)
    {
        Dictionary<string, Color> colorLookup = new Dictionary<string, Color>();
        foreach (Series series in chart.Series)
        {
            if (series.ShowInLegend && series.Name.IndexOf("~") < 0)
            {
                colorLookup.Add(series.Name, series.Color);
            }
        }

        foreach (Series series in chart.Series)
        {
            if (!series.ShowInLegend && series.Name.IndexOf("~") >= 0)
            {
                string baseName = series.Name.Substring(0, series.Name.IndexOf("~"));
                if (colorLookup.ContainsKey(baseName))
                    series.Color = colorLookup[baseName];
            }
        }
    }


}
