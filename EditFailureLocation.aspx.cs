using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class EditFailureLocation : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		this.EntityNavBar.ItemChangedEvent += new CommandEventHandler(EntityNavBar_ItemChangedEvent);
		this.AddButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditFailureLocation.aspx?{0}={1}', null); return false;", DieboldConstants.DEVICE_TYPE_KEY, this.EntityNavBar.DeviceTypeId));
		
		BindData();

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
	}

	void EntityNavBar_ItemChangedEvent(object sender, CommandEventArgs e)
	{
		DataGrid1.CurrentPageIndex = 0;
		this.AddButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditFailureLocation.aspx?{0}={1}', null); return false;", DieboldConstants.DEVICE_TYPE_KEY, this.EntityNavBar.DeviceTypeId));
		
		BindData();
	}

	private void BindData()
	{
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_FailureLocationsByDeviceType", this.EntityNavBar.DeviceTypeId, true);
		DataGrid1.DataBind();
	}
}
