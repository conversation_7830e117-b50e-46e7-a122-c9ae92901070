using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class NewReportWizard_Distribution : System.Web.UI.Page
{
	private string[] SupressedDimensions = new string[] { "Cell", "Session", "Setting", "Setting 2", "Setting 3", "Agilis", "Device", "Event", "Event Status", "Event Reason", "Failure Location", "Failure Type", "Investigation Area", "Measures", 
        "Metric", "Metric Values", "Engineering Field" , "Engineering Values", "Engineering Note Number", "Engineering Index Number", "Operator", "Severity", "Test Location", "Time", "Test Session", "Censoring", 
		"Distribution Values", "Statistic", "Event Type", "Sensor", "File Number", "Media Number", "Command Number" };

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			this.RepInfo = Utility.GetReportInfoFromTransfer();
			if (this.RepInfo == null)
				Response.Redirect("popuperror.aspx");

			reportLabel.Text = this.RepInfo.ReportName;
			reportTypeLabel.Text = this.RepInfo.ReportTypeName;
            subReportDimCheck.Checked = this.RepInfo.SubReportsByDimension;
			//splitByDevice.Checked = this.RepInfo.DistributionInfo.SplitByDevice;

			if (this.RepInfo.DistributionInfo.Target != Decimal.MinValue)
				targetField.Text = this.RepInfo.DistributionInfo.Target.ToString("0.#");
            ShowHistogram.Checked = this.RepInfo.DistributionInfo.ShowHistogram;
            ShowNormal.Checked = this.RepInfo.DistributionInfo.ShowNormalDistribution;
			if (this.RepInfo.DistributionInfo.LowerSpecLimit != Decimal.MinValue)
				lowerSpecLimitField.Text = this.RepInfo.DistributionInfo.LowerSpecLimit.ToString("0.#");
			if (this.RepInfo.DistributionInfo.UpperSpecLimit != Decimal.MinValue)
				upperSpecLimitField.Text = this.RepInfo.DistributionInfo.UpperSpecLimit.ToString("0.#");

			if (this.RepInfo.DistributionInfo.FilterStartValue != Decimal.MinValue)
				filterStartField.Text = this.RepInfo.DistributionInfo.FilterStartValue.ToString("0.#");
            if (this.RepInfo.DistributionInfo.FilterEndValue != Decimal.MinValue)
				filterEndField.Text = this.RepInfo.DistributionInfo.FilterEndValue.ToString("0.#");

			if (this.RepInfo.DistributionInfo.XAxisStartValue != Decimal.MinValue)
				xAxisStartField.Text = this.RepInfo.DistributionInfo.XAxisStartValue.ToString("0.#");
			if (this.RepInfo.DistributionInfo.XAxisEndValue != Decimal.MinValue)
				xAxisEndField.Text = this.RepInfo.DistributionInfo.XAxisEndValue.ToString("0.#");

			if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
				this.dimensionSel.PopulateDimensions(true, SupressedDimensions);
			else
				this.dimensionSel.PopulateDimensions(false, SupressedDimensions);

			this.dimensionSel.DimensionName = this.RepInfo.DimensionName;
			this.dimensionSel.CheckedDimensionMembers = this.RepInfo.DimensionMembers;
			this.dimensionSel.UpdateTreeDisplay();
			dimensionSel.Focus();
		}
    }

	protected void NextButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			//Set Page Values
			this.RepInfo.DimensionName = this.dimensionSel.DimensionName;
			this.RepInfo.DimensionMembers = this.dimensionSel.CheckedDimensionMembers;

			if (!string.IsNullOrEmpty(targetField.Text.Trim()))
				this.RepInfo.DistributionInfo.Target = Convert.ToDecimal(targetField.Text.Trim());

            this.RepInfo.DistributionInfo.ShowHistogram = ShowHistogram.Checked;
            this.RepInfo.DistributionInfo.ShowNormalDistribution = ShowNormal.Checked;

            if (!string.IsNullOrEmpty(lowerSpecLimitField.Text.Trim()))
                this.RepInfo.DistributionInfo.LowerSpecLimit = Convert.ToDecimal(lowerSpecLimitField.Text.Trim());
            else
                this.RepInfo.DistributionInfo.LowerSpecLimit = decimal.MinValue;

            if (!string.IsNullOrEmpty(upperSpecLimitField.Text.Trim()))
                this.RepInfo.DistributionInfo.UpperSpecLimit = Convert.ToDecimal(upperSpecLimitField.Text.Trim());
            else
                this.RepInfo.DistributionInfo.UpperSpecLimit = decimal.MinValue;

			if (!string.IsNullOrEmpty(filterStartField.Text.Trim()))
				this.RepInfo.DistributionInfo.FilterStartValue = Convert.ToDecimal(filterStartField.Text.Trim());
			else
				this.RepInfo.DistributionInfo.FilterStartValue = decimal.MinValue;
			 
			if (!string.IsNullOrEmpty(filterEndField.Text.Trim()))
				this.RepInfo.DistributionInfo.FilterEndValue = Convert.ToDecimal(filterEndField.Text.Trim());
			else
				this.RepInfo.DistributionInfo.FilterEndValue = decimal.MinValue;

			if (!string.IsNullOrEmpty(xAxisStartField.Text.Trim()))
				this.RepInfo.DistributionInfo.XAxisStartValue = Convert.ToDecimal(xAxisStartField.Text.Trim());
			else
				this.RepInfo.DistributionInfo.XAxisStartValue = decimal.MinValue;

			if (!string.IsNullOrEmpty(xAxisEndField.Text.Trim()))
				this.RepInfo.DistributionInfo.XAxisEndValue = Convert.ToDecimal(xAxisEndField.Text.Trim());
			else
				this.RepInfo.DistributionInfo.XAxisEndValue = decimal.MinValue;

            this.RepInfo.SubReportsByDimension = subReportDimCheck.Checked;
			//this.RepInfo.DistributionInfo.SplitByDevice = splitByDevice.Checked;

			Utility.SetReportInfoForTransfer(this.RepInfo);
			Response.Redirect("NewReportWizard_Format.aspx");
		}
	}

	protected void ValidateChecksFields(object sender, ServerValidateEventArgs e)
    {
        if (e != null)
            e.IsValid = (ShowHistogram.Checked || ShowNormal.Checked);
    }
}
