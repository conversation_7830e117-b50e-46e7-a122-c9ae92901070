﻿(function($) {
    $.fn.initListSelect = function(callerSettings) {
        var items = $(this);
        var settings = $.extend({
            availableHolder: null,
            sortAvailable: false,
            selectedHolder: null,
            sortSelected: false,
            sortOrderList: null,
            addButton: null,
            addItems: null,
            removeButton: null,
            checkAvailableButton: null,
            uncheckAvailableButton: null,
            checkSelectedButton: null,
            uncheckSelectedButton: null,
            upButton: null,
            downButton: null,
            buildItem: function(item) { return '<div>' + item.Name + '</div>' },
            isSelected: function(item) { return item.Selected; },
            onListChanged: null
        }, callerSettings || {});
        if (settings.availableHolder)
            settings.availableHolder = $(settings.availableHolder);
        if (settings.selectedHolder)
            settings.selectedHolder = $(settings.selectedHolder);
        if (settings.sortOrderList)
            settings.sortOrderList = $(settings.sortOrderList);
        if (settings.addButton)
            settings.addButton = $(settings.addButton);
        if (settings.removeButton)
            settings.removeButton = $(settings.removeButton);
        if (settings.checkAvailableButton)
            settings.checkAvailableButton = $(settings.checkAvailableButton);
        if (settings.uncheckAvailableButton)
            settings.uncheckAvailableButton = $(settings.uncheckAvailableButton);
        if (settings.checkSelectedButton)
            settings.checkSelectedButton = $(settings.checkSelectedButton);
        if (settings.uncheckSelectedButton)
            settings.uncheckSelectedButton = $(settings.uncheckSelectedButton);
        if (settings.upButton)
            settings.upButton = $(settings.upButton);
        if (settings.downButton)
            settings.downButton = $(settings.downButton);

        $.each(items, function(index, item) {
            if (settings.isSelected(item)) {
                $(settings.buildItem(item)).appendTo(settings.selectedHolder);
            } else {
                $(settings.buildItem(item)).appendTo(settings.availableHolder);
            }
        });

        if (settings.sortOrderList) {
            $(settings.sortOrderList).change(function() {
                if (settings.sortOrderList.val() == 'cat') {
                    $.tinysort.defaults.attr = 'CatSort';
                    settings.availableHolder.removeClass('AlphaSort').addClass('CatSort');
                }
                else {
                    $.tinysort.defaults.attr = '';
                    settings.availableHolder.removeClass('CatSort').addClass('AlphaSort');
                }
                $('div', settings.availableHolder).tsort();
                if (settings.onListChanged)
                    settings.onListChanged();
            });
        }

        if (settings.sortOrderList) {
            if (settings.sortOrderList.val() == 'cat') {
                $.tinysort.defaults.attr = 'CatSort';
                settings.availableHolder.removeClass('AlphaSort').addClass('CatSort');
            }
            else {
                $.tinysort.defaults.attr = '';
                settings.availableHolder.removeClass('CatSort').addClass('AlphaSort');
            }
            $('div', settings.availableHolder).tsort();
        } else if (settings.sortAvailable)
            $('div', settings.availableHolder).tsort();

        if (settings.sortSelected)
            $('div', settings.selectedHolder).tsort();

        $('label:has(:checkbox)', settings.availableHolder)
	    .add('label:has(:checkbox)', settings.selectedHolder)
	    .dblclick(function(e) {
	        if ($(this).hasClass('valueHeader'))
	            $(this).remove();
	        else {
	            if ($(this).parents('#' + settings.availableHolder.attr('id')).size() > 0) {
	                if (settings.addItems) {
	                    settings.addItems($(':checkbox', this));
	                } else {
	                    $(':checkbox', this).attr('checked', false).parents('div:first').appendTo(settings.selectedHolder);
	                }
	                if (settings.sortSelected)
	                    $('div', settings.selectedHolder).tsort();
	            } else {
	                $(':checked', this).attr('checked', false).parents('div:first').appendTo(settings.availableHolder);
	                if (settings.sortAvailable)
	                    $('div', settings.availableHolder).tsort();
	            }
	        }
	        e.stopPropagation();
	        e.preventDefault();

	        settings.selectedHolder.hide().show(); // Fix IE redraw issue
	        if (document.selection && document.selection.empty) // IE fix for selections
	            document.selection.empty();

	        if (settings.onListChanged)
	            settings.onListChanged();
	    });

        if (settings.addButton) {
            $(settings.addButton).click(function(e) {
                if (settings.addItems) {
                    settings.addItems($(':checked', settings.availableHolder));
                } else {
                    $.each($(':checked', settings.availableHolder), function(index, item) {
                        $(item).attr('checked', false).parents('div:first').appendTo(settings.selectedHolder);
                    });
                }
                if (settings.sortAvailable)
                    $('div', settings.availableHolder).tsort();
                if (settings.sortSelected)
                    $('div', settings.selectedHolder).tsort();
                e.stopPropagation();
                e.preventDefault();
                settings.selectedHolder.hide().show(); // Fix IE redraw issue
                if (settings.onListChanged)
                    settings.onListChanged();
            });
        }

        if (settings.removeButton) {
            $(settings.removeButton).click(function(e) {
                $('.valueHeader:has(:checked)', settings.selectedHolder).remove();
                $.each($(':checked', settings.selectedHolder), function(index, item) {
                    $(item).attr('checked', false).parents('div:first').appendTo(settings.availableHolder);
                });
                if (settings.sortAvailable)
                    $('div', settings.availableHolder).tsort();
                if (settings.sortSelected)
                    $('div', settings.selectedHolder).tsort();
                e.stopPropagation();
                e.preventDefault();
                settings.selectedHolder.hide().show(); // Fix IE redraw issue
                if (settings.onListChanged)
                    settings.onListChanged();
            });
        }

        if (settings.checkAvailableButton) {
            $(settings.checkAvailableButton).click(function(e) {
                $(':checkbox', settings.availableHolder).attr('checked', true);
                e.stopPropagation();
                e.preventDefault();
            });
        }

        if (settings.uncheckAvailableButton) {
            $(settings.uncheckAvailableButton).click(function(e) {
                $(':checkbox', settings.availableHolder).attr('checked', false);
                e.stopPropagation();
                e.preventDefault();
            });
        }

        if (settings.checkSelectedButton) {
            $(settings.checkSelectedButton).click(function(e) {
                $(':checkbox', settings.selectedHolder).attr('checked', true);
                e.stopPropagation();
                e.preventDefault();
            });
        }

        if (settings.uncheckSelectedButton) {
            $(settings.uncheckSelectedButton).click(function(e) {
                $(':checkbox', settings.selectedHolder).attr('checked', false);
                e.stopPropagation();
                e.preventDefault();
            });
        }

        if (settings.upButton) {
            $(settings.upButton).click(function(e) {
                $.each($('div:has(:checked)', settings.selectedHolder), function(index, item) {
                    var sib = $(item).prev(':last');
                    if ($(':checked', sib).size() == 0)
                        sib.before($(item));
                });
                e.stopPropagation();
                e.preventDefault();
                settings.selectedHolder.hide().show(); // Fix IE redraw issue
                if (settings.onListChanged)
                    settings.onListChanged();
            });
        }

        if (settings.downButton) {
            $(settings.downButton).click(function(e) {
                var firstChecked = null;
                $.each($('div', settings.selectedHolder), function(index, item) {
                    if ($(':checked', $(item)).size() == 0) {
                        if (firstChecked)
                            firstChecked.before($(item));
                        firstChecked = null;
                    } else {
                        if (firstChecked == null)
                            firstChecked = $(item);
                    }
                });
                e.stopPropagation();
                e.preventDefault();
                settings.selectedHolder.hide().show(); // Fix IE redraw issue
                if (settings.onListChanged)
                    settings.onListChanged();
            });
        }
    };
})(jQuery);
