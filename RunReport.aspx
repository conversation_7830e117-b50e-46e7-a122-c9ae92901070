<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="RunReport.aspx.cs" Inherits="RunReport" %>
<%@ Import Namespace="Microsoft.AnalysisServices.AdomdClient" %>
<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="dcwc" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
	
<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">

	function distroValueClick(val) {
		if (val) {
			var hidCntrl = document.getElementById('<%= distroClickedVal.ClientID %>');
			var hidBtn = document.getElementById('<%= distroHidBtn.ClientID %>');
			hidCntrl.value = val;

			hidBtn.click();
		}
	}
	
</script>

<asp:panel id="InitialLoadingPanel" runat="server" visible="true">
	<div style="padding-top:50px;"><center><img src="images/loading.gif" alt="Loading Report..." border="0" /></center></div>
</asp:panel>
<asp:panel id="MainPanel" runat="server" visible="false">
	<telerik:radwindowmanager runat="server" ID="RadWindowManager1" height="680" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="SaveReportWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Height="600" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="EditWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" Height="680" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="promptsessions.aspx" Height="400" Width="600" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="SnapshotWindow" VisibleOnPageLoad="false" NavigateUrl="reportsnapshot.aspx" Height="592" Width="720" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="ScheduleWindow" VisibleOnPageLoad="false" NavigateUrl="popup_editscheduledreport.aspx" height="630" width="620" ></telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>
				
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"> 
					<tr>
						<td class="widgetTitle">Run Report</td>
						<td class="widgetTop" style="width:155px;">&nbsp;</td>
						<td class="widgetTop" style="width:260px;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="width:55px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="printButton" onclick="PrintButton_Click">Print</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>
									<td style="width:65px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="exportButton" onclick="ExportButton_Click">Export</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>
									<%--<td style="width:65px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="scheduleBtn" onclick="ScheduleButton_Click">Schedule</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>--%>
									<td style="width:55px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="snapshotButton" onclick="SnapshotButton_Click">Snapshot</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>
									<td style="width:55px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="viewTranButton" onclick="ViewTranButton_Click">Transactions</asp:linkbutton></div></td>
								</tr>
							</table>
						</td>
						<td class="widgetTop">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget" style="padding-bottom:20px;">
					<table width="700px" border="0" cellpadding="0" cellspacing="10" runat="server" id="SnapshotTable" visible="false">
						<tr>
							<td class="rowHeading" style="width:140px;">Snapshot Name:</td>
							<td><asp:label id="SnapshotName" runat="server"></asp:label></td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:140px;">Snapshot Date:</td>
							<td><asp:label id="SnapshotDate" runat="server"></asp:label></td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:140px;">Description:</td>
							<td><asp:label id="SnapshotDesc" runat="server"></asp:label></td>
						</tr>
					</table>
					<table width="700px" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="title"><asp:label id="reportNameLabel" runat="server"></asp:label></td>
							<td>&nbsp;</td>
							<td style="width:90px;text-align:right;padding-right:10px;"><div class="goButton" id="EditBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="EditButton_Click" id="editButton">Edit Report</asp:linkbutton></div></td>
							<td style="width:95px;text-align:right;padding-right:10px;"><div class="goButton" id="SaveBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save Report</asp:linkbutton></div></td>
						</tr>
					</table>
                    <dcwc:Chart ID="chartCntrl" runat="server" Width="700px" Palette="Dundas" Height="450px" BackColor="WhiteSmoke" BackGradientEndColor="White" 
                                BackGradientType="DiagonalLeft" BorderLineColor="26, 59, 105" BorderLineStyle="Solid">                                
                        <ChartAreas><dcwc:ChartArea Name="Default" BackColor="White" BorderColor="26, 59, 105" BorderStyle="Solid" ShadowOffset="2">
                            <AxisY>
                                <MajorGrid LineColor="Silver" />
                                <MinorGrid LineColor="Silver" />
                            </AxisY>
                            <AxisX>
                                <MajorGrid LineColor="Silver" />
                                <MinorGrid LineColor="Silver" />
                            </AxisX>
                            <AxisX2>
                                <MajorGrid LineColor="Silver" />
                                <MinorGrid LineColor="Silver" />
                            </AxisX2>
                            <AxisY2>
                                <MajorGrid LineColor="Silver" />
                                <MinorGrid LineColor="Silver" />
                            </AxisY2>
                        </dcwc:ChartArea></ChartAreas>
                        <Titles>
                            <dcwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></dcwc:Title>
                        </Titles>
                        <Legends>
                            <dcwc:Legend BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
                            </dcwc:Legend>
                        </Legends>
                        <UI>
                            <Toolbar Enabled="True" Placement="OutsideChart" BorderColor="26, 59, 105"><BorderSkin SkinStyle="Emboss" PageColor="Transparent" /></Toolbar>
                            <ContextMenu Enabled="True">
                                <Items>
                                    <dcwc:CommandUIItem CommandName="Properties" />
                                    <dcwc:CommandUIItem CommandName="Separator" />
                                    <dcwc:CommandUIItem CommandName="SelectChartGroup" />
                                    <dcwc:CommandUIItem CommandName="Separator" />
                                    <dcwc:CommandUIItem CommandName="ToggleMajorGridLines" />
                                    <dcwc:CommandUIItem CommandName="ToggleMinorGridLines" />
                                    <dcwc:CommandUIItem CommandName="ToggleInterlacedAxes" />
                                    <dcwc:CommandUIItem CommandName="ToggleReverseAxes" />
                                    <dcwc:CommandUIItem CommandName="Separator" />
                                    <dcwc:CommandUIItem CommandName="BringToFront" />
                                    <dcwc:CommandUIItem CommandName="SendToBack" />
                                    <dcwc:CommandUIItem CommandName="Separator" />
                                </Items>
                            </ContextMenu>
                        </UI>
                        <BorderSkin FrameBackColor="CornflowerBlue" FrameBackGradientEndColor="CornflowerBlue" SkinStyle="Emboss" />
                    </dcwc:Chart>
					<br />
				</div>
				
				<div class="widget" style="padding-bottom:20px; text-align:center;">
		            <asp:Panel ID="GridBindingPanel" runat="server">
						<table border="0" cellpadding="0" cellspacing="1">
	                        <asp:Repeater ID="ColumnHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders %>'>
	                            <ItemTemplate>
	                                <tr>
	                                    <asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
	                                        <ItemTemplate>
	                                            <td><%# "&nbsp;" %></td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                    <asp:Repeater ID="ColumnHeaderRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders[ColumnHeaderLevelRepeater.Items.Count] %>'>
	                                        <ItemTemplate>
	                                            <td class="cellHeading" style="padding:4px 2px 4px 2px;"><%# Container.DataItem %></td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                </tr>
	                            </ItemTemplate>
	                        </asp:Repeater>
	                        <asp:Repeater ID="GridRowsRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayData %>'>
	                            <ItemTemplate>
	                                <tr style="background-color: #f9f9f9;">
	                                    <asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
	                                        <ItemTemplate>
	                                            <td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                    <asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
	                                        <ItemTemplate>
	                                            <td <%# (((string)Container.DataItem) != null && ((string)Container.DataItem).StartsWith("~")) ? "style='color:red;'" : "" %>>
                                                    <%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : ((string)Container.DataItem).Replace("~", "")) %>
	                                            </td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                </tr>
	                            </ItemTemplate>
	                            <alternatingitemtemplate>
								    <tr style="background-color: #e9e9e9;">
	                                    <asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
	                                        <ItemTemplate>
	                                            <td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                    <asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
	                                        <ItemTemplate>
	                                            <td <%# (((string)Container.DataItem) != null && ((string)Container.DataItem).StartsWith("~")) ? "style='color:red;'" : "" %>>
                                                    <%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : ((string)Container.DataItem).Replace("~", "")) %>
	                                            </td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                </tr>
	                            </alternatingitemtemplate>
	                            <footertemplate>
	                            </footertemplate>
	                        </asp:Repeater>
					    </table> 
		            </asp:Panel>
				</div>
			</td>
		</tr>
	</table>
	<input id="distroClickedVal" type="hidden" runat="server" />
	<asp:button id="distroHidBtn" runat="server" style="display:none;" onclick="GridItem_Click" causesvalidation="false" />

</asp:panel>

</asp:Content>

