using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public class MessageSource
{
	public static DataSet GetAllMessages()
	{
		return SqlHelper.ExecuteDataset("RPT_GetAllMessages");
	}

	public static DataSet GetRecentMessages()
	{
		return SqlHelper.ExecuteDataset("RPT_GetRecentMessages");
	}

	public static DataSet GetMessagesByDate(DateTime startDate, DateTime endDate)
	{
		object startDateTime = null;
		object endDateTime = null;

		if (startDate != DateTime.MinValue)
			startDateTime = startDate;

		if (endDate != DateTime.MinValue)
			endDateTime = endDate;

		return SqlHelper.ExecuteDataset("RPT_GetMessagesByDate", startDateTime, endDateTime);
	}

	public static DataSet GetNewMessages(string userName, DateTime lastLogin)
	{
		string owner = null;
		object loginObj = null;

		if (lastLogin != DateTime.MinValue)
			loginObj = lastLogin;

		if (!string.IsNullOrEmpty(userName))
			owner = userName;

		return SqlHelper.ExecuteDataset("RPT_GetNewMessages", owner, loginObj);
	}
}
