<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="CreateFolderPopup.aspx.cs" Inherits="CreateFolderPopup" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<table width="100%" border="0" cellpadding="0" cellspacing="10">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Create Folder</td>
					<td class="widgetTop">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-top:20px;">
				<div class="rowHeading">Folder Name:</div>
				<br />
				<asp:textbox id="folderNameField" runat="server" cssclass="entryControl"></asp:textbox>
				<asp:requiredfieldvalidator id="val1" runat="server" display="dynamic" controltovalidate="folderNameField" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
				<br /><asp:customvalidator id="val2" runat="server" controltovalidate="folderNameField" onservervalidate="CheckName" errormessage="* Name is not unique."></asp:customvalidator>

				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save</asp:linkbutton></div></td>
						<td><div class="cancelButton"><a href="javascript:CloseRadWindow();">Cancel</a></div></td>
					</tr>
				</table>
				<br />&nbsp;<br />
			</div>
		</td>
	</tr>
</table>		

</asp:Content>

