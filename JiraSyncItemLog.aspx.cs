﻿using System;
using System.Web.UI;

public partial class JiraSyncItemLog : System.Web.UI.Page {

    public Int64 SyncId
    {
        get { if (this.ViewState["s"] != null) return (Int64)this.ViewState["s"]; else return 0; }
        set { this.ViewState["s"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e) {
        if (!Page.IsPostBack) {
            if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.JIRA_SYNC_ID_KEY])) {
                this.SyncId = Convert.ToInt64(Request.Params[DieboldConstants.JIRA_SYNC_ID_KEY]);
            }

            if (!string.IsNullOrEmpty(Request.Params["page"])) {
                DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
            }
        }

        DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_JiraSyncItems", this.SyncId);
        DataGrid1.DataBind();
    }
}