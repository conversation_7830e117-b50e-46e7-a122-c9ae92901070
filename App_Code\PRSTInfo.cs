using System;
using System.Collections;
using System.Runtime.Serialization;

[Serializable()]
public class PRSTInfo : ISerializable
{
	public bool IncludeTrendline = false;
	public bool IncludeUncensoredSeries = false;
    public ReportHelper.XAxisTypeEnum XaxisTypeId = ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS;
	public decimal MTBFSpec = Decimal.MinValue;
	public bool CustomizeRatioRisk = false;
	public decimal DiscriminationRatio = Decimal.MinValue;
	public decimal ProducersRisk = Decimal.MinValue;
	public decimal ConsumersRisk = Decimal.MinValue;
    public bool AllInOneFormat = false;
    public int NumberOfUnits = int.MinValue;

	// Default constructor.
	public PRSTInfo() { }

	// Deserialization constructor.
	public PRSTInfo(SerializationInfo info, StreamingContext context)
	{
		IncludeTrendline = (bool)info.GetValue("it", typeof(bool));
		IncludeUncensoredSeries = (bool)info.GetValue("ius", typeof(bool));
		CustomizeRatioRisk = (bool)info.GetValue("crr", typeof(bool));
        XaxisTypeId = (ReportHelper.XAxisTypeEnum)info.GetValue("xti", typeof(int));
		MTBFSpec = (decimal)info.GetValue("mtbf", typeof(decimal));
		DiscriminationRatio = (decimal)info.GetValue("dr", typeof(decimal));
		ProducersRisk = (decimal)info.GetValue("pr", typeof(decimal));
		ConsumersRisk = (decimal)info.GetValue("cr", typeof(decimal));
        AllInOneFormat = (bool)info.GetValue("aiof", typeof(bool));
        NumberOfUnits = (int)info.GetValue("nou", typeof(int));
    }

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("it", IncludeTrendline);
		info.AddValue("ius", IncludeUncensoredSeries);
		info.AddValue("crr", CustomizeRatioRisk);
		info.AddValue("xti", (int)XaxisTypeId);
		info.AddValue("mtbf", MTBFSpec);
		info.AddValue("dr", DiscriminationRatio);
		info.AddValue("pr", ProducersRisk);
		info.AddValue("cr", ConsumersRisk);
        info.AddValue("aiof", AllInOneFormat);
        info.AddValue("nou", NumberOfUnits);
    }
}
