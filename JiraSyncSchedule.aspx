<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="JiraSyncSchedule.aspx.cs" Inherits="JiraSyncSchedule" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
	var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= RadGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
	function ShowJiraSyncMessage() {
	    $('#jiraMessageDiv').slideDown();
	    setTimeout(function () { $('#jiraMessageDiv').slideUp(); }, 10000);
	}
</script>

<telerik:radwindowmanager runat="server" id="RadWindowManager" height="630" width="620" modal="true" title="Diebold Reporting" 
	behaviors="Close,Reload" reloadonshow="true" visiblestatusbar="false" destroyonclose="true"></telerik:radwindowmanager>

<div id="jiraMessageDiv" style="display:none; background-color:#369347; color:#fff; padding:10px;">An update has been started to sync the below grid items with Jira. This process will run in the background and may take some time to complete.</div>
<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Jira Sync Schedule</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">
                        <table cellpadding="0" cellspacing="0" border="0">
                            <tr>
								<td style="width:10px;">&nbsp;</td>
								<td style="width:77px;">
									<div class="goButtonTop"><a href="JiraSyncLog.aspx">View Log</a></div>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<table border="0" cellpadding="0" cellspacing="0" style="padding-left:10px;">
					<tr>
						<td style="width:185px;"><div class="goButton" style="margin:10px 0px;"><a href="EditJiraSyncSchedule.aspx">Schedule New Sync</a></div></td>
					</tr>
				</table>
				<telerik:radgrid id="RadGrid1" allowmultirowselection="false" 
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="repeaterItem" />
					<alternatingitemstyle cssclass="repeaterItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings enablerowhoverstyle="true" resizing-allowcolumnresize="true">
						<selecting allowrowselect="true" />
						<clientevents onrowclick="RowClick" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="ScheduleId" editmode="EditForms" autogeneratecolumns="False" width="100%" cellpadding="5">
						<columns>
							<telerik:gridtemplatecolumn headertext="Session Name" datafield="SessionName" sortexpression="SessionName" uniquename="SessionName">
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "SessionName")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Sun" sortexpression="Sunday" uniquename="Sunday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Sunday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : "" %>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Mon" sortexpression="Monday" uniquename="Monday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Monday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Tues" sortexpression="Tuesday" uniquename="Tuesday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Tuesday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Wed" sortexpression="Wednesday" uniquename="Wednesday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Wednesday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Thur" sortexpression="Thursday" uniquename="Thursday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Thursday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Fri" sortexpression="Friday" uniquename="Friday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Friday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Sat" sortexpression="Saturday" uniquename="Saturday">
								<headerstyle width="4%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Saturday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Run Time" sortexpression="NextScheduledDate" uniquename="NextScheduledDate">
								<headerstyle width="10%" />
								<itemtemplate><%# DataFormatter.FormatDate(Container.DataItem, "NextScheduledDate", "h:mm tt", "")%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Start" sortexpression="RelativeStartName" uniquename="RelativeStartName">
								<headerstyle width="10%" />
								<itemtemplate><%# GetScheduledDateString(DataFormatter.Format(Container.DataItem, "RelativeStartName"), DataFormatter.FormatDate(Container.DataItem, "FixedStartDate", "yyyy-MM-dd hh:mm:ss", ""))%></itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="End" sortexpression="RelativeEndName" uniquename="RelativeEndName">
								<headerstyle width="10%" />
								<itemtemplate><%# GetScheduledDateString(DataFormatter.Format(Container.DataItem, "RelativeEndName"), DataFormatter.FormatDate(Container.DataItem, "FixedEndDate", "yyyy-MM-dd hh:mm:ss", ""))%></itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><asp:linkbutton id="ExecuteSync" runat="server" oncommand="ExecuteSync_Command" commandargument='<%# DataFormatter.Format(Container.DataItem, "ScheduleId", "") %>'>Sync Now</asp:linkbutton></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="5%" />
								<itemtemplate>
									<div class="goButton"><a href="<%# string.Format("EditJiraSyncSchedule.aspx?{0}={1}&page={2}", DieboldConstants.JIRA_SYNC_ID_KEY, DataFormatter.Format(Container.DataItem, "ScheduleId", ""), RadGrid1.CurrentPageIndex) %>">Edit</a></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>
				
			</div>
		</td>
	</tr>
</table>

</asp:Content>


