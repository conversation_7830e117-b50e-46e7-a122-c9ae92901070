<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditMessage.aspx.cs" Inherits="EditMessage" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server">
<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Message Center</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">
				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr style="padding-top:10px;">
						<td style="width:55%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>	
									<td style="width:130px;" class="rowHeading">Subject:</td>
									<td>
										<asp:textbox id="subjectField" runat="server" maxlength="255" width="200"></asp:textbox>
										<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="subjectField" display="dynamic" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Session:</td>
									<td><telerik:radcombobox id="sessionList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Mark as Urgent:</td>
									<td>
										<asp:checkbox id="isUrgentCheck" runat="server" />
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding-top:20px;">
					<tr>
						<td style="width:100%;" class="rowHeading">Message:</td>
					</tr>						
					<tr>
						<td colspan="3" style="padding:10px 0px 10px 0px;">
							<asp:textbox id="bodyField" runat="server" width="90%" cssclass="entryControl" rows="12" textmode="multiLine"></asp:textbox><br />
							<asp:requiredfieldvalidator id="val2" runat="server" controltovalidate="bodyField" display="dynamic" errormessage="* Required" cssclass="leftPad error"></asp:requiredfieldvalidator>										
						</td>
					</tr>
				</table>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>	
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="submitButton" onclick="SaveButton_Click">Send</asp:linkbutton></div></td>
						<td style="width:120px;"><div class="cancelButton"><asp:linkbutton runat="server" id="cancelButton" onclick="CancelButton_Click" causesvalidation="false">Cancel</asp:linkbutton></div></td>
						<td><div class="cancelButton"><asp:linkbutton runat="server" id="deleteButton" onclientclick="return confirm('Are you sure you wish to delete this message?');" onclick="DeleteButton_Click" causesvalidation="false">Delete</asp:linkbutton></div></td>
					</tr>
				</table>
				<br />
				<br />
			</div>
		</td>
	</tr>
</table>
</asp:panel>

</asp:Content>
