using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Text;
using System.Text.RegularExpressions;
using System.Reflection;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;

public class DataFormatter
{
	public static string getString(SqlDataReader dataReader, string columnName)
	{
		return getString(dataReader, columnName, null);
	}

	public static string getString(SqlDataReader dataReader, string columnName, string defaultValue)
	{
		String retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetString(ordinal);
		}

		return retVal;
	}

	public static string getString(DataRow row, string columnName)
	{
		return getString(row, columnName, null);
	}

	public static string getString(DataRow row, string columnName, string defaultValue)
	{
		String retVal = defaultValue;
		if (row.IsNull(columnName) == false)
		{
			retVal = (string)row[columnName];
		}

		return retVal;
	}

	public static Guid getGuid(SqlDataReader dataReader, string columnName)
	{
		return getGuid(dataReader, columnName, Guid.Empty);
	}

	public static Guid getGuid(SqlDataReader dataReader, string columnName, Guid defaultValue)
	{
		Guid retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetGuid(ordinal);
		}

		return retVal;
	}

	public static Guid getGuid(DataRow row, string columnName)
	{
		return getGuid(row, columnName, Guid.Empty);
	}

	public static Guid getGuid(DataRow row, string columnName, Guid defaultValue)
	{
		Guid retVal = defaultValue;
		if (row.IsNull(columnName) == false)
		{
			retVal = (Guid)row[columnName];
		}

		return retVal;
	}

	public static int getInt32(SqlDataReader dataReader, string columnName)
	{
		return getInt32(dataReader, columnName, 0);
	}

	public static int getInt32(SqlDataReader dataReader, string columnName, int defaultValue)
	{
		int retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetInt32(ordinal);
		}

		return retVal;
	}

	public static int getInt32(DataRow row, string columnName)
	{
		return getInt32(row, columnName, 0);
	}

	public static int getInt32(DataRow row, string columnName, int defaultValue)
	{
		int retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = (int)row[columnName];
		}

		return retVal;
	}

	public static Int64 getInt64(SqlDataReader dataReader, string columnName)
	{
		return getInt64(dataReader, columnName, 0);
	}

	public static Int64 getInt64(SqlDataReader dataReader, string columnName, int defaultValue)
	{
		Int64 retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetInt64(ordinal);
		}

		return retVal;
	}

	public static Int64 getInt64(DataRow row, string columnName)
	{
		return getInt64(row, columnName, 0);
	}

	public static Int64 getInt64(DataRow row, string columnName, Int64 defaultValue)
	{
		Int64 retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = (Int64)row[columnName];
		}

		return retVal;
	}

	public static bool getBool(SqlDataReader dataReader, string columnName)
	{
		return getBool(dataReader, columnName, false);
	}

	public static bool getBool(SqlDataReader dataReader, string columnName, bool defaultValue)
	{
		bool retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetBoolean(ordinal);
		}

		return retVal;
	}

	public static bool getBool(DataRow row, string columnName)
	{
		return getBool(row, columnName, false);
	}

	public static bool getBool(DataRow row, string columnName, bool defaultValue)
	{
		bool retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = Convert.ToBoolean(row[columnName]);
		}

		return retVal;
	}

	public static DateTime getDateTime(SqlDataReader dataReader, string columnName)
	{
		return getDateTime(dataReader, columnName, DateTime.MinValue);
	}

	public static DateTime getDateTime(SqlDataReader dataReader, string columnName, DateTime defaultValue)
	{
		DateTime retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetDateTime(ordinal);
		}

		return retVal;
	}

	public static DateTime getDateTime(DataRow row, string columnName)
	{
		return getDateTime(row, columnName, DateTime.MinValue);
	}

	public static DateTime getDateTime(DataRow row, string columnName, DateTime defaultValue)
	{
		DateTime retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = Convert.ToDateTime(row[columnName]);
		}

		return retVal;
	}

	public static decimal getDecimal(SqlDataReader dataReader, string columnName)
	{
		return getDecimal(dataReader, columnName, 0m);
	}

	public static decimal getDecimal(SqlDataReader dataReader, string columnName, decimal defaultValue)
	{
		decimal retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetDecimal(ordinal);
		}

		return retVal;
	}

	public static decimal getDecimal(DataRow row, string columnName)
	{
		return getDecimal(row, columnName, 0m);
	}

	public static decimal getDecimal(DataRow row, string columnName, decimal defaultValue)
	{
		decimal retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = (decimal)row[columnName];
		}

		return retVal;
	}

	public static double getDouble(SqlDataReader dataReader, string columnName)
	{
		return getDouble(dataReader, columnName, 0.0);
	}

	public static double getDouble(SqlDataReader dataReader, string columnName, double defaultValue)
	{
		double retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetDouble(ordinal);
		}

		return retVal;
	}

	public static double getDouble(DataRow row, string columnName)
	{
		return getDouble(row, columnName, 0.0);
	}

	public static double getDouble(DataRow row, string columnName, double defaultValue)
	{
		double retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = (double)row[columnName];
		}

		return retVal;
	}

	public static object getObject(SqlDataReader dataReader, string columnName)
	{
		return getObject(dataReader, columnName, null);
	}

	public static object getObject(SqlDataReader dataReader, string columnName, object defaultValue)
	{
		object retVal = defaultValue;
		int ordinal = 0;

		ordinal = dataReader.GetOrdinal(columnName);
		if (dataReader.IsDBNull(ordinal) == false)
		{
			retVal = dataReader.GetValue(ordinal);
		}

		return retVal;
	}

	public static object getObject(DataRow row, string columnName)
	{
		return getObject(row, columnName, null);
	}

	public static object getObject(DataRow row, string columnName, object defaultValue)
	{
		object retVal = defaultValue;

		if (row.IsNull(columnName) == false)
		{
			retVal = (object)row[columnName];
		}

		return retVal;
	}

	public static string FormatHtml(string dataVal, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = dataVal;

		if (String.IsNullOrEmpty(retVal) == false && htmlEncode)
			retVal = HttpContext.Current.Server.HtmlEncode(retVal);

		if (String.IsNullOrEmpty(retVal) == false && convertBreakToBR)
			retVal = retVal.Replace("\r\n", "<br/>").Replace("\n\r", "<br/>").Replace("\n", "<br/>").Replace("\r", "<br/>");

		return retVal;
	}

	public static string Format(object dataObject, string fieldName)
	{
		return Format(dataObject, fieldName, null, true, true);
	}

	public static string Format(object dataObject, string fieldName, string onNullValue)
	{
		return Format(dataObject, fieldName, onNullValue, true, true);
	}

	public static string Format(object dataObject, string fieldName, string prefixStr, string suffixStr)
	{
		return Format(dataObject, fieldName, prefixStr, suffixStr, null, true, true);
	}

	public static string Format(object dataObject, string fieldName, string prefixStr, string suffixStr, string onNullValue)
	{
		return Format(dataObject, fieldName, prefixStr, suffixStr, onNullValue, true, true);
	}

	public static string Format(object dataObject, string fieldName, string prefixStr, string suffixStr, string onNullValue, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = Format(dataObject, fieldName, onNullValue, htmlEncode, convertBreakToBR);

		if (String.IsNullOrEmpty(retVal) == false)
			retVal = prefixStr + retVal + suffixStr;

		return retVal;
	}

	public static string Format(object dataObject, string fieldName, string onNullValue, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = null;
		object evalObj = GetField(dataObject, fieldName);

		if (evalObj != null)
			retVal = evalObj.ToString();

		if (String.IsNullOrEmpty(retVal))
			retVal = onNullValue;

		retVal = FormatHtml(retVal, htmlEncode, convertBreakToBR);

		return retVal;
	}

	public static string FormatDate(DateTime dateVal, string formatString, string onMinDateValue)
	{
		if (DateTime.MinValue.Equals(dateVal))
			return onMinDateValue;
		else
			return dateVal.ToString(formatString);
	}

	public static string FormatDate(object dataObject, string fieldName, string formatString, string onMinDateValue)
	{
		return FormatDate(dataObject, fieldName, formatString, onMinDateValue, true, true);
	}

	public static string FormatDate(object dataObject, string fieldName, string formatString, string onMinDateValue, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = null;
		object evalObj = GetField(dataObject, fieldName);

		if (evalObj != null)
			retVal = FormatDate(Convert.ToDateTime(evalObj), formatString, onMinDateValue);

		retVal = FormatHtml(retVal, htmlEncode, convertBreakToBR);

		return retVal;
	}

	public static string FormatPhone(string sourcePhone)
	{
		string retVal = null;

		if (!String.IsNullOrEmpty(sourcePhone))
		{
			Regex r = new Regex(@"^(\()?(?<areacode>\d{3})([-\)\.\s])?\s?(?<prefix>\d{3})([-\.\s])?(?<number>\d{4})(?<ext>.*)", RegexOptions.Compiled);
			Match m = r.Match(sourcePhone);
			if (m.Success)
				retVal = m.Result("(${areacode}) ${prefix}-${number}${ext}");
			else
				retVal = sourcePhone;
		}

		return retVal;
	}

	public static string FormatBool(object dataObject, string fieldName, string onTrueValue, string onFalseValue, string onNullValue)
	{
		return FormatBool(dataObject, fieldName, onTrueValue, onFalseValue, onNullValue, true, true);
	}

	public static string FormatBool(object dataObject, string fieldName, string onTrueValue, string onFalseValue, string onNullValue, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = null;
		object evalObj = GetField(dataObject, fieldName);

		if (evalObj == null)
			retVal = onNullValue;
		else if (Convert.ToBoolean(evalObj))
			retVal = onTrueValue;
		else
			retVal = onFalseValue;

		retVal = FormatHtml(retVal, htmlEncode, convertBreakToBR);

		return retVal;
	}

	public static string FormatDecimal(object dataObject, string fieldName)
	{
		return FormatDecimal(dataObject, fieldName, "C2", null);
	}

	public static string FormatDecimal(object dataObject, string fieldName, string formatString, string onNullValue)
	{
		return FormatDecimal(dataObject, fieldName, formatString, onNullValue, "~Undefined~", true, true);
	}

	public static string FormatDecimal(object dataObject, string fieldName, string formatString, string onNullValue, string onZeroValue)
	{
		return FormatDecimal(dataObject, fieldName, formatString, onNullValue, onZeroValue, true, true);
	}

	public static string FormatDecimal(object dataObject, string fieldName, string formatString, string onNullValue, string onZeroValue, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = null;
		object evalObj = GetField(dataObject, fieldName);

		if (evalObj == null)
		{
			retVal = onNullValue;
		}
		else
		{
			decimal evalDec = Convert.ToDecimal(evalObj);
			if (evalDec.CompareTo(0) == 0)
			{
				if ("~Undefined~".Equals(onZeroValue))
					retVal = evalDec.ToString(formatString);
				else
					retVal = onZeroValue;
			}
			else
			{
				retVal = evalDec.ToString(formatString);
			}
		}

		retVal = FormatHtml(retVal, htmlEncode, convertBreakToBR);

		return retVal;
	}

	public static string FormatDouble(object dataObject, string fieldName)
	{
		return FormatDouble(dataObject, fieldName, "C2", null);
	}

	public static string FormatDouble(object dataObject, string fieldName, string formatString, string onNullValue)
	{
		return FormatDouble(dataObject, fieldName, formatString, onNullValue, "~Undefined~", true, true);
	}

	public static string FormatDouble(object dataObject, string fieldName, string formatString, string onNullValue, string onZeroValue)
	{
		return FormatDouble(dataObject, fieldName, formatString, onNullValue, onZeroValue, true, true);
	}

	public static string FormatDouble(object dataObject, string fieldName, string formatString, string onNullValue, string onZeroValue, bool htmlEncode, bool convertBreakToBR)
	{
		string retVal = null;
		object evalObj = GetField(dataObject, fieldName);

		if (evalObj == null)
		{
			retVal = onNullValue;
		}
		else
		{
			double evalDbl = Convert.ToDouble(evalObj);
			if (evalDbl.CompareTo(0) == 0)
			{
				if ("~Undefined~".Equals(onZeroValue))
					retVal = evalDbl.ToString(formatString);
				else
					retVal = onZeroValue;
			}
			else
			{
				retVal = evalDbl.ToString(formatString);
			}
		}

		retVal = FormatHtml(retVal, htmlEncode, convertBreakToBR);

		return retVal;
	}

	public static DataRowCollection getRows(DataSet ds)
	{
		DataTable dt = null;
		DataRowCollection retVal = null;
		if ((ds != null) && (ds.Tables.Count > 0))
			dt = ds.Tables[0];

		if (dt != null)
			retVal = dt.Rows;
		else
		{
			DataTable dt2 = new DataTable();
			retVal = dt2.Rows;
		}

		return retVal;
	}

	public static DataRow getFirstRow(DataSet ds)
	{
		DataTable dt = null;
		DataRow row = null;
		if ((ds != null) && (ds.Tables.Count > 0))
			dt = ds.Tables[0];

		if ((dt != null) && (dt.Rows.Count > 0))
			row = dt.Rows[0];

		return row;
	}

	public static void setListIndex(ListControl list, string boundValue)
	{
		list.ClearSelection();
		for (int i = 0; i < list.Items.Count; i++)
		{
			if (list.Items[i].Value != null)
			{
				if (list.Items[i].Value == boundValue)
				{
					list.SelectedIndex = i;
					break;
				}
			}
			else
			{
				if (list.Items[i].Text == boundValue)
				{
					list.SelectedIndex = i;
					break;
				}
			}
		}
	}

	public static void setListIndex(HtmlSelect list, string boundValue)
	{
		if (list.SelectedIndex >= 0)
			list.SelectedIndex = -1;

		for (int i = 0; i < list.Items.Count; i++)
		{
			if (list.Items[i].Value != null)
			{
				if (list.Items[i].Value == boundValue)
				{
					list.SelectedIndex = i;
					break;
				}
			}
			else
			{
				if (list.Items[i].Text == boundValue)
				{
					list.SelectedIndex = i;
					break;
				}
			}
		}
	}

	public static void setListIndexByText(ListControl list, string textValue)
	{
		list.ClearSelection();
		for (int i = 0; i < list.Items.Count; i++)
		{
			if (list.Items[i].Text == textValue)
			{
				list.SelectedIndex = i;
				break;
			}
		}
	}

	public static void setListIndexByText(HtmlSelect list, string textValue)
	{
		if (list.SelectedIndex >= 0)
			list.SelectedIndex = -1;

		for (int i = 0; i < list.Items.Count; i++)
		{
			if (list.Items[i].Text == textValue)
			{
				list.SelectedIndex = i;
				break;
			}
		}
	}

	private static object GetField(object dataObject, string fieldName)
	{
		object evalObj = null;
		if (dataObject is DataRowView)
			dataObject = ((DataRowView)dataObject).Row;

		if (dataObject is DataRow)
		{
			if (((DataRow)dataObject).IsNull(fieldName) == false)
			{
				evalObj = ((DataRow)dataObject)[fieldName];
			}
		}
		else if (dataObject is SqlDataReader)
		{
			int ord = ((SqlDataReader)dataObject).GetOrdinal(fieldName);
			if (((SqlDataReader)dataObject).IsDBNull(ord) == false)
			{
				evalObj = ((SqlDataReader)dataObject).GetValue(ord);
			}
		}
		else if (dataObject is DbDataRecord)
		{
			int ord = ((DbDataRecord)dataObject).GetOrdinal(fieldName);
			if (((DbDataRecord)dataObject).IsDBNull(ord) == false)
			{
				evalObj = ((DbDataRecord)dataObject).GetValue(ord);
			}
		}
		else
		{
			evalObj = DataBinder.Eval(dataObject, fieldName);
		}

		return evalObj;
	}

	public static string TruncateString(string originalStr, int minLen, int maxLen) { return TruncateString(originalStr, minLen, maxLen, true); }
	public static string TruncateString(string originalStr, int minLen, int maxLen, bool convertCRLFtoSpace)
	{
		string retVal = null;
		if (!string.IsNullOrEmpty(originalStr))
		{
			retVal = originalStr.Trim();

			if (convertCRLFtoSpace)
				retVal = retVal.Replace("\r\n", " ").Replace("\n\r", " ").Replace("\n", " ").Replace("\r", " ");

			// Clean up double spaces
			retVal = retVal.Replace("  ", " ").Replace("  ", " ").Replace("  ", " ").Replace("  ", " ");

			if (minLen > 0 && maxLen > 0 && retVal.Length > maxLen)
			{
				int pos = -1;
				if ((maxLen - minLen) >= 3)
				{
					if (maxLen > 4)
						pos = retVal.LastIndexOf(" ", maxLen - 3);

					if (pos >= 0 && (pos + 1) >= minLen)
						retVal = retVal.Substring(0, pos + 1).Trim() + "...";
					else if (maxLen > 3)
						retVal = retVal.Substring(0, maxLen - 3).Trim() + "...";
				}

				// Truncate ellipses for very short max lengths and short min to max distances
				if (retVal.Length > maxLen)
					retVal = retVal.Substring(0, maxLen);
			}
		}
		return retVal;
	}
}
