using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Microsoft.AnalysisServices.AdomdClient;

public partial class controls_StatisticSelector : System.Web.UI.UserControl
{
	public int DeviceTypeId
	{
		get { if (this.ViewState["d"] != null) return (int)this.ViewState["d"]; else return 0; }
		set { this.ViewState["d"] = value; }
	}

	public List<SelectedField> SelectedFieldsList
	{
		get { if (this.ViewState["s"] != null) return (List<SelectedField>)this.ViewState["s"]; else return null; }
		set { this.ViewState["s"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		
	}

	public void PopulateTree(int deviceTypeId)
	{
		this.DeviceTypeId = deviceTypeId;

		if (Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE] != null)
			this.SelectedFieldsList = (List<SelectedField>)Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE];

		statisticFieldsTree.Nodes.Clear();
		foreach (TypeCodeEntry fieldType in Utility.GetFieldTypeList())
		{
			//Don't add Extensions
			if (fieldType.Code != "1")
			{
				//prepend "t" to the value to signify it is a FieldType being checked for Load on Demand functionality.
				RadTreeNode node = new RadTreeNode(fieldType.Name, "t" + fieldType.Code);
				node.Checkable = false;
				node.Expanded = false;
				node.ExpandMode = TreeNodeExpandMode.ServerSideCallBack;

				statisticFieldsTree.Nodes.Add(node);
			}
		}
	}

	protected void Node_Expanded(object sender, RadTreeNodeEventArgs e)
	{
		if (e.Node != null && e.Node.Value != null)
		{
			if (e.Node.Value.StartsWith("t"))
				LoadFields(e.Node);
			else if (e.Node.Value.StartsWith("f"))
				LoadFieldOptions(e.Node);
		}
	}

	private void LoadFields(RadTreeNode expandedNode)
	{
		string fieldTypeId = expandedNode.Value.Substring(1, expandedNode.Value.Length - 1);

		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_StatisticFields", this.DeviceTypeId, fieldTypeId);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			if (DataFormatter.getInt32(row, "FieldTypeId") != 0)
			{
				//prepend "f" to the value to signify it is a Field being checked for Load on Demand functionality.
				RadTreeNode node = new RadTreeNode(DataFormatter.getString(row, "FieldName"), "f" + DataFormatter.getInt32(row, "FieldId").ToString());
				node.Checkable = true;
				node.Expanded = false;
				node.ExpandMode = TreeNodeExpandMode.ServerSideCallBack;
				
				//Check any nodes loaded on demand if they are in the selectedFields collection
				if (this.SelectedFieldsList != null)
				{
					foreach (SelectedField field in this.SelectedFieldsList)
					{
						if (field.FieldId == DataFormatter.getInt32(row, "FieldId") && field.IsSelected)
							node.Checked = true;
					}
				}

				expandedNode.Nodes.Add(node);
			}
		}
	}

	private void LoadFieldOptions(RadTreeNode expandedNode)
	{
		string fieldTypeId = expandedNode.ParentNode.Value.Substring(1, expandedNode.ParentNode.Value.Length - 1);
		string fieldId = expandedNode.Value.Substring(1, expandedNode.Value.Length - 1);
		
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_StatisticFieldOptions", this.DeviceTypeId, fieldTypeId, fieldId);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			//prepend "p" to the value to signify it is a FieldOption being checked for Load on Demand functionality.
			RadTreeNode node = new RadTreeNode(DataFormatter.getString(row, "DisplayName"), "p" + DataFormatter.getInt32(row, "FieldOptionId").ToString());
			node.Checkable = true;
			node.Expanded = false;

			//Check any nodes loaded on demand if they are in the selectedFields collection
			if (this.SelectedFieldsList != null)
			{
				foreach (SelectedField field in this.SelectedFieldsList)
				{
					if (field.FieldId == Convert.ToInt32(fieldId))
					{
						foreach (SelectedFieldOption fieldOption in field.SelectedFieldOptions)
						{
							if (fieldOption.FieldOptionId == DataFormatter.getInt32(row, "FieldOptionId"))
								node.Checked = true;
						}
					}
				}
			}

			expandedNode.Nodes.Add(node);
		}
	}

	public void SaveSelectedValuesToSession()
	{
		List<SelectedField> tempList = new List<SelectedField>();

		foreach (RadTreeNode node in statisticFieldsTree.CheckedNodes)
		{
			if (node.Value.StartsWith("f"))
			{
				SelectedField tempField = new SelectedField();
				tempField.FieldName = node.Text;
				tempField.FieldId = Convert.ToInt32(node.Value.Substring(1, node.Value.Length - 1));
				tempField.FieldTypeId =  Convert.ToInt32(node.ParentNode.Value.Substring(1, node.ParentNode.Value.Length - 1));
				tempField.IsSelected = true;
				tempList.Add(tempField);
			}
			else if (node.Value.StartsWith("p"))
			{
				//find the field options parent field and add the option to its collection
				SelectedField parentField = null;
				int parentFieldId = Convert.ToInt32(node.ParentNode.Value.Substring(1, node.ParentNode.Value.Length - 1));
				foreach(SelectedField field in tempList)
				{
					if (field.FieldId == parentFieldId)
						parentField = field;
				}

				//if parent is not already in the list add it first.
				if (parentField == null)
				{
					parentField = new SelectedField();
					parentField.FieldId = parentFieldId;
					parentField.FieldName = node.ParentNode.Text;
					parentField.IsSelected = false;
					parentField.FieldTypeId = Convert.ToInt32(node.ParentNode.ParentNode.Value.Substring(1, node.ParentNode.ParentNode.Value.Length - 1));
					tempList.Add(parentField);
				}

				//Add the option to the parent
				SelectedFieldOption tempOption = new SelectedFieldOption();
				tempOption.FieldOptionName = node.Text;
				tempOption.FieldOptionId =  Convert.ToInt32(node.Value.Substring(1, node.Value.Length - 1));
				parentField.SelectedFieldOptions.Add(tempOption);				
			}
		}

		//add in any previously checked items that are no yet loaded into the tree from load-on-demand items.
		if (this.SelectedFieldsList != null)
		{
			foreach (SelectedField field in this.SelectedFieldsList)
			{
				bool fieldIsInTree = false;
				foreach (RadTreeNode node in this.statisticFieldsTree.GetAllNodes())
				{
					if (node.Value == "f" + field.FieldId)
					{
						fieldIsInTree = true;
						break;
					}
				}

				if (!fieldIsInTree)
				{
					tempList.Add(field);
				}
				else
				{
					//Look through the FieldOptions and make sure all previously selected values are still selected
					foreach (SelectedFieldOption fieldOption in field.SelectedFieldOptions)
					{
						bool optionIsInTree = false;
						foreach (RadTreeNode node in this.statisticFieldsTree.GetAllNodes())
						{
							if (node.Value == "p" + fieldOption.FieldOptionId)
							{
								optionIsInTree = true;
								break;
							}
						}

						if (!optionIsInTree)
							tempList.Add(field);
					}
				}
			}
		}

		if (tempList.Count != 0)
		{
            Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] = Utility.FormatFieldIdList(tempList, true, false, ", ", null, null); ;
            Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] = Utility.FormatFieldIdList(tempList, false, true, null, null, ", ");
            Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME] = Utility.FormatFieldNameList(tempList, true, true, "; ", " - ", ", ");
            Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE] = tempList; //object list for rebuilding tree
		}
		else
		{
			Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] = null;
			Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] = null;
			Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME] = null;
			Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE] = null;
		}
	}

	public void ClearTree()
	{
		statisticFieldsTree.Nodes.Clear();
		this.SelectedFieldsList = null;
	}

	public void ClearSelections()
	{
		statisticFieldsTree.ClearCheckedNodes();
		statisticFieldsTree.ClearSelectedNodes();
		this.SelectedFieldsList = null;
	}
}
