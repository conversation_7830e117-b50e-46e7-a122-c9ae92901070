<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="SessionArchiveEdit.aspx.cs" Inherits="SessionArchiveEdit" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ register tagprefix="rpt" tagname="RelativeDate" src="~/controls/RelativeDate.ascx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
<script type="text/javascript">
    function ToggleDatePopup(dateFieldId) { $find(dateFieldId).showPopup(); }   
</script>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Purge Session Data</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<div class="title"><asp:label id="SessionNameLabel" runat="server"></asp:label></div>
				
				<div id="ErrorDiv" runat="server" visible="false" style="margin:10px 14px 20px 14px; border:solid 1px #ff0000; padding:10px 10px 20px 14px;">
					<asp:label id="ErrorMessage" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				</div>
				
				<div class="cellHeading">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td style="width:40%;">Transactions & Observations</td>
							<td style="width:30%; text-align:right; padding-right:8px;">% Disk Space: </td>
							<td style="width:30%;">
								<div style="background-color:#bfb9b5; width:95%; border:solid 1px #0077cc;">
									<div id="TranDiskGraph" runat="server" style="background-color:#0077cc; padding:2px 0px 2px 1px; color:#ffffff;">
										<asp:label id="TranDiskGraphLabel" runat="server">&nbsp;</asp:label>
									</div>
								</div>
							</td>
						</tr>
					</table>
				</div>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Transaction Data</b>
						</td>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Rollup Data</b>					
						</td>
					</tr>
					<tr>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Tran Keep After Date:</td>
									<td>
										<rpt:relativedate id="TranKeepDataAfterDate" runat="server"></rpt:relativedate>
										<%--<div class="goButton" style="display:inline;padding-top:3px;"><asp:linkbutton runat="server" id="submitButton" onclientclick="return confirm('All transaction dates below will be overridden with this value.  Would you like to proceed?'); return false;" onclick="FillTransactionsButton_Click">Fill Below</asp:linkbutton></div>--%>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="TranDataDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Row Count:</td>
									<td style="padding-left:5px;"><asp:label id="TranRowCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;"  class="rowHeading">Rollup Keep After Date:</td>
									<td>
										<rpt:relativedate id="TranKeepRollupAfterDate" runat="server"></rpt:relativedate>
										<%--<div class="goButton" style="display:inline;padding-top:3px;"><asp:linkbutton runat="server" id="Linkbutton1" onclientclick="return confirm('All rollup dates below will be overridden with this value.  Would you like to proceed?'); return false;" onclick="FillRollupButton_Click">Fill Below</asp:linkbutton></div>--%>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="TranRollupDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Rollup Count:</td>
									<td style="padding-left:5px;"><asp:label id="TranRollupCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />		
				<br />
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td colspan="3" style="padding:0px 0px 20px 14px;">
							<div style="width:95%;padding-bottom:5px;">
								<b>Changing the archive dates will permenantly delete large amounts of data from the system.
								Data will be purged using these new dates during nightly processing.
								Because changes to these dates can purge large amounts of data from the system, please confirm
								the change by typing "YES" into the text box below before saving.</b>
							</div>
							<asp:textbox id="ConfirmBox2" width="60" runat="server"></asp:textbox> 
						</td>
					</tr>
					<tr>
						<td style="width:100px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveBtn2">Save</asp:linkbutton></div></td>
						<td style="width:100px;"><div class="cancelButton"><a href="SessionArchive.aspx">Cancel</a></div></td>
						<td style="width:100%;">&nbsp;</td>
					</tr>
				</table>
				<br />
				<div style="width:100%; height:18px; background-color:#5b5551;"></div>
				<div class="title">Individual Data Types</div>
				<div style="padding:0px 14px 14px 14px;">
					<b>Warning:</b> Specifiying a relative date for the Transactions & Observations section above can result in the deletion 
					of data for the itemized sections below regardless of their saved dates. 
				</div>
				<!-- Status Entity -->
				<div class="cellHeading">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td style="width:40%;">Status Entities</td>
							<td style="width:30%; text-align:right; padding-right:8px;">% Disk Space: </td>
							<td style="width:30%;">
								<div style="background-color:#bfb9b5; width:95%; border:solid 1px #0077cc;">
									<div id="StatusDiskGraph" runat="server" style="background-color:#0077cc; padding:2px 0px 2px 1px; color:#ffffff;">
										<asp:label id="StatusDiskGraphLabel" runat="server">&nbsp;</asp:label>
									</div>
								</div>
							</td>
						</tr>
					</table>
				</div>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Transaction Data</b>
						</td>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Rollup Data</b>					
						</td>
					</tr>
					<tr>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;"  class="rowHeading">Status Keep After Date:</td>
									<td>
										<rpt:relativedate id="StatusKeepDataAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="StatusDataDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Row Count:</td>
									<td style="padding-left:5px;"><asp:label id="StatusRowCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Rollup Keep After Date:</td>
									<td>
										<rpt:relativedate id="StatusKeepRollupAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="StatusRollupDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Rollup Count:</td>
									<td style="padding-left:5px;"><asp:label id="StatusRollupCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<!-- Info Entity -->
				<div class="cellHeading">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td style="width:40%;">Info Entities</td>
							<td style="width:30%; text-align:right; padding-right:8px;">% Disk Space: </td>
							<td style="width:30%;">
								<div style="background-color:#bfb9b5; width:95%; border:solid 1px #0077cc;">
									<div id="InfoDiskGraph" runat="server" style="background-color:#0077cc; padding:2px 0px 2px 1px; color:#ffffff;">
										<asp:label id="InfoDiskGraphLabel" runat="server">&nbsp;</asp:label>
									</div>
								</div> 
							</td>
						</tr>
					</table>
				</div>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Transaction Data</b>
						</td>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Rollup Data</b>					
						</td>
					</tr>
					<tr>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Info Keep After Date:</td>
									<td>
										<rpt:relativedate id="InfoKeepDataAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="InfoDataDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Row Count:</td>
									<td style="padding-left:5px;"><asp:label id="InfoRowCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Rollup Keep After Date:</td>
									<td>
										<rpt:relativedate id="InfoKeepRollupAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="InfoRollupDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Rollup Count:</td>
									<td style="padding-left:5px;"><asp:label id="InfoRollupCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<!-- Metric Entity -->
				<div class="cellHeading">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td style="width:40%;">Metric Entities</td>
							<td style="width:30%; text-align:right; padding-right:8px;">% Disk Space: </td>
							<td style="width:30%;">
								<div style="background-color:#bfb9b5; width:95%; border:solid 1px #0077cc;">
									<div id="MetricDiskGraph" runat="server" style="background-color:#0077cc; padding:2px 0px 2px 1px; color:#ffffff;">
										<asp:label id="MetricDiskGraphLabel" runat="server">&nbsp;</asp:label>
									</div> 
								</div>
							</td>
						</tr>
					</table>
				</div>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Transaction Data</b>
						</td>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Rollup Data</b>					
						</td>
					</tr>
					<tr>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Metric Keep After Date:</td>
									<td>
										<rpt:relativedate id="MetricKeepDataAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="MetricDataDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Row Count:</td>
									<td style="padding-left:5px;"><asp:label id="MetricRowCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Rollup Keep After Date:</td>
									<td>
										<rpt:relativedate id="MetricKeepRollupAfterDate" runat="server"></rpt:relativedate>										
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="MetricRollupDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Rollup Count:</td>
									<td style="padding-left:5px;"><asp:label id="MetricRollupCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<!-- Result Data -->
				<div class="cellHeading">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td style="width:40%;">Result Data</td>
							<td style="width:30%; text-align:right; padding-right:8px;">% Disk Space: </td>
							<td style="width:30%;">
								<div style="background-color:#bfb9b5; width:95%; border:solid 1px #0077cc;">
									<div id="ResultDiskGraph" runat="server" style="background-color:#0077cc; padding:2px 0px 2px 1px; color:#ffffff;">
										<asp:label id="ResultDiskGraphLabel" runat="server">&nbsp;</asp:label>
									</div>  
								</div>
							</td>
						</tr>
					</table>
				</div>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Transaction Data</b>
						</td>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Rollup Data</b>					
						</td>
					</tr>
					<tr>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Result Keep After Date:</td>
									<td>
										<rpt:relativedate id="ResultKeepDataAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="ResultDataDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Row Count:</td>
									<td style="padding-left:5px;"><asp:label id="ResultRowCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Rollup Keep After Date:</td>
									<td>
										<rpt:relativedate id="ResultKeepRollupAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="ResultRollupDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Rollup Count:</td>
									<td style="padding-left:5px;"><asp:label id="ResultRollupCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<!-- Engineering -->
				<div class="cellHeading">
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td style="width:40%;">Engineering Data</td>
							<td style="width:30%; text-align:right; padding-right:8px;">% Disk Space: </td>
							<td style="width:30%;">
								<div style="background-color:#bfb9b5; width:95%; border:solid 1px #0077cc;">
									<div id="EngDiskGraph" runat="server" style="background-color:#0077cc; padding:2px 0px 2px 1px; color:#ffffff;">
										<asp:label id="EngDiskGraphLabel" runat="server">&nbsp;</asp:label>
									</div> 
								</div>
							</td>
						</tr>
					</table>
				</div>
				<table border="0" cellpadding="0" cellspacing="0" width="100%">
					<tr>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Transaction Data</b>
						</td>
						<td style="width:50%;padding:10px 0px 0px 14px;">
							<b>Rollup Data</b>					
						</td>
					</tr>
					<tr>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Eng. Keep After Date:</td>
									<td>
										<rpt:relativedate id="EngKeepDataAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="EngDataDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Row Count:</td>
									<td style="padding-left:5px;"><asp:label id="EngRowCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
						<td style="width:50%;">
							<table width="100%" border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:180px;" class="rowHeading">Rollup Keep After Date:</td>
									<td>
										<rpt:relativedate id="EngKeepRollupAfterDate" runat="server"></rpt:relativedate>
									</td>
								</tr>
								<tr>
									<td class="rowHeading">Disk Space Used:</td>
									<td style="padding-left:5px;"><asp:label id="EngRollupDiskSpace" runat="server"></asp:label></td>
								</tr>
								<tr>
									<td class="rowHeading">Rollup Count:</td>
									<td style="padding-left:5px;"><asp:label id="EngRollupCount" runat="server"></asp:label></td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<br />
				
				<div id="ErrorDiv1" runat="server" visible="false" style="margin:10px 14px 20px 14px; border:solid 1px #ff0000; padding:10px 10px 20px 14px;">
					<asp:label id="ErrorMessage1" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				</div>
				
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td colspan="3" style="padding:0px 0px 20px 14px;">
							<div style="width:95%;padding-bottom:5px;">
								<b>Changing the archive dates will permenantly delete large amounts of data from the system.
								Data will be purged using these new dates during nightly processing.
								Because changes to these dates can purge large amounts of data from the system, please confirm
								the change by typing "YES" into the text box below before saving.</b>
							</div>
							<asp:textbox id="ConfirmBox" width="60" runat="server"></asp:textbox> 
						</td>
					</tr>
					<tr>
						<td style="width:100px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
						<td style="width:100px;"><div class="cancelButton"><a href="SessionArchive.aspx">Cancel</a></div></td>
						<td style="width:100%;">&nbsp;</td>
					</tr>
				</table>
			</div>
		</td>
	</tr>
</table>

</asp:Content>

