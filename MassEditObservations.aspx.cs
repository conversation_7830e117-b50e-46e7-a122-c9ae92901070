﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using Telerik.Web.UI;

public partial class MassEditObservations : System.Web.UI.Page {
	public List<TypeCodeEntry> solutionStateValues = new List<TypeCodeEntry>();
    public List<TypeCodeEntry> moduleTypes = new List<TypeCodeEntry>();
	public List<TypeCodeEntry> owners = new List<TypeCodeEntry>();
	public List<TypeCodeEntry> failureTypes = new List<TypeCodeEntry>();
	public List<TypeCodeEntry> investigationAreas = new List<TypeCodeEntry>();
	public List<TypeCodeEntry> disciplines = new List<TypeCodeEntry>();
	public List<TypeCodeEntry> triageTypes = new List<TypeCodeEntry>();

	protected void Page_Load(object sender, EventArgs e) {

		if (!Page.IsPostBack) {
			LoadDropDownData();

			obsRepeater.DataSource = TransactionSource.GetTransactionsFromSessionParameters();
			obsRepeater.DataBind();
		}
	}

	protected void CancelButton_Click(object sender, EventArgs e) {
		Response.Redirect("Search.aspx");
	}

	protected void SaveButton_Click(object sender, EventArgs e) {
		ErrorDiv.Visible = ErrorDiv1.Visible = false;

		string connString = ConfigurationManager.ConnectionStrings["TransactionalDatabase"].ConnectionString;
		using (SqlConnection connection = new SqlConnection(connString)) {
			connection.Open();
			SqlTransaction sqlTran = connection.BeginTransaction("SaveTran");

			try {
				foreach (RepeaterItem item in obsRepeater.Items) {	
					Int64 obsId = Convert.ToInt32(((HiddenField)item.FindControl("obsId")).Value);
					Int64 tranId = Convert.ToInt32(((HiddenField)item.FindControl("tranId")).Value);
					string moduleType = ((DropDownList)item.FindControl("moduleTypeList")).SelectedValue;
					string failureLocation = ((DropDownList)item.FindControl("failureLocationList")).SelectedValue;
					string failureType = ((DropDownList)item.FindControl("failureTypeList")).SelectedValue;
					string discipline = ((DropDownList)item.FindControl("disciplineList")).SelectedValue;
					string investigationArea = ((DropDownList)item.FindControl("investigationAreaList")).SelectedValue;
					string triageType = ((DropDownList)item.FindControl("triageTypeList")).SelectedValue;
					string owner = ((DropDownList)item.FindControl("ownerList")).SelectedValue;
					string operatorName = ((DropDownList)item.FindControl("operatorList")).SelectedValue;
					string scrNumber = ((TextBox)item.FindControl("scrNumberField")).Text;
					string link = ((TextBox)item.FindControl("linkField")).Text;
					string tranNotes = ((TextBox)item.FindControl("tranNotes")).Text;

					object projectedDate = null;
					RadDatePicker projectedDateCntrl = (RadDatePicker)item.FindControl("projectedDate");
					if (projectedDateCntrl != null && projectedDateCntrl.SelectedDate != null) {
						projectedDate = (DateTime)((RadDatePicker)item.FindControl("projectedDate")).SelectedDate;
					}
					bool isCensored = ((CheckBox)item.FindControl("censorCheck")).Checked;

					//Calculate Severity value based on Solution State 7, and Failure Type
					object severity = null;
					string solutionState7 = ((DropDownList)item.FindControl("solutionState7")).SelectedValue;
                    string failureTypeName = ((DropDownList)item.FindControl("failureTypeList")).SelectedItem.Text;
                    if (solutionState7.Equals("skip") || solutionState7.Equals("complete")) {
						if (failureTypeName == "CA" || failureTypeName == "CA Jam" || failureTypeName == "RA" || failureTypeName == "RA Jam") {
							severity = 4; //Closed (No Solution)
						}
						else if (failureTypeName == "C" || isCensored) {
							severity = 3; //Closed (Fixed)
						}
						else {
							severity = 2; //Closed
						}
					}
					else if (solutionState7.Equals("pending")) {
						severity = 1; //Open
					}
					else {
						severity = null;
					}

					int success = SqlHelper.ExecuteNonQuery(connection, sqlTran, "RPT_UpdateObservationGroupItem", obsId, tranId, failureType, failureLocation, investigationArea,
					                                            scrNumber, severity, owner, operatorName, isCensored, tranNotes, link, moduleType, triageType, discipline, projectedDate);
										
					//remove all prior Solution State Values
					SqlHelper.ExecuteNonQuery(connection, sqlTran, "RPT_ClearObservationSolutionStates", obsId);

					//insert new Solution State Values for any observations
					if (obsId > 0) {
						InsertSolutionStateValue(connection, sqlTran, obsId, 1, ((DropDownList)item.FindControl("solutionState1")).SelectedValue);
						InsertSolutionStateValue(connection, sqlTran, obsId, 2, ((DropDownList)item.FindControl("solutionState2")).SelectedValue);
						InsertSolutionStateValue(connection, sqlTran, obsId, 3, ((DropDownList)item.FindControl("solutionState3")).SelectedValue);
						InsertSolutionStateValue(connection, sqlTran, obsId, 4, ((DropDownList)item.FindControl("solutionState4")).SelectedValue);
						InsertSolutionStateValue(connection, sqlTran, obsId, 5, ((DropDownList)item.FindControl("solutionState5")).SelectedValue);
						InsertSolutionStateValue(connection, sqlTran, obsId, 6, ((DropDownList)item.FindControl("solutionState6")).SelectedValue);
						InsertSolutionStateValue(connection, sqlTran, obsId, 7, ((DropDownList)item.FindControl("solutionState7")).SelectedValue);
					}
				}
			}
			catch (Exception ex) {
				sqlTran.Rollback();
				string errorMessage = "Unable to save observations. " + ex.Message;
				ErrorDiv.InnerHtml = ErrorDiv1.InnerHtml = errorMessage;
				ErrorDiv.Visible = ErrorDiv1.Visible = true;
                return;
            }
			sqlTran.Commit();
		}
		Response.Redirect("Search.aspx");
	}

	private void InsertSolutionStateValue(SqlConnection conn, SqlTransaction tran, Int64 obsId, int stateNumber, string selectedValue) {
		if (!string.IsNullOrEmpty(selectedValue)) {
			SqlHelper.ExecuteNonQuery(conn, tran, "RPT_InsertObservationSolutionState", obsId, stateNumber, selectedValue.Equals("pending"), selectedValue.Equals("complete"), selectedValue.Equals("skip"));
		}
	}

	protected void obsRepeater_ItemDataBound(object sender, System.Web.UI.WebControls.RepeaterItemEventArgs e) {
		if (e.Item != null && e.Item.DataItem != null) {
			object deviceTypeId = DataBinder.Eval(e.Item.DataItem, "DeviceTypeId");
			
			//load possible failure locations based on what DeviceType is selected
			if (e.Item.FindControl("failureLocationList") != null) {
				DropDownList flList = (DropDownList)e.Item.FindControl("failureLocationList");
				if (flList != null) {
                    flList.DataSource = Utility.GetFailureLocationList(Convert.ToInt32(deviceTypeId));
					flList.DataBind();

					object flId = DataBinder.Eval(e.Item.DataItem, "FailureLocationId");
                    flList.SelectedValue = Convert.ToString(flId);
				}
			}

			SetDropDownListValue(e, "operatorList", "OperatorId");
			SetDropDownListValue(e, "operatorList", "OperatorId");
			SetDropDownListValue(e, "operatorList", "OperatorId");
			SetDropDownListValue(e, "moduleTypeList", "ModuleDeviceTypeId");
			SetDropDownListValue(e, "failureLocationList", "FailureLocationId");
			SetDropDownListValue(e, "failureTypeList", "FailureTypeId");
			SetDropDownListValue(e, "disciplineList", "DisciplineId");
			SetDropDownListValue(e, "investigationAreaList", "InvestigationAreaId");
			SetDropDownListValue(e, "triageTypeList", "TriageTypeId");
			SetDropDownListValue(e, "ownerList", "OwnerId");
			SetDropDownListValue(e, "operatorList", "OperatorId");

			if (e.Item.FindControl("projectedDate") != null) {
				RadDatePicker field = (RadDatePicker)e.Item.FindControl("projectedDate");
				if (field != null) {
					if (DataFormatter.FormatDate(e.Item.DataItem, "ProjectedSolutionDate", "MM/dd/yyyy", "") != null) {
						field.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(e.Item.DataItem, "ProjectedSolutionDate", "MM/dd/yyyy", "")));
					}
				}
            }

			//load a list of all selected solution state values
			DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservationSolutionStates", DataBinder.Eval(e.Item.DataItem, "ObservationId"));
			if (ds != null && ds.Tables[0] != null) {
				foreach (DataRow row in ds.Tables[0].Rows) {
					SetSolutionStateDropDown(e, row);
				}
			}
		}
	}

	private void SetDropDownListValue(RepeaterItemEventArgs e, string controlName, string dataItemName) {
		if (e.Item.FindControl(controlName) != null) {
			DropDownList list = (DropDownList)e.Item.FindControl(controlName);
			if (list != null) {
				object id = DataBinder.Eval(e.Item.DataItem, dataItemName);
				list.SelectedValue = Convert.ToString(id);
			}
		}
	}

	private void SetSolutionStateDropDown(RepeaterItemEventArgs e, DataRow row) {
		int stateId = (int)row["SolutionStateId"];
		string listControlName = "solutionState" + stateId;

		if (e.Item.FindControl(listControlName) != null) {
			DropDownList list = (DropDownList)e.Item.FindControl(listControlName);
			if (list != null) {
				if ((bool)row["Pending"]) {
					list.SelectedValue = "pending";
				}
				else if ((bool)row["Complete"]) {
					list.SelectedValue = "complete";
				}
				else if ((bool)row["Skip"]) {
					list.SelectedValue = "skip";
				}
			}
		}
	}

	private void LoadDropDownData() {
		solutionStateValues = new List<TypeCodeEntry>();
		solutionStateValues.Add(new TypeCodeEntry("pending", "Pending"));
		solutionStateValues.Add(new TypeCodeEntry("complete", "Complete"));
		solutionStateValues.Add(new TypeCodeEntry("skip", "Skip"));

		this.moduleTypes = Utility.GetDeviceTypeList();
		this.owners = Utility.GetOperatorList();
		this.failureTypes = Utility.GetFailureTypeList();
		this.investigationAreas = Utility.GetInvestigationAreaList();
		this.disciplines = Utility.GetDisciplineList();
		this.triageTypes = Utility.GetTriageTypeList();
	}
}