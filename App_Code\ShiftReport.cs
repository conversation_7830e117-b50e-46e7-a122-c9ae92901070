using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.AnalysisServices.AdomdClient;

/// <summary>
/// Summary description for ShiftReport
/// </summary>
public class ShiftReport : ProgressReport
{
    public string SettingsList = null;

	public ShiftReport(ReportInfo repInfo) : base(repInfo, true, false, false) { }
	
    public override void BuildGridDisplay()
    {
		TupleCollection colTuples = null;
		int rowCount = 0;

        colTuples = this.cellSet.Axes[0].Set.Tuples;
		if(this.cellSet.Axes.Count > 1)
	        rowCount = this.cellSet.Axes[1].Set.Tuples.Count;
		
		List<string> measureLevels = new List<string>();
        List<string> deviceLevels = new List<string>();

        for (int x = 0; x < colTuples.Count; x++)
        {
            Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[x];
            string colName = colTuple.Members[0].Caption;
            string deviceName = colTuple.Members[1].Caption;
            if (!measureLevels.Contains(colName))
                measureLevels.Add(colName);
            if (!deviceLevels.Contains(deviceName))
                deviceLevels.Add(deviceName);
        }

		List<double> totalsRowData = new List<double>();
        for (int col = 0; col < measureLevels.Count; col++)
			totalsRowData.Add(0);

        for (int dev = 0; dev < deviceLevels.Count; dev++)
		{
			List<string> curRowData = new List<string>();
            for (int col = 0; col < measureLevels.Count; col++)
			{
                int colIndex = dev + (col * deviceLevels.Count);
                double subTotal = 0;
                double fallbackTotal = 0;
                if (rowCount > 0)
				{
					for (int row = 0; row < rowCount; row++)
					{
						if (this.cellSet.Cells[colIndex, row].Value != null)
						{
                            double curVal = double.Parse(this.cellSet.Cells[colIndex, row].Value.ToString());
                            if (curVal > 0)
                                subTotal += curVal;
                            else
                                fallbackTotal += 0 - curVal;
						}
					}
				}
				else
				{
                    if (this.cellSet.Cells[colIndex].Value != null)
                    {
                        double curVal = double.Parse(this.cellSet.Cells[colIndex].Value.ToString());
                        if (curVal > 0)
                            subTotal += curVal;
                        else
                            fallbackTotal += 0 - curVal;
                    }
				}

                if (col == 2) // Media Per Tran
                {
                    try {
                        decimal top = decimal.Parse(curRowData[1], System.Globalization.NumberStyles.Any);
                        decimal bottom = decimal.Parse(curRowData[0], System.Globalization.NumberStyles.Any);
                        decimal mediaPerTran = (top / bottom);
                        curRowData.Add(mediaPerTran.ToString("#,##0.#"));
                    }
                    catch {
                        curRowData.Add("Invalid Media Value");
                    }
                }
                else
                {
                    if (subTotal > 0)
                        curRowData.Add(subTotal.ToString("#,##0.#"));
                    else if(fallbackTotal > 0)
                        curRowData.Add("~" + fallbackTotal.ToString("#,##0.#"));
                    else
                        curRowData.Add("0");
                }

                totalsRowData[col] += subTotal;
			}
			this.RowDisplayData.Add(curRowData);
		}

        if (deviceLevels.Count > 0)
        {
            // Media Per Tran
            if (totalsRowData.Count >= 2)
            {
                if (totalsRowData[0] > 0)
                    totalsRowData[2] = totalsRowData[1] / totalsRowData[0];
                else
                    totalsRowData[2] = 0;
            }

            int baseIndex = 3;
            baseIndex = UpdateTotal(totalsRowData, baseIndex, this.repInfo.ProgressInfo.RateTypeId1, this.repInfo.ProgressInfo.Normalize, this.repInfo.ProgressInfo.SpecLabel1, this.repInfo.ProgressInfo.SpecRate1, this.repInfo.ProgressInfo.WorkLoad1);
            baseIndex = UpdateTotal(totalsRowData, baseIndex, this.repInfo.ProgressInfo.RateTypeId2, this.repInfo.ProgressInfo.Normalize, this.repInfo.ProgressInfo.SpecLabel2, this.repInfo.ProgressInfo.SpecRate2, this.repInfo.ProgressInfo.WorkLoad2);
            baseIndex = UpdateTotal(totalsRowData, baseIndex, this.repInfo.ProgressInfo.RateTypeId3, this.repInfo.ProgressInfo.Normalize, this.repInfo.ProgressInfo.SpecLabel3, this.repInfo.ProgressInfo.SpecRate3, this.repInfo.ProgressInfo.WorkLoad3);
            baseIndex = UpdateTotal(totalsRowData, baseIndex, this.repInfo.ProgressInfo.RateTypeId4, this.repInfo.ProgressInfo.Normalize, this.repInfo.ProgressInfo.SpecLabel4, this.repInfo.ProgressInfo.SpecRate4, this.repInfo.ProgressInfo.WorkLoad4);
        }
        deviceLevels.Add("Totals");

		List<string> totalsRowDisplay = new List<string>();
        foreach (double val in totalsRowData)
        {
            if(double.MinValue.Equals(val))
                totalsRowDisplay.Add("0");
            else if(val < 0)
                totalsRowDisplay.Add("~" + Math.Abs(val).ToString("#,##0.##"));
            else
                totalsRowDisplay.Add(val.ToString("#,##0.##"));
        }
		this.RowDisplayData.Add(totalsRowDisplay);

        this.ColumnDisplayHeaders = new List<List<string>>();
        this.ColumnDisplayHeaders.Add(measureLevels);
        this.RowDisplayHeaders = new List<List<string>>();
        this.RowDisplayHeaders.Add(deviceLevels);
    }

    private int UpdateTotal(List<double> totalsRowData, int baseIndex, ReportHelper.RateTypeEnum rateTypeId, bool normalize, string specName, decimal specRate, decimal workloadAmount)
    {
        int offset = 0;
		if ((baseIndex + 1) < totalsRowData.Count) {
			try {
				double rawAmount = totalsRowData[baseIndex];

				if (workloadAmount <= 0)
					workloadAmount = 0;

				if (this.repInfo.ProgressInfo.AssumeOneFailure && rawAmount == 0)
					rawAmount = -1;

				if (rateTypeId == ReportHelper.RateTypeEnum.BY_STAT_VALUE) {
					if ((baseIndex) < totalsRowData.Count)
						totalsRowData[baseIndex] = double.MinValue;

					// Normalize
					if (normalize && !string.IsNullOrEmpty(specName) && specRate > 0) {
						totalsRowData[baseIndex + 1] = totalsRowData[baseIndex + 1] / ((double)specRate);
					}
				}
				else {
					if ((baseIndex + 1) < totalsRowData.Count) {
						switch (rateTypeId) {
							case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
								if (totalsRowData[1] > 0)
									totalsRowData[baseIndex + 1] = (rawAmount * (1000000 / totalsRowData[1]));
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
								if (totalsRowData[0] > 0)
									totalsRowData[baseIndex + 1] = (rawAmount * (1000000 / totalsRowData[0]));
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
								if (totalsRowData[1] > 0)
									totalsRowData[baseIndex + 1] = (rawAmount / totalsRowData[1] * 100);
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
								if (totalsRowData[0] > 0)
									totalsRowData[baseIndex + 1] = (rawAmount / totalsRowData[0] * 100);
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.MEDIA_PER_INCIDENT: // Media per Incident
								if (totalsRowData[1] > 0 && rawAmount != 0)
									totalsRowData[baseIndex + 1] = (totalsRowData[1] / rawAmount);
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.TRANSACTIONS_PER_INCIDENT: // Transactions per Incident
								if (totalsRowData[0] > 0 && rawAmount != 0)
									totalsRowData[baseIndex + 1] = (totalsRowData[0] / rawAmount);
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.PER_WORKLOAD_MEDIA: //Per Workload Media
								if (workloadAmount > 0 && totalsRowData[1] > 0)
									totalsRowData[baseIndex + 1] = (rawAmount * (((double)workloadAmount) / totalsRowData[1]));
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							case ReportHelper.RateTypeEnum.PER_WORKLOAD_TRANSACTIONS: //Per Workload Transactions
								if (workloadAmount > 0 && totalsRowData[0] > 0)
									totalsRowData[baseIndex + 1] = (rawAmount * (((double)workloadAmount) / totalsRowData[0]));
								else
									totalsRowData[baseIndex + 1] = double.MinValue;
								break;
							default:
								throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
						}

						// Normalize
						if (normalize && !string.IsNullOrEmpty(specName) && specRate > 0) {
							totalsRowData[baseIndex + 1] = totalsRowData[baseIndex + 1] / ((double)specRate);
						}
					}
					offset++; // Adjust for transformed column
				}

				if (!string.IsNullOrEmpty(specName) && specRate > 0) {
					offset++; // Adjust for spec
					if ((baseIndex + offset) < totalsRowData.Count)
						totalsRowData[baseIndex + offset] = double.MinValue;
				}

			}
			catch (Exception ex) {
				StringBuilder error = new StringBuilder();
				error.AppendLine("Index out of bounds.");
				error.AppendLine(string.Format("Base Index: {0}", baseIndex));
				error.AppendLine(string.Format("Offset: {0}", offset));
				error.AppendLine(string.Format("Totals Row Data Count: {0}", totalsRowData.Count));
				error.AppendLine("");
				error.AppendLine(ex.Message);
				error.AppendLine("");
				error.AppendLine(ex.StackTrace);
				throw new ApplicationException(error.ToString());
			}
		}
        offset++; // Move to next series
        return baseIndex + offset;
    }

    public override void LoadData()
    {
		//**************************************
		//Removed to speed up report run time, taking excessive amounts of time just to display which firmware is included.  
		//Add back in if display of Firmware versions is needed
        //LoadSettings();
		//**************************************

        base.LoadData();
    }

    private void LoadSettings()
    {
        StringBuilder mdxSetup = new StringBuilder();
        StringBuilder mdxSelect = new StringBuilder();
        string mdxFromWhere = null;
        string mdxCategory = null;
        string timeHier = OLAPHelper.GetTimeHier(repInfo.ProgressInfo.GroupingId);
        bool splitByDevice = this.repInfo.ProgressInfo.SplitByCell;

        mdxSetup.Append(string.Format("WITH {0}\r\n", OLAPHelper.BuildMdxFilteredTime(repInfo.ProgressInfo.GroupingId, repInfo.StartDate, repInfo.EndDate, "FilteredTime")));

        mdxSetup.Append("MEMBER [Transaction Cnt] AS ' ");
        mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, repInfo.StartDate, repInfo.EndDate, "FilteredTime", "[Measures].[Transaction Count]"));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");

		mdxSelect.Append("SELECT [Measures].[Transaction Cnt] on columns, \r\n");

        mdxCategory = "NON EMPTY ([Setting].[Setting Type].[Setting Type], [Setting].[Option Type].[Option Type]) on rows\r\n";

        mdxFromWhere = string.Format("FROM [Reporting] {0}",
                OLAPHelper.BuildMdxWhereTuples(repInfo.AttachedSessions, repInfo.ReportFilters, false, "WHERE ", true));

        string mdx = mdxSetup.ToString() + mdxSelect.ToString() + mdxCategory + mdxFromWhere;

        if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Request.Params["showmdx"])) {
            throw new ApplicationException(mdx);
        }
        ExecuteMdx(mdx);
        this.SettingsList = BuildSettingsList();
    }

    private string BuildSettingsList()
    {
        StringBuilder retVal = new StringBuilder();
        TupleCollection rowTuples = null;
        HierarchyCollection rowHierarchies = null;
        TupleCollection colTuples = null;
        HierarchyCollection colHierarchies = null;

        rowTuples = this.cellSet.Axes[1].Set.Tuples;
        rowHierarchies = this.cellSet.Axes[1].Set.Hierarchies;
        colTuples = this.cellSet.Axes[0].Set.Tuples;
        colHierarchies = this.cellSet.Axes[0].Set.Hierarchies;

        string lastSettingName = null;
        bool includeSetting = false;
        bool isFirstOption = true;

        for (int x = 0; x < rowTuples.Count; x++)
        {
            Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[x];
            string settingName = rowTuple.Members[0].Caption;
            string optionName = rowTuple.Members[1].Caption;

            if (string.Compare(settingName, lastSettingName) != 0)
            {
                includeSetting = settingName.ToLower().Contains("firmware");
                if (includeSetting)
                {
                    if (retVal.Length > 0)
                        retVal.Append("; ");
                    //retVal.Append(settingName);
                    //retVal.Append(": ");
                }
                lastSettingName = settingName;
                //isFirstOption = true;
            }

            if (includeSetting)
            {
                double tranCount = double.Parse(this.cellSet.Cells[0, x].Value.ToString());
                if (tranCount > 0)
                {
                    if (!isFirstOption)
                        retVal.Append(", ");
                    retVal.Append(optionName);
                    isFirstOption = false;
                }
            }
        }

        return retVal.ToString();
    }
}
