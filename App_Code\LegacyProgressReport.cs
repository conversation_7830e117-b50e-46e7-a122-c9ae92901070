using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class LegacyProgressReport : BaseReport
{
	public LegacyProgressReport(ReportInfo repInfo) : base(repInfo, true) { }

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        StringBuilder mdxSelect = new StringBuilder();
        string mdxCategory = null;
        string mdxFromWhere = null;
        string timeHier = OLAPHelper.GetTimeHier(repInfo.ProgressInfo.GroupingId);
		bool splitByCell = this.repInfo.ProgressInfo.SplitByCell;

        mdxSetup.Append(string.Format("WITH {0}\r\n", OLAPHelper.BuildMdxFilteredTime(repInfo.ProgressInfo.GroupingId, repInfo.StartDate, repInfo.EndDate, "FilteredTime")));
        
		mdxSetup.Append("MEMBER [Transaction Cnt] AS ' " + BuildFilteredMeasure(timeHier, "[Measures].[Transaction Count]", repInfo.StartDate, repInfo.EndDate) + "', FORMAT_STRING = \"#,#\" \r\n");
        mdxSetup.Append("MEMBER [Media Cnt] AS ' " + BuildFilteredMeasure(timeHier, "[Measures].[Media Count]", repInfo.StartDate, repInfo.EndDate) + "', FORMAT_STRING = \"#,#\" \r\n");

        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.RateTypeId1, repInfo.DimensionName,
                repInfo.DimensionMembers, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel1, repInfo.ProgressInfo.SpecRate1));
        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.RateTypeId2, repInfo.ProgressInfo.DimensionName2,
                repInfo.ProgressInfo.DimensionMembers2, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel2, repInfo.ProgressInfo.SpecRate2));
        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.RateTypeId3, repInfo.ProgressInfo.DimensionName3,
                repInfo.ProgressInfo.DimensionMembers3, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel3, repInfo.ProgressInfo.SpecRate3));
        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.RateTypeId4, repInfo.ProgressInfo.DimensionName4,
                repInfo.ProgressInfo.DimensionMembers4, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel4, repInfo.ProgressInfo.SpecRate4));

        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName1, repInfo.DimensionName, repInfo.ProgressInfo.SpecLabel1, repInfo.ProgressInfo.SpecRate1);
        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.DimensionName2, repInfo.ProgressInfo.SpecLabel2, repInfo.ProgressInfo.SpecRate2);
        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.DimensionName3, repInfo.ProgressInfo.SpecLabel3, repInfo.ProgressInfo.SpecRate3);
        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.DimensionName4, repInfo.ProgressInfo.SpecLabel4, repInfo.ProgressInfo.SpecRate4);

        if (splitByCell)
        {
            string cellList = OLAPHelper.BuildMdxCellTuple(true, repInfo.CellFilters);
            if (string.IsNullOrEmpty(cellList))
                cellList = "[Test Location].[Location-Cell].[Cell]";
            string condition = null;

            if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName1) && !string.IsNullOrEmpty(repInfo.DimensionName))
                condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.SeriesName1);

            if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName2) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName2))
				condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.SeriesName2);

            if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName3) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName3))
				condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.SeriesName3);

            if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName4) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName4))
				condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.SeriesName4);

            if (condition == null)
                condition = " [Measures].[Transaction Cnt] > 0 ";

            mdxSelect.Insert(0, string.Format("SELECT ({{[Measures].[Transaction Cnt], [Measures].[Media Cnt], "));
            mdxSelect.Append(string.Format("}}, Filter({0}, {1})) on columns, \r\n", cellList, condition));
        }
        else
        {
            mdxSelect.Insert(0, "SELECT {[Measures].[Transaction Cnt], [Measures].[Media Cnt], ");
            mdxSelect.Append("} on columns, \r\n");
        }

        mdxCategory = "NON EMPTY {FilteredTime} on rows \r\n";

        if (splitByCell)
        {
            if (repInfo.AttachedSessions != null && repInfo.AttachedSessions.Count > 0)
                mdxFromWhere = string.Format("FROM [Transactions] WHERE ({0})", OLAPHelper.BuildMdxWhereTuples(true, repInfo.AttachedSessions, null, null));
            else
                mdxFromWhere = string.Format("FROM [Transactions] ");
        }
        else
        {
            if ((repInfo.AttachedSessions != null && repInfo.AttachedSessions.Count > 0) || (repInfo.CellFilters != null && repInfo.CellFilters.Count > 0))
                mdxFromWhere = string.Format("FROM [Transactions] WHERE ({0})", OLAPHelper.BuildMdxWhereTuples(true, repInfo.AttachedSessions, repInfo.CellFilters, null));
            else
                mdxFromWhere = string.Format("FROM [Transactions] ");
        }

        string mdx = mdxSetup.ToString() + mdxSelect.ToString() + mdxCategory + mdxFromWhere;

        ExecuteMdx(mdx);
        BuildGridDisplay();
    }

    private string BuildFilteredMeasure(string timeHier, string measureName, DateTime startDate, DateTime endDate)
    {
        string retVal = null;

        if (DateTime.MinValue.Equals(startDate) && DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]), {2})",
                timeHier, timeHier, measureName);
        else if (!DateTime.MinValue.Equals(startDate) && DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue >= CDate(\"{3}\")), {4})",
                timeHier, timeHier, timeHier, startDate.ToString("yyyy-MM-dd HH:mm:ss"), measureName);
        else if (DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue < CDate(\"{3}\")), {4})",
                timeHier, timeHier, timeHier, endDate.ToString("yyyy-MM-dd HH:mm:ss"), measureName);
        else // (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue >= CDate(\"{3}\") AND [Time].[{4}].CurrentMember.MemberValue < CDate(\"{5}\")), {6})",
                timeHier, timeHier, timeHier, startDate.ToString("yyyy-MM-dd HH:mm:ss"), timeHier, endDate.ToString("yyyy-MM-dd HH:mm:ss"), measureName);

        return retVal;
    }

    private string BuildProgressSeries(string timeHier, DateTime startDate, DateTime endDate, string seriesName, ReportHelper.RateTypeEnum rateTypeId, string dimensionName,
            List<string> dimensionMembers, bool normalize, string specName, decimal specRate)
    {
        StringBuilder retVal = new StringBuilder();

        if (!string.IsNullOrEmpty(seriesName) && !string.IsNullOrEmpty(dimensionName))
        {
            string normalizeString = null;
            string rateMeasure = null;
            string formatString = null;
			string rawFormatString = null;
            string measureField = BuildMeasureName(dimensionName);

			rawFormatString = "FORMAT_STRING = \"#,#\"";
			
			switch (rateTypeId)
            {
                case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
                    rateMeasure = "[Measures].[Media Cnt]";
                    formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
                    rateMeasure = "[Measures].[Transaction Cnt]";
                    formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                    rateMeasure = "[Measures].[Media Cnt]";
                    formatString = "FORMAT_STRING = \"#,##0.000\\%\"";
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                    rateMeasure = "[Measures].[Transaction Cnt]";
                    formatString = "FORMAT_STRING = \"#,##0.000\\%\"";
                    break;
                default:
                    throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
            }

            if (normalize && specRate > 0)
            {
                normalizeString = "/" + specRate.ToString();
            }

			retVal.Append(string.Format("SET [{0}Set] AS ' {1} '\r\n", seriesName, OLAPHelper.BuildMdxLevelTuple(true, dimensionName, dimensionMembers, false)));
            
			// Add the raw member
			retVal.Append(string.Format("MEMBER [Count: {0}] AS ' IIF({1}>0, SUM( [{2}Set], " + BuildFilteredMeasure(timeHier, measureField, startDate, endDate) + "),NULL) ', {3}\r\n", seriesName, rateMeasure, seriesName, rawFormatString));

			// Add the transformed member
            retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0, SUM( [{2}Set], " + BuildFilteredMeasure(timeHier, measureField, startDate, endDate) + "),NULL) ", seriesName, rateMeasure, seriesName));

            switch (rateTypeId)
            {
                case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
                    retVal.Append("*(1000000/" + rateMeasure + ") " + normalizeString + "', " + formatString + "\r\n");
                    break;
                case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
                    retVal.Append("*(1000000/" + rateMeasure + ") " + normalizeString + "', " + formatString + "\r\n");
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                    retVal.Append("/" + rateMeasure + " * 100 " + normalizeString + "', " + formatString + "\r\n");
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                    retVal.Append("/" + rateMeasure + " * 100 " + normalizeString + "', " + formatString + "\r\n");
                    break;
                default:
                    throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
            }

			// Add the spec member
			if (!string.IsNullOrEmpty(specName) && specRate > 0)
            {
                retVal.Append(string.Format("MEMBER [Spec: {0}] AS ' IIF({1}>0,{2},NULL) ', FORMAT_STRING = \"#,#\"\r\n", specName, rateMeasure, specRate));
            }
        }

        return retVal.ToString();
    }

    private string BuildMeasureName(string dimensionName)
    {
        string retVal = null;

        if (!string.IsNullOrEmpty(dimensionName))
        {
            if (dimensionName.StartsWith("[Metric]."))
                retVal = "[Measures].[Value Sum]";
            else if (dimensionName.StartsWith("[Engineering Field]."))
                retVal = "[Measures].[Field Sum]";
            else if (dimensionName.StartsWith("[Event]."))
                retVal = "[Measures].[Event Count]";
            else if (dimensionName.StartsWith("[Event Status]."))
                retVal = "[Measures].[Status Count]";
            else if (dimensionName.StartsWith("[Event Reason]."))
                retVal = "[Measures].[Reason Count]";
            else
                retVal = "[Measures].[Observation Count]";
        }
        return retVal;
    }

    private string BuildFilteredSeries(string timeHier, string measureName, DateTime startDate, DateTime endDate)
    {
        string retVal = null;

        if (DateTime.MinValue.Equals(startDate) && DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]), {2}))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, measureName);
        else if (!DateTime.MinValue.Equals(startDate) && DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue >= CDate(\"{3}\")), {4}))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, timeHier, startDate.ToString("yyyy-MM-dd HH:mm:ss"), measureName);
        else if (DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue < CDate(\"{3}\")), {4}))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, timeHier, endDate.ToString("yyyy-MM-dd HH:mm:ss"), measureName);
        else // (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal = string.Format("SUM(FILTER(DESCENDANTS([Time].[{0}].CurrentMember, [Time].[{1}].[DateTime]),[Time].[{2}].CurrentMember.MemberValue >= CDate(\"{3}\") AND [Time].[{4}].CurrentMember.MemberValue < CDate(\"{5}\")), {6}))', FORMAT_STRING = \"#,##0\"\r\n",
                timeHier, timeHier, timeHier, startDate.ToString("yyyy-MM-dd HH:mm:ss"), timeHier, endDate.ToString("yyyy-MM-dd HH:mm:ss"), measureName);

        return retVal;
    }


    private void BuildProgressSelect(StringBuilder mdxSelect, string seriesName, string dimensionName, string specName, decimal specRate)
    {
        if (!string.IsNullOrEmpty(seriesName) && !string.IsNullOrEmpty(dimensionName))
        {
            if (mdxSelect.Length > 0)
                mdxSelect.Append(",");
			// Add the raw member
			mdxSelect.Append(string.Format("[Count: {0}]", seriesName));

			// Add the transformed member
            mdxSelect.Append(string.Format(", [{0}]", seriesName));

			// Add the spec
            if (!string.IsNullOrEmpty(specName) && specRate > 0)
            {
                mdxSelect.Append(string.Format(", [Spec: {0}]", specName));
            }
        }
    }

    public override void PopulateChart(Chart chart, bool includeToolTips)
    {
        TupleCollection colTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[1].Set.Hierarchies;

        Dundas.Charting.WebControl.Axis axisX = chart.ChartAreas[0].AxisX;
        Dundas.Charting.WebControl.Axis axisY = chart.ChartAreas[0].AxisY;

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
            priorSeries.Add(series.Name, series);

        chart.Series.Clear();
        double maxYVal = 0;

        // Build chart series data
        for (int col = 0; col < colTuples.Count; col++) // Skip Transaction Count and Media Count
        {
            Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];

			Series series = null;
            string seriesName = null;

            if (colTuple.Members.Count > 1)
                seriesName = string.Format("{0}: {1}", colTuple.Members[0].Caption, colTuple.Members[1].Caption);
            else
                seriesName = colTuple.Members[0].Caption;

			if (seriesName.StartsWith("Count: ") || seriesName.StartsWith("Transaction Cnt: ") || seriesName.StartsWith("Media Cnt: ")
                    || seriesName.Equals("Transaction Cnt") || seriesName.Equals("Media Cnt"))
				continue;

            if (priorSeries.ContainsKey(seriesName))
            {
                series = priorSeries[seriesName];
                priorSeries.Remove(series.Name);
                series.Points.Clear();
            }
            else
            {
                series = new Series(seriesName);
                series["DrawingStyle"] = "Cylinder";

                series.Type = SeriesChartType.Line;
            }
            series.XValueIndexed = true;

            for (int row = 0; row < rowTuples.Count; row++)
            {
                Cell cellA = cellSet.Cells[col, row];

                double YVal = 0;

                if (this.repInfo.ProgressInfo.Normalize && seriesName.StartsWith("Spec: "))
                    YVal = 1;
                else
                    YVal = (cellA.Value != null) ? double.Parse(cellA.Value.ToString()) : 0;

                maxYVal = (maxYVal < YVal ? YVal : maxYVal);

                DataPoint dp = new DataPoint(0, YVal); ;
                dp.AxisLabel = rowTuples[row].Members[0].Caption;

                if (includeToolTips)
                    dp.ToolTip = string.Format("{0}\r\nValue: {1}", series.Name, cellA.FormattedValue);

                series.Points.Add(dp);
            }

            chart.Series.Add(series);
        }

        chart.Titles["Title1"].Text = repInfo.ChartTitle;
		
		if (string.IsNullOrEmpty(axisY.Title))
		{
			axisY.Title = "Rate";
			axisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
		}

        axisY.Minimum = 0;

        if (maxYVal > 0 && maxYVal < int.MaxValue)
        {
            axisY.Maximum = RoundAxis(maxYVal, 100);
            ResetAxisMarks(axisY);
        }

        ResetAxisMarks(axisX);

        axisX.Minimum = 1;
        axisX.Maximum = rowTuples.Count;

        chart.ChartAreas[0].ReCalc();
    }
}
