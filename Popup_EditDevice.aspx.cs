using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

public partial class Popup_EditDevice : System.Web.UI.Page
{
	public string DeviceId
	{
		get { return (string)this.ViewState["d"]; }
		set { this.ViewState["d"] = value; }
	}

	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			statusList.DataSource = Utility.GetDeviceStatusList();
			statusList.DataBind();
			
			deviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList(); ;
            deviceTypeList.DataBind();
			
			productLineList.DataSource = Utility.GetProductLineList();
			productLineList.DataBind();

			familyLineList.DataSource = Utility.GetFamilyLineList();
			familyLineList.DataBind();

			modelTypeList.DataSource = Utility.GetModelTypeList();
			modelTypeList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_ID_KEY])) {
				this.DeviceId = Request.Params[DieboldConstants.DEVICE_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetDevice", this.DeviceId);
				foreach (DataRow row in ds.Tables[0].Rows) {
					this.deviceTypeList.SelectedValue = DataFormatter.getInt32(row, "DeviceTypeId").ToString();
					this.serialNumberField.Text = DataFormatter.getString(row, "SerialNumber");
					this.statusList.SelectedValue = DataFormatter.getInt32(row, "DeviceStatusId").ToString();
					this.productLineList.SelectedValue = DataFormatter.getInt32(row, "ProductLineId").ToString();
					this.familyLineList.SelectedValue = DataFormatter.getInt32(row, "FamilyLineId").ToString();
					this.modelTypeList.SelectedValue = DataFormatter.getInt32(row, "ModelTypeId").ToString();

					bool isTerminalType = DataFormatter.getBool(row, "IsTerminalType");
					if (isTerminalType) {
						this.familyLineRow.Visible = true;
						this.productLineRow.Visible = true;
						this.modelTypeRow.Visible = true;
					}
				}
				this.deviceTypeList.Enabled = false;
			}
			else if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY])) {
				this.deviceTypeList.SelectedValue = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];
				this.deviceTypeList.Enabled = false;

				DetermineIfDeviceIsTerminalType();
            }
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		if (serialNumberField.Text.Contains("#"))
		{
			serialError.Visible = true;
		}
		else
		{
			serialError.Visible = false;
			int result = 1;

			if (string.IsNullOrEmpty(this.DeviceId))
				result = (int)SqlHelper.ExecuteScalar("RPT_InsertDevice", this.deviceTypeList.SelectedValue, this.serialNumberField.Text, this.statusList.SelectedValue,
								this.productLineList.SelectedValue, this.familyLineList.SelectedValue, this.modelTypeList.SelectedValue);
			else
				SqlHelper.ExecuteNonQuery("RPT_UpdateDevice", this.DeviceId, this.serialNumberField.Text, this.statusList.SelectedValue,
					this.productLineList.SelectedValue, this.familyLineList.SelectedValue, this.modelTypeList.SelectedValue);

			if (result == 0) {
				this.ErrorLabel.Visible = true;
			}
			else {
				if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditDevices.aspx?{0}={1}&page={2}';CloseRadWindow(); ", DieboldConstants.DEVICE_TYPE_KEY, this.deviceTypeList.SelectedValue, this.PageNo), true);
				else
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditDevices.aspx?{0}={1}';CloseRadWindow(); ", DieboldConstants.DEVICE_TYPE_KEY, this.deviceTypeList.SelectedValue), true);
			}
		}
	}

	protected void deviceTypeList_SelectedIndexChanged(object sender, EventArgs e) {
		DetermineIfDeviceIsTerminalType();
    }

	private void DetermineIfDeviceIsTerminalType() {
		if (!string.IsNullOrEmpty(this.deviceTypeList.SelectedValue)) {
			int curDeviceId = Convert.ToInt32(this.deviceTypeList.SelectedValue);

			DataSet ds = SqlHelper.ExecuteDataset("RPT_GetDeviceType", curDeviceId);
			foreach (DataRow row in ds.Tables[0].Rows) {
				bool isTerminalType = DataFormatter.getBool(row, "IsTerminalType");
				if (isTerminalType) {
					this.familyLineRow.Visible = true;
					this.productLineRow.Visible = true;
					this.modelTypeRow.Visible = true;
				}
				else {
					this.familyLineRow.Visible = false;
					this.productLineRow.Visible = false;
					this.modelTypeRow.Visible = false;
				}
			}
		}
	}
}
