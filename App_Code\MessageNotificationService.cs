using System;
using System.Web;
using System.Data;
using System.Web.Security;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;

[WebService(Namespace = "http://diebold.com/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
public class MessageNotificationService : System.Web.Services.WebService {

    public MessageNotificationService () 
	{
    }

	[WebMethod]
	public DateTime GetLastLogin(string userName)
	{
		DateTime retVal = DateTime.MinValue;

		object lastLoginObj = SqlHelper.ExecuteScalar("RPT_GetMessagesLastLogin", userName);
		if (lastLoginObj != null && lastLoginObj != DBNull.Value)
			retVal = (Convert.ToDateTime(lastLoginObj));

		return retVal;
	}

	[WebMethod]
	public void UpdateLastLogin(string userName, DateTime lastLogin)
	{
		SqlHelper.ExecuteNonQuery("RPT_UpdateSubscriptionLastLogin", userName, DateTime.Now);
	}

	[WebMethod]
	public DataSet GetAllMessages()
	{
		return MessageSource.GetAllMessages();
	}

	[WebMethod]
	public DataSet GetNewMessages(string userName, DateTime lastLogin)
	{
		DataSet retVal = new DataSet();
		if (!string.IsNullOrEmpty(userName))
			retVal = MessageSource.GetNewMessages(userName, lastLogin);
		else
			throw new ApplicationException("Unable to get new messages information.");

		return retVal;
	}
}

