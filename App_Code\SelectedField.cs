using System;
using System.Data;
using System.Configuration;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

[Serializable()]
public class SelectedField : ISerializable
{
	public bool IsSelected = false;
	public int FieldTypeId = 0;
	public string FieldName = null;
	public int FieldId = 0;
	public List<SelectedFieldOption> SelectedFieldOptions = new List<SelectedFieldOption>();

	// Default constructor.
	public SelectedField() { }

	// Deserialization constructor.
	public SelectedField(SerializationInfo info, StreamingContext context)
    {
		IsSelected = (bool)info.GetValue("IsSelected", typeof(bool));
		FieldTypeId = (int)info.GetValue("FieldTypeId", typeof(int));
		FieldName = (string)info.GetValue("FieldName", typeof(string));
		FieldId = (int)info.GetValue("FieldId", typeof(int));
		SelectedFieldOptions = (List<SelectedFieldOption>)info.GetValue("SelectedFieldOptions", typeof(List<SelectedFieldOption>));
    }

    // Serialization function.
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
		info.AddValue("IsSelected", IsSelected);
		info.AddValue("FieldTypeId", FieldTypeId);
		info.AddValue("FieldName", FieldName);
        info.AddValue("FieldId", FieldId);
		info.AddValue("SelectedFieldOptions", SelectedFieldOptions);
    }	
}

