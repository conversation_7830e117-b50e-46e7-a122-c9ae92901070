using System;
using System.Configuration;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Xml;
using Microsoft.AnalysisServices.AdomdClient;

/// <summary>
/// Summary description for BaseReport
/// </summary>
public abstract class BaseReport
{
    public List<List<string>> ColumnDisplayHeaders = new List<List<string>>();
    public List<List<string>> RowDisplayHeaders = new List<List<string>>();
    public List<List<string>> RowDisplayData = new List<List<string>>();
    protected bool HideRowHeaders = false;

    protected ReportInfo repInfo = null;
    protected bool isLegacy = false;
    protected CellSet cellSet = null;
	protected DataSet dataSet = null;

    public virtual bool IsStale
    {
        get
        {
            return ((this.cellSet == null) || (DateTime.Now.AddMinutes(0 - DieboldConstants.DEFAULT_REPORT_CACHE_MINUTES) >= repInfo.LastSaveDate));
        }
    }

    public BaseReport(ReportInfo repInfo, bool isLegacy)
	{
        this.repInfo = repInfo;
        this.isLegacy = isLegacy;

        if (!string.IsNullOrEmpty(this.repInfo.CellSetData))
        {
            XmlReader reader = XmlReader.Create(new StringReader(this.repInfo.CellSetData));
            try
            {
                cellSet = CellSet.LoadXml(reader);
            }
            catch
            {
                this.repInfo.CellSetData = null;
                cellSet = null;
            }
            finally
            {
                reader.Close();
            }
        }
	}

    protected void ExecuteMdx(string mdx)
    {
        XmlReader reader = OLAPHelper.ExecuteXmlReader(isLegacy, mdx);
        this.repInfo.CellSetData = reader.ReadOuterXml();
        reader.Close();

        reader = XmlReader.Create(new StringReader(this.repInfo.CellSetData));
        try
        {
            cellSet = CellSet.LoadXml(reader);
        }
        catch
        {
            this.repInfo.CellSetData = null;
            cellSet = null;
        }
        finally
        {
            reader.Close();
        }

        // Retry one time to accommodate a reprocessed cube 
        if (this.repInfo.CellSetData == null || cellSet == null)
        {
            reader = OLAPHelper.ExecuteXmlReader(isLegacy, mdx);
            this.repInfo.CellSetData = reader.ReadOuterXml();
            reader.Close();

            reader = XmlReader.Create(new StringReader(this.repInfo.CellSetData));
            try
            {
                cellSet = CellSet.LoadXml(reader);
            }
            catch(Exception ex)
            {
                this.repInfo.CellSetData = null;
                cellSet = null;
                if (string.Compare("The response is not in data set format.", ex.Message) == 0)
                    throw new ApplicationException(string.Format("Invalid MDX query string: {0}", mdx));
                else
                    throw ex;
            }
            finally
            {
                reader.Close();
            }
        }
    }

    public virtual void InitializeChart(Dundas.Charting.WebControl.Chart chart, string pageOrControlName)
    {
        string origTemplateStr = (string)HttpContext.Current.Session["OriginalTemplate:" + pageOrControlName];
        if (string.IsNullOrEmpty(origTemplateStr))
            HttpContext.Current.Session["OriginalTemplate:" + pageOrControlName] = ReportHelper.ConvertChartToTemplate(chart);
        else
            ReportHelper.ApplyTemplateToChart(chart, origTemplateStr);

        ReportHelper.ApplyTemplateToChart(chart, this.repInfo.ReportData);
    }

    public abstract void LoadData();

    public virtual void BuildGridDisplay()
    {
        TupleCollection rowTuples = null;
        HierarchyCollection rowHierarchies = null;
        TupleCollection colTuples = null;
        HierarchyCollection colHierarchies = null;
        int maxColumns = 0;
        bool seriesAsColumns = true;

        if (this.repInfo.ReportTypeId == ReportHelper.ReportTypeEnum.LEGACY_PARETO
            || this.repInfo.ReportTypeId == ReportHelper.ReportTypeEnum.PARETO)
        {
            maxColumns = this.repInfo.ParetoInfo.MaxColumns;
            seriesAsColumns = false;
        }
        
        if (seriesAsColumns)
        {
            rowTuples = this.cellSet.Axes[1].Set.Tuples;
            rowHierarchies = this.cellSet.Axes[1].Set.Hierarchies;
            colTuples = this.cellSet.Axes[0].Set.Tuples;
            colHierarchies = this.cellSet.Axes[0].Set.Hierarchies;
        }
        else
        {
            rowTuples = this.cellSet.Axes[0].Set.Tuples;
            rowHierarchies = this.cellSet.Axes[0].Set.Hierarchies;
            colTuples = this.cellSet.Axes[1].Set.Tuples;
            colHierarchies = this.cellSet.Axes[1].Set.Hierarchies;
        }

        this.ColumnDisplayHeaders = new List<List<string>>();
        List<string> categoryList = new List<string>();

		for (int x = 0; x < colHierarchies.Count; x++)
		{
			Hierarchy colHier = colHierarchies[x];
            List<string> curLevel = new List<string>();
            for (int y = 0; y < colTuples.Count; y++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[y];
                string catName = colTuple.Members[0].Caption;
                if (!categoryList.Contains(catName))
                {
                    if (maxColumns == 0 || categoryList.Count < maxColumns)
                        categoryList.Add(catName);
                }
                if (categoryList.Contains(catName))
                {
                    curLevel.Add(colTuple.Members[x].Caption);
                }
            }
            if(curLevel.Count > 0)
                this.ColumnDisplayHeaders.Add(curLevel);
		}

        this.RowDisplayHeaders = new List<List<string>>();
        if (!this.HideRowHeaders)
        {
            for (int x = 0; x < rowHierarchies.Count; x++)
            {
                Hierarchy rowHier = rowHierarchies[x];
                List<string> curLevel = new List<string>();
                for (int y = 0; y < rowTuples.Count; y++)
                {
                    Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[y];
                    curLevel.Add(rowTuple.Members[x].Caption);
                }

                this.RowDisplayHeaders.Add(curLevel);
            }
        }

        this.RowDisplayData = new List<List<string>>();
		for (int row = 0; row < rowTuples.Count; row++)
		{
            List<string> curRowData = new List<string>();
			for (int col = 0; col < colTuples.Count; col++)
			{
                Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
                string catName = colTuple.Members[0].Caption;
                if (categoryList.Contains(catName))
				{
					Cell cellA = null;
					string formattedValue = null;
					string tempName = catName;

					if (seriesAsColumns)
						cellA = this.cellSet.Cells[col, row];
					else
						cellA = this.cellSet.Cells[row, col];

                    if (cellA.Value != null && cellA.FormattedValue != null && double.Parse(cellA.Value.ToString()) < 0)
                    {
                        formattedValue = "~" + cellA.FormattedValue.Replace("-", "");
                    }
                    else
                    {
                        formattedValue = cellA.FormattedValue;
                    }

					if (tempName.StartsWith("Spec: "))
						tempName = tempName.Substring("Spec: ".Length, tempName.Length - "Spec: ".Length);

					if (tempName.StartsWith("Count: "))
						tempName = tempName.Substring("Count: ".Length, tempName.Length - "Count: ".Length);

					if ((this.repInfo.ReportTypeId == ReportHelper.ReportTypeEnum.LEGACY_PROGRESS || this.repInfo.ReportTypeId == ReportHelper.ReportTypeEnum.PROGRESS)
							&& ((string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName1) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format1) == 0)
							|| (string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName2) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format2) == 0)
							|| (string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName3) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format3) == 0)
							|| (string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName4) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format4) == 0))
						)
					{
						double YVal = 0;
						if (this.repInfo.ProgressInfo.Normalize && catName.StartsWith("Spec: "))
							YVal = 1;
						else
							YVal = (cellA.Value != null) ? double.Parse(cellA.Value.ToString()) : 0;

						formattedValue = String.Format("{0:X}", Convert.ToInt32(YVal));
					}

					//Create links on Distribution Reports to perform an Advanced Transaction search using the selected Quantized Value
					if (col == 0 && this.repInfo.ReportTypeId == ReportHelper.ReportTypeEnum.DISTRIBUTION)
					{
						formattedValue = string.Format("<a style=\"cursor:pointer;\" onclick=\"distroValueClick({0}); return false; \">{0}</a>", formattedValue);
					}

					curRowData.Add(formattedValue);
				}
			}

			this.RowDisplayData.Add(curRowData);
		}
    }

    public abstract void PopulateChart(Dundas.Charting.WebControl.Chart chart, bool includeToolTips);

    public byte[] ConvertChartToGif(Dundas.Charting.WebControl.Chart chart)
    {
        MemoryStream stream = new MemoryStream();

        chart.Save(stream, Dundas.Charting.WebControl.ChartImageFormat.Gif);
        stream.Flush();

        return stream.ToArray();
    }

    protected int RoundAxis(double newMax, int defaultValue)
    {
        int retVal = 0;
        double smallMax = 0;

        int len = ((int)Math.Ceiling(newMax)).ToString().Length - 2;

        if (len > 0)
            smallMax = Math.Ceiling(Math.Ceiling((double)newMax / (Math.Pow(10, len))));
        else
            smallMax = Math.Ceiling((double)newMax);

        smallMax++;

        if ((smallMax % 5) > 0)
            smallMax = (smallMax + 5 - (smallMax % 5));

        if (len > 0)
            retVal = (int)(smallMax * Math.Pow(10, len));
		else
		{
			retVal = (int)smallMax;
			if (retVal == 5 && newMax <= 2)
				retVal = 2;
			else if (retVal == 5 && newMax <= 4)
				retVal = 4;
		}

        if (retVal <= 0)
            retVal = defaultValue;

        return retVal;
    }

    protected void ResetAxisMarks(Dundas.Charting.WebControl.Axis axis)
    {
        axis.CustomLabels.Clear();
        axis.LabelStyle.Interval = 0;
        axis.LabelStyle.IntervalOffset = 0;
        axis.Interval = 0;
        axis.IntervalOffset = 0;
        axis.MajorGrid.Interval = 0;
        axis.MajorGrid.IntervalOffset = 0;
        axis.MinorGrid.Interval = 0;
        axis.MinorGrid.IntervalOffset = 0;
        axis.MajorTickMark.Interval = 0;
        axis.MajorTickMark.IntervalOffset = 0;
        axis.MinorTickMark.Interval = 0;
        axis.MinorTickMark.IntervalOffset = 0;
    }

    protected void FixAxisMarks(Dundas.Charting.WebControl.Axis axis, double maxValue)
    {
        axis.CustomLabels.Clear();
        axis.LabelStyle.Interval = maxValue/4;
        axis.LabelStyle.IntervalOffset = 0;
        axis.Interval = maxValue/4;
        axis.IntervalOffset = 0;
        axis.MajorGrid.Interval = maxValue/4;
        axis.MajorGrid.IntervalOffset = 0;
        axis.MinorGrid.Interval = maxValue/20;
        axis.MinorGrid.IntervalOffset = 0;
        axis.MajorTickMark.Interval = maxValue/4;
        axis.MajorTickMark.IntervalOffset = 0;
        axis.MinorTickMark.Interval = maxValue/20;
        axis.MinorTickMark.IntervalOffset = 0;
    }

    protected void SetChartTitle(Dundas.Charting.WebControl.Chart chart, string titleName, string text, Dundas.Charting.WebControl.Docking docking, ContentAlignment alignment, Font font)
    {
        SetChartTitle(chart, titleName, text, docking, alignment, font, Color.Black);
    }

    protected void SetChartTitle(Dundas.Charting.WebControl.Chart chart, string titleName, string text, Dundas.Charting.WebControl.Docking docking, ContentAlignment alignment, Font font, Color color)
    {
        Dundas.Charting.WebControl.Title tmpTitle = null;

        try
        {
            tmpTitle = chart.Titles[titleName];
        }
        catch { }

        if (tmpTitle == null)
            tmpTitle = chart.Titles.Add(titleName);

        tmpTitle.Text = text;
        tmpTitle.Docking = docking;
        tmpTitle.Alignment = alignment;
        tmpTitle.Name = titleName;
        tmpTitle.Font = font;
        tmpTitle.Color = color;
    }
}
