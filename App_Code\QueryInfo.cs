﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class QueryInfo : ISerializable
{
	public Int64 MinTranId = 0;
	public Int64 MaxTranId = 0;
	public bool AcceptLevel3Notes = false;
	public int FieldTypeId = 0;
	
	// Default constructor.
	public QueryInfo() { }

	// Deserialization constructor.
	public QueryInfo(SerializationInfo info, StreamingContext context)
	{
		MinTranId = (Int64)info.GetValue("min", typeof(Int64));
		MaxTranId = (Int64)info.GetValue("max", typeof(Int64));
		AcceptLevel3Notes = (bool)info.GetValue("level", typeof(bool));
		FieldTypeId = (int)info.GetValue("fti", typeof(int));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("min", MinTranId);
		info.AddValue("max", MaxTranId);
		info.AddValue("level", AcceptLevel3Notes);
		info.AddValue("fti", FieldTypeId);
	}
}