using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditInvestigationArea : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string InvestigationAreaId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.INVESTIGATION_AREA_ID_KEY]))
			{
				this.InvestigationAreaId = Request.Params[DieboldConstants.INVESTIGATION_AREA_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetInvestigationArea", this.InvestigationAreaId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.investigationAreaNameField.Text = DataFormatter.getString(row, "InvestigationAreaName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(InvestigationAreaId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertInvestigationArea", this.investigationAreaNameField.Text);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateInvestigationArea", Convert.ToInt32(this.InvestigationAreaId), this.investigationAreaNameField.Text, this.isActive.Checked);

		if (result == 0)
			this.ErrorLabel.Visible = true;
		else
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditInvestigationArea.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditInvestigationArea.aspx';CloseRadWindow(); ", true);
	}
}
