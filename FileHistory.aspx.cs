﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QueueServiceClient;

public partial class FileHistory : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
		string fileName = Request.QueryString["f"];
		if (!string.IsNullOrEmpty(fileName))
		{
			this.fileNameLabel.Text = fileName;

			List<SourceControlHistoryInfo> historyList = SourceControlClient.GetFileHistory(fileName);
			fileHistoryRep.DataSource = historyList;
			fileHistoryRep.DataBind();
		}
    }

	protected void DownloadBtn_Click(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null)
		{
			byte[] fileData = SourceControlClient.GetFileData(e.CommandArgument.ToString());
			if (fileData != null && fileData.Length > 0)
			{
				Response.ClearContent();
				Response.ClearHeaders();
				Response.ContentType = "application/octet-stream";
				Response.AppendHeader("content-disposition", "attachment; filename=" + this.fileNameLabel.Text + ";");
				Response.BinaryWrite(fileData);
				Response.End();
			}
		}
	}
}