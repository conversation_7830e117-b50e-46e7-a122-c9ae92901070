using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.IO;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class GeneratePDF : System.Web.UI.Page
{
	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(Request.Params["r"]))
			this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString["r"]));
		else
			this.RepInfo = Utility.GetReportInfoFromTransfer();

		if (this.RepInfo == null)
			throw new ApplicationException("Unable to generate a PDF, could not load the Report Info.");

		string tempFileName = ReportHelper.GeneratePDF(this.RepInfo, Utility.GetPrintReportPageName(this.RepInfo.ReportTypeId));
		string displayName = this.RepInfo.ReportName + "_" + DateTime.Now.Ticks + ".pdf";

		try
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = "application/pdf";
			Response.AppendHeader("content-disposition", "attachment; filename=" + displayName + ";");

			Response.WriteFile(tempFileName);
			Response.Flush();
		}
		finally
		{
			if (!string.IsNullOrEmpty(tempFileName) && File.Exists(tempFileName))
				File.Delete(tempFileName);
		}

		Response.End();
	}
}
