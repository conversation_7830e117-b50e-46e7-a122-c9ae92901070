using System;
using System.Data;
using System.Text;
using System.Web.UI;

public partial class ScheduledReports : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_ScheduledReports");
		ds.Relations.Add("Children", ds.Tables[0].Columns["ScheduledReportId"], ds.Tables[1].Columns["ScheduledReportId"]);

		RadGrid1.DataSource = ds;
		RadGrid1.DataBind();

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				RadGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
	}

	protected string GetReports(object dataItem)
	{
		StringBuilder sb = new StringBuilder();
		if (dataItem != null)
		{
			sb.Append("<br />");
			foreach (DataRowView rowView in ((DataRowView)dataItem).CreateChildView("Children"))
			{
				sb.Append(DataFormatter.Format(rowView, "ReportName"));
				sb.Append("<br />");
			}
			sb.Append("<br />");

		}
		return sb.ToString();
	}

	protected string GetEmailList(object dataItem)
	{
		StringBuilder sb = new StringBuilder();
        string emailList = DataFormatter.Format(dataItem, "EmailList");
        if (!string.IsNullOrEmpty(emailList)) {
            string[] emails = emailList.Replace(",", ";").Replace(" ", "").Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);

            if (dataItem != null) {
                sb.Append("<br />");
                foreach (string email in emails) {
                    sb.Append(email);
                    sb.Append("<br />");
                }
                sb.Append("<br />");
            }
        }
		return sb.ToString();
	}
}
