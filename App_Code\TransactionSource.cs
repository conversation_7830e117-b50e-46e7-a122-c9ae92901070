using System;
using System.Data;
using System.Web;

public class TransactionSource
{
    public static DataSet GetTransactions(bool allowLiteSearch, bool onlyShowObservations, string observationText, DateTime startDate, DateTime endDate, 
		DateTime startTime, DateTime endTime, bool includePastSessions, int transactionIdStart, int transactionIdEnd,
		string sessionList, string cellList, int deviceTypeId, string deviceList, string failureLocationList, string failureTypeList, string moduleTypeList, 
		string investigationAreaList, string triageTypeList, string disciplineList, string ownerList, string operatorList, string eventList, string mediaNumberList, 
		string fileNumberList, string commandNumberList, string statisticFieldsList, string statisticFieldOptionsList, string settingList, 
		string settingList2, string settingList3, decimal minQuantizedValue, decimal maxQuantizedValue, 
		DateTime projectedImplementationStartDate, DateTime projectedImplementationEndDate, string solutionStateList, bool includeSettings)
	{
        bool doLiteSearch = allowLiteSearch;
        bool doAdvancedSearch = false;
        bool hasSearchCriteriaSpecified = false;
		string obvText = null;
		object startDateObj = null;
		object endDateObj = null;
		object tranIdStart = null;
		object tranIdEnd = null;
		object deviceTypeIdObj = null;
		object minQuantizedValObj = null;
		object maxQuantizedValObj = null;
		object projectedImplementationStartDateObj = null;
		object projectedImplementationEndDateObj = null;

		if (!string.IsNullOrEmpty(observationText))
		{
			obvText = observationText;
			doAdvancedSearch = true;
            hasSearchCriteriaSpecified = true;

        }
		if (!DateTime.MinValue.Equals(startDate) && !string.IsNullOrEmpty(startDate.ToString()))
		{
			TimeSpan time = TimeSpan.Zero;

			if (startTime != null)
				time = new TimeSpan(startTime.Hour, startTime.Minute, startTime.Second);

			startDateObj = startDate.Add(time);
			doAdvancedSearch = true;
            hasSearchCriteriaSpecified = true;
        }
		if (!DateTime.MinValue.Equals(endDate) && !string.IsNullOrEmpty(endDate.ToString()))
		{
			TimeSpan time = TimeSpan.Zero;

			if (endTime != null)
				time = new TimeSpan(endTime.Hour, endTime.Minute, endTime.Second);

			endDateObj = endDate.Add(time);
			doAdvancedSearch = true;
            hasSearchCriteriaSpecified = true;
        }
        if (!string.IsNullOrEmpty(sessionList)) {
            doAdvancedSearch = true;
            hasSearchCriteriaSpecified = true;
        }
				
		if (transactionIdStart > 0)
		{
			tranIdStart = transactionIdStart;
			doAdvancedSearch = true;
            hasSearchCriteriaSpecified = true;
        }
		if (transactionIdEnd > 0)
		{
			tranIdEnd = transactionIdEnd;
			doAdvancedSearch = true;
            hasSearchCriteriaSpecified = true;
        }

        if (!string.IsNullOrEmpty(cellList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }
		
		if (deviceTypeId > 0)
		{
			deviceTypeIdObj = deviceTypeId;
            doLiteSearch = false;
            doAdvancedSearch = true;
		}

        if (!string.IsNullOrEmpty(deviceList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(failureLocationList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }
		
		if (!string.IsNullOrEmpty(failureTypeList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(moduleTypeList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(investigationAreaList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(triageTypeList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(disciplineList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(ownerList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(operatorList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(eventList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(mediaNumberList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(fileNumberList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(commandNumberList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(statisticFieldsList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(statisticFieldOptionsList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(settingList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(settingList2)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!string.IsNullOrEmpty(settingList3)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (minQuantizedValue != Decimal.MinValue)
		{
			minQuantizedValObj = minQuantizedValue;
            doLiteSearch = false;
			doAdvancedSearch = true;
		}
		if (maxQuantizedValue != Decimal.MinValue) 
		{
			maxQuantizedValObj = maxQuantizedValue;
            doLiteSearch = false;
			doAdvancedSearch = true;
		}

		if (!string.IsNullOrEmpty(solutionStateList)) {
            doLiteSearch = false;
            doAdvancedSearch = true;
        }

        if (!DateTime.MinValue.Equals(projectedImplementationStartDate)) {
			projectedImplementationStartDateObj = projectedImplementationStartDate;
            doLiteSearch = false;
            doAdvancedSearch = true;
		}
		if (!DateTime.MinValue.Equals(projectedImplementationEndDate)) {
			projectedImplementationEndDateObj = projectedImplementationEndDate;
            doLiteSearch = false;
            doAdvancedSearch = true;
		}

        if (doLiteSearch && hasSearchCriteriaSpecified) {
            return SqlHelper.ExecuteDataset("RPT_SearchTransactionsLite", onlyShowObservations, obvText, startDateObj, endDateObj, includePastSessions,
                                                sessionList, tranIdStart, tranIdEnd);
        }
		else if (doAdvancedSearch)
		{
			return SqlHelper.ExecuteDataset("RPT_SearchTransactions", onlyShowObservations, obvText, startDateObj, endDateObj, includePastSessions,
												sessionList, tranIdStart, tranIdEnd, cellList, deviceTypeIdObj, deviceList, failureLocationList,
                                                failureTypeList, moduleTypeList, investigationAreaList, triageTypeList, disciplineList, ownerList, operatorList, 
												eventList, mediaNumberList, fileNumberList, commandNumberList, statisticFieldsList, statisticFieldOptionsList, 
												settingList, settingList2, settingList3, minQuantizedValObj, maxQuantizedValObj, 
												projectedImplementationStartDateObj, projectedImplementationEndDateObj, solutionStateList, includeSettings);
		}
		else
		{
            //Return Recent
            return SqlHelper.ExecuteDataset("RPT_GetRecentTransactions", includePastSessions, onlyShowObservations);
		}
	}

	public static DataSet GetTransactionsFromSessionParameters() {
        //Function used for Mass Edit screen for Observations

        System.Web.SessionState.HttpSessionState s = HttpContext.Current.Session;

		bool observationsOnly = true;
		string searchText = null;
		DateTime startDate = DateTime.MinValue;
		DateTime endDate = DateTime.MinValue;
		bool includeInactiveSessions = false;
		int tranIdStart = 0;
		int tranIdEnd = 0;
		string sessions = null;
		string cells = null;
		int deviceTypeId = 0;
		string devices = null;
		string failureLocations = null;
		string failureTypes = null;
		string moduleTypes = null;
		string investigationAreas = null;
		string triageTypes = null;
		string disciplines = null;
		string owners = null;
		string operators = null;
		string events = null;
		string mediaNumbers = null;
		string fileNumbers = null;
		string commandNumbers = null;
		string statFields = null;
		string statFieldOptions = null;
		string setting1 = null;
		string setting2 = null;
		string setting3 = null;
		decimal minQuantizedValue = decimal.MinValue;
		decimal maxQuantizedValue = decimal.MinValue;
		DateTime projectedStartDate = DateTime.MinValue;
		DateTime projectedEndDate = DateTime.MinValue;
		string solutionState = null;

        try {
            if (s[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY] != null)
                bool.TryParse(s[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY].ToString(), out observationsOnly);

            if (s[DieboldConstants.ADV_SEARCH_TEXT] != null)
                searchText = (string)s[DieboldConstants.ADV_SEARCH_TEXT];

            if (s[DieboldConstants.ADV_SEARCH_START_DATE] != null)
                DateTime.TryParse(s[DieboldConstants.ADV_SEARCH_START_DATE].ToString(), out startDate);

            if (s[DieboldConstants.ADV_SEARCH_END_DATE] != null)
                DateTime.TryParse(s[DieboldConstants.ADV_SEARCH_END_DATE].ToString(), out endDate);

            if (s[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY] != null)
                bool.TryParse(s[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY].ToString(), out includeInactiveSessions);

            if (s[DieboldConstants.ADV_SEARCH_TRANSACTION_START] != null)
                int.TryParse(s[DieboldConstants.ADV_SEARCH_TRANSACTION_START].ToString(), out tranIdStart);

            if (s[DieboldConstants.ADV_SEARCH_TRANSACTION_END] != null)
                int.TryParse(s[DieboldConstants.ADV_SEARCH_TRANSACTION_END].ToString(), out tranIdEnd);

            if (s[DieboldConstants.ADV_SEARCH_SESSION] != null)
                sessions = (string)s[DieboldConstants.ADV_SEARCH_SESSION];

            if (s[DieboldConstants.ADV_SEARCH_CELL] != null)
                cells = (string)s[DieboldConstants.ADV_SEARCH_CELL];

            if (s[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] != null)
                int.TryParse(s[DieboldConstants.ADV_SEARCH_DEVICE_TYPE].ToString(), out deviceTypeId);

            if (s[DieboldConstants.ADV_SEARCH_DEVICE] != null)
                devices = (string)s[DieboldConstants.ADV_SEARCH_DEVICE];

            if (s[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] != null)
                failureLocations = (string)s[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION];

            if (s[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] != null)
                failureTypes = (string)s[DieboldConstants.ADV_SEARCH_FAILURE_TYPE];

            if (s[DieboldConstants.ADV_SEARCH_MODULE_TYPE] != null)
                moduleTypes = (string)s[DieboldConstants.ADV_SEARCH_MODULE_TYPE];

            if (s[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] != null)
                investigationAreas = (string)s[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA];

            if (s[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] != null)
                triageTypes = (string)s[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE];

            if (s[DieboldConstants.ADV_SEARCH_DISCIPLINE] != null)
                disciplines = (string)s[DieboldConstants.ADV_SEARCH_DISCIPLINE];

            if (s[DieboldConstants.ADV_SEARCH_OWNER] != null)
                owners = (string)s[DieboldConstants.ADV_SEARCH_OWNER];

            if (s[DieboldConstants.ADV_SEARCH_OPERATOR] != null)
                operators = (string)s[DieboldConstants.ADV_SEARCH_OPERATOR];

            if (s[DieboldConstants.ADV_SEARCH_EVENT] != null)
                events = (string)s[DieboldConstants.ADV_SEARCH_EVENT];

            if (s[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] != null)
                mediaNumbers = (string)s[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER];

            if (s[DieboldConstants.ADV_SEARCH_FILE_NUMBER] != null)
                fileNumbers = (string)s[DieboldConstants.ADV_SEARCH_FILE_NUMBER];

            if (s[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] != null)
                commandNumbers = (string)s[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER];

            if (s[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] != null)
                statFields = (string)s[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS];

            if (s[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] != null)
                statFieldOptions = (string)s[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS];

            if (s[DieboldConstants.ADV_SEARCH_SETTING] != null)
                setting1 = (string)s[DieboldConstants.ADV_SEARCH_SETTING];

            if (s[DieboldConstants.ADV_SEARCH_SETTING2] != null)
                setting2 = (string)s[DieboldConstants.ADV_SEARCH_SETTING2];

            if (s[DieboldConstants.ADV_SEARCH_SETTING3] != null)
                setting3 = (string)s[DieboldConstants.ADV_SEARCH_SETTING3];

            if (s[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] != null)
                decimal.TryParse(s[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE].ToString(), out minQuantizedValue);

            if (s[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] != null)
                decimal.TryParse(s[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE].ToString(), out maxQuantizedValue);

            if (s[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] != null)
                DateTime.TryParse(s[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE].ToString(), out projectedStartDate);

            if (s[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] != null)
                DateTime.TryParse(s[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE].ToString(), out projectedEndDate);

            if (s[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] != null)
                solutionState = (string)s[DieboldConstants.ADV_SEARCH_SOLUTION_STATE];

        }
        catch (Exception ex) {
            throw new ApplicationException("Error loading session parameters: " + ex.Message + " : " + ex.StackTrace);
        }
		return GetTransactions(false, observationsOnly, searchText, startDate, endDate, DateTime.MinValue, DateTime.MinValue, includeInactiveSessions, tranIdStart, tranIdEnd, sessions, cells, deviceTypeId, devices,
								failureLocations, failureTypes, moduleTypes, investigationAreas, triageTypes, disciplines, owners, operators, events, mediaNumbers, fileNumbers, commandNumbers, statFields, statFieldOptions,
								setting1, setting2, setting3, minQuantizedValue, maxQuantizedValue, projectedStartDate, projectedEndDate, solutionState, false);
	}
}
