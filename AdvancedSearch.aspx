<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="AdvancedSearch.aspx.cs" Inherits="AdvancedSearch" %>
<%@ register tagprefix="rpt" tagname="StatisticSelector" src="~/controls/StatisticSelector.ascx" %>
<%@ register tagprefix="rpt" tagname="SettingSelector" src="~/controls/SettingSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
<script type="text/javascript">
	function ToggleStartDatePopup() { $find("<%= projectedStartDateField.ClientID %>").showPopup(); }  
	function ToggleEndDatePopup() { $find("<%= projectedEndDateField.ClientID %>").showPopup(); }
	function ToggleStartTimePopup()
	{
		var picker = $find("<%= projectedStartTimeField.ClientID %>");
		picker.showTimePopup();
	}   
	function ToggleEndTimePopup()
	{
		var picker = $find("<%= projectedEndTimeField.ClientID %>");
		picker.showTimePopup();
	}   
</script>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="SearchButton">
    
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Advanced Search</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>
                			
				<div class="widget">
				
					<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
						<AjaxSettings>
							<telerik:AjaxSetting AjaxControlID="deviceTypeList">
								<UpdatedControls>
									<telerik:AjaxUpdatedControl ControlID="deviceList" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
									<telerik:AjaxUpdatedControl ControlID="failureLocationList" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
									<telerik:AjaxUpdatedControl ControlID="eventList" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
									<telerik:AjaxUpdatedControl ControlID="statisticSelector" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
									<telerik:AjaxUpdatedControl ControlID="settingSelector" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
								</UpdatedControls>
							</telerik:AjaxSetting>
						</AjaxSettings>
					</telerik:RadAjaxManager>
					
					<div class="title" style="padding-bottom:2px;">Select or fill in your desired criteria.</div>
					<table border="0" cellpadding="0" cellspacing="0" style="width:100%;">
						<tr>	
							<td style="width:50%; vertical-align:top;">
								<table border="0" cellpadding="0" cellspacing="10" style="width:100%;">
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Device Type:</td>
										<td>
											<telerik:radcombobox id="deviceTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
												 appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" autopostback="true"
												 onselectedindexchanged="DevceTypeList_SelectedIndexChanged" causesvalidation="false">
											</telerik:radcombobox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Device:</td>
										<td>
											<asp:listbox id="deviceList" runat="server" appenddatabounditems="true"
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="280px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Failure Location:</td>
										<td>
											<asp:listbox id="failureLocationList" runat="server" appenddatabounditems="true"
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="280px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Event:</td>
										<td>
											<asp:listbox id="eventList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="280px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Statistic:</td>
										<td>
											<rpt:statisticselector id="statisticSelector" runat="server"></rpt:statisticselector>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Setting:</td>
										<td>
											<rpt:settingselector id="settingSelector" runat="server"></rpt:settingselector>
										</td>
									</tr>
                                    <tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Cell:</td>
										<td>
											<asp:listbox id="cellList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="9" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
                                    <tr>
                                        <td style="width:130px; vertical-align:top;" class="rowHeading">Solution States:</td>
										<td>
                                            <table>
                                                <tr>
												    <td>&nbsp;</td>
												    <th>Pending</th>
												    <th>Complete</th>
												    <th>Skip</th>
											    </tr>
                                                <asp:Repeater id="solutionStateRep" runat="server">
												    <ItemTemplate>
													    <tr>
														    <td>
															    <asp:hiddenfield id="SolutionStateId" runat="server" value='<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' />
                                                                <asp:hiddenfield id="SolutionStateName" runat="server" value='<%# DataBinder.Eval(Container.DataItem, "SolutionStateName") %>' />
															    <%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>. <%# DataBinder.Eval(Container.DataItem, "SolutionStateName") %>
														    </td>
                                                            <td><input type="checkbox" runat="server" id="pending" groupname='state<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' /></td>
														    <td><input type="checkbox" runat="server" id="complete" groupname='state<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' /></td>
														    <td><input type="checkbox" runat="server" id="skip" groupname='state<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' /></td>
													    </tr>
												    </ItemTemplate>
											    </asp:Repeater>
                                            </table>
                                        </td>
                                    </tr>
								</table>	
							</td>
							<td style="width:50%; vertical-align:top;">
								<table border="0" cellpadding="0" cellspacing="10" style="width:100%;">
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Failure Type:</td>
										<td>
											<asp:listbox id="failureTypeList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
                                    <tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Module Type:</td>
										<td>
											<asp:listbox id="moduleTypeList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Investigation Area:</td>
										<td>
											<asp:listbox id="investigationAreaList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Triage Type:</td>
										<td>
											<asp:listbox id="triageTypeList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Discipline:</td>
										<td>
											<asp:listbox id="disciplineList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="3" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
                                    <tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Owner:</td>
										<td>
											<asp:listbox id="ownerList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Operator:</td>
										<td>
											<asp:listbox id="operatorList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="6" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Media Number:</td>
										<td>
											<asp:listbox id="mediaNumberList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="4" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">File Number:</td>
										<td>
											<asp:listbox id="fileNumberList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="4" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Command Number:</td>
										<td>
											<asp:listbox id="commandNumberList" runat="server" appenddatabounditems="true" 
												datatextfield="Name" datavaluefield="Code" rows="4" selectionmode="multiple" width="250px"></asp:listbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Minimum Value:</td>
										<td>
											<asp:textbox id="minQuantizedVal" runat="server" width="100"></asp:textbox>
										</td>
									</tr>
									<tr>	
										<td style="width:130px; vertical-align:top;" class="rowHeading">Maximum Value:</td>
										<td>
											<asp:textbox id="maxQuantizedVal" runat="server" width="100"></asp:textbox>
										</td>
									</tr>
                                    <tr>
										<td style="width:130px;" valign="top" class="rowHeading">Implementation Projected Start:</td>
										<td valign="top">
											<telerik:raddatepicker id="projectedStartDateField" runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" style="padding-top: 1px;" width="80px">
												<calendar skin="Default2006" showrowheaders="false"></calendar>
												<datepopupbutton visible="False"></datepopupbutton>
												<dateinput onclick="ToggleStartDatePopup()"></dateinput>
											</telerik:raddatepicker>
											<telerik:radtimepicker id="projectedStartTimeField" runat="server" timepopupbutton-visible="false" width="80">
												<dateinput onclick="ToggleStartTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
												<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
											</telerik:radtimepicker>
										</td>
									</tr>
                                    <tr>
                                        <td style="width:130px;" valign="top" class="rowHeading">Implementation Projected End:</td>
                                        <td valign="top">
                                            <telerik:raddatepicker id="projectedEndDateField" runat="server" style="padding-top: 1px;" width="80px">
												<calendar skin="Default2006" showrowheaders="false"></calendar>
												<datepopupbutton visible="False"></datepopupbutton>
												<dateinput onclick="ToggleEndDatePopup()"></dateinput>
											</telerik:raddatepicker>
											<telerik:radtimepicker id="projectedEndTimeField" runat="server" timepopupbutton-visible="false" width="80">
												<dateinput onclick="ToggleEndTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
												<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
											</telerik:radtimepicker>
                                        </td>
                                    </tr>
								</table>	
							</td>
						</tr>
					</table>
					
                    <table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SearchButton_Click" id="SearchButton">Search</asp:linkbutton></div></td>
							<td style="width:120px;" class="leftPad"><div class="minusButton"><asp:linkbutton runat="server" onclick="ResetButton_Click" id="Linkbutton1">Clear Filters</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a href="search.aspx">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>

			</td>
		</tr>
	</table>
	
	<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
		<asp:Image id="Image1" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
	</telerik:RadAjaxLoadingPanel>
</asp:panel>

</asp:Content>

