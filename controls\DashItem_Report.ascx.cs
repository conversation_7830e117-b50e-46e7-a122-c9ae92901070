using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public partial class controls_DashItem_Report : System.Web.UI.UserControl, IDashboardItem
{
	public bool UseMasterUser
    {
        get { if (this.ViewState["um"] != null) return (bool)this.ViewState["um"]; else return false; }
        set { this.ViewState["um"] = value; }
    }
    
    private string UserName = Utility.GetUserName();
	protected Guid UniqueControlGuid = Guid.Empty;

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	DashReportInfo dashRptInfo = null;
	public DashReportInfo DashRptInfo
	{
		get
		{
			if (dashRptInfo != null) { return dashRptInfo; }
			else
			{
				dashRptInfo = new DashReportInfo();
                string ctrlXML = null;

                if (this.UseMasterUser)
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME));
                else
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, this.UserName));
                
                if (!string.IsNullOrEmpty(ctrlXML))
					dashRptInfo = (DashReportInfo)XmlSerializable.LoadFromXmlText(typeof(DashReportInfo), ctrlXML);

				return dashRptInfo;
			}
		}
		set
		{
			dashRptInfo = value;
		}
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		AdomdNetDataProvider1.ConnectionString = ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString;
		this.OlapManager1.CurrentCubeName = "Reporting";
	}

    protected void OlapManager1_ErrorDetected(object sender, Dundas.Olap.Manager.ErrorDetectedEventArgs e)
    {
        this.OlapManager1.CurrentCubeName = "Reporting";
        this.OlapManager1.DataProvider.Close();
        this.OlapManager1.DataProvider.Open();
        e.Handled = true;
    }

	private void LoadReport()
	{
        this.chartDiv.Visible = false;
        this.olapDiv.Visible = false;

		this.chartCntrl.ChartAreas[0].AxisX.Minimum = 0;
		this.chartCntrl.ChartAreas[0].AxisY.Minimum = 0;

		if (this.DashRptInfo != null)
		{
			Unit h = this.chartCntrl.Height;
			Unit w = this.chartCntrl.Width;

			this.RepInfo = Utility.LoadReportInfo(this.DashRptInfo.ReportId);

            if (this.RepInfo != null && !string.IsNullOrEmpty(this.RepInfo.ReportName) && !this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
            {
			
				AdomdNetDataProvider1.ConnectionString = ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString;
				this.OlapManager1.CurrentCubeName = "Reporting";
                this.OlapManager1.DataProvider.Close();
                this.OlapManager1.DataProvider.Open();
				
				//}
				//else
				//{
				//    AdomdNetDataProvider1.ConnectionString = ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString;
				//    this.OlapManager1.CurrentCubeName = "Transactions";
				//    this.OlapManager1.DataProvider.Close();
				//    this.OlapManager1.DataProvider.Open();
				//}

				//select the current report on settings page
				RadTreeView tree = (RadTreeView)this.reportCombo.Items[0].FindControl("reportTree");
				if (this.RepInfo.ReportId != 0 && tree != null)
				{
					RadTreeNode node = tree.FindNodeByValue(this.RepInfo.ReportId.ToString());
					if (node != null)
					{
						reportCombo.Text = node.Text;
						node.Selected = true;
						node.ParentNode.ParentNode.Expanded = true;
						node.ParentNode.Expanded = true;
					}
				}

                this.chartDiv.Visible = (this.RepInfo.ReportTypeId != ReportHelper.ReportTypeEnum.GENERAL);
                this.olapDiv.Visible = (this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.GENERAL);

				if (this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.GENERAL)
				{
					bool firstBuild = string.IsNullOrEmpty(this.RepInfo.ReportData);

					if (!firstBuild)
						ReportHelper.ConvertStringToReports(this.OlapManager1, this.RepInfo.ReportData);

					ReportHelper.BuildGeneralReport(false, this.OlapManager1, this.RepInfo, firstBuild);

					if (this.DashRptInfo.DisableLegend)
					{
						foreach (Dundas.Olap.WebUIControls.Legend leg in this.olapChartCntrl.Legends)
							this.olapChartCntrl.Legends[0].Enabled = false;
					}

					this.olapChartCntrl.RenderType = Dundas.Olap.WebUIControls.RenderType.ImageTag;
					this.olapChartCntrl.ImageType = Dundas.Olap.WebUIControls.ChartImageType.Jpeg;
					this.olapChartCntrl.MaxNumberOfPointsInView = 10000;
					this.olapChartCntrl.MapEnabled = false;
					this.olapChartCntrl.ImageResolution = 48;
					this.olapChartCntrl.Height = this.chartCntrl.Height;
					this.olapChartCntrl.Width = this.chartCntrl.Width;
				}
				else if (this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.LEGACY_GENERAL)
				{
					bool firstBuild = string.IsNullOrEmpty(this.RepInfo.ReportData);

					if (!firstBuild)
						ReportHelper.ConvertStringToReports(this.OlapManager1, this.RepInfo.ReportData);

					ReportHelper.BuildGeneralReport(true, this.OlapManager1, this.RepInfo, firstBuild);

					if (this.DashRptInfo.DisableLegend)
					{
						foreach (Dundas.Olap.WebUIControls.Legend leg in this.olapChartCntrl.Legends)
							this.olapChartCntrl.Legends[0].Enabled = false;
					}

					this.olapChartCntrl.RenderType = Dundas.Olap.WebUIControls.RenderType.ImageTag;
					this.olapChartCntrl.ImageType = Dundas.Olap.WebUIControls.ChartImageType.Jpeg;
					this.olapChartCntrl.MaxNumberOfPointsInView = 10000;
					this.olapChartCntrl.MapEnabled = false;
					this.olapChartCntrl.ImageResolution = 48;
					this.olapChartCntrl.Height = this.chartCntrl.Height;
					this.olapChartCntrl.Width = this.chartCntrl.Width;
				}
                else
                {
                    BaseReport reportObj = ReportHelper.LoadReportObject(this.RepInfo, null);

                    if (reportObj.IsStale)
                    {
                        reportObj.LoadData();
                        Utility.SaveReportInfo(this.RepInfo);
                    }

                    reportObj.InitializeChart(chartCntrl, "controls_DashItem_Report");
                    reportObj.PopulateChart(chartCntrl, false);

                    if (this.DashRptInfo.DisableLegend)
                    {
                        foreach (Legend leg in chartCntrl.Legends)
                            chartCntrl.Legends[0].Enabled = false;
                    }

                    chartCntrl.RenderType = RenderType.ImageTag;
                    chartCntrl.ImageType = ChartImageType.Jpeg;
                    chartCntrl.MapEnabled = false;
                    chartCntrl.UI.Toolbar.Enabled = false;
                    chartCntrl.UI.ContextMenu.Enabled = false;

                    this.chartCntrl.ImageResolution = 48;
                    this.chartCntrl.Height = h;
                    this.chartCntrl.Width = w;
                }
            }
            else
            {
                EditSettings();
            }
		}
	}

    public void Initialize(Guid dockUniqueName, bool useMasterUser)
    {
        this.UseMasterUser = useMasterUser;
        this.UniqueControlGuid = dockUniqueName;

		//populate the report tree
		RadTreeView tree = GetReportTree();
		if (tree != null)
		{
			tree.Nodes.Clear();

			RadTreeNode sessionNode = new RadTreeNode("Sessions");
			PopulateSessions(sessionNode);
			tree.Nodes.Add(sessionNode);

			RadTreeNode myLibraryNode = new RadTreeNode("My Library");
			PopulateMyLibrary(myLibraryNode);
			tree.Nodes.Add(myLibraryNode);

			RadTreeNode sharedLibraryNode = new RadTreeNode("Shared Library");
			PopulateSharedLibrary(sharedLibraryNode);
			tree.Nodes.Add(sharedLibraryNode);
		}

		if (this.DashRptInfo.ReportId == 0)
			EditSettings();
	}

	public void SaveSettings()
	{
		if (!string.IsNullOrEmpty(GetReportTree().SelectedNode.Value))
		{
			this.DashRptInfo.ReportId = Convert.ToInt32(GetReportTree().SelectedNode.Value);
			this.DashRptInfo.DisableLegend = disableLegendCheck.Checked;

            if (this.UseMasterUser)
                SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME, XmlSerializable.ConvertToXml(this.DashRptInfo));
            else
                SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, UserName, XmlSerializable.ConvertToXml(this.DashRptInfo));

			//Load new selected report
			this.RepInfo = Utility.LoadReportInfo(this.DashRptInfo.ReportId);
			this.chartCntrl.Titles["Title1"].Text = this.RepInfo.ChartTitle;

			SettingsPanel.Visible = false;
			DisplayPanel.Visible = true;
            errorLabel.Visible = false;
        }
		else
		{
			errorLabel.Visible = true;
		}
	}

	protected void ViewReport_Click(object sender, EventArgs e)
	{
        if (this.RepInfo.ReportId > 0)
        {
            if (this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.GENERAL)
                Response.Redirect("RunClientReport.aspx?r=" + this.RepInfo.ReportId);
            else
                Response.Redirect("RunReport.aspx?r=" + this.RepInfo.ReportId);
        } 
	}

	private void PopulateSessions(RadTreeNode sessionNode)
	{
		DataSet ds = LibrarySource.GetSessions(false, UserName);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			RadTreeNode newNode = new RadTreeNode(DataFormatter.getString(row, "SessionName"));
			foreach (DataRow childRow in row.GetChildRows("Children"))
			{
				if (!DataFormatter.getBool(childRow, "PromptSessions"))
				{
					ReportHelper.ReportTypeEnum reportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(childRow, "ReportTypeId");
					if (reportTypeId != ReportHelper.ReportTypeEnum.SHIFT)
					{
						RadTreeNode newChildNode = new RadTreeNode(DataFormatter.getString(childRow, "ReportName"));
						newChildNode.Value = DataFormatter.getInt32(childRow, "ReportId").ToString();
						newNode.Nodes.Add(newChildNode);
					}
				}
			}
			sessionNode.Nodes.Add(newNode);
		}
	}

	private void PopulateMyLibrary(RadTreeNode myLibraryNode)
	{
		DataSet ds = LibrarySource.GetMyLibrary(false, UserName);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			RadTreeNode newNode = new RadTreeNode(DataFormatter.getString(row, "FolderName"));
			foreach (DataRow childRow in row.GetChildRows("Children"))
			{
				if (!DataFormatter.getBool(childRow, "PromptSessions"))
				{
					ReportHelper.ReportTypeEnum reportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(childRow, "ReportTypeId");
					if (reportTypeId != ReportHelper.ReportTypeEnum.SHIFT)
					{
						RadTreeNode newChildNode = new RadTreeNode(DataFormatter.getString(childRow, "ReportName"));
						newChildNode.Value = DataFormatter.getInt32(childRow, "ReportId").ToString();
						newNode.Nodes.Add(newChildNode);
					}
				}
			}
			myLibraryNode.Nodes.Add(newNode);
		}
	}

	private void PopulateSharedLibrary(RadTreeNode sharedLibraryNode)
	{
		DataSet ds = LibrarySource.GetSharedLibrary(false);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			RadTreeNode newNode = new RadTreeNode(DataFormatter.getString(row, "FolderName"));
			foreach (DataRow childRow in row.GetChildRows("Children"))
			{
				if (!DataFormatter.getBool(childRow, "PromptSessions"))
				{
					ReportHelper.ReportTypeEnum reportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(childRow, "ReportTypeId");
					if (reportTypeId != ReportHelper.ReportTypeEnum.SHIFT)
					{
						RadTreeNode newChildNode = new RadTreeNode(DataFormatter.getString(childRow, "ReportName"));
						newChildNode.Value = DataFormatter.getInt32(childRow, "ReportId").ToString();
						newNode.Nodes.Add(newChildNode);
					}
				}
			}
			sharedLibraryNode.Nodes.Add(newNode);
		}
	}

	protected void RunButton_Command(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null)
		{
			ReportInfo rpt = Utility.LoadReportInfo(Convert.ToInt32(e.CommandArgument));

			if (rpt.PromptSessions)
			{
				foreach (RadWindow win in RadWindowManager.Windows)
				{
					if (win.ID == "PromptSessionsWindow")
					{
						win.NavigateUrl = "promptsessions.aspx?r=" + e.CommandArgument.ToString();
						win.VisibleOnPageLoad = true;
					}
				}
			}
			else
			{
                Response.Redirect(rpt.RunPageName);
			}
		}
	}

	public void DisplayEdit()
	{
		RadAjaxManager manager = RadAjaxManager.GetCurrent(this.Parent.Page);
		if (manager != null)
			manager.ResponseScripts.Add(string.Format("document.getElementById('{0}').click();", this.HidEditButton.ClientID));
	}

	public void DisplayContent()
	{
        LoadReport();
    }

	public void EditSettings()
	{
		SettingsPanel.Visible = true;
		DisplayPanel.Visible = false;
	}

	protected void SaveSettings_Click(object sender, EventArgs e)
	{
		SaveSettings();	
	}

	protected void EditSettings_Click(object sender, EventArgs e)
	{
		EditSettings();
	}

	protected void CancelSettings_Click(object sender, EventArgs e)
	{
		SettingsPanel.Visible = false;
		DisplayPanel.Visible = true;
	}

	private RadTreeView GetReportTree()
	{
		RadTreeView retVal = null;
		retVal = (RadTreeView)this.reportCombo.Items[0].FindControl("reportTree");
		return retVal;
	}
}
