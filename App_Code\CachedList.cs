using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;

public class CachedList
{
    protected object lockObj = new object();
    protected List<TypeCodeEntry> innerList = null;
    protected string taskName = null;
    protected string codeFieldName = null;
    protected string nameFieldName = null;
	protected bool includeInactive = false;

    public CachedList(string taskName, string codeFieldName, string nameFieldName)
    {
        this.taskName = taskName;
        this.codeFieldName = codeFieldName;
        this.nameFieldName = nameFieldName;
    }

	public CachedList(string taskName, string codeFieldName, string nameFieldName, bool includeInactive) {
		this.taskName = taskName;
		this.codeFieldName = codeFieldName;
		this.nameFieldName = nameFieldName;
		this.includeInactive = includeInactive;
	}

    public List<TypeCodeEntry> GetList()
    {
        List<TypeCodeEntry> retVal = null;

        lock (lockObj)
        {
            if (innerList == null)
            {
                innerList = LoadList();
            }
            retVal = innerList;
        }

        return retVal;
    }

    protected void OnListChange(object sender, SqlNotificationEventArgs e)
    {
        if (string.Compare(e.Info.ToString(), "Invalid") == 0) {
            throw new ApplicationException("Unable to register for SQL notification.");
        }

        ClearList();
    }

    protected List<TypeCodeEntry> LoadList()
    {
        List<TypeCodeEntry> retVal = new List<TypeCodeEntry>();

		DataSet ds = null;
		try {
			ds = SqlHelper.ExecuteDataset(this.taskName, new OnChangeEventHandler(OnListChange), includeInactive); 
		}
		catch {
			ds = SqlHelper.ExecuteDataset(this.taskName, new OnChangeEventHandler(OnListChange), new SqlParameter[0]);
		}
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            if (row.IsNull(this.codeFieldName) == false)
                retVal.Add(new TypeCodeEntry(row[this.codeFieldName].ToString(), DataFormatter.getString(row, this.nameFieldName)));
        }

        return retVal;
    }

    public void ClearList()
    {
        lock (lockObj)
        {
            innerList = null;
        }
    }
}

