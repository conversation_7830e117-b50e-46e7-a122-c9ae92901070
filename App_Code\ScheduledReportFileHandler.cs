﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using QueueServiceClient;

public class ScheduledReportFileHandler : IHttpHandler {
	public void ProcessRequest(HttpContext context) {
		HttpResponse Response = context.Response;
		HttpRequest Request = context.Request;

		string fileName = null;
		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FILE_NAME_KEY])) {
			fileName = Request.Params[DieboldConstants.FILE_NAME_KEY];

			DataFile result = TxFiles.DownloadScheduledReportFile(context.Server.UrlDecode(Request.Params[DieboldConstants.FILE_NAME_KEY]));

			if (result != null && result.FileData.Length > 0) {
				Response.ClearContent();
				Response.ClearHeaders();
				Response.ContentType = result.MimeType;
				Response.AppendHeader("content-disposition", "attachment; filename=" + result.FileName + "; size=" + result.FileSize.ToString() + ";");
				Response.BinaryWrite(result.FileData);
				Response.End();
			}
			else {
				throw new ApplicationException("Unable to find the requested file. " + Request.Params[DieboldConstants.FILE_NAME_KEY]);
			}
		}
		else {
			throw new ApplicationException("Unable to identify the filename.");
		}
	}

	public bool IsReusable {
		get { return true; }
	}
}
