<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Files.aspx.cs" Inherits="Files" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10" style="background-color:#5b5551;">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Upload Files</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;"></td>
					</tr>
				</table>				
				<div class="widget" id="FileUploadDiv" runat="server">
					<table width="100%" border="0" cellpadding="0">
						<tr>
							<td class="title">File Upload</td>
						</tr>
						<tr>
							<td style="padding:0px 10px 0px 14px;">
								You may upload up to 4 files at a time by filling out the fields below. <br />
								More files may be uploaded after the process is complete.	
								<br /><hr />
							</td>
						</tr>
						<tr>
							<td>
								<asp:fileupload id="FileUpload1" runat="server" cssclass="entryControl" />&nbsp;
								<asp:requiredfieldvalidator id="v1" runat="server" controltovalidate="FileUpload1" display="dynamic" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator>
								<br /><br />
								<asp:fileupload id="FileUpload2" runat="server" cssclass="entryControl" />
								<br /><br />
								<asp:fileupload id="FileUpload3" runat="server" cssclass="entryControl" />
								<br /><br />
								<asp:fileupload id="FileUpload4" runat="server" cssclass="entryControl" />
								<br /><br />
								<asp:label cssclass="entryControl" id="SuccessMessage" runat="server" visible="false" style="color:#c00;">Your files were uploaded successfully.</asp:label>
							</td>
						</tr>
					</table>
					<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding:20px 10px 20px 20px;">
						<tr>
							<td style="width:80px;">
								<div class="goButton"><asp:linkbutton id="Linkbutton2" runat="server" onclick="UploadButton_Click">Upload</asp:linkbutton></div>
							</td>
							<td>
								<div class="cancelButton"><asp:linkbutton id="Linkbutton1" runat="server" onclick="CloseButton_Click" causesvalidation="false">Close</asp:linkbutton></div>
							</td>
						</tr>
					</table>
				</div>
				<div class="widget" id="ErrorDiv" style="display:none;" runat="server">
					<table width="80%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td>
								<div class="title">
									Unable to find the transaction id.<br /><br />
								</div>
							</td>
						</tr>
					</table>
				</div>
			</td>
		</tr>
	</table>		
</asp:Content>
