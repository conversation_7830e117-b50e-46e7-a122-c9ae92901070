﻿using System;
using System.Data;
using System.IO;
using System.Web.UI;
using Telerik.Web.UI;

public partial class RunQuery : System.Web.UI.Page
{
    protected BaseReport ReportObj = null;

    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

	public bool IsReportLoaded
	{
		get { if (this.ViewState["ld"] != null) { return (bool)this.ViewState["ld"]; } else { return false; } }
		set { this.ViewState["ld"] = value; }
	}
    
    public SubReportInfo SubReportInfo
    {
        get { if (this.ViewState["sr"] != null) { return (SubReportInfo)this.ViewState["sr"]; } else { return null; } }
        set { this.ViewState["sr"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
                this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]))
            {
                this.SubReportInfo = Utility.LoadSubReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]));
            }

            if (this.RepInfo == null)
                Response.Redirect("~/reportlibrary.aspx");

            string loadScript = "<script language='javascript'>\r\ndocument.forms[0].submit();\r\n</script>";
			Page.ClientScript.RegisterStartupScript(typeof(RunQuery), "LoadReportScript", loadScript);
        }

        if (Page.IsPostBack && this.IsReportLoaded == false)
        {
            InitialLoadingPanel.Visible = false;
            MainPanel.Visible = true;

            this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, this.SubReportInfo);

			//require user to be admin to edit or save reports that are associated with sessions
			if (this.RepInfo.FolderId == 0 && this.RepInfo.ReportId != 0)
			{
				bool isAdmin = Utility.IsUserAdmin();
				EditBtnDiv.Visible = isAdmin;
				SaveBtnDiv.Visible = isAdmin;
			}
            this.ReportObj.LoadData();
			
		    GridBindingPanel.DataBind();

            if (this.SubReportInfo != null)
                reportNameLabel.Text = this.SubReportInfo.SubReportName;
            else
                reportNameLabel.Text = this.RepInfo.ReportName;

            this.IsReportLoaded = true;
        }

		//if request is from the Queue Service, return the export file immediately
		if (!string.IsNullOrEmpty(Request.QueryString["qs"]))
		{
			InitialLoadingPanel.Visible = false;
			MainPanel.Visible = true;

			this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, this.SubReportInfo);
			this.ReportObj.LoadData();
			GridBindingPanel.DataBind();

			if (this.SubReportInfo != null)
				reportNameLabel.Text = this.SubReportInfo.SubReportName;
			else
				reportNameLabel.Text = this.RepInfo.ReportName;

			ExportButton_Click(null, null);
		}
    }

	protected void PrintButton_Click(object sender, EventArgs e)
	{
        Utility.SetReportInfoForTransfer(this.RepInfo);
        Utility.SetSubReportInfoForTransfer(this.SubReportInfo);
        Response.Redirect("PrintQueryReport.aspx");
	}

	protected void GridItem_Click(object sender, EventArgs e)
	{
		SearchCriteria searchCriteria = new SearchCriteria(this.Page);
		
		//set the value that was clicked to both the min and max to form a single point range
		searchCriteria.MinQuantizedValue = Convert.ToDecimal(distroClickedVal.Value); 
		searchCriteria.MaxQuantizedValue = Convert.ToDecimal(distroClickedVal.Value);

        searchCriteria.CalculateSearchCriteria(this.RepInfo, this.SubReportInfo);
		searchCriteria.SetToSession();
		Response.Redirect("Search.aspx");
	}

   	protected void ExportButton_Click(object sender, EventArgs e)
	{
		Response.ClearHeaders();
		Response.ClearContent();
		Response.Charset = "";
        if(this.SubReportInfo != null)
            Response.AppendHeader("content-disposition", "attachment; filename=" + this.SubReportInfo.SubReportName + ".xls");
        else
            Response.AppendHeader("content-disposition", "attachment; filename=" + this.RepInfo.ReportName + ".xls");
        Response.ContentType = "application/vnd.ms-excel";

		StringWriter colWrite = new StringWriter();
		StringWriter rowWrite = new StringWriter();
		HtmlTextWriter rowsHtmlWrite = new HtmlTextWriter(rowWrite);

		GridBindingPanel.RenderControl(rowsHtmlWrite);
		
		Response.Write(rowWrite.ToString());
		Response.End();
	}

	protected void SaveButton_Click(object sender, EventArgs e)
    {
        Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "SaveReportWindow")
				win.VisibleOnPageLoad = true;
		}
    }

    protected void EditButton_Click(object sender, EventArgs e)
    {
        Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "EditWindow")
			{
                win.NavigateUrl = this.RepInfo.WizardPageName;
				win.VisibleOnPageLoad = true;
			}
		}
    }
}
