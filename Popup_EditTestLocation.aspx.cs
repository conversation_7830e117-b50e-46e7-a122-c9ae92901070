﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditTestLocation : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string TestLocationId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.TEST_LOCATION_ID_KEY]))
			{
				this.TestLocationId = Request.Params[DieboldConstants.TEST_LOCATION_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTestLocation", this.TestLocationId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.testLocationNameField.Text = DataFormatter.getString(row, "TestLocationName");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(TestLocationId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertTestLocation", this.testLocationNameField.Text);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateTestLocation", Convert.ToInt32(this.TestLocationId), this.testLocationNameField.Text);

		if (result == 0)
			this.ErrorLabel.Visible = true;
		else
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditTestLocations.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditTestLocations.aspx';CloseRadWindow(); ", true);
	}
}
