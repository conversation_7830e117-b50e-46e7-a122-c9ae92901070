<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="PromptSessions.aspx.cs" Inherits="PromptSessions" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Report Sessions</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Select the sessions to view in this report:</div>
					<br />
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td class="rowHeading">Report Sessions: <span style="font-weight:normal;">(hold Ctrl to select multiple)</span></td>
						</tr>
						<tr>
							<td style="padding:10px 0px 10px 0px;">
								<asp:listbox id="sessionsList" width="350" rows="8" selectionmode="multiple" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" runat="server" cssclass="entryControl"></asp:listbox>
								<asp:requiredfieldvalidator id="val2" runat="server" errormessage="* Required" cssclass="error" display="dynamic" controltovalidate="sessionsList"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td>
								<asp:checkbox id="saveSessionsCheck" runat="server" cssclass="entryControl" text=" Save and always use selected sessions" />
							</td>
						</tr>
					</table>
					<br /><br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:100px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="RunButton_Click" id="nextButton">Run Report</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

<asp:panel id="RedirectingPanel" runat="server" visible="false">
	<div style="padding-top:50px;"><center><img src="images/loading.gif" alt="Redirecting to Report..." border="0" /></center></div>
</asp:panel>

</asp:Content>

