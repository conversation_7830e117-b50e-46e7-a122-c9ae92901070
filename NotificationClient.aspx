<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="NotificationClient.aspx.cs" Inherits="NotificationClient" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Diebold Notification Client</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">
				<table border="0" cellpadding="0" cellspacing="0" style="padding-top:10px; padding-left:14px;">
					<tr>
						<td style="width:115px;"><div class="goButton" style="padding-bottom:5px;"><a href="clientdownload/DieboldNotificationInstaller.msi">Download</a></div></td>
						<td>&nbsp;</td>
					</tr>
				</table>
				
				<div class="title" style="padding-bottom:0px;">Client Installation Instructions</div>
				<table width="100%" border="0" cellpadding="0" cellspacing="20" style="padding-left:14px; font-size:12px;">
					<tr>
						<td style="width:400px; vertical-align:top;"><b>1.</b> Click the "Download" button above.</td>
						<td>&nbsp;</td>
					</tr>
					<tr>
						<td style="width:400px; vertical-align:top;"><b>2.</b> When the popup box appears, select "Run".</td>
						<td><img src="clientdownload/tutorial1.jpg" border="0" /></td>
					</tr>
					<tr>
						<td style="width:400px; vertical-align:top;"><b>3.</b> Follow the steps in the installation wizard</td>
						<td><img src="clientdownload/tutorial2.jpg" border="0" /></td>
					</tr>
					<tr>
						<td style="width:400px; vertical-align:top;"><b>4.</b> Go to the Programs folder on your Start Menu, find the newly added Diebold folder and select Diebold Notification Client.</td>
						<td><img src="clientdownload/tutorial3.jpg" border="1" /></td>
					</tr>
					<tr>
						<td style="width:400px; vertical-align:top;"><b>5.</b> At this point the Notification Client will be running in your taskbar. You can double click on it to open the client settings menu which will allow you to customize how frequently you check for new messages.</td>
						<td><img src="clientdownload/tutorial4.jpg" border="1" /></td>
					</tr>
					<tr>
						<td style="width:400px; vertical-align:top;"><b>6.</b> Thats it! The Notification Client will start up each time you log in, and will popup new message alerts when new messages are generated.</td>
						<td><img src="clientdownload/tutorial5.jpg" border="0" /></td>
					</tr>
				</table>
			</div>
		</td>
	</tr>
</table>

</asp:Content>

