<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" Async="true" AutoEventWireup="true" CodeFile="EditObservation.aspx.cs" Inherits="EditObservation"%>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ import namespace="QueueServiceClient" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="600" width="800" modal="true" title="Pictures" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
	<windows>
		<telerik:radwindow runat="server" destroyonclose="true" ID="PicturesWindow" openerelementid="PicturesButton" VisibleOnPageLoad="false" OffsetElementID="offsetElement"
			Top="30" Left="30" NavigateUrl="Files.aspx" Title="Pictures" Height="600" Width="800" >
		</telerik:radwindow>
	</windows>
</telerik:radwindowmanager>

<script type="text/javascript">
	function ToggleSolutionDatePopup() {
		$find("<%= projectedDate.ClientID %>").showPopup();
	}
	function ToggleDatePopup() {
        $find("<%= dateField.ClientID %>").showPopup();
    }    
    function ToggleTimePopup() {
        var picker = $find("<%= timeField.ClientID %>");
        picker.showTimePopup();
    }
    function updateObservationState(highlightUpdate) {
        //calculate the 'Observation State' (called 'Severity' in database) based on Solution State, Failure Type, and Is Censored values
        $('input[state-id]').each(function (index) {
            if ($(this).attr('state-id') == 7) {
                if ($(this).is(':checked')) {
                    if ($(this).val() == 'skip' || $(this).val() == 'complete') {
                        var failureType = $('#<%= failureTypeList.ClientID %>').val();
                        var isCensor = $('#<%= censorCheck.ClientID %>').is(':checked');

                        if (failureType == "CA" || failureType == "CA Jam" || failureType == "RA" || failureType == "RA Jam") {
                            $('#<%= severityBox.ClientID %>').val('Closed (No Solution)');
                            $('#<%= severityValueHid.ClientID %>').val('4');
                        } else if (failureType == "C" || isCensor) {
                            $('#<%= severityBox.ClientID %>').val('Closed (Fixed)');
                            $('#<%= severityValueHid.ClientID %>').val('3');
                        } else {
                            $('#<%= severityBox.ClientID %>').val('Closed');
                            $('#<%= severityValueHid.ClientID %>').val('2');
                        }
                    }
                    else if ($(this).val() == 'pending') {
                        $('#<%= severityBox.ClientID %>').val('Open');
                        $('#<%= severityValueHid.ClientID %>').val('1');
                    }
                    else if ($(this).val() == 'notset') {
                        $('#<%= severityBox.ClientID %>').val('');
                        $('#<%= severityValueHid.ClientID %>').val('');
                    }

                    if (highlightUpdate) {
                        $('#<%= severityBox.ClientID %>').css('background-color', '#d2e1cf');
                        setTimeout(function () { $('#<%= severityBox.ClientID %>').removeAttr('style'); }, 1000);
                    }
                }
            }
        });
    }
    $(function () {
        $("input:radio").change(function () {
            if ($(this).attr('state-id') == 7) {
                updateObservationState(true);
            }
        });
        $('#<%= failureTypeList.ClientID %>').change(function () { updateObservationState(true); });
        $('#<%= censorCheck.ClientID %>').change(function () { updateObservationState(true); });

        setTimeout(function () { updateObservationState(false); }, 100);
    });
</script>

<asp:panel id="DefaultPanel" runat="server">

<div id="jiraErrorDiv" runat="server" visible="false" style="background-color:#dd8f8f; color:#fff; padding:10px;">
</div>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Reliability Observation Log</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">
				<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
					<AjaxSettings>
						<telerik:AjaxSetting AjaxControlID="deviceTypeList">
							<UpdatedControls>
								<telerik:AjaxUpdatedControl ControlID="deviceList" LoadingPanelID="LoadingPanel1">
								</telerik:AjaxUpdatedControl>
								<telerik:AjaxUpdatedControl ControlID="failureLocationList" LoadingPanelID="LoadingPanel1">
								</telerik:AjaxUpdatedControl>
							</UpdatedControls>
						</telerik:AjaxSetting>
					</AjaxSettings>
				</telerik:RadAjaxManager>
				
				<div id="ErrorDiv" runat="server" visible="false" style="margin:10px 14px 0px 14px; border:solid 1px #ff0000; padding:10px 10px 10px 14px;">
					<asp:label id="ErrorMessage" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				</div>
				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr style="padding-top:10px;">
						<td style="width:50%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>	
									<td style="width:110px;" class="rowHeading">Date:</td>
									<td>
										<telerik:RadDatePicker id="dateField" runat="server" style="padding-top:1px;" width="80">        
											<calendar skin="Default2006" showrowheaders="false"></calendar>       
											<DatePopupButton Visible="False"></DatePopupButton>
											<DateInput onclick="ToggleDatePopup()"></DateInput>                           	                                             
										</telerik:RadDatePicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator2" runat="server" enableclientscript="false" controltovalidate="dateField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<telerik:radtimepicker id="timeField" runat="server" timepopupbutton-visible="false" width="80">
											<dateinput onclick="ToggleTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
											<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
										</telerik:radtimepicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator3" runat="server" enableclientscript="false" controltovalidate="timeField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Cell:</td>
									<td>
                                        <telerik:radcombobox id="cellList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>
										<asp:requiredfieldvalidator id="cellValidator" runat="server" controltovalidate="cellList" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Session:</td>
									<td>
                                        <telerik:radcombobox id="sessionList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>
										<asp:requiredfieldvalidator id="sessionValidator" runat="server" controltovalidate="sessionList" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>										
									</td>
								</tr>
								<tr id="TransactionNoRow" runat="server">	
									<td style="width:130px;" class="rowHeading">Transaction No:</td>
									<td>
										<asp:textbox id="transactionField" runat="server"></asp:textbox>
										<asp:requiredfieldvalidator id="val3" runat="server" controltovalidate="transactionField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<asp:comparevalidator id="val4" runat="server" display="dynamic" controltovalidate="transactionField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="* Invalid format"></asp:comparevalidator>
									</td>
								</tr>
								<tr id="CumTranCountRow" visible="false" runat="server">	
									<td style="width:130px;" class="rowHeading">Cumulative Transactions Count:</td>
									<td>
										<asp:textbox id="cumTranCountField" runat="server"></asp:textbox>
										<asp:comparevalidator id="Comparevalidator1" runat="server" display="dynamic" controltovalidate="cumTranCountField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="* Invalid format"></asp:comparevalidator>
									</td>
								</tr>
								<tr id="CumMediaCountRow" visible="false" runat="server">	
									<td style="width:130px;" class="rowHeading">Cumulative Media Count:</td>
									<td>
										<asp:textbox id="cumMediaCountField" runat="server"></asp:textbox>
										<asp:comparevalidator id="Comparevalidator2" runat="server" display="dynamic" controltovalidate="cumMediaCountField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="* Invalid format"></asp:comparevalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Device Type:</td>
									<td>
									    <telerik:radcombobox id="deviceTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" autopostback="true"
                                             onselectedindexchanged="DevceTypeList_SelectedIndexChanged" causesvalidation="false">
                                        </telerik:radcombobox>
										<asp:requiredfieldvalidator id="deviceTypeValidator" runat="server" controltovalidate="deviceTypeList" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>										
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Device:</td>
									<td>
									    <telerik:radcombobox id="deviceList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" enabled="false">
                                        </telerik:radcombobox><asp:requiredfieldvalidator id="deviceValidator" runat="server" controltovalidate="deviceList" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>                                    
                                    </td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Module Type:</td>
									<td>
									    <telerik:radcombobox id="moduleTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Failure Location:</td>
									<td><telerik:radcombobox id="failureLocationList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" enabled="false">
                                        </telerik:radcombobox></td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Failure Type:</td>
									<td><telerik:radcombobox id="failureTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox></td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Discipline:</td>
									<td><telerik:radcombobox id="disciplineList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox></td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Investigation Area:</td>
									<td><telerik:radcombobox id="investigationAreaList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox></td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Triage Name:</td>
									<td>
                                        <asp:dropdownlist id="triageTypeList" style="font-size:12px;color:#333;font-family:'Segoe UI',Arial,Helvetica,sans-serif;" runat="server" width="225px" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:dropdownlist>
										<%--<telerik:radcombobox id="triageTypeList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>--%>
									</td>
								</tr>
								<tr style="display:none;">	
									<td style="width:110px;" class="rowHeading">Agilis Version:</td>
									<td>
                                        <telerik:radcombobox id="agilisList" runat="server" width="175px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>
									</td>
								</tr>
								<tr>
									<td style="width:110px;" class="rowHeading">Owner:</td>
									<td>
                                        <asp:dropdownlist id="ownerList" style="font-size:12px;color:#333;font-family:'Segoe UI',Arial,Helvetica,sans-serif;" runat="server" width="225px" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:dropdownlist>
                                        <%--<telerik:radcombobox id="ownerList" runat="server" width="175px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                                appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>--%>
                                    </td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Operator:</td>
									<td>
                                        <asp:dropdownlist id="operatorList" style="font-size:12px;color:#333;font-family:'Segoe UI',Arial,Helvetica,sans-serif;" runat="server" width="225px" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:dropdownlist>
                                        <%--<telerik:radcombobox id="operatorList" runat="server" width="175px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
                                             appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
                                        </telerik:radcombobox>--%>
									</td>
								</tr>
							</table>
						</td>
						<td style="width:50%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>	
									<td style="width:110px;" class="rowHeading">Solution State:</td>
									<td style="padding-left:4px;">
										<table>
											<tr>
												<td>&nbsp;</td>
												<th>Not Set</th>
                                                <th>Pending</th>
												<th>Complete</th>
												<th>Skip</th>
											</tr>
											<asp:Repeater id="solutionStateRep" runat="server">
												<ItemTemplate>
													<tr>
														<td>
															<asp:hiddenfield id="SolutionStateId" runat="server" value='<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' />
															<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>. <%# DataBinder.Eval(Container.DataItem, "SolutionStateName") %>
														</td>
														<td><input type="radio" runat="server" id="notset" state-id='<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' checked='<%# IsSolutionStateCheck((int)DataBinder.Eval(Container.DataItem, "SolutionStateId"), "notset") %>' disabled='<%# IsSolutionStateDisabled() %>'  /></td>
                                                        <td><input type="radio" runat="server" id="pending" state-id='<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' checked='<%# IsSolutionStateCheck((int)DataBinder.Eval(Container.DataItem, "SolutionStateId"), "pending") %>' disabled='<%# IsSolutionStateDisabled() %>' /></td>
														<td><input type="radio" runat="server" id="complete" state-id='<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' checked='<%# IsSolutionStateCheck((int)DataBinder.Eval(Container.DataItem, "SolutionStateId"), "complete") %>' disabled='<%# IsSolutionStateDisabled() %>' /></td>
														<td><input type="radio" runat="server" id="skip" state-id='<%# DataBinder.Eval(Container.DataItem, "SolutionStateId") %>' checked='<%# IsSolutionStateCheck((int)DataBinder.Eval(Container.DataItem, "SolutionStateId"), "skip") %>' disabled='<%# IsSolutionStateDisabled() %>' /></td>
													</tr>
												</ItemTemplate>
											</asp:Repeater>
										</table>
									</td>
								</tr>
                                <tr>	
									<td style="width:110px;" class="rowHeading">Censor:</td>
									<td><asp:checkbox id="censorCheck" runat="server"></asp:checkbox></td>
								</tr>
                                <tr>
									<td style="width:110px;" class="rowHeading">Observation State: <!-- Severity --></td>
									<td>
                                        <asp:textbox id="severityBox" runat="server" enabled="false"></asp:textbox>
                                        <asp:hiddenfield id="severityValueHid" runat="server" />
									</td>
								</tr>
                                
                                <tr>	
									<td style="width:110px;" class="rowHeading">Projected Solution Date:</td>
									<td>
										<telerik:RadDatePicker id="projectedDate" runat="server" style="padding-top:1px;" width="80">        
											<calendar skin="Default2006" showrowheaders="false"></calendar>       
											<DatePopupButton Visible="False"></DatePopupButton>
											<DateInput onclick="ToggleSolutionDatePopup()"></DateInput>                           	                                             
										</telerik:RadDatePicker>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Issue No:</td>
									<td>
                                        <asp:textbox id="scrNumberField" runat="server"></asp:textbox> 
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Link:</td>
									<td>
										<asp:textbox id="linkField" runat="server"></asp:textbox> 
                                        <a runat="server" target="_blank" id="linkFieldActiveLink" visible="false">Go to Url</a>
									</td>
								</tr>
								<tr runat="server" id="settingsRow">	
									<td style="width:110px;" class="rowHeading">Settings:</td>
									<td style="padding-left:4px;">
										<div class="goButton"><a runat="server" id="SettingsLink">View Settings</a></div>
									</td>
								</tr>
								<tr runat="server" id="statisticsRow">	
									<td style="width:110px;" class="rowHeading">Statistics:</td>
									<td style="padding-left:4px;">
										<div class="goButton"><a runat="server" id="StatsLink">View Statistics</a></div>
									</td>
								</tr>
								<tr runat="server" id="picturesRow">	
									<td style="width:110px;" class="rowHeading">Pictures:</td>
									<td style="padding-left:4px;">
										<div class="goButton"><a runat="server" id="PicturesButton">View Pictures</a></div>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Upload Files:</td>
									<td style="padding-left:4px;">
										<div class="goButton"><a runat="server" id="UploadFileBtn">Upload Files</a></div>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td valign="top">
							<table style="width:100%; padding-left:10px; padding-top:10px;" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td class="rowHeading">XML Files: <span style="font-weight:normal;">(right click and choose save file as)</span></td>
								</tr>
								<asp:repeater id="xmlRepeater" runat="server">
									<itemtemplate>
										<tr><td class="repeaterSubItem"><a style="padding-left:0px;" href='TxFile.aspx?tx=1&<%# DieboldConstants.FILE_IS_DATA_KEY %>=1&<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46) %></a></td></tr>
									</itemtemplate>
									<alternatingitemtemplate>
										<tr><td class="repeaterSubItemAlt"><a style="padding-left:0px;" href='TxFile.aspx?tx=1&<%# DieboldConstants.FILE_IS_DATA_KEY %>=1&<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46) %></a></td></tr>
									</alternatingitemtemplate>
								</asp:repeater>
								<tr>
									<td style="padding-left:14px;padding-top:10px;">
										<asp:label id="NoXMLLabel" runat="server">No XML files found.<br /><br /></asp:label>
									</td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table style="width:100%; padding-top:10px;"" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td class="rowHeading">Engineering & Other Files: <span style="font-weight:normal;">(right click and choose save file as)</span></td>
								</tr>
								<tr id="downloadAllRow" runat="server">
									<td style="padding:10px 0 0 15px;">
										<div class="goButton"><asp:linkbutton id="DownloadAllBtn" runat="server" causesvalidation="false" onclick="DownloadAllBtn_Click">Download All</asp:linkbutton></div>
									</td>
								</tr>
								<asp:repeater id="engRepeater" runat="server">
									<itemtemplate>
										<tr><td class="repeaterSubItem"><a style="padding-left:0px;" href='TxFile.aspx?tx=1&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46)%></a></td></tr>
									</itemtemplate>
									<alternatingitemtemplate>
										<tr><td class="repeaterSubItemAlt"><a style="padding-left:0px;" href='TxFile.aspx?tx=1&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46)%></a></td></tr>
									</alternatingitemtemplate>
								</asp:repeater>
								<tr>
									<td style="padding-left:14px;padding-top:10px;">
										<asp:label id="NoFilesLabel" runat="server">No engineering files found.<br /><br /></asp:label>
										<%--<div class="goButton"><a runat="server" id="UploadButton">Upload a file</a></div>--%>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding-top:20px;">
					<tr>
						<td style="width:100%;" class="rowHeading">Observations:</td>
					</tr>						
					<tr>
						<td colspan="3" style="padding:10px 0px 10px 0px;"><asp:textbox id="observationField" runat="server" width="90%" cssclass="entryControl" rows="6" textmode="multiLine"></asp:textbox></td>
					</tr>
				</table>
				<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding-top:20px;">
					<tr>
						<td style="width:100%;" class="rowHeading">Transaction Notes:</td>
					</tr>						
					<tr>
						<td colspan="3" style="padding:10px 0px 10px 0px;"><asp:textbox id="transactionNotesField" runat="server" width="90%" cssclass="entryControl" rows="6" textmode="multiLine"></asp:textbox></td>
					</tr>
				</table>
				
				<div id="ErrorDiv1" runat="server" visible="false" style="margin:10px 14px 10px 14px; border:solid 1px #ff0000; padding:10px 10px 10px 14px;">
					<asp:label id="ErrorMessage1" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				</div>
				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>	
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="submitButton" onclick="SaveButton_Click">Save</asp:linkbutton></div></td>
						<td style="width:120px;"><div class="cancelButton"><asp:linkbutton runat="server" id="cancelButton" onclick="CancelButton_Click" causesvalidation="false">Cancel</asp:linkbutton></div></td>
						<td><div class="cancelButton" runat="server" id="DelBtnDiv"><asp:linkbutton runat="server" id="deleteButton" onclientclick="return confirm('Are you sure you wish to delete this observation?');" onclick="DeleteButton_Click" causesvalidation="false">Delete</asp:linkbutton></div></td>
						<td>&nbsp;</td>
					</tr>
				</table>
				<br />
				<br />
			</div>
		</td>
	</tr>
</table>

<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

</asp:panel>

</asp:Content>

