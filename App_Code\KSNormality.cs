using System;
using System.Data;
using System.Collections.Generic;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Drawing;

public class KSNormality
{
	double[] NormDistKey = { -5.00, -4.99, -4.98, -4.97, -4.96, -4.95, -4.94, -4.93, -4.92, -4.91, -4.90, -4.89, -4.88, -4.87, -4.86, -4.85, -4.84, -4.83, -4.82, -4.81, -4.80, -4.79, -4.78, -4.77, -4.76, -4.75, -4.74, -4.73, -4.72, -4.71, -4.70, -4.69, -4.68, -4.67, -4.66, -4.65, -4.64, -4.63, -4.62, -4.61, -4.60, -4.59, -4.58, -4.57, -4.56, -4.55, -4.54, -4.53, -4.52, -4.51, -4.50, -4.49, -4.48, -4.47, -4.46, -4.45, -4.44, -4.43, -4.42, -4.41, -4.40, -4.39, -4.38, -4.37, -4.36, -4.35, -4.34, -4.33, -4.32, -4.31, -4.30, -4.29, -4.28, -4.27, -4.26, -4.25, -4.24, -4.23, -4.22, -4.21, -4.20, -4.19, -4.18, -4.17, -4.16, -4.15, -4.14, -4.13, -4.12, -4.11, -4.10, -4.09, -4.08, -4.07, -4.06, -4.05, -4.04, -4.03, -4.02, -4.01, -4.00, -3.99, -3.98, -3.97, -3.96, -3.95, -3.94, -3.93, -3.92, -3.91, -3.90, -3.89, -3.88, -3.87, -3.86, -3.85, -3.84, -3.83, -3.82, -3.81, -3.80, -3.79, -3.78, -3.77, -3.76, -3.75, -3.74, -3.73, -3.72, -3.71, -3.70, -3.69, -3.68, -3.67, -3.66, -3.65, -3.64, -3.63, -3.62, -3.61, -3.60, -3.59, -3.58, -3.57, -3.56, -3.55, -3.54, -3.53, -3.52, -3.51, -3.50, -3.49, -3.48, -3.47, -3.46, -3.45, -3.44, -3.43, -3.42, -3.41, -3.40, -3.39, -3.38, -3.37, -3.36, -3.35, -3.34, -3.33, -3.32, -3.31, -3.30, -3.29, -3.28, -3.27, -3.26, -3.25, -3.24, -3.23, -3.22, -3.21, -3.20, -3.19, -3.18, -3.17, -3.16, -3.15, -3.14, -3.13, -3.12, -3.11, -3.10, -3.09, -3.08, -3.07, -3.06, -3.05, -3.04, -3.03, -3.02, -3.01, -3.00, -2.99, -2.98, -2.97, -2.96, -2.95, -2.94, -2.93, -2.92, -2.91, -2.90, -2.89, -2.88, -2.87, -2.86, -2.85, -2.84, -2.83, -2.82, -2.81, -2.80, -2.79, -2.78, -2.77, -2.76, -2.75, -2.74, -2.73, -2.72, -2.71, -2.70, -2.69, -2.68, -2.67, -2.66, -2.65, -2.64, -2.63, -2.62, -2.61, -2.60, -2.59, -2.58, -2.57, -2.56, -2.55, -2.54, -2.53, -2.52, -2.51, -2.50, -2.49, -2.48, -2.47, -2.46, -2.45, -2.44, -2.43, -2.42, -2.41, -2.40, -2.39, -2.38, -2.37, -2.36, -2.35, -2.34, -2.33, -2.32, -2.31, -2.30, -2.29, -2.28, -2.27, -2.26, -2.25, -2.24, -2.23, -2.22, -2.21, -2.20, -2.19, -2.18, -2.17, -2.16, -2.15, -2.14, -2.13, -2.12, -2.11, -2.10, -2.09, -2.08, -2.07, -2.06, -2.05, -2.04, -2.03, -2.02, -2.01, -2.00, -1.99, -1.98, -1.97, -1.96, -1.95, -1.94, -1.93, -1.92, -1.91, -1.90, -1.89, -1.88, -1.87, -1.86, -1.85, -1.84, -1.83, -1.82, -1.81, -1.80, -1.79, -1.78, -1.77, -1.76, -1.75, -1.74, -1.73, -1.72, -1.71, -1.70, -1.69, -1.68, -1.67, -1.66, -1.65, -1.64, -1.63, -1.62, -1.61, -1.60, -1.59, -1.58, -1.57, -1.56, -1.55, -1.54, -1.53, -1.52, -1.51, -1.50, -1.49, -1.48, -1.47, -1.46, -1.45, -1.44, -1.43, -1.42, -1.41, -1.40, -1.39, -1.38, -1.37, -1.36, -1.35, -1.34, -1.33, -1.32, -1.31, -1.30, -1.29, -1.28, -1.27, -1.26, -1.25, -1.24, -1.23, -1.22, -1.21, -1.20, -1.19, -1.18, -1.17, -1.16, -1.15, -1.14, -1.13, -1.12, -1.11, -1.10, -1.09, -1.08, -1.07, -1.06, -1.05, -1.04, -1.03, -1.02, -1.01, -1.00, -0.99, -0.98, -0.97, -0.96, -0.95, -0.94, -0.93, -0.92, -0.91, -0.90, -0.89, -0.88, -0.87, -0.86, -0.85, -0.84, -0.83, -0.82, -0.81, -0.80, -0.79, -0.78, -0.77, -0.76, -0.75, -0.74, -0.73, -0.72, -0.71, -0.70, -0.69, -0.68, -0.67, -0.66, -0.65, -0.64, -0.63, -0.62, -0.61, -0.60, -0.59, -0.58, -0.57, -0.56, -0.55, -0.54, -0.53, -0.52, -0.51, -0.50, -0.49, -0.48, -0.47, -0.46, -0.45, -0.44, -0.43, -0.42, -0.41, -0.40, -0.39, -0.38, -0.37, -0.36, -0.35, -0.34, -0.33, -0.32, -0.31, -0.30, -0.29, -0.28, -0.27, -0.26, -0.25, -0.24, -0.23, -0.22, -0.21, -0.20, -0.19, -0.18, -0.17, -0.16, -0.15, -0.14, -0.13, -0.12, -0.11, -0.10, -0.09, -0.08, -0.07, -0.06, -0.05, -0.04, -0.03, -0.02, -0.01, 0.00, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.10, 0.11, 0.12, 0.13, 0.14, 0.15, 0.16, 0.17, 0.18, 0.19, 0.20, 0.21, 0.22, 0.23, 0.24, 0.25, 0.26, 0.27, 0.28, 0.29, 0.30, 0.31, 0.32, 0.33, 0.34, 0.35, 0.36, 0.37, 0.38, 0.39, 0.40, 0.41, 0.42, 0.43, 0.44, 0.45, 0.46, 0.47, 0.48, 0.49, 0.50, 0.51, 0.52, 0.53, 0.54, 0.55, 0.56, 0.57, 0.58, 0.59, 0.60, 0.61, 0.62, 0.63, 0.64, 0.65, 0.66, 0.67, 0.68, 0.69, 0.70, 0.71, 0.72, 0.73, 0.74, 0.75, 0.76, 0.77, 0.78, 0.79, 0.80, 0.81, 0.82, 0.83, 0.84, 0.85, 0.86, 0.87, 0.88, 0.89, 0.90, 0.91, 0.92, 0.93, 0.94, 0.95, 0.96, 0.97, 0.98, 0.99, 1.00, 1.01, 1.02, 1.03, 1.04, 1.05, 1.06, 1.07, 1.08, 1.09, 1.10, 1.11, 1.12, 1.13, 1.14, 1.15, 1.16, 1.17, 1.18, 1.19, 1.20, 1.21, 1.22, 1.23, 1.24, 1.25, 1.26, 1.27, 1.28, 1.29, 1.30, 1.31, 1.32, 1.33, 1.34, 1.35, 1.36, 1.37, 1.38, 1.39, 1.40, 1.41, 1.42, 1.43, 1.44, 1.45, 1.46, 1.47, 1.48, 1.49, 1.50, 1.51, 1.52, 1.53, 1.54, 1.55, 1.56, 1.57, 1.58, 1.59, 1.60, 1.61, 1.62, 1.63, 1.64, 1.65, 1.66, 1.67, 1.68, 1.69, 1.70, 1.71, 1.72, 1.73, 1.74, 1.75, 1.76, 1.77, 1.78, 1.79, 1.80, 1.81, 1.82, 1.83, 1.84, 1.85, 1.86, 1.87, 1.88, 1.89, 1.90, 1.91, 1.92, 1.93, 1.94, 1.95, 1.96, 1.97, 1.98, 1.99, 2.00, 2.01, 2.02, 2.03, 2.04, 2.05, 2.06, 2.07, 2.08, 2.09, 2.10, 2.11, 2.12, 2.13, 2.14, 2.15, 2.16, 2.17, 2.18, 2.19, 2.20, 2.21, 2.22, 2.23, 2.24, 2.25, 2.26, 2.27, 2.28, 2.29, 2.30, 2.31, 2.32, 2.33, 2.34, 2.35, 2.36, 2.37, 2.38, 2.39, 2.40, 2.41, 2.42, 2.43, 2.44, 2.45, 2.46, 2.47, 2.48, 2.49, 2.50, 2.51, 2.52, 2.53, 2.54, 2.55, 2.56, 2.57, 2.58, 2.59, 2.60, 2.61, 2.62, 2.63, 2.64, 2.65, 2.66, 2.67, 2.68, 2.69, 2.70, 2.71, 2.72, 2.73, 2.74, 2.75, 2.76, 2.77, 2.78, 2.79, 2.80, 2.81, 2.82, 2.83, 2.84, 2.85, 2.86, 2.87, 2.88, 2.89, 2.90, 2.91, 2.92, 2.93, 2.94, 2.95, 2.96, 2.97, 2.98, 2.99, 3.00, 3.01, 3.02, 3.03, 3.04, 3.05, 3.06, 3.07, 3.08, 3.09, 3.10, 3.11, 3.12, 3.13, 3.14, 3.15, 3.16, 3.17, 3.18, 3.19, 3.20, 3.21, 3.22, 3.23, 3.24, 3.25, 3.26, 3.27, 3.28, 3.29, 3.30, 3.31, 3.32, 3.33, 3.34, 3.35, 3.36, 3.37, 3.38, 3.39, 3.40, 3.41, 3.42, 3.43, 3.44, 3.45, 3.46, 3.47, 3.48, 3.49, 3.50, 3.51, 3.52, 3.53, 3.54, 3.55, 3.56, 3.57, 3.58, 3.59, 3.60, 3.61, 3.62, 3.63, 3.64, 3.65, 3.66, 3.67, 3.68, 3.69, 3.70, 3.71, 3.72, 3.73, 3.74, 3.75, 3.76, 3.77, 3.78, 3.79, 3.80, 3.81, 3.82, 3.83, 3.84, 3.85, 3.86, 3.87, 3.88, 3.89, 3.90, 3.91, 3.92, 3.93, 3.94, 3.95, 3.96, 3.97, 3.98, 3.99, 4.00, 4.01, 4.02, 4.03, 4.04, 4.05, 4.06, 4.07, 4.08, 4.09, 4.10, 4.11, 4.12, 4.13, 4.14, 4.15, 4.16, 4.17, 4.18, 4.19, 4.20, 4.21, 4.22, 4.23, 4.24, 4.25, 4.26, 4.27, 4.28, 4.29, 4.30, 4.31, 4.32, 4.33, 4.34, 4.35, 4.36, 4.37, 4.38, 4.39, 4.40, 4.41, 4.42, 4.43, 4.44, 4.45, 4.46, 4.47, 4.48, 4.49, 4.50, 4.51, 4.52, 4.53, 4.54, 4.55, 4.56, 4.57, 4.58, 4.59, 4.60, 4.61, 4.62, 4.63, 4.64, 4.65, 4.66, 4.67, 4.68, 4.69, 4.70, 4.71, 4.72, 4.73, 4.74, 4.75, 4.76, 4.77, 4.78, 4.79, 4.80, 4.81, 4.82, 4.83, 4.84, 4.85, 4.86, 4.87, 4.88, 4.89, 4.90, 4.91, 4.92, 4.93, 4.94, 4.95, 4.96, 4.97, 4.98, 4.99, 5.00 };
	double[] LookupTable = { 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000000, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000001, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000002, 0.000003, 0.000003, 0.000003, 0.000003, 0.000003, 0.000003, 0.000003, 0.000004, 0.000004, 0.000004, 0.000004, 0.000004, 0.000004, 0.000005, 0.000005, 0.000005, 0.000005, 0.000006, 0.000006, 0.000006, 0.000007, 0.000007, 0.000007, 0.000007, 0.000008, 0.000008, 0.000009, 0.000009, 0.000009, 0.000010, 0.000010, 0.000011, 0.000011, 0.000012, 0.000012, 0.000013, 0.000013, 0.000014, 0.000015, 0.000015, 0.000016, 0.000017, 0.000017, 0.000018, 0.000019, 0.000020, 0.000021, 0.000022, 0.000023, 0.000024, 0.000025, 0.000026, 0.000027, 0.000028, 0.000029, 0.000030, 0.000032, 0.000033, 0.000034, 0.000036, 0.000037, 0.000039, 0.000041, 0.000042, 0.000044, 0.000046, 0.000048, 0.000050, 0.000052, 0.000054, 0.000057, 0.000059, 0.000062, 0.000064, 0.000067, 0.000069, 0.000072, 0.000075, 0.000078, 0.000082, 0.000085, 0.000088, 0.000092, 0.000096, 0.000100, 0.000104, 0.000108, 0.000112, 0.000117, 0.000121, 0.000126, 0.000131, 0.000136, 0.000142, 0.000147, 0.000153, 0.000159, 0.000165, 0.000172, 0.000178, 0.000185, 0.000193, 0.000200, 0.000208, 0.000216, 0.000224, 0.000233, 0.000242, 0.000251, 0.000260, 0.000270, 0.000280, 0.000291, 0.000302, 0.000313, 0.000325, 0.000337, 0.000349, 0.000362, 0.000376, 0.000390, 0.000404, 0.000419, 0.000434, 0.000450, 0.000466, 0.000483, 0.000501, 0.000519, 0.000538, 0.000557, 0.000577, 0.000598, 0.000619, 0.000641, 0.000664, 0.000687, 0.000711, 0.000736, 0.000762, 0.000789, 0.000816, 0.000845, 0.000874, 0.000904, 0.000935, 0.000968, 0.001001, 0.001035, 0.001070, 0.001107, 0.001144, 0.001183, 0.001223, 0.001264, 0.001306, 0.001350, 0.001395, 0.001441, 0.001489, 0.001538, 0.001589, 0.001641, 0.001695, 0.001750, 0.001807, 0.001866, 0.001926, 0.001988, 0.002052, 0.002118, 0.002186, 0.002256, 0.002327, 0.002401, 0.002477, 0.002555, 0.002635, 0.002718, 0.002803, 0.002890, 0.002980, 0.003072, 0.003167, 0.003264, 0.003364, 0.003467, 0.003573, 0.003681, 0.003793, 0.003907, 0.004025, 0.004145, 0.004269, 0.004396, 0.004527, 0.004661, 0.004799, 0.004940, 0.005085, 0.005234, 0.005386, 0.005543, 0.005703, 0.005868, 0.006037, 0.006210, 0.006387, 0.006569, 0.006756, 0.006947, 0.007143, 0.007344, 0.007549, 0.007760, 0.007976, 0.008198, 0.008424, 0.008656, 0.008894, 0.009137, 0.009387, 0.009642, 0.009903, 0.010170, 0.010444, 0.010724, 0.011011, 0.011304, 0.011604, 0.011911, 0.012224, 0.012545, 0.012874, 0.013209, 0.013553, 0.013903, 0.014262, 0.014629, 0.015003, 0.015386, 0.015778, 0.016177, 0.016586, 0.017003, 0.017429, 0.017864, 0.018309, 0.018763, 0.019226, 0.019699, 0.020182, 0.020675, 0.021178, 0.021692, 0.022216, 0.022750, 0.023295, 0.023852, 0.024419, 0.024998, 0.025588, 0.026190, 0.026803, 0.027429, 0.028067, 0.028717, 0.029379, 0.030054, 0.030742, 0.031443, 0.032157, 0.032884, 0.033625, 0.034380, 0.035148, 0.035930, 0.036727, 0.037538, 0.038364, 0.039204, 0.040059, 0.040930, 0.041815, 0.042716, 0.043633, 0.044565, 0.045514, 0.046479, 0.047460, 0.048457, 0.049471, 0.050503, 0.051551, 0.052616, 0.053699, 0.054799, 0.055917, 0.057053, 0.058208, 0.059380, 0.060571, 0.061780, 0.063008, 0.064255, 0.065522, 0.066807, 0.068112, 0.069437, 0.070781, 0.072145, 0.073529, 0.074934, 0.076359, 0.077804, 0.079270, 0.080757, 0.082264, 0.083793, 0.085343, 0.086915, 0.088508, 0.090123, 0.091759, 0.093418, 0.095098, 0.096800, 0.098525, 0.100273, 0.102042, 0.103835, 0.105650, 0.107488, 0.109349, 0.111232, 0.113139, 0.115070, 0.117023, 0.119000, 0.121000, 0.123024, 0.125072, 0.127143, 0.129238, 0.131357, 0.133500, 0.135666, 0.137857, 0.140071, 0.142310, 0.144572, 0.146859, 0.149170, 0.151505, 0.153864, 0.156248, 0.158655, 0.161087, 0.163543, 0.166023, 0.168528, 0.171056, 0.173609, 0.176186, 0.178786, 0.181411, 0.184060, 0.186733, 0.189430, 0.192150, 0.194895, 0.197663, 0.200454, 0.203269, 0.206108, 0.208970, 0.211855, 0.214764, 0.217695, 0.220650, 0.223627, 0.226627, 0.229650, 0.232695, 0.235762, 0.238852, 0.241964, 0.245097, 0.248252, 0.251429, 0.254627, 0.257846, 0.261086, 0.264347, 0.267629, 0.270931, 0.274253, 0.277595, 0.280957, 0.284339, 0.287740, 0.291160, 0.294599, 0.298056, 0.301532, 0.305026, 0.308538, 0.312067, 0.315614, 0.319178, 0.322758, 0.326355, 0.329969, 0.333598, 0.337243, 0.340903, 0.344578, 0.348268, 0.351973, 0.355691, 0.359424, 0.363169, 0.366928, 0.370700, 0.374484, 0.378280, 0.382089, 0.385908, 0.389739, 0.393580, 0.397432, 0.401294, 0.405165, 0.409046, 0.412936, 0.416834, 0.420740, 0.424655, 0.428576, 0.432505, 0.436441, 0.440382, 0.444330, 0.448283, 0.452242, 0.456205, 0.460172, 0.464144, 0.468119, 0.472097, 0.476078, 0.480061, 0.484047, 0.488034, 0.492022, 0.496011, 0.500000, 0.503989, 0.507978, 0.511966, 0.515953, 0.519939, 0.523922, 0.527903, 0.531881, 0.535856, 0.539828, 0.543795, 0.547758, 0.551717, 0.555670, 0.559618, 0.563559, 0.567495, 0.571424, 0.575345, 0.579260, 0.583166, 0.587064, 0.590954, 0.594835, 0.598706, 0.602568, 0.606420, 0.610261, 0.614092, 0.617911, 0.621720, 0.625516, 0.629300, 0.633072, 0.636831, 0.640576, 0.644309, 0.648027, 0.651732, 0.655422, 0.659097, 0.662757, 0.666402, 0.670031, 0.673645, 0.677242, 0.680822, 0.684386, 0.687933, 0.691462, 0.694974, 0.698468, 0.701944, 0.705401, 0.708840, 0.712260, 0.715661, 0.719043, 0.722405, 0.725747, 0.729069, 0.732371, 0.735653, 0.738914, 0.742154, 0.745373, 0.748571, 0.751748, 0.754903, 0.758036, 0.761148, 0.764238, 0.767305, 0.770350, 0.773373, 0.776373, 0.779350, 0.782305, 0.785236, 0.788145, 0.791030, 0.793892, 0.796731, 0.799546, 0.802337, 0.805105, 0.807850, 0.810570, 0.813267, 0.815940, 0.818589, 0.821214, 0.823814, 0.826391, 0.828944, 0.831472, 0.833977, 0.836457, 0.838913, 0.841345, 0.843752, 0.846136, 0.848495, 0.850830, 0.853141, 0.855428, 0.857690, 0.859929, 0.862143, 0.864334, 0.866500, 0.868643, 0.870762, 0.872857, 0.874928, 0.876976, 0.879000, 0.881000, 0.882977, 0.884930, 0.886861, 0.888768, 0.890651, 0.892512, 0.894350, 0.896165, 0.897958, 0.899727, 0.901475, 0.903200, 0.904902, 0.906582, 0.908241, 0.909877, 0.911492, 0.913085, 0.914657, 0.916207, 0.917736, 0.919243, 0.920730, 0.922196, 0.923641, 0.925066, 0.926471, 0.927855, 0.929219, 0.930563, 0.931888, 0.933193, 0.934478, 0.935745, 0.936992, 0.938220, 0.939429, 0.940620, 0.941792, 0.942947, 0.944083, 0.945201, 0.946301, 0.947384, 0.948449, 0.949497, 0.950529, 0.951543, 0.952540, 0.953521, 0.954486, 0.955435, 0.956367, 0.957284, 0.958185, 0.959070, 0.959941, 0.960796, 0.961636, 0.962462, 0.963273, 0.964070, 0.964852, 0.965620, 0.966375, 0.967116, 0.967843, 0.968557, 0.969258, 0.969946, 0.970621, 0.971283, 0.971933, 0.972571, 0.973197, 0.973810, 0.974412, 0.975002, 0.975581, 0.976148, 0.976705, 0.977250, 0.977784, 0.978308, 0.978822, 0.979325, 0.979818, 0.980301, 0.980774, 0.981237, 0.981691, 0.982136, 0.982571, 0.982997, 0.983414, 0.983823, 0.984222, 0.984614, 0.984997, 0.985371, 0.985738, 0.986097, 0.986447, 0.986791, 0.987126, 0.987455, 0.987776, 0.988089, 0.988396, 0.988696, 0.988989, 0.989276, 0.989556, 0.989830, 0.990097, 0.990358, 0.990613, 0.990863, 0.991106, 0.991344, 0.991576, 0.991802, 0.992024, 0.992240, 0.992451, 0.992656, 0.992857, 0.993053, 0.993244, 0.993431, 0.993613, 0.993790, 0.993963, 0.994132, 0.994297, 0.994457, 0.994614, 0.994766, 0.994915, 0.995060, 0.995201, 0.995339, 0.995473, 0.995604, 0.995731, 0.995855, 0.995975, 0.996093, 0.996207, 0.996319, 0.996427, 0.996533, 0.996636, 0.996736, 0.996833, 0.996928, 0.997020, 0.997110, 0.997197, 0.997282, 0.997365, 0.997445, 0.997523, 0.997599, 0.997673, 0.997744, 0.997814, 0.997882, 0.997948, 0.998012, 0.998074, 0.998134, 0.998193, 0.998250, 0.998305, 0.998359, 0.998411, 0.998462, 0.998511, 0.998559, 0.998605, 0.998650, 0.998694, 0.998736, 0.998777, 0.998817, 0.998856, 0.998893, 0.998930, 0.998965, 0.998999, 0.999032, 0.999065, 0.999096, 0.999126, 0.999155, 0.999184, 0.999211, 0.999238, 0.999264, 0.999289, 0.999313, 0.999336, 0.999359, 0.999381, 0.999402, 0.999423, 0.999443, 0.999462, 0.999481, 0.999499, 0.999517, 0.999534, 0.999550, 0.999566, 0.999581, 0.999596, 0.999610, 0.999624, 0.999638, 0.999651, 0.999663, 0.999675, 0.999687, 0.999698, 0.999709, 0.999720, 0.999730, 0.999740, 0.999749, 0.999758, 0.999767, 0.999776, 0.999784, 0.999792, 0.999800, 0.999807, 0.999815, 0.999822, 0.999828, 0.999835, 0.999841, 0.999847, 0.999853, 0.999858, 0.999864, 0.999869, 0.999874, 0.999879, 0.999883, 0.999888, 0.999892, 0.999896, 0.999900, 0.999904, 0.999908, 0.999912, 0.999915, 0.999918, 0.999922, 0.999925, 0.999928, 0.999931, 0.999933, 0.999936, 0.999938, 0.999941, 0.999943, 0.999946, 0.999948, 0.999950, 0.999952, 0.999954, 0.999956, 0.999958, 0.999959, 0.999961, 0.999963, 0.999964, 0.999966, 0.999967, 0.999968, 0.999970, 0.999971, 0.999972, 0.999973, 0.999974, 0.999975, 0.999976, 0.999977, 0.999978, 0.999979, 0.999980, 0.999981, 0.999982, 0.999983, 0.999983, 0.999984, 0.999985, 0.999985, 0.999986, 0.999987, 0.999987, 0.999988, 0.999988, 0.999989, 0.999989, 0.999990, 0.999990, 0.999991, 0.999991, 0.999991, 0.999992, 0.999992, 0.999993, 0.999993, 0.999993, 0.999993, 0.999994, 0.999994, 0.999994, 0.999995, 0.999995, 0.999995, 0.999995, 0.999996, 0.999996, 0.999996, 0.999996, 0.999996, 0.999996, 0.999997, 0.999997, 0.999997, 0.999997, 0.999997, 0.999997, 0.999997, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999998, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 0.999999, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000, 1.000000 };

	public KSNormality()
	{
	}

	public double FindKSNormality(double normDist)
    {
		if (normDist < -5)
			return 0;

		if (normDist > 5)
			return 1;

		int normDistLowIdx = FindLowKeyIndex(normDist);
		int normDistHighIdx = FindHighKeyIndex(normDist);

		if (normDistLowIdx == -1 || normDistHighIdx == -1)
            return double.MinValue;

		double lowNormDist = NormDistKey[normDistLowIdx];
		double highNormDist = NormDistKey[normDistHighIdx];

		double lowValue = LookupTable[normDistLowIdx];
		double highValue = LookupTable[normDistHighIdx];

		return Interpolate(lowNormDist, highNormDist, normDist, lowValue, highValue);
    }

    private double Interpolate(double lowKey, double highKey, double targetKey, double lowResult, double highResult)
	{
		if (lowResult == highResult)
			return lowResult;
		else
			return Math.Round(((highKey - targetKey) / (highKey - lowKey) * lowResult) + ((targetKey - lowKey) / (highKey - lowKey) * highResult), 6);
    }

    private int FindLowKeyIndex(double targetValue)
    {
        int retVal = -1;
		for (int x = 0; x < NormDistKey.Length; x++)
        {
			if (NormDistKey[x] <= targetValue)
                retVal = x;
            else
                break;
        }
        return retVal;
    }

    private int FindHighKeyIndex(double targetValue)
    {
        int retVal = -1;
		for (int x = (NormDistKey.Length - 1); x >= 0; x--)
        {
			if (NormDistKey[x] >= targetValue)
                retVal = x;
            else
                break;
        }
        return retVal;
    }
}
