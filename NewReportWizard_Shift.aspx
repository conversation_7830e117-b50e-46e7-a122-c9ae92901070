﻿<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Shift.aspx.cs" Inherits="NewReportWizard_Shift" %>
<%@ register tagprefix="rpt" tagname="DimensionSelector" src="~/controls/DimensionSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">
	function AddTab()
	{
		var btn = document.getElementById('<%= AddTabBtn.ClientID %>');
		btn.click();
	}
	function OnChanged(sender, args)
	{
	    sender.get_clientStateField().value = sender.saveClientState();
	}
</script>

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="NewSeriesButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series1Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series2Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series3Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series4Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="DeleteSeriesButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<asp:button style="display:none;" runat="server" id="AddTabBtn" causesvalidation="false" onclick="AddSeries_Click" />
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"> 
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				 
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px;padding-bottom:5px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
				
					<div id="ButtonUpdateDiv" runat="server" style="padding-left:14px; padding-right:14px; padding-bottom:0px; height:44px;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series1Btn" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="1"><div id="Tab1" runat="server" class="tab_selected">Series 1</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series2Btn" visible="false" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="2"><div id="Tab2" runat="server" class="tab">Series 2</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series3Btn" visible="false" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="3"><div id="Tab3" runat="server" class="tab">Series 3</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series4Btn" visible="false" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="4"><div id="Tab4" runat="server" class="tab">Series 4</div></asp:linkbutton></td>
								<td>&nbsp;</td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="DeleteSeriesButton" visible="false" runat="server" onclick="DeleteSeries_Click" causesvalidation="false"><div id="Div1" runat="server" class="tab"><img style="padding-top:4px;" src="images/buttons/minus.png" border="0" />&nbsp;Delete Series</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="NewSeriesButton" visible="false" runat="server" onclick="AddSeries_Click"><div id="TabNew" runat="server" class="tab"><img style="padding-top:4px;" src="images/buttons/add.png" border="0" />&nbsp;Add Series</div></asp:linkbutton></td>
							</tr>
						</table>
					</div>
					<div style="padding-left:14px; padding-right:14px;">
						<div id="ContentUpdateDiv" runat="server" style="border:solid 2px #2388d8;">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>
									<td style="width:200px;"><div style="width:200px;" class="rowHeading">Series <asp:label id="SeriesNumLabel" runat="server"></asp:label> Name:</div></td>
									<td>
										<asp:textbox id="seriesName" width="244" cssclass="entryControl" runat="server"></asp:textbox>
										<asp:requiredfieldvalidator id="SeriesVal1" runat="server" controltovalidate="seriesName" cssclass="error" errormessage="* Required"></asp:requiredfieldvalidator>
										<br /><asp:customvalidator display="dynamic" id="Val" runat="server" style="padding-left:14px;" onservervalidate="ValidateSeriesNameCollision" cssclass="error" errormessage="* All series and spec label names must be unique."></asp:customvalidator>
									</td>
								</tr>
								<tr>
									<td style="padding-bottom:5px;width:200px;" valign="top">
										<div class="rowHeading" style="width:200px;">Select Dimension:</div>
										<div class="leftPad body" style="padding-top:15px;">
											All checked items will display in the final chart. 
										</div>
									</td>
									<td style="padding-left:14px;"><rpt:DimensionSelector id="dimensionSel" runat="server" isrequired="true"></rpt:DimensionSelector></td>
								</tr>
								<tr>
									<td style="width:200px;" valign="top"><div style="width:200px;" class="rowHeading">Rate Type:</div></td>
									<td> 
										<asp:dropdownlist id="rateTypeList" autopostback="true" onselectedindexchanged="rateTypeList_selectedIndexChanged" cssclass="entryControl" runat="server" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:dropdownlist>
										<asp:requiredfieldvalidator id="val3" runat="server" controltovalidate="rateTypeList" cssclass="error" errormessage="*"></asp:requiredfieldvalidator>
										<asp:customvalidator display="dynamic" id="c" runat="server" onservervalidate="ValidateRate" cssclass="error" errormessage="* 'By Statistic Value' is only valid with the Statistic dimension."></asp:customvalidator>
									</td>
								</tr>
								<tr>
									<td style="width:200px;" valign="top"><div style="width:200px;" class="rowHeading">Workload Amount:</div></td>
									<td> 
										<asp:textbox id="workLoadField" runat="server" enabled="false" cssclass="entryControl"></asp:textbox>
										<asp:customvalidator display="dynamic" id="workLoadVal" runat="server" onservervalidate="ValidateWorkLoad" cssclass="error" errormessage="* Must be > 0"></asp:customvalidator>
									</td>
								</tr>
							</table>
						</div>
						
					</div>
					<table border="0" cellpadding="0" cellspacing="0" style="padding:5px 0px 0px 14px;">
						<tr>
							<td style="padding-bottom:5px;width:200px;" valign="top">
								<div class="rowHeading" style="width:200px;">Options:</div>
							</td>
                            <td style="padding-left:10px;">
    							<asp:checkbox id="assumeOneFailureCheck" runat="server" text="Assume 1 Failure" />
                            </td>
						</tr>
					</table>
					<table border="0" cellpadding="0" cellspacing="0" style="padding:5px 0px 0px 14px;">
						<tr>
							<td style="padding-bottom:5px;width:200px;" valign="top">
								<div class="rowHeading" style="width:200px;">Observation Filter:</div>
								<div class="leftPad body" style="padding-top:15px;">
									Select the observations to include in the lower section of the Shift Report.
								</div>
							</td>
							<td style="padding-left:14px;"><rpt:DimensionSelector id="observationSel" runat="server" isrequired="false"></rpt:DimensionSelector></td>
						</tr>
					</table>
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
				</div>
			</td>
		</tr> 
	</table>
</asp:panel>

</asp:Content>

