using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class controls_ReadOnlyDashboard : System.Web.UI.UserControl
{
	public string UserName
	{
		get { if (this.ViewState["u"] != null) return (string)this.ViewState["u"]; else return Utility.GetUserName(); }
		set { this.ViewState["u"] = value; }
	}

    private List<DockState> CurrentDockStates
    {
        get
        {
            string key = "ReadOnlyDashboard";
            List<DockState> currentDockStates = (List<DockState>) HttpContext.Current.Items[key];
            if (currentDockStates == null)
            {
                currentDockStates = new List<DockState>();
                object dashState = SqlHelper.ExecuteScalar("RPT_LoadDashboardState", DieboldConstants.DASHBOARD_MASTER_NAME);

                if (dashState != null)
                {
                    string[] dockStates = dashState.ToString().Split('|');
                    foreach (string serializedState in dockStates)
                    {
                        if (!string.IsNullOrEmpty(serializedState))
                        {
                            DockState state = DockState.Deserialize(serializedState);

                            //Fix for control names being part of the dock zone saved from the edit page.
                            state.DockZoneID = state.DockZoneID.Replace("EditableDashboardCtrl", "SharedDashboardCtrl");
                            currentDockStates.Add(state);
                        }
                    }
                }

                HttpContext.Current.Items[key] = currentDockStates;
            }
            return currentDockStates;
        }       
    }

	protected void Page_Init(object sender, EventArgs e)
	{
		//Recreate the docks in order to ensure their proper operation
		for (int i = 0; i < CurrentDockStates.Count; i++)
		{
			if (CurrentDockStates[i].Closed == false)
			{
				RadDock dock = CreateRadDockFromState(CurrentDockStates[i]);
				dock.Skin = "Diebold";
				dock.EnableEmbeddedSkins = false;
				// The RadDockLayout control will automatically move the RadDock
				// controls to their corresponding zone in the LoadDockLayout event (see below).
				RadDockLayout1.Controls.Add(dock);
				//Load the selected widget
				LoadWidget(dock);
			}
		}
	}

    protected void Page_Load(object sender, EventArgs e)
    {
        this.Page.PreRender += new EventHandler(Page_PreRender);
        if (!Page.IsPostBack)
        {
            if (Utility.IsUserAdmin())
                AdminButton.Visible = true;
        }
    }

    private void Page_PreRender(object sender, EventArgs e)
    {
        foreach (RadDock dock in RadDockLayout1.RegisteredDocks)
        {
            foreach (Control cntrl in dock.ContentContainer.Controls)
                if (cntrl is IDashboardItem)
                    ((IDashboardItem)cntrl).DisplayContent();
        }
    }

    protected void AdminEditShared_Click(object sender, EventArgs e)
    {
        Response.Redirect("EditSharedDashboard.aspx");
    }
    
    protected void RadDockLayout1_LoadDockLayout(object sender, DockLayoutEventArgs e)
	{
		//Populate the event args with the state information. The RadDockLayout control
		// will automatically move the docks according that information.
		foreach (DockState state in CurrentDockStates)
		{
			e.Positions[state.UniqueName] = state.DockZoneID;
			e.Indices[state.UniqueName] = state.Index;
		}
	}

	private RadDock CreateRadDockFromState(DockState state)
	{
		RadDock dock = new RadDock();
		dock.Skin = "Diebold";
		dock.DockMode = DockMode.Docked;
		dock.EnableEmbeddedSkins = false;
		dock.ID = string.Format("RadDock{0}", state.UniqueName);
		dock.ApplyState(state);
		dock.DefaultCommands = Telerik.Web.UI.Dock.DefaultCommands.None;
		dock.EnableDrag = false;

		return dock;
	}

	private void LoadWidget(RadDock dock)
	{
		if (string.IsNullOrEmpty(dock.Tag))
		{
			return;
		}
		Control widget = LoadControl(dock.Tag);
		if (widget is IDashboardItem)
			((IDashboardItem)widget).Initialize(new Guid(dock.UniqueName), true);
		dock.ContentContainer.Controls.Add(widget);
	}
}

