using System;
using System.Data;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Olap.Data;
using Dundas.Olap.Manager;
using Dundas.Olap.WebUIControls;
using WebSupergoo.ABCpdf9;
using System.Text.RegularExpressions;

public class ReportHelper
{
    public enum DateGroupingEnum { BY_DATE = 1, BY_WEEK = 2, BY_MONTH = 3, BY_DATETIME = 4, BY_ALL_TIME = 5 };

    public enum ReportTypeEnum { UNSPECIFIED = 0, LEGACY_PRST = 1, LEGACY_GENERAL = 2, LEGACY_PARETO = 3, LEGACY_PROGRESS = 4, LEGACY_DISTRIBUTION = 5, PRST = 6, GENERAL = 7,
        PARETO = 8, PROGRESS = 9, DISTRIBUTION = 10, SHIFT = 11, SENSOR = 12, ENA = 13, ECRM = 14, AFD = 15, STATISTICS = 16, TBT = 17, BUBBLE = 18,
        AFD_CONFIGURATION_MANAGER = 19 };

    public enum XAxisTypeEnum { CUMULATIVE_TRANSACTIONS = 1, CUMULATIVE_MEDIA = 2 };
    public enum RateTypeEnum
    {
        OCCURRANCES = 0, PER_MILLION_MEDIA = 1, PER_MILLION_TRANSACTIONS = 2, PERCENTAGE_OF_MEDIA = 3, PERCENTAGE_OF_TRANSACTIONS = 4, BY_STAT_VALUE = 5,
        MEDIA_PER_INCIDENT = 6, TRANSACTIONS_PER_INCIDENT = 7, PER_WORKLOAD_MEDIA = 8, PER_WORKLOAD_TRANSACTIONS = 9,
        INVERSE_PER_MILLION_MEDIA = 10, INVERSE_PER_MILLION_TRANSACTIONS = 11, INVERSE_PERCENTAGE_OF_MEDIA = 12, INVERSE_PERCENTAGE_OF_TRANSACTIONS = 13,
        INVERSE_MEDIA_PER_INCIDENT = 14, INVERSE_TRANSACTIONS_PER_INCIDENT = 15, INVERSE_PER_WORKLOAD_MEDIA = 16, INVERSE_PER_WORKLOAD_TRANSACTIONS = 17
    };

    public enum RelativeTimeEnum
    {
        UNSPECIFIED = 0, TODAY = 1, YESTERDAY = 2, PREVIOUS_WORK_DAY = 3, TWO_DAYS_AGO = 4, TWO_WORK_DAYS_AGO = 5, START_CURRENT_WEEK = 6,
        START_CURRENT_WORK_WEEK = 7, START_LAST_WEEK = 8, START_LAST_WORK_WEEK = 9, END_LAST_WEEK = 10, END_LAST_WORK_WEEK = 11,
        START_CURRENT_MONTH = 12, START_LAST_MONTH = 13, END_LAST_MONTH = 14, TEN_DAYS_AGO = 15, TEN_WORK_DAYS_AGO = 16
    };

    public enum PrebuiltReportsEnum
    {
        UNSPECIFIED = 0, SUMMARY_TRANSACTION_VOLUME = 1, SUMMARY_MEDIA_VOLUME = 2, SUMMARY_OBSERVATION_VOLUME = 3,
        DAILY_TRANSACTION_VOLUME = 4, DAILY_MEDIA_VOLUME = 5, DAILY_OBSERVATION_VOLUME = 6
    };

    public static DimensionDescriptor BuildDimDescriptorDateTime(OlapManager mgr, DateTime startDate, DateTime endDate)
    {
        CubeDataSchema schema = mgr.GetDataSchema(mgr.CurrentCubeName);

        Level dateLevel = schema.FindLevelByUniqueName("[Time].[Year-Week-Date].[DateTime]");
        List<string> days = new List<string>();
        if (!DateTime.MinValue.Equals(startDate) || !DateTime.MinValue.Equals(endDate))
        {
            foreach (Member dateMember in dateLevel.Members)
            {
                DateTime memberDate = DateTime.ParseExact(dateMember.Name, "MM/dd/yyyy HH:mm:ss", System.Globalization.DateTimeFormatInfo.CurrentInfo);
                if ((DateTime.MinValue.Equals(startDate) || DateTime.Compare(memberDate, startDate) >= 0)
                        && (DateTime.MinValue.Equals(endDate) || DateTime.Compare(memberDate, endDate) < 0))
                {
                    days.Add(dateMember.ParentMember.Name);
                }
            }
        }

        if (days.Count > 0)
            return new DimensionDescriptor("Time", "DateTime", days.ToArray());
        else
            return new DimensionDescriptor("Time", dateLevel.UniqueName);
    }

    public static DimensionDescriptor BuildDimDescriptorDays(OlapManager mgr, DateTime startDate, DateTime endDate)
    {
        CubeDataSchema schema = mgr.GetDataSchema(mgr.CurrentCubeName);

        Level dateLevel = schema.FindLevelByUniqueName("[Time].[Year-Week-Date].[Date]");
        List<string> days = new List<string>();
        if (!DateTime.MinValue.Equals(startDate) || !DateTime.MinValue.Equals(endDate))
        {
            foreach (Member dateMember in dateLevel.Members)
            {
                DateTime memberDate = DateTime.ParseExact(dateMember.Name, "MM/dd/yyyy", System.Globalization.DateTimeFormatInfo.CurrentInfo);
                if ((DateTime.MinValue.Equals(startDate) || DateTime.Compare(memberDate, startDate) >= 0)
                        && (DateTime.MinValue.Equals(endDate) || DateTime.Compare(memberDate, endDate) < 0))
                {
                    days.Add(dateMember.Name);
                }
            }
        }

        if (days.Count > 0)
            return new DimensionDescriptor("Time", "Date", days.ToArray());
        else
            return new DimensionDescriptor("Time", dateLevel.UniqueName);
    }

    public static DimensionDescriptor BuildDimDescriptorWeeks(OlapManager mgr, DateTime startDate, DateTime endDate)
    {
        CubeDataSchema schema = mgr.GetDataSchema(mgr.CurrentCubeName);

        Level dateLevel = schema.FindLevelByUniqueName("[Time].[Year-Week-Date].[Date]");
        Level weekLevel = schema.FindLevelByUniqueName("[Time].[Year-Week-Date].[Week]");
        List<string> weeks = new List<string>();
        if (!DateTime.MinValue.Equals(startDate) || !DateTime.MinValue.Equals(endDate))
        {
            foreach (Member dateMember in dateLevel.Members)
            {
                DateTime memberDate = DateTime.ParseExact(dateMember.Name, "MM/dd/yyyy", System.Globalization.DateTimeFormatInfo.CurrentInfo);
                if ((DateTime.MinValue.Equals(startDate) || DateTime.Compare(memberDate, startDate) >= 0)
                        && (DateTime.MinValue.Equals(endDate) || DateTime.Compare(memberDate, endDate) < 0))
                {
                    Member weekMember = dateMember.ParentMember;
                    if (!weeks.Contains(weekMember.Name))
                        weeks.Add(weekMember.Name);
                }
            }
        }

        if (weeks.Count > 0)
            return new DimensionDescriptor("Time", "Week", weeks.ToArray());
        else
            return new DimensionDescriptor("Time", weekLevel.UniqueName);
    }

    public static DimensionDescriptor BuildDimDescriptorMonths(OlapManager mgr, DateTime startDate, DateTime endDate)
    {
        CubeDataSchema schema = mgr.GetDataSchema(mgr.CurrentCubeName);

        Level dateLevel = schema.FindLevelByUniqueName("[Time].[Year-Week-Date].[Date]");
        Level monthLevel = schema.FindLevelByUniqueName("[Time].[Year-Week-Date].[Month]");
        List<string> months = new List<string>();
        if (!DateTime.MinValue.Equals(startDate) || !DateTime.MinValue.Equals(endDate))
        {
            foreach (Member dateMember in dateLevel.Members)
            {
                DateTime memberDate = DateTime.ParseExact(dateMember.Name, "MM/dd/yyyy", System.Globalization.DateTimeFormatInfo.CurrentInfo);
                if ((DateTime.MinValue.Equals(startDate) || DateTime.Compare(memberDate, startDate) >= 0)
                        && (DateTime.MinValue.Equals(endDate) || DateTime.Compare(memberDate, endDate) < 0))
                {
                    Member monthMember = dateMember.ParentMember.ParentMember;
                    if (!months.Contains(monthMember.Name))
                        months.Add(monthMember.Name);
                }
            }
        }

        if (months.Count > 0)
            return new DimensionDescriptor("Time", "Month", months.ToArray());
        else
            return new DimensionDescriptor("Time", monthLevel.UniqueName);
    }

    public static DimensionDescriptor BuildDimDescriptorStatusSession(bool isLegacy, OlapManager mgr, List<SessionInfo> sessionList)
    {
        CubeDataSchema schema = mgr.GetDataSchema(mgr.CurrentCubeName);
        DimensionDescriptor retVal = null;
        if (isLegacy)
        {
            retVal = new DimensionDescriptor("Test Session", "[Test Session].[Status-Session].[Status]");

            if (schema != null)
            {
                Level sessionsLevel = schema.FindLevelByUniqueName("[Test Session].[Status-Session].[Session]");
                if (sessionsLevel != null)
                {
                    foreach (SessionInfo session in sessionList)
                    {
                        Member sessionMember = sessionsLevel.Members.FindByUniqueName("[Test Session].[Status-Session].[Session].&[" + session.SessionId.ToString() + "]");
                        if (sessionMember != null)
                        {
                            DimensionMemberDescriptor desc = new DimensionMemberDescriptor(sessionMember.Name, sessionMember.UniqueName);
                            if (!retVal.Members.Contains(desc))
                                retVal.Members.Add(desc);
                        }
                    }
                }
            }
        }
        else
        {
            retVal = new DimensionDescriptor("Session", "[Session].[Status - Session].[Status]");

            if (schema != null)
            {
                Level sessionsLevel = schema.FindLevelByUniqueName("[Session].[Status - Session].[Session]");
                if (sessionsLevel != null)
                {
                    foreach (SessionInfo session in sessionList)
                    {
                        Member sessionMember = sessionsLevel.Members.FindByUniqueName("[Session].[Status - Session].[Session].&[" + session.SessionId.ToString() + "]");
                        if (sessionMember != null)
                        {
                            DimensionMemberDescriptor desc = new DimensionMemberDescriptor(sessionMember.Name, sessionMember.UniqueName);
                            if (!retVal.Members.Contains(desc))
                                retVal.Members.Add(desc);
                        }
                    }
                }
            }
        }

        return retVal;
    }

    public static DimensionDescriptor BuildDimDescriptor(OlapManager mgr, string levelName, string memberUniqueName)
    {
        CubeDataSchema schema = mgr.GetDataSchema(mgr.CurrentCubeName);
        List<string> memberNameList = new List<string>();
        memberNameList.Add(memberUniqueName);

        return BuildDescriptor(schema, levelName, memberNameList);
    }

    public static string ConvertReportsToString(OlapManager mgr)
    {
        string retVal = null;
        MemoryStream stream = new MemoryStream();
        StreamReader reader = new StreamReader(stream, Encoding.UTF8);

        mgr.SaveReports(stream);
        stream.Flush();
        stream.Seek(0, SeekOrigin.Begin);

        retVal = reader.ReadToEnd();

        return retVal;
    }

    public static string ConvertChartToTemplate(Dundas.Charting.WebControl.Chart chart)
    {
        string retVal = null;
        MemoryStream stream = new MemoryStream();
        StreamReader reader = new StreamReader(stream, Encoding.UTF8);

        chart.Serializer.Content = Dundas.Charting.WebControl.SerializationContent.All;
        chart.Serializer.Save(stream);
        stream.Flush();
        stream.Seek(0, SeekOrigin.Begin);

        retVal = reader.ReadToEnd();

        return retVal;
    }

    public static void ConvertStringToReports(OlapManager mgr, string reportData)
    {
        if (!string.IsNullOrEmpty(reportData))
        {
            MemoryStream stream = new MemoryStream();
            byte[] data = Encoding.UTF8.GetBytes(reportData);
            if (data != null && data.Length > 0)
            {
                stream.Write(data, 0, data.Length);
                stream.Flush();
                stream.Seek(0, SeekOrigin.Begin);

                //try
                //{
                mgr.LoadReports(stream);
                //}
                //catch
                //{
                //	mgr.CurrentCubeName = "Reporting";
                //	mgr.LoadReports(stream);
                //}
            }
        }
    }

    public static BaseReport LoadReportObject(ReportInfo repInfo, SubReportInfo subReportInfo)
    {
        BaseReport retVal = null;

        if (!string.IsNullOrEmpty(repInfo.StoredProcName)) {
            retVal = new QueryReport(repInfo);
            return retVal;
        }

        switch (repInfo.ReportTypeId)
        {
            case ReportHelper.ReportTypeEnum.PARETO:
                retVal = new ParetoReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.PRST:
                retVal = new PRSTReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.PROGRESS:
                retVal = new ProgressReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.SHIFT:
                retVal = new ShiftReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.SENSOR:
                retVal = new SensorReport(repInfo, subReportInfo);
                break;
            case ReportHelper.ReportTypeEnum.DISTRIBUTION:
                retVal = new DistributionReport(repInfo, subReportInfo);
                break;
            case ReportHelper.ReportTypeEnum.LEGACY_PARETO:
                retVal = new LegacyParetoReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.LEGACY_PRST:
                retVal = new LegacyPRSTReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.LEGACY_PROGRESS:
                retVal = new LegacyProgressReport(repInfo);
                break;
            case ReportHelper.ReportTypeEnum.LEGACY_DISTRIBUTION:
                retVal = new LegacyDistributionReport(repInfo);
                break;
            case ReportTypeEnum.ENA:
            case ReportTypeEnum.ECRM:
            case ReportTypeEnum.AFD:
            case ReportTypeEnum.AFD_CONFIGURATION_MANAGER:
            case ReportTypeEnum.TBT:
            case ReportTypeEnum.STATISTICS:
            case ReportTypeEnum.BUBBLE:
                retVal = new QueryReport(repInfo);
                break;
            default:
                throw new ApplicationException("Unsupported report type - " + repInfo.ReportTypeId.ToString());
        }

        return retVal;
    }

    public static void ApplyTemplateToChart(Dundas.Charting.WebControl.Chart chart, string templateData)
    {
        if (!string.IsNullOrEmpty(templateData))
        {
            MemoryStream stream = new MemoryStream();
            byte[] data = Encoding.UTF8.GetBytes(templateData);
            if (data != null && data.Length > 0)
            {
                stream.Write(data, 0, data.Length);
                stream.Flush();
                stream.Seek(0, SeekOrigin.Begin);

                chart.Serializer.IgnoreUnknownXmlAttributes = false;
                chart.Serializer.Load(stream);
            }
        }
    }

    public static void BuildGeneralReport(bool isLegacy, OlapManager mgr, ReportInfo repInfo, bool firstBuild)
    {
        if (firstBuild)
        {
            switch (repInfo.PrebuildType)
            {
                case PrebuiltReportsEnum.SUMMARY_TRANSACTION_VOLUME:
                case PrebuiltReportsEnum.DAILY_TRANSACTION_VOLUME:
                    mgr.GetAxisDescriptor(DataAxisType.Series).Dimensions.Clear();
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Series, ReportHelper.BuildDimDescriptor(mgr, "[Measures].[MeasuresLevel]", "[Measures].[Transaction Count]"));
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Categorical, ReportHelper.BuildDimDescriptorDays(mgr, repInfo.StartDate, repInfo.EndDate));
                    break;

                case PrebuiltReportsEnum.SUMMARY_MEDIA_VOLUME:
                case PrebuiltReportsEnum.DAILY_MEDIA_VOLUME:
                    mgr.GetAxisDescriptor(DataAxisType.Series).Dimensions.Clear();
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Series, ReportHelper.BuildDimDescriptor(mgr, "[Measures].[MeasuresLevel]", "[Measures].[Media Count]"));
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Categorical, ReportHelper.BuildDimDescriptorDays(mgr, repInfo.StartDate, repInfo.EndDate));
                    break;

                case PrebuiltReportsEnum.SUMMARY_OBSERVATION_VOLUME:
                case PrebuiltReportsEnum.DAILY_OBSERVATION_VOLUME:
                    mgr.GetAxisDescriptor(DataAxisType.Series).Dimensions.Clear();
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Series, ReportHelper.BuildDimDescriptor(mgr, "[Measures].[MeasuresLevel]", "[Measures].[Observation Count]"));
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Categorical, ReportHelper.BuildDimDescriptorDays(mgr, repInfo.StartDate, repInfo.EndDate));
                    break;

                default:
                    //mgr.AddAxisDimensionDescriptor(FindDataAxisType(mgr.CurrentOlapReport, "Time", DataAxisType.Categorical),
                    //        ReportHelper.BuildDimDescriptorWeeks(mgr, repInfo.StartDate, repInfo.EndDate));
                    mgr.AddAxisDimensionDescriptor(DataAxisType.Categorical, ReportHelper.BuildDimDescriptorWeeks(mgr, repInfo.StartDate, repInfo.EndDate));
                    break;
            }
        }

        mgr.AddAxisDimensionDescriptor(FindDataAxisType(mgr.CurrentOlapReport, (isLegacy ? "Test Session" : "Session"), DataAxisType.Slicer),
                ReportHelper.BuildDimDescriptorStatusSession(isLegacy, mgr, repInfo.AttachedSessions));
    }

    public static DimensionDescriptor FindDescriptor(OlapReport report, string dimensionName)
    {
        DimensionDescriptor retVal = null;
        foreach (AxisDescriptor axisDesc in report.AxisDescriptors)
        {
            foreach (DimensionDescriptor dimDesc in axisDesc.Dimensions)
            {
                if (string.Compare(dimensionName, dimDesc.DimensionName, true) == 0 || string.Compare("[" + dimensionName + "]", dimDesc.DimensionName, true) == 0)
                {
                    retVal = dimDesc;
                    break;
                }
            }
        }
        return retVal;
    }

    private static DataAxisType FindDataAxisType(OlapReport report, string dimensionName, DataAxisType defaultAxisType)
    {
        DataAxisType retVal = defaultAxisType;
        foreach (DimensionDescriptor dimDesc in report.AxisDescriptorCategorical.Dimensions)
        {
            if (string.Compare(dimensionName, dimDesc.DimensionName, true) == 0 || string.Compare("[" + dimensionName + "]", dimDesc.DimensionName, true) == 0)
            {
                retVal = DataAxisType.Categorical;
                break;
            }
        }
        foreach (DimensionDescriptor dimDesc in report.AxisDescriptorSeries.Dimensions)
        {
            if (string.Compare(dimensionName, dimDesc.DimensionName, true) == 0 || string.Compare("[" + dimensionName + "]", dimDesc.DimensionName, true) == 0)
            {
                retVal = DataAxisType.Series;
                break;
            }
        }
        foreach (DimensionDescriptor dimDesc in report.AxisDescriptorSlicer.Dimensions)
        {
            if (string.Compare(dimensionName, dimDesc.DimensionName, true) == 0 || string.Compare("[" + dimensionName + "]", dimDesc.DimensionName, true) == 0)
            {
                retVal = DataAxisType.Slicer;
                break;
            }
        }
        return retVal;
    }

    private static DimensionDescriptor BuildDescriptor(CubeDataSchema schema, string uniqueName, List<string> memberNames)
    {
        DimensionDescriptor retVal = null;

        if (!string.IsNullOrEmpty(uniqueName))
        {
            Level level = schema.FindLevelByUniqueName(uniqueName);
            if (level != null)
            {
                retVal = new DimensionDescriptor(level.ParentHierarchy.ParentDimension.Name, level.UniqueName);
            }
            else
            {
                Hierarchy hier = schema.FindHierarchyByUniqueName(uniqueName);
                if (hier != null)
                {
                    level = hier.GetDefaultLevel();
                    retVal = new DimensionDescriptor(hier.ParentDimension.Name, level.UniqueName);

                }
                else
                {
                    Dimension dim = schema.Dimensions.FindByUniqueName(uniqueName);
                    if (dim != null)
                    {
                        level = dim.GetDefaultHierarchy().GetDefaultLevel();
                        retVal = new DimensionDescriptor(dim.Name, level.UniqueName);
                    }
                }
            }

            if (retVal != null && memberNames != null && memberNames.Count > 0)
            {
                buildMemberCollection(memberNames, level.Members, retVal.Members);
            }
        }

        return retVal;
    }

    private static void buildMemberCollection(List<string> memberNames, MemberCollection coll, DimensionMemberDescriptorCollection destMembers)
    {
        List<Member> addedMembers = new List<Member>();

        foreach (string curName in memberNames)
        {
            Member mem = coll.FindByUniqueName(curName);
            if (mem != null)
            {
                destMembers.Add(mem.Name, mem.UniqueName);
                addedMembers.Add(mem);

                if (mem.ParentMember != null)
                {
                    foreach (DimensionMemberDescriptor memDescript in destMembers)
                    {
                        if (string.Compare(memDescript.MemberUniqueName, mem.ParentMember.UniqueName) == 0)
                        {
                            destMembers.Remove(memDescript);
                            break;
                        }
                    }
                }
            }
        }

        foreach (Member addedMember in addedMembers)
            buildMemberCollection(memberNames, addedMember.ChildMembers, destMembers);
    }

    public static string GeneratePDF(ReportInfo repInfo, string renderingPageName, string layout = "landscape", string chartScale= "", string gridScale = "", bool splitPages = false, int subReportId = 0)
	{
		string previewUrl = null;
		string dir = HttpContext.Current.Server.MapPath(ConfigurationManager.AppSettings["TempPDFDirectory"]);
		string fileName = repInfo.ReportName + "_" + DateTime.Now.Ticks + ".pdf";

		//Check for a new client directory and create if needed
		if (!Directory.Exists(dir))
			Directory.CreateDirectory(dir);

		string[] urlParts = HttpContext.Current.Request.Url.ToString().Split(new string[] { "/" }, StringSplitOptions.None);
		for (int i = 0; i < urlParts.Length - 1; i++)
			previewUrl += urlParts[i] + "/";

		string pdfPage = string.Format("{0}{1}?r={2}&pdf=1&layout={3}&chart={4}&data={5}&splitData={6}", previewUrl, renderingPageName, repInfo.ReportId, layout, chartScale, gridScale, splitPages.ToString());

		if (subReportId > 0)
			pdfPage += "&s=" + subReportId;

		Doc curPDF = new Doc();
		
		if (layout.Equals("landscape"))
		{
			// apply a rotation transform
			double w = curPDF.MediaBox.Width;
			double h = curPDF.MediaBox.Height;
			double l = curPDF.MediaBox.Left;
			double b = curPDF.MediaBox.Bottom;
			curPDF.Transform.Rotate(90, l, b);
			curPDF.Transform.Translate(w, 0);

			// rotate our rectangle
			curPDF.Rect.Width = h;
			curPDF.Rect.Height = w;
		}

		curPDF.TopDown = true;
        //curPDF.Rect.Inset(50, 50);
        //Update 10/11/17 - Fitting PDF of reports to entire page
        curPDF.Rect.Inset(0, 50);
        curPDF.HtmlOptions.Timeout = 1200000; //20 minutes
		curPDF.Page = curPDF.AddPage();
		int id = curPDF.AddImageUrl(pdfPage, true, 0, true);

		while (true)
		{
			//curPDF.FrameRect(); // add a black border
			if (!curPDF.Chainable(id))
				break;
			curPDF.Page = curPDF.AddPage();
			id = curPDF.AddImageToChain(id);
		}

		for (int i = 1; i <= curPDF.PageCount; i++)
		{
			curPDF.PageNumber = i;
			curPDF.Flatten();
		}

		if (layout.Equals("landscape"))
		{
			// adjust the default rotation and save
			int theID = curPDF.GetInfoInt(curPDF.Root, "Pages");
			curPDF.SetInfo(theID, "/Rotate", "90");
		}

		string regexSearch = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
		Regex r = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));
		string cleanFileName = r.Replace(fileName, "");

		curPDF.Save(dir + "\\" + cleanFileName);
		curPDF.Clear(); 
		curPDF.Dispose();

		return (dir + "\\" + cleanFileName);
	}
}
