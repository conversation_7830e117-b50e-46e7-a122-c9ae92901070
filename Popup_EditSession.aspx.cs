using System;
using System.Data;
using System.Web.UI;

public partial class Popup_EditSession : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string SessionId
	{
		get { return (string)this.ViewState["s"]; }
		set { this.ViewState["s"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			statusList.DataSource = Utility.GetSessionStatusList();
			statusList.DataBind();

			testTypeList.DataSource = Utility.GetTestTypeList();
			testTypeList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SESSION_ID_KEY]))
			{
				this.SessionId = Request.Params[DieboldConstants.SESSION_ID_KEY];
                sessionFormat.Enabled = false;

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetSession", this.SessionId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.sessionNameField.Text = DataFormatter.getString(row, "SessionName");
					this.statusList.SelectedValue = DataFormatter.getInt32(row, "SessionStatusId").ToString();
                    this.testTypeList.SelectedValue = DataFormatter.getInt32(row, "TestTypeID").ToString();
                    this.targetMediaType.SelectedValue = DataFormatter.getString(row, "TargetMediaType");

                    if (DataFormatter.getBool(row, "IsManualVolume"))
                        this.sessionFormat.SelectedValue = "manual";

                    if (DataFormatter.getBool(row, "IsObservationOnly"))
                        this.sessionFormat.SelectedValue = "observations";

                    if (DataFormatter.getInt32(row, "TargetMedia") > 0)
						this.targetMediaCount.Text = DataFormatter.getInt32(row, "TargetMedia").ToString();

                    this.createdBy.Text = DataFormatter.getString(row, "CreatedBy");
                    if (DataFormatter.getDateTime(row, "CreatedDate") != DateTime.MinValue)
                        this.createdDate.Text = DataFormatter.getDateTime(row, "CreatedDate").ToShortDateString();

                    this.lastModifiedBy.Text = DataFormatter.getString(row, "LastModifiedBy");
                    if (DataFormatter.getDateTime(row, "LastModifiedDate") != DateTime.MinValue)
                        this.lastModifiedDate.Text = DataFormatter.getDateTime(row, "LastModifiedDate").ToShortDateString();
                }
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

        bool isManualVolume = false;
        bool isObservationOnly = false;
        if (this.sessionFormat.SelectedValue == "manual") {
            isManualVolume = true;
        }
        if (this.sessionFormat.SelectedValue == "observations") {
            isObservationOnly = true;
        }

        if (string.IsNullOrEmpty(this.SessionId)) {
            result = (int)SqlHelper.ExecuteScalar("RPT_InsertSession", this.sessionNameField.Text.Trim(),
                this.statusList.SelectedValue, isManualVolume, isObservationOnly, this.testTypeList.SelectedValue,
                this.targetMediaCount.Text, this.targetMediaType.SelectedValue, Utility.GetUserName());
        }
        else {
            SqlHelper.ExecuteNonQuery("RPT_UpdateSession", this.SessionId, this.sessionNameField.Text.Trim(), this.statusList.SelectedValue,
                this.testTypeList.SelectedValue, this.targetMediaCount.Text, this.targetMediaType.SelectedValue, Utility.GetUserName());

            if (string.Compare(this.statusList.SelectedValue, "1") == 0) {
                //active session - move all archived data back into main tables
                SqlHelper.ExecuteNonQuery("ARC_RestoreTranHeaderBySession", this.SessionId);
            } else if (string.Compare(this.statusList.SelectedValue, "2") == 0) {
                //inactive session - move all data to archived tables
                SqlHelper.ExecuteNonQuery("ARC_ArchiveTranHeaderBySession", this.SessionId); 
            }
        }

		if (result == 0)
			this.ErrorLabel.Visible = true;
		else
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditSessions.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditSessions.aspx';CloseRadWindow(); ", true);
	}
}
