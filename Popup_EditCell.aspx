<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditCell.aspx.cs" Inherits="Popup_EditCell" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Cell</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Add/Edit Cell</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="14" width="100%">
						<tr>
							<td class="rowHeading" style="width:150px;">Cell Name</td>
							<td>
								<asp:textbox runat="server" width="230" id="cellNameField"></asp:textbox>
								<asp:requiredfieldvalidator id="val1" runat="server" errormessage="* Required" controltovalidate="cellNameField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Test Location</td>
							<td>
								<asp:dropdownlist id="testLocationList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Cell Status</td>
							<td>
								<asp:dropdownlist id="statusList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist>
							</td>
						</tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Last Modified By</td>
							<td>
								<asp:textbox runat="server" width="230" id="lastModifiedBy" enabled="false"></asp:textbox>
							</td>
                        </tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Last Modified Date</td>
							<td>
								<asp:textbox runat="server" width="230" id="lastModifiedDate" enabled="false"></asp:textbox>
							</td>
                        </tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Created By</td>
							<td>
								<asp:textbox runat="server" width="230" id="createdBy" enabled="false"></asp:textbox>
							</td>
                        </tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Created Date</td>
							<td>
								<asp:textbox runat="server" width="230" id="createdDate" enabled="false"></asp:textbox>
							</td>
                        </tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />A cell already exists with the specified name.<br /></div>
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

