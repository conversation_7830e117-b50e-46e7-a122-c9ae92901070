using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class SaveReport : System.Web.UI.Page
{
    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

    public string SelectedFolder
    {
        get { return (string)this.ViewState["s"]; }
        set { this.ViewState["s"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
			hidUserName.Value = Utility.GetUserName();

            //Presence of query string param indicates performing a copy.
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
            {
                this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));

                if (this.RepInfo == null)
                    throw new ApplicationException("Unable to load the requested report for copying.");

                //Clear reportId from Copied Report.
                this.RepInfo.ReportId = 0;

				//Truncate report names that will break due to length
                if (this.RepInfo.ReportName.Length < 100)
                    reportNameField.Text = "Copy_" + this.RepInfo.ReportName;
                else
                    reportNameField.Text = "Copy_" + this.RepInfo.ReportName.Substring(0, 94);
            }
            else
            {
                this.RepInfo = Utility.GetReportInfoFromTransfer();

                if (this.RepInfo == null)
                    throw new ApplicationException("Unable to load the report data.");

                reportNameField.Text = this.RepInfo.ReportName;
            }

			if (this.RepInfo.PromptSessions)
			{
				this.promptCheck.Checked = true;
				this.promptCheck.Enabled = true;
			}

			if (this.RepInfo.FolderId != 0)
			{
				libraryRadio.Checked = true;
				this.promptCheck.Enabled = true;
                this.SelectedFolder = this.RepInfo.FolderId.ToString();
			}
			ToggleLibrary(null, null);
            LoadSessions();

			reportNameField.Focus();
        }
        LoadLibraryData();
    }

    protected void CheckReportName(object sender, ServerValidateEventArgs e)
    {
		e.IsValid = false;

		try
		{
			int collisionCount = 0;

			if (libraryRadio.Checked)
			{
				if (!string.IsNullOrEmpty(myLibraryList.SelectedValue))
					collisionCount = (int)SqlHelper.ExecuteScalar("RPT_CheckReportNameCollision", this.RepInfo.ReportId, reportNameField.Text, Convert.ToInt32(myLibraryList.SelectedValue), hidUserName.Value);
				else if (!string.IsNullOrEmpty(sharedLibraryList.SelectedValue))
					collisionCount = (int)SqlHelper.ExecuteNonQuery("RPT_CheckReportNameCollision", this.RepInfo.ReportId, reportNameField.Text, Convert.ToInt32(sharedLibraryList.SelectedValue), null);
			}
			else
			{
				collisionCount = (int)SqlHelper.ExecuteNonQuery("RPT_CheckReportNameCollision", this.RepInfo.ReportId, reportNameField.Text, 0, null);
			}

			if (collisionCount <= 0)
				e.IsValid = true;
		}
		catch
		{
			e.IsValid = false;
		}
    }

    protected void SaveButton_Click(object sender, EventArgs e)
    {
        reqFolderLabel.Visible = false;

		Page.Validate();
        if (Page.IsValid)
        {
            this.RepInfo.ReportName = this.reportNameField.Text.Trim();
            this.RepInfo.FolderId = 0;
            if (libraryRadio.Checked)
            {
                if (!string.IsNullOrEmpty(myLibraryList.SelectedValue))
                    this.RepInfo.FolderId = Convert.ToInt32(myLibraryList.SelectedValue);
                else if (!string.IsNullOrEmpty(sharedLibraryList.SelectedValue))
                    this.RepInfo.FolderId = Convert.ToInt32(sharedLibraryList.SelectedValue);

                if (this.RepInfo.FolderId <= 0)
                {
                    reqFolderLabel.Visible = true;
                    return;
                }
            }

            this.RepInfo.PromptSessions = (libraryRadio.Checked && promptCheck.Checked);

            if (this.RepInfo.PromptSessions && this.RepInfo.SubReportsBySession)
                this.RepInfo.SubReportsBySession = false;

            this.RepInfo.ReportId = Utility.SaveReportInfo(this.RepInfo);

            RadAjaxManager.GetCurrent(this.Page).ResponseScripts.Add("closeSavePopup();");
        }
    }

    private void LoadSessions()
    {
        foreach (SessionInfo session in this.RepInfo.AttachedSessions)
        {
            sessionListLabel.Text += session.SessionName + "<br />";
        }

		if (string.IsNullOrEmpty(sessionListLabel.Text))
		{
			sessionsRadio.Enabled = false;
			libraryRadio.Checked = true;
			sessionListLabel.Text = "<span style=\"color:#999999;\">No sessions were found for the report.</span>";
		}
		else if (!Utility.IsUserAdmin())
		{
			sessionsRadio.Enabled = false;
			libraryRadio.Checked = true;
			ToggleLibrary(null, null);
			sessionListLabel.Text = "<span style=\"color:#999999;\">" + sessionListLabel.Text + "</span>";
		}
    }

    private void LoadLibraryData()
    {
        if (!string.IsNullOrEmpty(myLibraryList.SelectedValue))
            this.SelectedFolder = myLibraryList.SelectedValue;
        else if (!string.IsNullOrEmpty(sharedLibraryList.SelectedValue))
            this.SelectedFolder = sharedLibraryList.SelectedValue;

        myLibraryList.Items.Clear();
        sharedLibraryList.Items.Clear();

        DataSet ds = LibrarySource.GetMyLibrary(false, hidUserName.Value);
        if (ds.Tables[0] != null)
        {
            foreach (DataRow row in ds.Tables[0].Rows)
            {
                myLibraryList.Items.Add(new ListItem(DataFormatter.Format(row, "FolderName"), DataFormatter.Format(row, "FolderId")));
            }

            if (myLibraryList.Items.FindByValue(this.SelectedFolder) != null)
                myLibraryList.SelectedValue = this.SelectedFolder;
        }

        ds = LibrarySource.GetSharedLibrary(false);
        if (ds.Tables[0] != null)
        {
            foreach (DataRow row in ds.Tables[0].Rows)
            {
                sharedLibraryList.Items.Add(new ListItem(DataFormatter.Format(row, "FolderName"), DataFormatter.Format(row, "FolderId")));
            }

            if (sharedLibraryList.Items.FindByValue(this.SelectedFolder) != null)
                sharedLibraryList.SelectedValue = this.SelectedFolder;
        }
    }

	protected void ToggleLibrary(object sender, EventArgs e)
	{
        if (!sessionsRadio.Enabled)
            sessionsRadio.Checked = false;

		if (libraryRadio.Checked)
		{
			this.myLibraryList.Enabled = true;
			this.sharedLibraryList.Enabled = true;
			this.promptCheck.Enabled = true;
		}
		else
		{
			this.myLibraryList.Enabled = false;
			this.sharedLibraryList.Enabled = false;
			this.promptCheck.Enabled = false;
			this.promptCheck.Checked = false;
		}
	}

    protected void ClearSharedSelection(object sender, EventArgs e)
    {
        sharedLibraryList.SelectedIndex = -1;
    }

    protected void ClearMySelection(object sender, EventArgs e)
    {
        myLibraryList.SelectedIndex = -1;
    }
}
