﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="QueryReports.aspx.cs" Inherits="QueryReports" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<telerik:radwindowmanager runat="server" id="RadWindowManager" height="630" width="620" modal="true" title="Diebold Reporting" 
	behaviors="Close,Reload" reloadonshow="true" visiblestatusbar="false" destroyonclose="true"></telerik:radwindowmanager>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Query Reports</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				
				<table border="0" cellpadding="0" cellspacing="0" style="padding-left:10px;">
					<tr>
						<td style="width:185px;"><div class="goButton" style="margin:10px 0px;"><a href="EditQueryReport.aspx">New Query Report</a></div></td>
					</tr>
				</table>
				<telerik:radgrid id="RadGrid1" allowmultirowselection="false" 
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="repeaterItem" />
					<alternatingitemstyle cssclass="repeaterItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings enablerowhoverstyle="true" resizing-allowcolumnresize="true">
						<selecting allowrowselect="true" />
						<clientevents onrowclick="RowClick" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="ReportTypeId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="Report Name" datafield="ReportName" sortexpression="ReportName" uniquename="ReportName">
								<itemtemplate>
									<div><%# DataFormatter.Format(Container.DataItem, "ReportTypeName")%></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Stored Procedure Name" datafield="StoredProcName" sortexpression="StoredProcName" uniquename="StoredProcName">
								<itemtemplate>
									<div><%# DataFormatter.Format(Container.DataItem, "StoredProcName")%></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Filter Level 3 Notes" sortexpression="AllowFilterLevel3Notes" uniquename="AllowFilterLevel3Notes">
								<headerstyle width="15%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "AllowFilterLevel3Notes", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : "" %>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Filter Field Type" sortexpression="AllowFilterFieldType" uniquename="AllowFilterFieldType">
								<headerstyle width="15%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "AllowFilterFieldType", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><a href="<%# string.Format("EditQueryReport.aspx?{0}={1}&page={2}", DieboldConstants.QUERY_REPORT_ID_KEY, DataFormatter.Format(Container.DataItem, "ReportTypeId", ""), RadGrid1.CurrentPageIndex) %>">Edit</a></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>
			</div>
		</td>
	</tr>
</table>
</asp:Content>