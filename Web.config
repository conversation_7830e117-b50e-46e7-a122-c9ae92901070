<?xml version="1.0"?>
<configuration>
  <appSettings>
    <add key="RootUploadPath" value="~/TxFiles/"/>
    <add key="DashboardImagesPath" value="images/dashboardImages/"/>
    <add key="NotificationEmailFromAddress" value="<EMAIL>"/>
    <add key="AdminFeatures_WindowsRoleName" value="Diebold_Master\ReliabilityDatabaseADM"/>
    <add key="Access_WindowsRoleName" value="ALL"/>
    <add key="ChartHttpHandler" value="Storage=memory;Timeout=180;Url=~/temp/;"/>
    <add key="TempPDFDirectory" value="~/pdf"/>
    <add key="ValidationSettings:UnobtrusiveValidationMode" value="None"/>
    <add key="EnableSSLRedirect" value="false"/>
    <add key="JiraRestApi_URL" value="https://jerry.wincor-nixdorf.com"/>
    <add key="JiraRestApi_Username" value="<EMAIL>"/>
    <add key="JiraRestApi_Key" value="OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"/>
  </appSettings>
  <connectionStrings>
    <add name="TransactionalDatabase" connectionString="Server=USNCENG02;Trusted_Connection=false;uid=ReportingWebApp;pwd=********;database=Reliability;Persist Security Info=True"/>
    <add name="ReportingAnalysis" connectionString="Data Source=USNCENG02; Catalog=ReportingAnalysis2012;"/>
    <add name="QueueServicePath" connectionString="tcp://USNCENG03.ad.diebold.com:8090"/>
  </connectionStrings>
  <system.net>
    <mailSettings>
      <smtp deliveryMethod="network">
        <network host="mailrelay.dieboldnixdorf.com" port="25" defaultCredentials="true"/>
      </smtp>
    </mailSettings>
  </system.net>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <httpRuntime targetFramework="4.5" maxRequestLength="102400" executionTimeout="3600" requestValidationMode="2.0"/>
    <sessionState timeout="120"/>
    <authentication mode="Windows"/>
    <authorization>
      <deny users="?"/>
    </authorization>
    <identity impersonate="true"/>
    <pages validateRequest="false" enableEventValidation="false">
      <controls>
        <add namespace="AjaxControlToolkit" assembly="AjaxControlToolkit" tagPrefix="ajaxToolkit"/>
      </controls>
    </pages>
    <customErrors mode="Off"/>
    <compilation debug="true" targetFramework="4.8">
      <assemblies>
        <add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
        <add assembly="Microsoft.VisualBasic, Version=10.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="Microsoft.AnalysisServices.AdomdClient, Version=9.0.242.0, Culture=neutral, PublicKeyToken=89845DCD8080CC91"/>
        <add assembly="ABCpdf, Version=9.1.2.5, Culture=neutral, PublicKeyToken=A7A0B3F5184F2169"/>
      </assemblies>
    </compilation>
    <httpHandlers>
      <remove path="*.asmx" verb="*"/>
      <add path="OlapChartAxd.axd" verb="*" type="Dundas.Olap.WebUIControls.ChartHttpHandler" validate="false"/>
      <add path="ChartAxd.axd" verb="*" type="Dundas.Charting.WebControl.ChartHttpHandler" validate="false"/>
      <add path="TxFile.*" verb="*" type="TxFileHandler"/>
      <add path="ScheduledReportFile.*" verb="*" type="ScheduledReportFileHandler"/>
    </httpHandlers>
    <siteMap enabled="true">
      <providers>
        <add name="NavXmlSiteMapProvider" type="System.Web.XmlSiteMapProvider" siteMapFile="Nav.sitemap" securityTrimmingEnabled="false"/>
      </providers>
    </siteMap>
    <healthMonitoring enabled="true" heartbeatInterval="900">
      <providers>
        <add name="CriticalMailEventProvider" type="System.Web.Management.SimpleMailWebEventProvider" from="<EMAIL>" to="<EMAIL>" bodyHeader="Warning!" bodyFooter="Please investigate ASAP." subjectPrefix="Diebold Reporting Error." buffer="true" bufferMode="Critical Notification" maxEventLength="4096" maxMessagesPerNotification="5"/>
      </providers>
      <rules>
        <add name="Request Processing Errors" eventName="Request Processing Errors" provider="CriticalMailEventProvider" profile="Default"/>
        <add name="Infrastructure Errors" eventName="Infrastructure Errors" provider="CriticalMailEventProvider" profile="Default"/>
      </rules>
    </healthMonitoring>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="83886080"/>
      </requestFiltering>
    </security>
    <validation validateIntegratedModeConfiguration="false"/>
    <httpRedirect enabled="false" destination="https://reliability.diebold.com"/>
  </system.webServer>
  <location path="PrintReport.aspx">
    <system.web>
      <authorization>
        <allow users="*"/>
      </authorization>
    </system.web>
  </location>
  <location path="PrintShiftReport.aspx">
    <system.web>
      <authorization>
        <allow users="*"/>
      </authorization>
    </system.web>
  </location>
  <location path="PrintQueryReport.aspx">
    <system.web>
      <authorization>
        <allow users="*"/>
      </authorization>
    </system.web>
  </location>
  <location path="PrintSensorReport.aspx">
    <system.web>
      <authorization>
        <allow users="*"/>
      </authorization>
    </system.web>
  </location>
  <location path="RunQuery.aspx">
    <system.web>
      <authorization>
        <allow users="*"/>
      </authorization>
    </system.web>
  </location>
</configuration>