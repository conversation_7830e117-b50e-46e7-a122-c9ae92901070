﻿using System;
using System.Data;
using System.Web.UI;

public partial class EditQueryReport : System.Web.UI.Page {
    public int QueryReportId
    {
        get { if (this.ViewState["qr"] != null) return (int)this.ViewState["qr"]; else return 0; }
        set { this.ViewState["qr"] = value; }
    }
    
    protected void Page_Load(object sender, EventArgs e) {
        deleteBtn.Attributes.Add("onclick", "return confirm('Are you sure you wish to delete this query report?');");

        if (!Page.IsPostBack) {
            if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.QUERY_REPORT_ID_KEY]))
                QueryReportId = Convert.ToInt32(Request.QueryString[DieboldConstants.QUERY_REPORT_ID_KEY]);

            if (QueryReportId > 0) {
                DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadQueryReport", this.QueryReportId);
                foreach (DataRow row in ds.Tables[0].Rows) //only loads one row.
                {
                    reportTypeName.Text = DataFormatter.getString(row, "ReportTypeName");
                    storedProcName.Text = DataFormatter.getString(row, "StoredProcName");
                    allowFilterLevel3Notes.Checked = DataFormatter.getBool(row, "AllowFilterLevel3Notes");
                    allowFilterFieldType.Checked = DataFormatter.getBool(row, "AllowFilterFieldType");
                }

                deleteBtn.Visible = true;
            }
        }
    }

    protected void SaveButton_Click(object sender, EventArgs e) {
        this.QueryReportId = Convert.ToInt32(SqlHelper.ExecuteScalar("RPT_UpdateQueryReport",
            this.QueryReportId, this.reportTypeName.Text, this.storedProcName.Text, this.allowFilterLevel3Notes.Checked, this.allowFilterFieldType.Checked));

        Response.Redirect("QueryReports.aspx");
    }

    protected void DeleteButton_Click(object sender, EventArgs e) {
        SqlHelper.ExecuteNonQuery("RPT_DeleteQueryReport", this.QueryReportId);

        Response.Redirect("QueryReports.aspx");
    }
}
