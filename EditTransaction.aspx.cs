using System;
using System.Data;
using System.Collections.Generic;
using System.Web.UI;
using QueueServiceClient;

public partial class EditTransaction : System.Web.UI.Page
{
	public Int64 TranId
	{
		get { if (this.ViewState["t"] != null) return (Int64)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}
    public Int64 ObservationId
    {
        get { if (this.ViewState["o"] != null) return (Int64)this.ViewState["o"]; else return 0; }
        set { this.ViewState["o"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			NewObservationTitle.Visible = Utility.IsUserAdmin();
			NewObservationRow.Visible = NewObservationTitle.Visible;

			if (!string.IsNullOrEmpty(Request.Params["t"]))
			{
				this.TranId = Convert.ToInt64(Request.Params["t"]);
				this.SettingsLink.HRef = string.Format("settings.aspx?t={0}", this.TranId);
				this.StatsLink.HRef = string.Format("statistics.aspx?t={0}", this.TranId);
				LoadTransaction();
				UploadFileBtn.Attributes.Add("onclick", String.Format("window.radopen('Files.aspx?t={0}', null);return false;", this.TranId));
			}
			else
			{
				throw new ApplicationException("The Transaction ID is missing.");
			}

			LoadFileData();

            addObservationRow.Visible = Utility.IsUserAdmin();
        }
    }

    private void LoadTransaction() {
        DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadTransaction", this.TranId);

        foreach (DataRow row in ds.Tables[0].Rows) {
            if (DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "") != null) {
                this.dateField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "")));
                this.timeField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "hh:mm:ss tt", "")));
            }

            if (DataFormatter.getBool(row, "IsManualVolumeSession")) {
                this.cumTranCountLabel.Text = DataFormatter.getInt32(row, "CumTranCount").ToString();
                this.cumMediaCountLabel.Text = DataFormatter.getInt32(row, "CumMediaCount").ToString();
                this.CumTranCountRow.Visible = true;
                this.CumMediaCountRow.Visible = true;
                this.TransactionNoRow.Visible = false;
            }

            this.PicturesButton.InnerText = "View Pictures (" + DataFormatter.getInt32(row, "PictureCount") + ")";

            if (DataFormatter.getInt32(row, "PictureCount") == 0)
                PicturesButton.Attributes.Add("onclick", String.Format("window.radopen('files.aspx?t={0}', null);return false;", this.TranId));
            else
                PicturesButton.Attributes.Add("onclick", String.Format("window.radopen('pictures.aspx?t={0}', null);return false;", this.TranId));

            this.transactionNumberLabel.Text = DataFormatter.getInt32(row, "RDToolTranId").ToString();
            this.hidCellId.Value = DataFormatter.getInt32(row, "CellId").ToString();
            this.cellLabel.Text = DataFormatter.getString(row, "CellName");
            this.hidSessionId.Value = DataFormatter.getInt32(row, "SessionId").ToString();
            this.sessionLabel.Text = DataFormatter.getString(row, "SessionName");
            this.notesField.Text = DataFormatter.getString(row, "Notes");
            this.deviceListLabel.Text += DataFormatter.getString(row, "DeviceFullName") + "<br />";
        }

        //look up observation fields for Link and Issue Number because Peter wants everyone to be able to edit these two fields
        DataSet obsDS = SqlHelper.ExecuteDataset("RPT_CheckForObservationInfoByTranId", this.TranId);
        if (obsDS.Tables != null && obsDS.Tables[0].Rows.Count > 0) {
            foreach (DataRow row in obsDS.Tables[0].Rows) {
                this.ObservationId = DataFormatter.getInt64(row, "ObservationId");

                ///if the user is an admin, and there is already an observation, redirect over to edit observation page to edit more fields there
                if (Utility.IsUserAdmin()) {
                    Response.Redirect(string.Format("EditObservation.aspx?o={0}", this.ObservationId));
                }

                if (this.ObservationId > 0) {
                    issueNoRow.Visible = true;
                    linkRow.Visible = true;
                    this.issueNumberField.Text = DataFormatter.getString(row, "SCRNumber");
                    this.linkField.Text = DataFormatter.getString(row, "Link");
                }
            }
        }
    }

    protected void SaveButton_Click(object sender, EventArgs e) {
        TimeSpan time = new TimeSpan(((DateTime)timeField.SelectedDate).Hour, ((DateTime)timeField.SelectedDate).Minute, ((DateTime)timeField.SelectedDate).Second);
        DateTime transactionDate = ((DateTime)dateField.SelectedDate).Add(time);

        int success = SqlHelper.ExecuteNonQuery("RPT_UpdateTransaction", this.TranId, transactionDate, this.notesField.Text);

        if (this.ObservationId > 0) {
            //Update Observation Issue No field, and Link
            SqlHelper.ExecuteNonQuery("RPT_UpdateObservationIssueInfo", this.ObservationId, this.issueNumberField.Text, this.linkField.Text);
        }
        Response.Redirect("Search.aspx");
    }

    protected void CancelButton_Click(object sender, EventArgs e) {
        Response.Redirect("Search.aspx");
    }

    protected void DownloadAllBtn_Click(object sender, EventArgs e)
	{
		List<Int64> tranIdList = new List<Int64>();
		List<Int64> observationIdList = new List<Int64>();
		List<string> filePatternList = new List<string>();
		List<string> deviceTypeList = new List<string>();
		List<string> serialNumberList = new List<string>();
		List<string> cellNameList = new List<string>();
		List<string> sessionNameList = new List<string>();
		List<DateTime> tranDateList = new List<DateTime>();

		List<DataFile> dataFiles = LoadFileData();
		foreach (DataFile dataFile in dataFiles)
		{
			if (!tranIdList.Contains(dataFile.TranId))
			{
				tranIdList.Add(dataFile.TranId);
				observationIdList.Add(dataFile.ObservationId);
				deviceTypeList.Add(dataFile.DeviceTypeName);
				serialNumberList.Add(dataFile.SerialNumber);
				cellNameList.Add(this.cellLabel.Text);
				sessionNameList.Add(this.sessionLabel.Text);
				tranDateList.Add(dataFile.TranDate);
				filePatternList.Add("CELL=" + this.hidCellId.Value.ToString() + "_SESSION=" + this.hidSessionId.Value.ToString() + "_TRX=" + this.transactionNumberLabel.Text);
			}
		}

		byte[] result = TxFiles.DownloadZipCollection(false, filePatternList, tranIdList, observationIdList, cellNameList, sessionNameList, tranDateList, deviceTypeList, serialNumberList);
		if (result != null && result.Length > 0)
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = "application/octet-stream";
			Response.AppendHeader("content-disposition", "attachment; filename=ReliabilityFiles_" + this.TranId.ToString() + "_" + DateTime.Now.Day + DateTime.Now.Month + DateTime.Now.Year + DateTime.Now.Hour + DateTime.Now.Minute + DateTime.Now.Second + ".zip;");
			Response.BinaryWrite(result);
			Response.End();
		}
	}

	protected void NewObservation_Click(object sender, EventArgs e)
	{
        Response.Redirect(string.Format("EditObservation.aspx?t={0}", this.TranId));
	}

	private List<DataFile> LoadFileData()
	{
		List<DataFile> engineeringFiles = new List<DataFile>();
		List<DataFile> xmlFiles = new List<DataFile>();
		
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTransactionDevices", 0, this.TranId);
		if (ds.Tables[0] != null)
		{
			for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
			{
				DataRow row = ds.Tables[0].Rows[i];
				string deviceTypeName = DataFormatter.getString(row, "DeviceTypeName");
				string serialNumber = DataFormatter.getString(row, "SerialNumber");
				DateTime tranDate = DataFormatter.getDateTime(row, "TranDate");

				string filePattern = "CELL=" + DataFormatter.getInt32(row, "CellId").ToString() + "_SESSION=" + DataFormatter.getInt32(row, "SessionId").ToString() + "_TRX=" + DataFormatter.getInt32(row, "RDToolTranId").ToString();

				try
				{
					List<DataFile> tempEngList = TxFiles.GetTxFileList(false, filePattern, this.TranId, 0, deviceTypeName, serialNumber, tranDate);
					List<DataFile> tempXMLList = TxFiles.GetTxFileList(true, filePattern, this.TranId, 0, deviceTypeName, serialNumber, tranDate);

                    if (tempEngList != null)
                    {
                        foreach (DataFile curFile in tempEngList)
                        {
                            bool isDup = false;
                            foreach (DataFile tmpFile in engineeringFiles)
                            {
                                if (string.Compare(tmpFile.FileName, curFile.FileName) == 0)
                                {
                                    isDup = true;
                                    break;
                                }
                            }
                            if (!isDup)
                                engineeringFiles.Add(curFile);
                        }
                    }

                    if (tempXMLList != null)
                    {
                        foreach (DataFile curFile in tempXMLList)
                        {
                            bool isDup = false;
                            foreach (DataFile tmpFile in xmlFiles)
                            {
                                if (string.Compare(tmpFile.FileName, curFile.FileName) == 0)
                                {
                                    isDup = true;
                                    break;
                                }
                            }
                            if (!isDup)
                                xmlFiles.Add(curFile);
                        }
                    }
				}
				catch
				{
					//ignore errors, can occur when the Queue Service is offline.
				}
			}

			engRepeater.DataSource = engineeringFiles;
			engRepeater.DataBind();

			xmlRepeater.DataSource = xmlFiles;
			xmlRepeater.DataBind();

			if (engRepeater.Items.Count > 0)
			{
				NoFilesLabel.Visible = false;
				downloadAllRow.Visible = true;
			}
			else
			{
				NoFilesLabel.Visible = true;
				downloadAllRow.Visible = false;
			}

			if (xmlRepeater.Items.Count > 0)
				NoXMLLabel.Visible = false;
			else
				NoXMLLabel.Visible = true;
		}

		return engineeringFiles;
	}
}
