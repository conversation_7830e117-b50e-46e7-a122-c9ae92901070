<%@ Page Language="C#" AutoEventWireup="true" CodeFile="PrintShiftReport.aspx.cs" Inherits="PrintShiftReport" %>
<%@ Import Namespace="Microsoft.AnalysisServices.AdomdClient" %>
	
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head runat="server">
    <title>Diebold Online Reporting</title>
    <link href="style.css" rel="stylesheet" type="text/css" />
</head>
<body style="margin: 0px 0px 0px 0px;">
    <form id="form1" runat="server">
     
		<script language="javascript" type="text/javascript">
			function printMyPage()
			{
				window.print();
			}
			
			var singlePageWidth = 612;
			function changeLayout()
			{
				var cntrl = document.getElementById('layoutList');
				var outerZoom = document.getElementById('OuterZoom');
				if (cntrl)
				{
					var layoutSelection = cntrl.options[cntrl.selectedIndex].value;
					
					//8.5x11 = 612 width x 792 height in points
					//8.5x11 = 816 width x 1056 height in pixels
					//1.5 inches of printer margin = 108 points
					if (layoutSelection == 'landscape')
					{
						singlePageWidth = 792;
						outerZoom.style.width = (792 - 108) + 'pt';
					}
					else
					{
						singlePageWidth = 612;
						outerZoom.style.width = (612 - 108) + 'pt';
					}
				}
				
				changeDataZoom();
			}
			
			function changeDataZoom()
			{
				var dataZoom = document.getElementById('DataZoomDiv');
				var cntrl = document.getElementById('dataList');
				if (cntrl)
				{
					dataZoom.style.zoom = '1';
					var printArea = document.body.createTextRange(); 
					printArea.moveToElementText(dataZoom);
					
					var newSelectedZoom = cntrl.options[cntrl.selectedIndex].value;	
					if (newSelectedZoom == 'hide')
					{	
						dataZoom.style.display = "none";
					}
					else if (newSelectedZoom == 'fit')
					{	
						//add 3/4 inch margin to each side of the page (IE default margin) = 108 points
						dataZoom.style.display = "";
						dataZoom.style.zoom = (singlePageWidth - 108) / (printArea.boundingWidth * .75);
					}
					else
					{ 	
						dataZoom.style.display = "";
						dataZoom.style.zoom = newSelectedZoom;							
					}
				}
			}
			
		</script>
		<asp:scriptmanager id="Scriptmanager1" runat="server"></asp:scriptmanager>
		<div class="body" style="vertical-align:top;">
			<asp:panel id="PrintControlsPanel" runat="server">
				<div style="padding-left:15px; padding-top:10px;" class="printControl">
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td style="width:80px;"><div class="goButton"><a style="padding-left:14px;" onclick="printMyPage();return false;">Print</a></div></td>
							<td style="width:80px;"><div class="goButton"><asp:linkbutton runat="server" style="padding-left:14px;" id="pdfButton" onclick="PDFButton_Click">PDF</asp:linkbutton></div></td>
							<td style="width:170px;">
								Layout: <asp:dropdownlist id="layoutList" runat="server" onchange="changeLayout(this); return false;" cssclass="body">
									<asp:listitem text="Portrait" value="portrait"></asp:listitem>
									<asp:listitem text="Landscape" value="landscape"></asp:listitem>
								</asp:dropdownlist>
							</td>
							<td style="width:220px;">
								Report Scale: 
								<asp:dropdownlist id="dataList" runat="server" onchange="changeDataZoom(); return false;" cssclass="body">
									<%--<asp:listitem text="Hide Data" value="hide"></asp:listitem>--%>
									<asp:listitem text="Fit to Page Width" value="fit"></asp:listitem>
									<asp:listitem text="25%" value="25%"></asp:listitem>
									<asp:listitem text="50%" value="50%"></asp:listitem>
									<asp:listitem text="60%" value="60%"></asp:listitem>
									<asp:listitem text="75%" value="75%"></asp:listitem>
									<asp:listitem text="90%" value="90%"></asp:listitem>
									<asp:listitem text="100%" value="100%" selected="true"></asp:listitem>
									<asp:listitem text="150%" value="150%"></asp:listitem>
									<asp:listitem text="200%" value="200%"></asp:listitem>
									<asp:listitem text="300%" value="300%"></asp:listitem>
									<asp:listitem text="400%" value="400%"></asp:listitem>
									<asp:listitem text="500%" value="500%"></asp:listitem>
								</asp:dropdownlist>
							</td>
							<td>&nbsp;</td>
						</tr>
					</table>
					<hr />
				</div>
			</asp:panel>
			
			<div id="OuterZoom" runat="server" style="width:504pt; text-align:center;">
				<div id="DataZoomDiv" runat="server">
					<table width="100%" border="0" cellpadding="0" cellspacing="10">
						<tr>
							<td>
								<!-- Title Section -->
								<div style="text-align:center;">
									<asp:label id="reportNameLabel" style="font-size:22px; font-weight:bold;" runat="server"></asp:label>
									<br />
									<span style="font-size:14px; font-weight:bold;">Shift Summary&nbsp;</span><span style="font-size:8px; color:red; font-weight:bold;">(red indicates one failure assumed)</span>
									<br />
									<asp:label id="dateLbl" style="font-size:13px;" runat="server"></asp:label>
									<asp:label id="firmwareLbl" style="font-size:13px;" runat="server"></asp:label>
								</div>
								<!-- END Title Section -->
							</td>
						</tr>
					</table>
					
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td>
								<asp:Panel ID="GridBindingPanel" runat="server">
									<table border="1" cellpadding="0" cellspacing="1" width="100%">
										<asp:Repeater ID="ColumnHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders %>'>
											<ItemTemplate>
												<tr>
													<asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
														<ItemTemplate>
															<td><%# "&nbsp;" %></td>
														</ItemTemplate>
													</asp:Repeater>
													<asp:Repeater ID="ColumnHeaderRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders[ColumnHeaderLevelRepeater.Items.Count] %>'>
														<ItemTemplate>
															<td style="padding:4px; font-weight:bold;"><%# Container.DataItem %></td>
														</ItemTemplate>
													</asp:Repeater>
												</tr>
											</ItemTemplate>
										</asp:Repeater>
										<asp:Repeater ID="GridRowsRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayData %>'>
											<ItemTemplate>
												<tr>
													<asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
														<ItemTemplate>
															<td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
														</ItemTemplate>
													</asp:Repeater>
													<asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
														<ItemTemplate>
	                                                        <td <%# (((string)Container.DataItem) != null && ((string)Container.DataItem).StartsWith("~")) ? "style='color:red;'" : "" %>>
                                                                <%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : ((string)Container.DataItem).Replace("~", "")) %>
	                                                        </td>
														</ItemTemplate>
													</asp:Repeater>
												</tr>
											</ItemTemplate>
											<footertemplate>
											</footertemplate>
										</asp:Repeater>
									</table> 
								</asp:Panel>
							</td>
						</tr>
						<tr>
							<td>
								<asp:repeater id="lowerRep" runat="server">
									<headertemplate>
										<table width="100%" cellpadding="0" cellspacing="0" border="1" class="shiftReportItem">
									</headertemplate>
									<itemtemplate>
											<%# FormatReportSeperator(Container.DataItem)%>
											<tr>
												<td><%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "DeviceFullName")) ? "&nbsp;" : DataFormatter.Format(Container.DataItem, "DeviceFullName") %></td>
												<td><%# string.IsNullOrEmpty(DataFormatter.FormatDate(Container.DataItem, "TranDate", "MMM d, yyyy h:mmtt", "")) ? "&nbsp;" : DataFormatter.FormatDate(Container.DataItem, "TranDate", "MMM d, yyyy h:mmtt", "") %></td>
												<td><%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "RDToolTranId")) ? "&nbsp;" : DataFormatter.Format(Container.DataItem, "RDToolTranId")%></td>
												<td><%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "ObservationText")) ? "&nbsp;" : DataFormatter.Format(Container.DataItem, "ObservationText")%></td>
											</tr>
									</itemtemplate>
									<footertemplate>
										</table>
									</footertemplate>
								</asp:repeater>
							</td>
						</tr>
					</table>
				</div>
			</div>	
		</div>
    </form>
</body>
</html>