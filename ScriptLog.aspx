﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ScriptLog.aspx.cs" Inherits="ScriptLog" %>

<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Recently Run Scripts</td>
						<td class="widgetTop" style="width: 30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align: right;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="width: 10px;">
										&nbsp;</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<div class="widget">
					<div class="title">Unregistered Scripts</div>
					<div style="padding:0px 14px; font-size:12px;">
						<asp:repeater id="unRegisteredFilesRep" runat="server">
							<headertemplate>
								<table width="100%" border="0" cellpadding="10">
									<tr>
										<td class="rowHeading">Script/Cell Name</td>
										<td class="rowHeading">Cell Id</td>
										<td class="rowHeading">Last Execution Date</td>
									</tr>
							</headertemplate>
							<itemtemplate>
								<tr>
									<td colspan="3" class="dataRowItem" style="line-height:20px;"><%# ((CellScriptExecution)Container.DataItem).ScriptName %></td>
								</tr>								
								<asp:repeater id="children" runat="server" datasource='<%# ((CellScriptExecution)Container.DataItem).Children %>'>
									<itemtemplate>
										<tr>
											<td style="padding-left:35px;color:#555;"><%# ((CellScriptExecution)Container.DataItem).CellName %></td>
											<td style="padding-left:14px;color:#555;"><%# ((CellScriptExecution)Container.DataItem).CellId %></td>
											<td style="padding-left:14px;color:#555;"><%# ((CellScriptExecution)Container.DataItem).LastExecutionDate.ToString("MM/dd/yyyy hh:mm tt") %></td>
										</tr>	
									</itemtemplate>
								</asp:repeater>
							</itemtemplate>
							<footertemplate>
								</table>
							</footertemplate>
						</asp:repeater>
						<asp:label id="unregMessage" cssclass="dataRowItem" runat="server" style="padding:14px;font-weight:bold;display:block;color:#555;" visible="false">No unregistered scripts have been run within the past six months.</asp:label>
					</div>
					<br /><br />

					<div class="title">Registered Scripts</div>
					<div style="padding:0px 14px; font-size:12px;">
						<asp:repeater id="registeredFilesRep" runat="server">
							<headertemplate>
								<table width="100%" border="0" cellpadding="10">
									<tr>
										<td class="rowHeading">Script/Cell Name</td>
										<td class="rowHeading">Cell Id</td>
										<td class="rowHeading">Last Execution Date</td>
									</tr>
							</headertemplate>
							<itemtemplate>
								<tr>
									<td colspan="3" class="dataRowItem" style="line-height:20px;"><%# ((CellScriptExecution)Container.DataItem).ScriptName %></td>
								</tr>								
								<asp:repeater id="children" runat="server" datasource='<%# ((CellScriptExecution)Container.DataItem).Children %>'>
									<itemtemplate>
										<tr>
											<td style="padding-left:35px;color:#555;"><%# ((CellScriptExecution)Container.DataItem).CellName %></td>
											<td style="padding-left:14px;color:#555;"><%# ((CellScriptExecution)Container.DataItem).CellId %></td>
											<td style="padding-left:14px;color:#555;"><%# ((CellScriptExecution)Container.DataItem).LastExecutionDate.ToString("MM/dd/yyyy hh:mm tt") %></td>
										</tr>	
									</itemtemplate>
								</asp:repeater>
							</itemtemplate>
							<footertemplate>
								</table>
							</footertemplate>
						</asp:repeater>
						<asp:label id="regMessage" cssclass="dataRowItem" runat="server" style="padding:14px;font-weight:bold;display:block;color:#555;" visible="false">No registered scripts have been run within the past six months.</asp:label>
					</div>
					<br /><br />
				</div>
			</td>
		</tr>
	</table>
	<br /><br />
</asp:Content>

