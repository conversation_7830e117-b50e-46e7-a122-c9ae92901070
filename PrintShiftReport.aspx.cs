using System;
using System.Data;
using System.Drawing;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Xml;
using Telerik.Web.UI;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public partial class PrintShiftReport : System.Web.UI.Page
{
    protected BaseReport ReportObj = null;

    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

	public int SnapshotId
    {
        get { if(this.ViewState["s"] != null) { return (int)this.ViewState["s"]; } else { return 0; } }
        set { this.ViewState["s"] = value; }
    }

	public SubReportInfo SubReportInfo
	{
		get { if (this.ViewState["sr"] != null) { return (SubReportInfo)this.ViewState["sr"]; } else { return null; } }
		set { this.ViewState["sr"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
                this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
            else
                this.RepInfo = Utility.GetReportInfoFromTransfer();

			if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]))
				this.SubReportInfo = Utility.LoadSubReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]));
			else
				this.SubReportInfo = Utility.GetSubReportInfoFromTransfer();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SNAPSHOT_ID_KEY]))
			{
				this.SnapshotId = Convert.ToInt32(Request.Params[DieboldConstants.SNAPSHOT_ID_KEY]);
				LoadSnapshotData();
			}

            if (this.RepInfo == null)
                Response.Redirect("~/reportlibrary.aspx");

			this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, this.SubReportInfo);

			if (this.SnapshotId == 0)
			{
				this.ReportObj.LoadData();
			}
			else
			{
				LoadSnapshotData();
				this.ReportObj.BuildGridDisplay();
			}

            reportNameLabel.Text = this.RepInfo.ReportName;

			if (this.RepInfo.StartDate != DateTime.MinValue)
			{
				dateLbl.Text = this.RepInfo.StartDate.ToString("MMM. d, yyyy h:mm tt");

				if (this.RepInfo.EndDate != DateTime.MinValue)
					dateLbl.Text += " - ";
				else
					dateLbl.Text += "<br />";
			}

			if (this.RepInfo.EndDate != DateTime.MinValue)
				dateLbl.Text += this.RepInfo.EndDate.ToString("MMM. d, yyyy h:mm tt") + "<br />";

			if (!string.IsNullOrEmpty(((ShiftReport)this.ReportObj).SettingsList))
				firmwareLbl.Text = "F/W: " + ((ShiftReport)this.ReportObj).SettingsList;

			SearchCriteria searchCriteria = new SearchCriteria(this.Page);
			searchCriteria.CalculateSearchCriteria(this.RepInfo, null);
			searchCriteria.SetToSession();

            DataSet ds = TransactionSource.GetTransactionsFromSessionParameters();
			DataView view = ds.Tables[0].DefaultView;
			view.Sort = "DeviceFullName, TranDate";
			lowerRep.DataSource = view;
			lowerRep.DataBind();

			GridBindingPanel.DataBind();

			//PDF mode, set parameters and render for PDF
			if (!string.IsNullOrEmpty(Request.QueryString["pdf"]))
			{
				this.PrintControlsPanel.Style.Add("display", "none");
				
				if (!string.IsNullOrEmpty(Request.QueryString["layout"]))
					this.layoutList.SelectedValue = Request.QueryString["layout"];

				if (!string.IsNullOrEmpty(Request.QueryString["data"]))
					dataList.SelectedValue = Request.QueryString["data"];

				if (dataList.SelectedValue == "hide")
					this.DataZoomDiv.Style.Add("display", "none");
				else if (dataList.SelectedValue != "fit")
					this.DataZoomDiv.Style.Add("zoom", dataList.SelectedValue);

				//if request is from the Queue Service, return the PDF immediately
				if (!string.IsNullOrEmpty(Request.QueryString["qs"]))
					PDFButton_Click(null, null);
			}
		}
    }

	private string priorDevice = null;
	private string priorSession = null;
	protected string FormatReportSeperator(object dataItem)
	{
		StringBuilder retVal = new StringBuilder();

		if (dataItem != null)
		{
			string device = DataFormatter.Format(dataItem, "DeviceFullName");
			string session = DataFormatter.Format(dataItem, "SessionName");
			if (string.Compare(priorDevice, device) != 0 || string.Compare(priorSession, session) != 0)
			{
				retVal.AppendLine("<tr class=\"shiftReportHeading\"><td style=\"border-top:solid 18px #000;\">&nbsp;</td><td style=\"border-top:solid 18px #000;\">&nbsp;</td><td style=\"border-top:solid 18px #000;\">Session</td><td style=\"border-top:solid 18px #000;\">");
				retVal.AppendLine(session);
				retVal.AppendLine("</td></tr>");
				retVal.AppendLine("<tr class=\"shiftReportHeading\"><td>Unit #</td><td>Date</td><td>TRX</td><td>Fault Description</td></tr>");

				priorDevice = device;
				priorSession = session;
			}
		}
		if (retVal.Length > 0)
			return retVal.ToString();
		else
			return null;
	}

	private void LoadSnapshotData()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadSnapshot", this.SnapshotId);
		this.RepInfo = null;

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				this.RepInfo = Utility.LoadReportInfo(DataFormatter.getInt32(row, "ReportId"));
				this.RepInfo.ReportData = DataFormatter.getString(row, "ReportData");
				this.RepInfo.CellSetData = DataFormatter.getString(row, "CellSetData");
				this.RepInfo.ChartTitle = DataFormatter.getString(row, "ChartTitle");
				this.RepInfo.FixedStartDate = DataFormatter.getDateTime(row, "StartDate");
				this.RepInfo.FixedEndDate = DataFormatter.getDateTime(row, "EndDate");
                this.RepInfo.ParetoInfo.SplitByCell = DataFormatter.getBool(row, "Pareto_SplitByCell");
                this.RepInfo.ParetoInfo.IncludeTotal = DataFormatter.getBool(row, "Pareto_IncludeTotal");
                this.RepInfo.ParetoInfo.ByStatisticValue = DataFormatter.getBool(row, "Pareto_ByStatValue");
                this.RepInfo.ParetoInfo.MaxColumns = DataFormatter.getInt32(row, "Pareto_MaxColumns");
				this.RepInfo.PRSTInfo.XaxisTypeId = (ReportHelper.XAxisTypeEnum)DataFormatter.getInt32(row, "PRST_XaxisTypeId");
				this.RepInfo.PRSTInfo.IncludeTrendline = DataFormatter.getBool(row, "PRST_IncludeTrendline");
				this.RepInfo.PRSTInfo.IncludeUncensoredSeries = DataFormatter.getBool(row, "PRST_IncludeUncensoredSeries");
				this.RepInfo.PRSTInfo.MTBFSpec = DataFormatter.getDecimal(row, "PRST_MTBFSpec");
				this.RepInfo.PRSTInfo.ProducersRisk = DataFormatter.getDecimal(row, "PRST_ProducersRisk");
				this.RepInfo.PRSTInfo.ConsumersRisk = DataFormatter.getDecimal(row, "PRST_ConsumersRisk");
				this.RepInfo.PRSTInfo.DiscriminationRatio = DataFormatter.getDecimal(row, "PRST_DiscriminationRatio");
				this.RepInfo.ProgressInfo.Normalize = DataFormatter.getBool(row, "Progress_Normalize");
				this.RepInfo.DistributionInfo.LowerSpecLimit = DataFormatter.getDecimal(row, "Distro_LowerSpecLimit");
				this.RepInfo.DistributionInfo.UpperSpecLimit = DataFormatter.getDecimal(row, "Distro_UpperSpecLimit");
				this.RepInfo.DistributionInfo.ShowNormalDistribution = DataFormatter.getBool(row, "Distro_ShowNormalDistribution");
				this.RepInfo.DistributionInfo.ShowHistogram = DataFormatter.getBool(row, "Distro_ShowHistogram");
			}
		}
	}

	protected void PDFButton_Click(object sender, EventArgs e)
	{
		PrintControlsPanel.Visible = false;
		
		int subReportId = 0;
		if (this.SubReportInfo != null)
			subReportId = this.SubReportInfo.SubReportId;

		string tempFileName = ReportHelper.GeneratePDF(this.RepInfo, this.RepInfo.PrintPageName, layoutList.SelectedValue, "", dataList.SelectedValue, false, subReportId);
		string displayName = this.RepInfo.ReportName + "_" + DateTime.Now.Ticks + ".pdf";
		
		if (this.SubReportInfo != null)
			displayName = this.SubReportInfo.SubReportName + "_" + DateTime.Now.Ticks + ".pdf";

		try
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = "application/pdf";
			Response.AppendHeader("content-disposition", "attachment; filename=" + displayName + ";");

			Response.WriteFile(tempFileName);
			Response.Flush();
		}
		finally
		{
			if (!string.IsNullOrEmpty(tempFileName) && File.Exists(tempFileName))
				File.Delete(tempFileName);
		}

		Response.End();
	}
}
