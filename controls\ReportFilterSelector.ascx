<%@ Control Language="C#" AutoEventWireup="true" CodeFile="ReportFilterSelector.ascx.cs" Inherits="controls_ReportFilterSelector" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<script type="text/javascript" language="javascript">
    function checkAllNodes(val)
	{
		var treeView = $find("<%= memberTree.ClientID %>");

		var nodes = treeView.get_allNodes();
		if (nodes.length > 0)
		{ 
			for (var i = 0; i < nodes.length; i++)
			{
				nodes[i].set_checked(val);
			}
		} 
	}	
    function nodeChecked(sender, args)
    {
		var curNode = args.get_node();
        var childNode = null;
        var childNodes = curNode.get_nodes(); 
        var subNodes = null;
        
		if (curNode.get_checked() == true)
		{
			//expand and check all chidren
			curNode.set_expanded(true);

			if (childNodes.get_count() > 0)
			{
				for (var i = 0; i < childNodes.get_count(); i++)
				{
				    childNode = childNodes.getNode(i);
					childNode.set_checked(true);
					subNodes = childNode.get_nodes();
			        if (subNodes.get_count() > 0)
			        {
				        for (var j = 0; j < subNodes.get_count(); j++)
				        {
					        subNodes.getNode(j).set_checked(true);
				        }
			        }						
				}
			}
		}
		else
		{
			//uncheck children
			if (childNodes.get_count() > 0)
			{
				for (var i = 0; i < childNodes.get_count(); i++)
				{
				    childNode = childNodes.getNode(i);
					childNode.set_checked(false);
					subNodes = childNode.get_nodes();
			        if (subNodes.get_count() > 0)
			        {
				        for (var j = 0; j < subNodes.get_count(); j++)
				        {
					        subNodes.getNode(j).set_checked(false);
				        }
			        }						
				}
			}
		}
    }
    function unselectNode(sender, args)
    {
        args.get_node().set_selected(false);        
    }
</script>

<telerik:radcodeblock id="codeBlockCntrl" runat="server">

<div>
   <table border="0" cellpadding="0" cellspacing="0">
		<tr>
			<td valign="top" style="padding-left:14px;">
			    <telerik:radcombobox id="dimensionCombo" causesvalidation="false" runat="server" width="292px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
					appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" autopostback="true" onselectedindexchanged="OnDimensionChanged">
                </telerik:radcombobox>
			</td>
			<td valign="middle" style="padding-left:5px;">
				&nbsp;
			</td>
		</tr>
		<tr>
			<td valign="top" style="padding-left:14px;">				
				<div style="height: 236px; width:290px; border: solid 1px #999999; overflow:auto;" >
					<telerik:RadTreeView id="memberTree" runat="Server" checkboxes="true" onclientnodeclicked="unselectNode" onclientnodechecked="nodeChecked" 
						EnableDragAndDrop="false" EnableDragAndDropBetweenNodes="false" Skin="WebBlue" onnodeexpand="Node_Expanded"></telerik:RadTreeView>
					
					<telerik:RadTreeView id="selectedItemsTree" enabled="false" runat="Server" checkboxes="true" Skin="WebBlue"></telerik:RadTreeView>	
				</div>
			</td>
			<td valign="top" style="padding-left:5px;">
				<div id="checkAllBtn" class="addButton" runat="server"><asp:linkbutton id="selectAllLink" runat="server" causesvalidation="false" onclick="SelectAll_Click">Check All</asp:linkbutton></div>
				<div id="clearAllBtn" class="minusButton" runat="server"><asp:linkbutton id="clearAllLink" runat="server" causesvalidation="false" onclick="ClearAll_Click">Uncheck All</asp:linkbutton></div>
				<div id="viewSelectedButton" class="goButton" runat="server"><asp:linkbutton id="viewSelectedLink" runat="server" causesvalidation="false" onclick="ViewSelected_Click">View Selected</asp:linkbutton></div>
				<div id="editButton" class="goButton" runat="server"><asp:linkbutton id="editLink" runat="server" causesvalidation="false" onclick="Edit_Click">Edit</asp:linkbutton></div>
			</td>
		</tr>
	</table>
</div>


	<telerik:RadAjaxManagerProxy ID="RadAjaxManager1" runat="server">
		<ajaxsettings>
			<telerik:ajaxsetting ajaxcontrolid="dimensionCombo">
				<updatedcontrols>
					<telerik:ajaxupdatedcontrol controlid="dimensionTree" />
					<telerik:ajaxupdatedcontrol controlid="checkAllBtn" />
					<telerik:ajaxupdatedcontrol controlid="clearAllBtn" />
					<telerik:ajaxupdatedcontrol controlid="editButton" />
					<telerik:ajaxupdatedcontrol controlid="viewSelectedButton" />
					<telerik:ajaxupdatedcontrol controlid="memberTree" loadingpanelid="LoadingPanel1" />
				</updatedcontrols>
			</telerik:ajaxsetting>
			<telerik:ajaxsetting ajaxcontrolid="selectAllLink">
				<updatedcontrols>
					<telerik:ajaxupdatedcontrol controlid="memberTree" loadingpanelid="LoadingPanel1" />
					<telerik:ajaxupdatedcontrol controlid="selectedItemsTree" loadingpanelid="LoadingPanel1" />
				</updatedcontrols>
			</telerik:ajaxsetting>
			<telerik:ajaxsetting ajaxcontrolid="clearAllLink">
				<updatedcontrols>
					<telerik:ajaxupdatedcontrol controlid="memberTree" loadingpanelid="LoadingPanel1" />
					<telerik:ajaxupdatedcontrol controlid="selectedItemsTree" loadingpanelid="LoadingPanel1" />
				</updatedcontrols>
			</telerik:ajaxsetting>
			<telerik:ajaxsetting ajaxcontrolid="editLink">
				<updatedcontrols>					
					<telerik:ajaxupdatedcontrol controlid="dimensionCombo" />
					<telerik:ajaxupdatedcontrol controlid="checkAllBtn" />
					<telerik:ajaxupdatedcontrol controlid="clearAllBtn" />
					<telerik:ajaxupdatedcontrol controlid="viewSelectedButton" />
					<telerik:ajaxupdatedcontrol controlid="memberTree" loadingpanelid="LoadingPanel1" />
					<telerik:ajaxupdatedcontrol controlid="selectedItemsTree" loadingpanelid="LoadingPanel1" />
				</updatedcontrols>
			</telerik:ajaxsetting>
			<telerik:ajaxsetting ajaxcontrolid="viewSelectedLink">
				<updatedcontrols>
					<telerik:ajaxupdatedcontrol controlid="dimensionCombo" />
					<telerik:ajaxupdatedcontrol controlid="checkAllBtn" />
					<telerik:ajaxupdatedcontrol controlid="clearAllBtn" />
					<telerik:ajaxupdatedcontrol controlid="editButton" />
					<telerik:ajaxupdatedcontrol controlid="memberTree" loadingpanelid="LoadingPanel1" />
					<telerik:ajaxupdatedcontrol controlid="selectedItemsTree" loadingpanelid="LoadingPanel1" />
				</updatedcontrols>
			</telerik:ajaxsetting>
		</ajaxsettings>
	</telerik:RadAjaxManagerProxy>
	
	<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
		<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
	</telerik:RadAjaxLoadingPanel>
</telerik:radcodeblock>
