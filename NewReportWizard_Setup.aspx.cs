using System;
using System.Web.UI;

public partial class NewReportWizard_Setup : System.Web.UI.Page
{
	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			//Presence of query string param indicates editing, redirect to step 2.
			if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
			{
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
				Utility.SetReportInfoForTransfer(this.RepInfo);
                Response.Redirect(this.RepInfo.WizardPageName);
			}

			reportTypeList.DataSource = Utility.GetReportTypeList();
			reportTypeList.DataBind();

			this.RepInfo = new ReportInfo();
			reportNameField.Focus();
		}
    }

	protected void NextButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			this.RepInfo.ReportName = reportNameField.Text.Trim();
			this.RepInfo.ChartTitle = reportNameField.Text.Trim();
			this.RepInfo.ReportTypeId = (ReportHelper.ReportTypeEnum) Convert.ToInt32(reportTypeList.SelectedValue);
			this.RepInfo.ReportTypeName = reportTypeList.SelectedItem.Text;

            this.RepInfo = Utility.LoadReportTypeInfo(this.RepInfo, Convert.ToInt32(reportTypeList.SelectedValue));

            Utility.SetReportInfoForTransfer(this.RepInfo);
            Response.Redirect(this.RepInfo.WizardPageName);
		}
	}

	protected void ReportTypeList_SelectedIndexChanged(object sender, EventArgs e)
	{
		this.RepInfo = new ReportInfo();
	}
}
