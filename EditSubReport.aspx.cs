﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class EditSubReport : System.Web.UI.Page
{
    public int SubReportId
    {
        get { if (this.ViewState["s"] != null) { return (int)this.ViewState["s"]; } else { return 0; } }
        set { this.ViewState["s"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!this.IsPostBack)
        {
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]))
                this.SubReportId = Convert.ToInt32(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]);
            else
                throw new ApplicationException("SubReport ID parameter was not supplied.");

            SubReportInfo subReportInfo = Utility.LoadSubReportInfo(this.SubReportId);
            if (subReportInfo == null)
                throw new ApplicationException("Invalid SubReport ID.");

            this.subReportNameField.Text = subReportInfo.SubReportName;
        }
    }

    protected void SaveButton_Click(object sender, EventArgs e)
    {
        if (Page.IsValid && this.subReportNameField.Text.Trim().Length > 0)
        {
            SqlHelper.ExecuteNonQuery("RPT_UpdateSubReport", this.SubReportId, this.subReportNameField.Text.Trim());

            string closeScript = "GetRadWindow().BrowserWindow.document.location.href='reportlibrary.aspx';";
            Page.ClientScript.RegisterStartupScript(typeof(EditSubReport), "CloseScript", closeScript, true);
        }
    }
}
