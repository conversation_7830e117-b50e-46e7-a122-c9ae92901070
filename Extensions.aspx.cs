using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class Extensions : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadExtensionFieldsByLibrary", this.LibraryNameList.SelectedValue);
			LibraryNameList.Items.Add(new RadComboBoxItem("All", ""));
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				if (LibraryNameList.Items.FindItemByValue(DataFormatter.getString(row, "LibraryName")) == null)
					LibraryNameList.Items.Add(new RadComboBoxItem(DataFormatter.getString(row, "LibraryName"), DataFormatter.getString(row, "LibraryName")));
			}

			if (!string.IsNullOrEmpty(Request.Params["page"]))
				ExtensionGrid.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}

		//bind every page load to handle javascript paging.
		BindGridData();
	}

	public void RebindData(object sender, EventArgs e)
	{
		//do nothing, data rebound on every page load.

		//BindGridData();
	}

	protected void ExtensionGrid_OnItemDataBound(object sender, Telerik.Web.UI.GridItemEventArgs e)
	{
		if (e.Item != null && e.Item.DataItem != null)
		{
			Label trackingLbl = null;
            Label statusLbl = null;

			if (e.Item.FindControl("TrackingStatusLabel") != null)
				trackingLbl = (Label)e.Item.FindControl("TrackingStatusLabel");
            if (e.Item.FindControl("StatusLabel") != null)
                statusLbl = (Label)e.Item.FindControl("StatusLabel");

			if (trackingLbl != null)
			{
				string trackAsSetting = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsSetting", "true", "false", "");
				string trackAsStatistic = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsStatistic", "true", "false", "");
				string trackAsMediaCount = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsMediaCount", "true", "false", "");

				if (Convert.ToBoolean(trackAsSetting) || Convert.ToBoolean(trackAsStatistic) || Convert.ToBoolean(trackAsMediaCount))
					trackingLbl.Text = "<span style=\"color:#666666;font-weight:bold;\">Enabled</span>";
				else
					trackingLbl.Text = "<span style=\"color:#aaaaaa;\">Disabled</span>";
			}
            if (statusLbl != null)
            {
                string status = DataFormatter.Format(e.Item.DataItem, "FieldStatusId", null, null, "0", false, false);
                if(string.Compare(status, "2", true) == 0)
                    statusLbl.Text = "<span style=\"color:#aaaaaa;\">Inactive</span>";
                else
                    statusLbl.Text = "<span style=\"color:#666666;\">Active</span>";
            }
        }
	}

	protected void LibraryNameList_SelectedIndexChanged(object sender, EventArgs e)
	{
		ExtensionGrid.CurrentPageIndex = 0;
		BindGridData();
	}

	private void BindGridData()
	{
		ExtensionGrid.DataSource = SqlHelper.ExecuteDataset("RPT_LoadExtensionFieldsByLibrary", this.LibraryNameList.SelectedValue);
		ExtensionGrid.DataBind();
	}
}
