<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Distribution.aspx.cs" Inherits="NewReportWizard_Distribution" %>
<%@ register tagprefix="rpt" tagname="DimensionSelector" src="~/controls/DimensionSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
					
					<div style="padding-left:14px; padding-right:14px;">
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="padding-bottom:5px;width:200px;" valign="top">
									<div class="rowHeading" style="width:200px;">Select Dimension:</div>
									<div class="leftPad body" style="padding-top:15px;">
										All data items will display in the final chart unless they are filtered off by unchecking them in the list. 
									</div>
								</td>
								<td style="padding-left:14px;"><rpt:DimensionSelector id="dimensionSel" runat="server" isrequired="true"></rpt:DimensionSelector></td>
							</tr>
							<tr style="display:none;">
								<td style="width:200px;padding-top:10px;" valign="top"><div class="rowHeading" style="width:200px;">Target:</div></td>
								<td style="padding-top:10px;"> 
									<asp:textbox id="targetField" cssclass="entryControl" runat="server"></asp:textbox>
									<asp:comparevalidator id="Comparevalidator2" runat="server" display="dynamic" cssclass="error" controltovalidate="targetField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
								</td>
							</tr>
							<tr>
								<td style="width:200px;padding-top:10px;" valign="top">&nbsp;</td>
								<td style="padding: 10px 0px 10px 10px;"> 
									<asp:checkbox id="ShowHistogram" runat="server" text="Show Histogram" />
									<br />
									<asp:checkbox id="ShowNormal" runat="server" text="Show Normal Distribution" />
									<asp:customvalidator display="dynamic" id="val5" runat="server" onservervalidate="ValidateChecksFields" cssclass="error" errormessage="<br />* At least one display option must be selected."></asp:customvalidator>
									<br /><br />
								    <asp:checkbox id="subReportDimCheck" runat="server" text="Generate sub-reports for selected dimensions" />
								    <%--<br />
							        <asp:checkbox id="splitByDevice" runat="server" text="Split Columns By Device" />--%>
								</td>
							</tr>
							<tr>
								<td style="width:200px;padding-top:10px;" valign="top"><div class="rowHeading" style="width:200px;">Lower Spec. Limit:</div></td>
								<td style="padding-top:10px;"> 
									<asp:textbox id="lowerSpecLimitField" cssclass="entryControl" runat="server"></asp:textbox>
									<asp:comparevalidator id="cv13" runat="server" display="dynamic" cssclass="error" controltovalidate="lowerSpecLimitField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
								</td>
							</tr>
							<tr>
								<td style="width:200px;padding-top:10px;" valign="top"><div class="rowHeading" style="width:200px;">Upper Spec. Limit:</div></td>
								<td style="padding-top:10px;" valign="top">
									<asp:textbox id="upperSpecLimitField" cssclass="entryControl" runat="server"></asp:textbox>
									<asp:comparevalidator id="val2" runat="server" display="dynamic" cssclass="error" controltovalidate="upperSpecLimitField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
								</td>
							</tr>
							<tr>
								<td style="width:200px;padding-top:10px;" valign="top"><div class="rowHeading" style="width:200px;">Filter Values:</div></td>
								<td style="padding-top:10px;padding-left:14px;" valign="top">
									Hide values 
									from: <asp:textbox id="filterStartField" width="80" runat="server"></asp:textbox> 
									<asp:comparevalidator id="val3" runat="server" display="dynamic" cssclass="error" controltovalidate="filterStartField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
									to: <asp:textbox id="filterEndField" width="80" runat="server"></asp:textbox>	
									<asp:comparevalidator id="val4" runat="server" display="dynamic" cssclass="error" controltovalidate="filterEndField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
								</td>
							</tr>
							<tr>
								<td style="width:200px;padding-top:10px;" valign="top"><div class="rowHeading" style="width:200px;">Override X-Axis Range:</div></td>
								<td style="padding-top:10px;padding-left:14px;" valign="top">
									start: <asp:textbox id="xAxisStartField" width="80" runat="server"></asp:textbox> 
									<asp:comparevalidator id="cv1" runat="server" display="dynamic" cssclass="error" controltovalidate="xAxisStartField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
									end: <asp:textbox id="xAxisEndField" width="80" runat="server"></asp:textbox>	
									<asp:comparevalidator id="cv2" runat="server" display="dynamic" cssclass="error" controltovalidate="xAxisEndField" operator="dataTypeCheck" type="Double" errormessage="* Invalid format"></asp:comparevalidator>
								</td>
							</tr>
						</table>
					</div>
					<br />&nbsp;<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

