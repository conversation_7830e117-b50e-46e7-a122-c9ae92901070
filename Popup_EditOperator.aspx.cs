using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditOperator : System.Web.UI.Page
{
	public string OperatorId
	{
		get { return (string)this.ViewState["o"]; }
		set { this.ViewState["o"] = value; }
	}

	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.OPERATOR_ID_KEY]))
			{
				this.OperatorId = Request.Params[DieboldConstants.OPERATOR_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetOperator", this.OperatorId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.operatorNameField.Text = DataFormatter.getString(row, "OperatorName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(this.OperatorId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertOperator", this.operatorNameField.Text.Trim(), this.isActive.Checked);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateOperator", this.OperatorId, this.operatorNameField.Text.Trim(), this.isActive.Checked);

		if (result == 0)
		{
			this.ErrorLabel.Visible = true;
		}
		else
		{
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditOperator.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditOperator.aspx';CloseRadWindow(); ", true);
		}
	}
}
