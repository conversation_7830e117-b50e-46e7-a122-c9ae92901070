﻿.body 
{
	font-family:<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Sans-Serif;
	font-size: 11px;
	color:#000000;	
}
img {
	border:none;
}
.body a
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:10px;
	color:#0077cc;
	text-decoration:none;
}
.body a:hover
{
	text-decoration:underline;	
}
.leftPad
{
	padding-left:14px;
}
.title
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:13px;
	font-weight:bold;
	text-decoration:none;
	padding: 12px 10px 12px 14px;
	color:#000000;	
}
.pagingDiv
{
	border:solid 1px #7c736e;
	width:20px;
	height:20px;
	line-height:20px;
	cursor:pointer;
	display:inline;
	padding: 3px 5px 3px 5px;
}
.pagingDiv_selected
{
	border:solid 1px #7c736e;
	width:20px;
	height:20px;	
	line-height:20px;
	background-color:#cccccc;
	display:inline;
	padding: 3px 5px 3px 5px;
}
.rowHeading
{
	font-family:<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Sans-Ser<PERSON>;
	font-size:11px;
	font-weight:bold;
	background-color:#cfc9c5;
	color:#000000;
	padding: 5px 10px 5px 14px;	
}
.cellHeading
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	background-color:#cfc9c5;
	color:#000000;
	padding: 10px 10px 10px 14px;	
	border-right:solid 1px #ffffff;
	border-top:solid 1px #ffffff;
}
.cellHeading
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	background-color:#cfc9c5;
	color:#000000;
	padding: 10px 10px 10px 14px;	
	border-right:solid 1px #ffffff;
	border-top:solid 1px #ffffff;
}
.gridHeading
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	text-align:left;
	font-weight:bold;
	background-color:#cfc9c5;
	color:#000000;
	padding: 10px 10px 10px 14px;	
	border-right:solid 1px #ffffff;
	border-top:solid 1px #ffffff;
}
.gridPager
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	background-color:#cfc9c5;
	color:#000000;
	padding: 0px 10px 5px 14px;	
}
.error
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size: 10px;
	font-weight:bold;
	color:#ff0000;
}
.dashboardNavItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	text-decoration:none;
	color:#ffffff;	
	padding: 10px 0px 0px 20px;
	background-image:url(images/dashboard_Nav.jpg);
	background-repeat:no-repeat;
	cursor:pointer;
	width:212px;
	height:20px;
}
.dashboardNavItem a
{
	color:#000000;
	cursor:pointer;
	text-decoration:none;
}
.dashboardNavItemSelected
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	text-decoration:none;
	color:#ffffff;	
	padding: 10px 0px 0px 20px;
	background-image:url(images/dashboard_NavSelected.jpg);
	background-repeat:no-repeat;
	width:212px;
	height:20px;
}
.dashboardNavItemSelected a
{
	color:#ffffff;
	cursor:pointer;
	text-decoration:none;
}
.tab
{
	color:#ffffff;
	text-align:center;
	text-decoration:none;
	background-image:url(images/tab.jpg);
	background-repeat:no-repeat;
	width:103px;
	height:26px;
	line-height:26px;
	cursor:pointer;
}
.tab:hover
{
	text-decoration:none;
	background-image:url(images/tab_selected.jpg);
	background-repeat:no-repeat;
}
.tab_selected
{
	color:#ffffff;
	text-align:center;
	text-decoration:none;
	background-image:url(images/tab_selected.jpg);
	background-repeat:no-repeat;
	width:103px;
	height:26px;
	line-height:26px;
	cursor:pointer;
}
.tab_selected:hover
{
	text-decoration:none;
}

/* BEGIN LEFT NAV STYLES */
.navCallout
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:15px;
	background-color:#2288d7;
	color:#ffffff;
	width:170px;
	padding: 18px 10px 18px 15px;	
	box-sizing:border-box;
}
.leftNav
{
	background-color:#beb7b1;
	width:170px;
}
.leftNav a
{
	text-decoration:none;
}
.leftNavTop
{
	background-image:url(images/nav/navTop.jpg);
	height:2px;
	line-height:2px;
	background-repeat:no-repeat;
}
.leftNavItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	padding-left:15px;
	color:#ffffff;
	background-image:url(images/nav/navItem.jpg); 
	width:150px;
	height:30px;
	line-height:30px;
	background-repeat:no-repeat;
	cursor:pointer;
	text-decoration:none;
}
.leftNavItem:hover
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	padding-left:15px;
	color:#ffffff;
	background-image:url(images/nav/navItem_selected.jpg);
	width:150px;
	height:30px;
	line-height:30px;
	background-repeat:no-repeat;
	text-decoration:none;
	cursor:pointer;
}
.leftNavItem_selected
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	padding-left:15px;
	color:#ffffff;
	background-image:url(images/nav/navItem_selected.jpg);
	width:150px;
	height:30px;
	line-height:30px;
	background-repeat:no-repeat;
	text-decoration:none;
	cursor:pointer;
}
.leftNavBottom
{
	background-image:url(images/nav/navBottom.jpg);
	height:5px;
	line-height:5px;
	background-repeat:no-repeat;
}
/* END LEFT NAV STYLES */

/* BEGIN WIDGET STYLES */
.widget
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:10px;
	padding: 0px 0px 0px 0px;
	background-color:#ffffff;
	color:#000000;	
}
.widget .goButton a, .widget .backButton a, .widget .cancelButton a, .widget .addButton a,
.widget .closeButton a, .widget .solidButton a, .widget .upButton a, .widget .minusButton a,
.widget .goButtonTop a, .widget .backButtonTop a, .widget .cancelButtonTop a, .widget .addButtonTop a,
.widget .closeButtonTop a
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:10px;
	color:#0077cc;
	padding: 0px 10px 0px 14px;
	text-decoration:none;	
	line-height:18px;
}

.widget a:hover
{
	text-decoration:underline;	
}
.widgetTitle
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	text-decoration:none;
	color:#ffffff;	
	padding: 4px 0px 0px 14px;
	background-image:url(images/widgetTitle.jpg);
	background-repeat:no-repeat;
	width:212px;
	height:26px;
}
.widgetTop
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:10px;
	text-align:right;
	text-decoration:none;
	color:#dddddd;	
	padding: 6px 10px 0px 0px;
	background-image:url(images/widgetTop.jpg);
	background-repeat:repeat-x;
	height:24px;
}
.widgetTop a
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:10px;
	color:#dddddd;
	padding: 6px 10px 0px 14px;
	text-decoration:none;
}
.widgetTop a:hover
{
	color:#eeeeee;	
	text-decoration:underline;
}

.repeaterItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	background-color:#ffffff;
	padding: 2px 10px 2px 14px;	
	cursor:pointer;
}
.repeaterItem:Hover
{
	background-color:#bcdaf2;
}
.repeaterItemAlt
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	background-color:#efefef;
	padding: 2px 10px 2px 14px;	
	cursor:pointer;
}
.repeaterItemAlt:Hover
{
	background-color:#bcdaf2;
}
.dataRowItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:12px;
	background-color:#efefef;
	padding: 2px 10px 2px 14px;	
}

.gridItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	background-color:#ffffff;
	padding: 2px 10px 2px 14px;	
}
.gridItem:Hover
{
	background-color:#bcdaf2;
}
.gridItemAlt
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	background-color:#efefef;
	padding: 2px 10px 2px 14px;	
}
.gridItemAlt:Hover
{
	background-color:#bcdaf2;
}
a.reportLibraryLink {
	color:#0077CC;
	font-size:10px;
	line-height:18px;
	display:block;
}
.repeaterSubItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	background-color:#ffffff;
	padding: 2px 10px 2px 12px;	
}
.repeaterSubItemAlt
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	background-color:#efefef;
	padding: 0px 10px 0px 12px;	
}
.repeaterItemSelected
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	background-color:#bcdaf2;
	color:#000000;
	cursor:pointer;
}
.gridItemSelected
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	background-color:#bcdaf2;
	color:#000000;
	padding: 2px 10px 2px 14px;	
	cursor:pointer;
}
.repeaterItemSelected TD
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	background-color:#bcdaf2;
	color:#000000;
	cursor:pointer;
}
.repeaterTreeItem
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:10px;
	font-weight:normal;
	padding: 4px 0px 4px 0px;
	color:#000000;
}
.repeaterTreeItemSelected
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	background-color:#bcdaf2;
	padding-left:14px;
	color:#000000;
}
.repeaterTreeItemSelected TD
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	background-color:#bcdaf2;
	padding-left:14px;
	color:#000000;
}
.treeControl
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	text-decoration:none;
	color:#000000;	
}
.treeControl a
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	text-decoration:none;	
	color:#000000;	
}
.treeControl a:hover
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	text-decoration:none;
	color:#000000;	
}
.entryControl
{
	margin-left:14px;
}
/* END WIDGET STYLES */

/* BEGIN BUTTON STYLES */

.goButton
{
	background-image:url(images/buttons/go.png);
	background-position:0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
@media print 
{ 
	.goButton { display:none; }
}
.backButton
{
	background-image:url(images/buttons/back.png);
	background-position:0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.cancelButton
{
	background-image:url(images/buttons/cancel.png);
	background-position:0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.addButton
{
	background-image:url(images/buttons/add.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.closeButton
{
	background-image:url(images/buttons/close.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.solidButton
{
	background-image:url(images/buttons/solid.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.upButton
{
	background-image:url(images/buttons/up.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.minusButton
{
	background-image:url(images/buttons/minus.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
}
.goButtonTop
{
	background-image:url(images/buttons/go2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	line-height:16px;
	cursor:pointer;
	overflow:hidden;
	height:16px;
}
.backButtonTop
{
	background-image:url(images/buttons/back2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	line-height:16px;
	cursor:pointer;
	overflow:hidden;
	height:16px;
}
.cancelButtonTop
{
	background-image:url(images/buttons/cancel2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	line-height:16px;
	cursor:pointer;
	overflow:hidden;
	height:16px;
}
.addButtonTop
{
	background-image:url(images/buttons/add2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	line-height:16px;
	cursor:pointer;
	overflow:hidden;
	height:16px;
}
.closeButtonTop
{
	background-image:url(images/buttons/close2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:bold;
	color:#2388d8;
	line-height:16px;
	cursor:pointer;
	overflow:hidden;
	height:16px;
}
.repeaterItem .expandCollapseButton
{
	background-image:url(images/buttons/add.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.repeaterItemAlt .expandCollapseButton
{
	background-image:url(images/buttons/add.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.gridItemSelected .expandCollapseButton
{
	background-image:url(images/buttons/minus.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.expandButton
{
	background-image:url(images/buttons/add.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.collapseButton
{
	background-image:url(images/buttons/minus.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.noChildrenButton
{
	background-image:url(images/buttons/solid2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.expandSubButton
{
	background-image:url(images/buttons/add.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.collapseSubButton
{
	background-image:url(images/buttons/minus.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
.noChildrenSubButton
{
	background-image:url(images/buttons/solid2.png);
	background-position: 0px 4px;
	background-repeat:no-repeat;
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	font-weight:normal;
	line-height:16px;
	cursor:pointer;
}
/* END BUTTON STYLES */

.gridHeading a
{
	font-family:Verdana, Arial, Sans-Serif;
	font-size:11px;
	text-align:left;
	font-weight:bold;
	color:#000000;
	padding-left:0px;
}

@media print 
{
  * { background-color: white !important; background-image: none !important; }
  .printControl { display:none; }
}

.shiftReportHeading {
	font-size:11px;
	text-align:left;
	font-weight:bold;
	color:#000;
	padding: 3px;
}
.shiftReportHeading a {
	color:#000;
}
.shiftReportItem
{
	text-align:left;
	padding: 3px;	
}
.listing {
	padding: 10px;
}
.listingContainer {
	height:250px;
	overflow: scroll;
	background: #FFF;
	border: solid 1px #cccccc;
}
.AlphaSort .reportGroup {
	display: none;
}
.CatSort .reportGroup {
	display: block;
}
.AlphaSort .grpName {
	display: inline;
}
.CatSort .grpName {
	display: none;
}
.fieldGroup hr {
	display: none;
}