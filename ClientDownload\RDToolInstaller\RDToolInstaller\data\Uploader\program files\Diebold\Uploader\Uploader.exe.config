<?xml version="1.0"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Uploader.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
  <connectionStrings>
    <add name="QueueServicePath" connectionString="tcp://USNCENG03.ad.diebold.com:8090"/>
  </connectionStrings>
  <userSettings>
        <Uploader.Properties.Settings>
              <setting name="WebSiteAddress" serializeAs="String">
                    <value>http://reliability.diebold.com/</value>
              </setting>
        </Uploader.Properties.Settings>
    </userSettings>
<startup><supportedRuntime version="v2.0.50727"/></startup></configuration>
