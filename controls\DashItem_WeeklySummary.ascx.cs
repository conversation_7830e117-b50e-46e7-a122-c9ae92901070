using System;
using System.Data;
using System.Drawing;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public partial class controls_DashItem_WeeklySummary : System.Web.UI.UserControl, IDashboardItem
{
    public bool UseMasterUser
    {
        get { if (this.ViewState["um"] != null) return (bool)this.ViewState["um"]; else return false; }
        set { this.ViewState["um"] = value; }
    }

    WeeklySummaryInfo wkInfo = null;
    public WeeklySummaryInfo WkInfo
    {
        get
        {
            if (wkInfo != null) { return wkInfo; }
            else
            {
                wkInfo = new WeeklySummaryInfo();
                string ctrlXML = null;

                if (this.UseMasterUser)
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME));
                else
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, this.UserName));

                if (!string.IsNullOrEmpty(ctrlXML))
                    wkInfo = (WeeklySummaryInfo)XmlSerializable.LoadFromXmlText(typeof(WeeklySummaryInfo), ctrlXML);

                return wkInfo;
            }
        }
        set { wkInfo = value; }
    }

    private string UserName = Utility.GetUserName();
    protected Guid UniqueControlGuid = Guid.Empty;

    protected void Page_Load(object sender, EventArgs e)
    {
        string script = "function allSessionsClick(chk, lstId){ var lst = document.getElementById(lstId);\r\nif(lst && chk.checked)\r\nlst.selectedIndex = -1;\r\n}\r\n"
            + "function sessionsListChange(lst, chkId){ var chk = document.getElementById(chkId);\r\nif(chk && lst.selectedIndex >= 0)\r\nchk.checked = false;\r\n}\r\n";
        this.Page.ClientScript.RegisterClientScriptBlock(typeof(ReportHelper), "SessionListToggle", script, true);
        this.allSessionsCheck.Attributes.Add("onclick", string.Format("allSessionsClick(this, '{0}')", CustomizeSessionsList.ClientID));
        this.CustomizeSessionsList.Attributes.Add("onchange", string.Format("sessionsListChange(this, '{0}')", allSessionsCheck.ClientID));
    }

    private void LoadDisplay()
    {
        string key = "DashItemWeeklySummary:" + UniqueControlGuid.ToString("N");
        string key2 = "DashItemWeeklySummary2:" + UniqueControlGuid.ToString("N");

        CellSet cellSet = (CellSet)this.Session[key];
        if (!this.Page.IsPostBack || cellSet == null)
        {
            cellSet = GenerateReportData();

            this.Session[key] = cellSet;
        }

        DataSet ds = (DataSet)this.Session[key2];
        if (!this.Page.IsPostBack || ds == null)
        {
            string filteredSessionList = null;
            if (this.WkInfo != null && this.WkInfo.FilterSessions)
            {
                foreach (int sesId in this.WkInfo.IncludedSessions)
                {
                    filteredSessionList += sesId + ",";
                }
                if (!string.IsNullOrEmpty(filteredSessionList))
                    filteredSessionList = filteredSessionList.Substring(0, filteredSessionList.Length - 1);
            }

            ds = LibrarySource.GetSessions(false, UserName, filteredSessionList);

            this.Session[key2] = ds;
        }

        SessionsRepeater.DataSource = ds;
        SessionsRepeater.DataBind();

        hidDivList.SelectedIndex = 0;

        FormatCharts(cellSet);
    }

    private CellSet GenerateReportData()
    {
        string mdxSetup = null;
        string mdxSelect = null;
        string mdxCategory = null;
        string mdxFromWhere = null;
        string[] mdxDaySet = new string[7];

        mdxSelect = "SELECT NON EMPTY {[Measures].[Transaction Count], [Measures].[Media Count], [Measures].[Observation Count]} on columns,\r\n ";

        for (int x = 1; x <= 7; x++)
        {
            mdxDaySet[x - 1] = "[Time].[Year-Week-Date].[Date].&[" + DateTime.Today.AddDays(0 - x).ToString("yyyy-MM-dd") + "T00:00:00]";
        }

        if (this.WkInfo != null && this.WkInfo.FilterSessions && this.WkInfo.IncludedSessions.Count > 0)
        {
            mdxCategory = string.Format(" NON EMPTY CROSSJOIN({0}, {1}) on rows\r\n", OLAPHelper.BuildMdxSessionTuple(false, this.WkInfo.IncludedSessions),
                "{" + string.Join(",", mdxDaySet) + "}");
            mdxFromWhere = "FROM [Reporting]";
        }
        else
        {
            mdxCategory = string.Format(" NON EMPTY CROSSJOIN([Session].[Session].[Session], {0}) on rows\r\n",
                "{" + string.Join(",", mdxDaySet) + "}");
            mdxFromWhere = "FROM [Reporting] WHERE ([Session].[Status].&[Active])";
        }

        string mdx = mdxSetup + mdxSelect + mdxCategory + mdxFromWhere;

        return OLAPHelper.ExecuteCellSet(false, mdx);
    }

    private void FormatCharts(CellSet cellSet)
    {
        TupleCollection colTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[1].Set.Hierarchies;

        foreach (RepeaterItem rptItem in SessionsRepeater.Items)
        {
            Chart chart = (Chart)rptItem.FindControl("chartCntrl");
            HiddenField hidSess = (HiddenField)rptItem.FindControl("SessionId");

            if (chart != null && hidSess != null && !string.IsNullOrEmpty(hidSess.Value))
            {
                string sessionUniqueName = "[Session].[Session].&[" + hidSess.Value + "]";
                chart.Series.Clear();
                ChartArea tranArea = chart.ChartAreas["Transactions"];
                ChartArea mediaArea = chart.ChartAreas["Media"];
                ChartArea obsArea = chart.ChartAreas["Observations"];

                FormatChartArea(tranArea, 5);
                FormatChartArea(mediaArea, 35);
                FormatChartArea(obsArea, 65);

                Series tranSeries = new Series("Transactions");
                tranSeries.Type = SeriesChartType.Line;
                tranSeries.ChartArea = "Transactions";
                tranSeries.Color = Color.Blue;

                Series mediaSeries = new Series("Media");
                mediaSeries.Type = SeriesChartType.Line;
                mediaSeries.ChartArea = "Media";
                mediaSeries.Color = Color.Blue;

                Series obsSeries = new Series("Observations");
                obsSeries.Type = SeriesChartType.Line;
                obsSeries.ChartArea = "Observations";
                obsSeries.Color = Color.Blue;

                double tranTotal = 0;
                double mediaTotal = 0;
                double obsTotal = 0;

                for (int row = 0; row < rowTuples.Count; row++)
                {
                    Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];
                    if (string.Compare(rowTuple.Members[0].UniqueName, sessionUniqueName) == 0)
                    {
                        Cell tranCell = cellSet.Cells[0, row];
                        Cell mediaCell = cellSet.Cells[1, row];
                        Cell obsCell = cellSet.Cells[2, row];

                        double tranVal = (tranCell.Value == null ? 0 : double.Parse(tranCell.Value.ToString()));
                        double mediaVal = (mediaCell.Value == null ? 0 : double.Parse(mediaCell.Value.ToString()));
                        double obsVal = (obsCell.Value == null ? 0 : double.Parse(obsCell.Value.ToString()));

                        tranSeries.Points.Add(tranVal);
                        mediaSeries.Points.Add(mediaVal);
                        obsSeries.Points.Add(obsVal);

                        tranTotal += tranVal;
                        mediaTotal += mediaVal;
                        obsTotal += obsVal;
                    }
                }

                chart.Series.Add(tranSeries);
                chart.Series.Add(mediaSeries);
                chart.Series.Add(obsSeries);

                System.Web.UI.WebControls.Label tranLabel = (System.Web.UI.WebControls.Label)rptItem.FindControl("TranCount");
                System.Web.UI.WebControls.Label mediaLabel = (System.Web.UI.WebControls.Label)rptItem.FindControl("MediaCount");
                System.Web.UI.WebControls.Label obsLabel = (System.Web.UI.WebControls.Label)rptItem.FindControl("ObsCount");

                tranLabel.Text = tranTotal.ToString("#,##0");
                mediaLabel.Text = mediaTotal.ToString("#,##0");
                obsLabel.Text = obsTotal.ToString("#,##0");
            }
        }
    }

    private void FormatChartArea(ChartArea area, float x)
    {
        area.BackColor = Color.WhiteSmoke;
        area.Position.Width = 25;
        area.Position.Height = 100;
        area.Position.X = x;
        area.InnerPlotPosition.Width = 100;
        area.InnerPlotPosition.Height = 100;
        area.AxisX.LineColor = Color.WhiteSmoke;
        area.AxisX.MajorTickMark.LineColor = Color.WhiteSmoke;
        area.AxisX.LabelStyle.FontColor = Color.WhiteSmoke;
        area.AxisY.LineColor = Color.WhiteSmoke;
        area.AxisY.MajorTickMark.LineColor = Color.WhiteSmoke;
        area.AxisY.LabelStyle.FontColor = Color.WhiteSmoke;
    }

    public void Initialize(Guid dockUniqueName, bool useMasterUser)
    {
        this.UseMasterUser = useMasterUser;
        this.UniqueControlGuid = dockUniqueName;

        CustomizeSessionsList.DataSource = Utility.GetActiveSessionsList();
        CustomizeSessionsList.DataBind();
    }

    public void DisplayEdit()
    {
        RadAjaxManager manager = RadAjaxManager.GetCurrent(this.Parent.Page);
        if (manager != null)
            manager.ResponseScripts.Add(string.Format("document.getElementById('{0}').click();", this.HidEditButton.ClientID));
    }

    public void DisplayContent()
    {
        HeaderPlaceHolder.Controls.Clear();
        hidDivList.Items.Clear();
        LoadDisplay();
    }

    public void EditSettings()
    {
        if (this.WkInfo != null)
        {
            allSessionsCheck.Checked = !this.WkInfo.FilterSessions;
            if (this.WkInfo.FilterSessions)
            {
                foreach (int sesId in this.WkInfo.IncludedSessions)
                {
                    ListItem item = CustomizeSessionsList.Items.FindByValue(sesId.ToString());

                    if (item != null)
                        item.Selected = true;
                }
            }
        }
        SettingsPanel.Visible = true;
        DisplayPanel.Visible = false;
    }

    public void SaveSettings()
    {
        if (allSessionsCheck.Checked)
        {
            this.WkInfo.FilterSessions = false;
            this.WkInfo.IncludedSessions.Clear();
        }
        else
        {
            this.WkInfo.FilterSessions = true;
            this.WkInfo.IncludedSessions.Clear();
            foreach (ListItem item in CustomizeSessionsList.Items)
            {
                if (item.Selected)
                    this.WkInfo.IncludedSessions.Add(Convert.ToInt32(item.Value));
            }
        }

        if (this.UseMasterUser)
            SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME, XmlSerializable.ConvertToXml(this.WkInfo));
        else
            SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, UserName, XmlSerializable.ConvertToXml(this.WkInfo));

        RadAjaxManager manager = RadAjaxManager.GetCurrent(this.Parent.Page);
        if (manager != null)
            manager.ResponseScripts.Add(string.Format("togglePlayPause_{0}('play');", this.UniqueControlGuid.ToString("N")));

        SettingsPanel.Visible = false;
        DisplayPanel.Visible = true;

        string key = "DashItemWeeklySummary:" + UniqueControlGuid.ToString("N");
        string key2 = "DashItemWeeklySummary2:" + UniqueControlGuid.ToString("N");
        this.Session[key] = null;
        this.Session[key2] = null;
    }

    protected void SessionsRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        System.Web.UI.WebControls.Label lbl = new System.Web.UI.WebControls.Label();
        LiteralControl space = new LiteralControl("&nbsp;");
        HtmlGenericControl contentDiv = new HtmlGenericControl();
        Repeater rptRepeater = new Repeater();
        System.Web.UI.WebControls.Label noReportsLbl = new System.Web.UI.WebControls.Label();

        if (e.Item != null && e.Item.DataItem != null)
        {
            lbl.Text = Convert.ToString(e.Item.ItemIndex + 1);
            lbl.ID = "pagingButton" + Convert.ToString(e.Item.ItemIndex + 1);

            //Create the Control Id
            string[] nameParts = HeaderPlaceHolder.ClientID.Split(new string[] { "_" }, StringSplitOptions.RemoveEmptyEntries);
            string newId = null;
            for (int i = 0; i < nameParts.Length - 1; i++)
            {
                newId += nameParts[i] + "_";
            }
            lbl.Attributes.Add("onclick", "togglePlayPause_" + this.UniqueControlGuid.ToString("N") + "('pause');toggleSelectedDiv_" + this.UniqueControlGuid.ToString("N") + "('" + newId + lbl.ID + "');");

            if (e.Item.ItemIndex == 0)
            {
                lbl.Attributes.Add("class", "pagingDiv_selected");
                //hidFirstSelectedDiv.Value = newId + lbl.ID;
            }
            else
            {
                lbl.Attributes.Add("class", "pagingDiv");
            }

            HeaderPlaceHolder.Controls.Add(lbl);
            HeaderPlaceHolder.Controls.Add(space);

            //add the div names to a hidden list for client side display changes later
            if (e.Item.FindControl("SessionDisplayDiv") != null)
                contentDiv = (HtmlGenericControl)e.Item.FindControl("SessionDisplayDiv");
            if (contentDiv != null)
            {
                hidDivList.Items.Add(new ListItem(newId + lbl.ID, contentDiv.ClientID));
            }
            //Hide all added divs except first
            if (contentDiv != null && e.Item.ItemIndex > 0)
                contentDiv.Attributes.Add("style", "display:none");

            //display message if no reports
            if (e.Item.FindControl("ReportRepeater") != null)
                rptRepeater = (Repeater)e.Item.FindControl("ReportRepeater");
            if (e.Item.FindControl("NoReportsLabel") != null)
                noReportsLbl = (System.Web.UI.WebControls.Label)e.Item.FindControl("NoReportsLabel");
            if (noReportsLbl != null && rptRepeater != null && rptRepeater.Items.Count == 0)
                noReportsLbl.Visible = true;
        }
    }

    protected void SaveSettings_Click(object sender, EventArgs e)
    {
        SaveSettings();
    }

    protected void EditSettings_Click(object sender, EventArgs e)
    {
        EditSettings();
    }

    protected void RunRpt(object sender, CommandEventArgs e)
    {
        ReportInfo repInfo = new ReportInfo();
        repInfo.ReportTypeId = ReportHelper.ReportTypeEnum.GENERAL;

        SessionInfo sessInfo = new SessionInfo();
        sessInfo.SessionId = Int32.Parse(e.CommandArgument.ToString());
        repInfo.AttachedSessions.Add(sessInfo);
		repInfo.FixedStartDate = DateTime.Today.AddDays(-7);
		repInfo.FixedEndDate = DateTime.Today;

        switch (e.CommandName)
        {
            case "Media":
                repInfo.ReportName = "Daily Summary - Media Volume";
                repInfo.PrebuildType = ReportHelper.PrebuiltReportsEnum.DAILY_MEDIA_VOLUME;
                break;
            case "Observation":
                repInfo.ReportName = "Daily Summary - Observation Volume";
                repInfo.PrebuildType = ReportHelper.PrebuiltReportsEnum.DAILY_OBSERVATION_VOLUME;
                break;
            case "Transaction":
            default:
                repInfo.ReportName = "Daily Summary - Transaction Volume";
                repInfo.PrebuildType = ReportHelper.PrebuiltReportsEnum.DAILY_TRANSACTION_VOLUME;
                break;
        }

        Utility.SetReportInfoForTransfer(repInfo);
        Response.Redirect(repInfo.RunPageName);
    }

    protected void CancelSettings_Click(object sender, EventArgs e)
    {
        RadAjaxManager manager = RadAjaxManager.GetCurrent(this.Parent.Page);
        if (manager != null)
            manager.ResponseScripts.Add(string.Format("togglePlayPause_{0}('play');", this.UniqueControlGuid.ToString("N")));

        SettingsPanel.Visible = false;
        DisplayPanel.Visible = true;
    }

    protected void RunButton_Command(object sender, CommandEventArgs e)
    {
        if (e.CommandArgument != null)
        {
            ReportInfo rpt = Utility.LoadReportInfo(Convert.ToInt32(e.CommandArgument));

            if (rpt.PromptSessions)
            {
                foreach (RadWindow win in RadWindowManager.Windows)
                {
                    if (win.ID == "PromptSessionsWindow")
                    {
                        win.NavigateUrl = "promptsessions.aspx?r=" + e.CommandArgument.ToString();
                        win.VisibleOnPageLoad = true;
                    }
                }
            }
            else
            {
                Response.Redirect(rpt.RunPageName);
            }
        }
    }
}
