using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class DistributionInfo : ISerializable
{
	public decimal Target = decimal.MinValue;
    public bool ShowHistogram = false;
    public bool ShowNormalDistribution = true;
	public bool SplitByDevice = false;
    public decimal LowerSpecLimit = decimal.MinValue;
	public decimal UpperSpecLimit = decimal.MinValue;
	public decimal FilterStartValue = decimal.MinValue;
    public decimal FilterEndValue = decimal.MinValue;
	public decimal XAxisStartValue = decimal.MinValue;
	public decimal XAxisEndValue = decimal.MinValue;
	
	// Default constructor.
	public DistributionInfo() { }

	// Deserialization constructor.
	public DistributionInfo(SerializationInfo info, StreamingContext context)
	{
		Target = (decimal)info.GetValue("t", typeof(decimal));
        ShowHistogram = (bool)info.GetValue("h", typeof(bool));
        ShowNormalDistribution = (bool)info.GetValue("n", typeof(bool));
		SplitByDevice = (bool)info.GetValue("sbd", typeof(bool));
        LowerSpecLimit = (decimal)info.GetValue("l", typeof(decimal));
		UpperSpecLimit = (decimal)info.GetValue("u", typeof(decimal));
		FilterStartValue = (decimal)info.GetValue("fs", typeof(decimal));
		FilterEndValue = (decimal)info.GetValue("fe", typeof(decimal));
		XAxisStartValue = (decimal)info.GetValue("xs", typeof(decimal));
		XAxisEndValue = (decimal)info.GetValue("xe", typeof(decimal));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
        info.AddValue("t", Target);
        info.AddValue("h", ShowHistogram);
        info.AddValue("n", ShowNormalDistribution);
		info.AddValue("sbd", SplitByDevice);
        info.AddValue("l", LowerSpecLimit);
		info.AddValue("u", UpperSpecLimit);
		info.AddValue("fs", FilterStartValue);
		info.AddValue("fe", FilterEndValue);
		info.AddValue("xs", XAxisStartValue);
		info.AddValue("xe", XAxisEndValue);
	}
}