﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using Telerik.Web.UI;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public partial class PrintSensorReport : System.Web.UI.Page
{
	protected BaseReport ReportObj = null;
	List<SensorChartInfo> chartColl = null;

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	public bool IsReportLoaded
	{
		get { if (this.ViewState["ld"] != null) { return (bool)this.ViewState["ld"]; } else { return false; } }
		set { this.ViewState["ld"] = value; }
	}

    public SubReportInfo SubReportInfo
    {
        get { if (this.ViewState["sr"] != null) { return (SubReportInfo)this.ViewState["sr"]; } else { return null; } }
        set { this.ViewState["sr"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]))
                this.SubReportInfo = Utility.LoadSubReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]));
            else
                this.SubReportInfo = Utility.GetSubReportInfoFromTransfer();

            this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, this.SubReportInfo);

			this.ReportObj.LoadData();

			chartColl = new List<SensorChartInfo>();
			TupleCollection colTuples = ((SensorReport)this.ReportObj).GetCellSet().Axes[0].Set.Tuples;

			// Iterate over chart columns to determine which when to begin a new chart
			string previousSensor = null;
			for (int col = 0; col < colTuples.Count; col++)
			{
                Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
				string[] seriesNameParts = colTuple.Members[0].Caption.Split(new string[] { ":" }, StringSplitOptions.RemoveEmptyEntries);
				string sensorName = seriesNameParts[0];

				if (!string.IsNullOrEmpty(previousSensor))
				{
					if (!previousSensor.Equals(sensorName))
					{
						SensorChartInfo chartInfo = new SensorChartInfo();

						if (chartColl.Count == 0)
						{
							chartInfo.StartColumnIndex = 0;
							chartInfo.EndColumnIndex = col - 1;
						}
						else
						{
							chartInfo.StartColumnIndex = ((SensorChartInfo)chartColl[chartColl.Count - 1]).EndColumnIndex + 1;
							chartInfo.EndColumnIndex = col - 1;
						}

						chartColl.Add(chartInfo);
					}
				}

				previousSensor = sensorName;
			}

			//Add in final value
			SensorChartInfo cInfo = new SensorChartInfo();
			cInfo.StartColumnIndex = ((SensorChartInfo)chartColl[chartColl.Count - 1]).EndColumnIndex + 1;
			cInfo.EndColumnIndex = colTuples.Count - 1;
			chartColl.Add(cInfo);

			ChartRepeater.DataSource = chartColl;
			ChartRepeater.DataBind();

			this.IsReportLoaded = true;

			//PDF mode, set parameters and render for PDF
			if (!string.IsNullOrEmpty(Request.QueryString["pdf"]))
			{
				this.PrintControlsPanel.Style.Add("display", "none");

				if (!string.IsNullOrEmpty(Request.QueryString["layout"]))
					this.layoutList.SelectedValue = Request.QueryString["layout"];

				//if (!string.IsNullOrEmpty(Request.QueryString["chart"]))
				//    chartList.SelectedValue = Request.QueryString["chart"];

				//if (!string.IsNullOrEmpty(Request.QueryString["data"]))
				//    dataList.SelectedValue = Request.QueryString["data"];

				//if (!string.IsNullOrEmpty(Request.QueryString["splitData"]))
				//    dataOnNewPageCheck.Checked = Convert.ToBoolean(Request.QueryString["splitData"]);

				//if (chartList.SelectedValue == "hide")
				//    this.ChartZoomDiv.Style.Add("display", "none");
				//else if (chartList.SelectedValue != "fit")
				//    this.ChartZoomDiv.Style.Add("zoom", chartList.SelectedValue);

				//if (dataList.SelectedValue == "hide")
				//    this.DataZoomDiv.Style.Add("display", "none");
				//else if (dataList.SelectedValue != "fit")
				//    this.DataZoomDiv.Style.Add("zoom", dataList.SelectedValue);

				//if (dataOnNewPageCheck.Checked)
				//    this.ChartZoomDiv.Style.Add("page-break-after", "always");
				//else
				//    this.ChartZoomDiv.Style.Add("page-break-after", "");

				//if request is from the Queue Service, return the PDF immediately
				if (!string.IsNullOrEmpty(Request.QueryString["qs"]))
					PDFButton_Click(null, null);
			}
		}
    }

	protected void ChartRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
	{
		if (e.Item != null && e.Item.DataItem != null)
		{
			if (e.Item.FindControl("chartCntrl") != null)
			{
				Chart chartObj = (Chart)e.Item.FindControl("chartCntrl");
				this.ReportObj.InitializeChart(chartObj, "RunSensorReport");

				((SensorReport)this.ReportObj).PopulateChart(chartObj, true, ((SensorChartInfo)chartColl[e.Item.ItemIndex]).StartColumnIndex, ((SensorChartInfo)chartColl[e.Item.ItemIndex]).EndColumnIndex);

                chartObj.RenderType = RenderType.ImageTag;
                chartObj.ImageType = ChartImageType.Jpeg;
                chartObj.MapEnabled = false;
                chartObj.UI.Toolbar.Enabled = false;
                chartObj.UI.ContextMenu.Enabled = false;
                
                //GridBindingPanel.DataBind();
			}
			else
			{
				throw new ApplicationException("Unable to find the chart object.");
			}
		}
	}

	protected void PDFButton_Click(object sender, EventArgs e)
	{
		PrintControlsPanel.Visible = false;

		int subReportId = 0;
		if (this.SubReportInfo != null)
			subReportId = this.SubReportInfo.SubReportId;

		string tempFileName = ReportHelper.GeneratePDF(this.RepInfo, this.RepInfo.PrintPageName, layoutList.SelectedValue, "", "", false, subReportId);
		string displayName = this.RepInfo.ReportName + "_" + DateTime.Now.Ticks + ".pdf";
        if (this.SubReportInfo != null)
            displayName = this.SubReportInfo.SubReportName + "_" + DateTime.Now.Ticks + ".pdf";

		try
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = "application/pdf";
			Response.AppendHeader("content-disposition", "attachment; filename=" + displayName + ";");

			Response.WriteFile(tempFileName);
			Response.Flush();
		}
		finally
		{
			if (!string.IsNullOrEmpty(tempFileName) && File.Exists(tempFileName))
				File.Delete(tempFileName);
		}

		Response.End();
	}
}
