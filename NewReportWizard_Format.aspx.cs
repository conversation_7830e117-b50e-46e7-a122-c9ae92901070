using System;
using System.Data;
using System.Configuration;
using System.Collections.Generic;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class NewReportWizard_Format : System.Web.UI.Page
{
    private string[] IncludedLevels = new string[] { "[Cell].[Status - Cell].[Status]", "[Device].[Type - Device].[Type]", "[Setting].[Device - Setting - Option].[Device]", "[Setting 2].[Device - Setting - Option].[Device]", "[Setting 3].[Device - Setting - Option].[Device]", "[Media Number].[Media Number].[Media Number]", "[File Number].[File Number].[File Number]", "[Command Number].[Command Number].[Command Number]" };

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			this.RepInfo = Utility.GetReportInfoFromTransfer();
			if (this.RepInfo == null)
				Response.Redirect("popuperror.aspx");

            PopulateSessionList(this.RepInfo.AttachedSessions);
			PopulateCellList(this.RepInfo.CellFilters);

			RelativeStartList.DataSource = Utility.GetRelativeTimeList();
			RelativeStartList.DataBind();

			RelativeEndList.DataSource = Utility.GetRelativeTimeList();
			RelativeEndList.DataBind();

            reportNameLabel.Text = this.RepInfo.ReportName;
			reportTypeLabel.Text = this.RepInfo.ReportTypeName;

			if (this.RepInfo.RelativeStartTimeId != ReportHelper.RelativeTimeEnum.UNSPECIFIED)
			{
				//Relative Time
				this.StartDateTypeList.SelectedIndex = 1;
				StartDateTypeList_IndexChanged(null, null);
				this.RelativeStartList.SelectedValue = ((int)this.RepInfo.RelativeStartTimeId).ToString();

				if (this.RepInfo.FixedStartDate.TimeOfDay != TimeSpan.Zero)
					startTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedStartDate.ToString("hh:mm:ss tt"));
			}
			else
			{
				this.StartDateTypeList.SelectedIndex = 0;

				if (this.RepInfo.FixedStartDate != DateTime.MinValue)
					startDateField.SelectedDate = this.RepInfo.FixedStartDate.Date;

				if (this.RepInfo.FixedStartDate.TimeOfDay != TimeSpan.Zero)
					startTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedStartDate.ToString("hh:mm:ss tt"));
			}

			if (this.RepInfo.RelativeEndTimeId != ReportHelper.RelativeTimeEnum.UNSPECIFIED)
			{
				//Relative Time
				this.EndDateTypeList.SelectedIndex = 1;
				EndDateTypeList_IndexChanged(null, null);
				this.RelativeEndList.SelectedValue = ((int)this.RepInfo.RelativeEndTimeId).ToString();
				
				if (this.RepInfo.FixedEndDate.TimeOfDay != TimeSpan.Zero)
					endTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedEndDate.ToString("hh:mm:ss tt"));
			}
			else
			{
				this.EndDateTypeList.SelectedIndex = 0;

				if (this.RepInfo.FixedEndDate != DateTime.MinValue)
					endDateField.SelectedDate = this.RepInfo.FixedEndDate.Date;

				if (this.RepInfo.FixedEndDate.TimeOfDay != TimeSpan.Zero)
					endTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedEndDate.ToString("hh:mm:ss tt"));
			}

			if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
			{
				furtherLimitLabel.Text = "Limit By Cell:";
				reportFilterSelector.Visible = false;
			}
			else
			{
				furtherLimitLabel.Text = "Further Limit Report:";
				cellList.Visible = false;

				this.reportFilterSelector.PopulateDimensions(false, IncludedLevels);
				this.reportFilterSelector.SetSelectedReportFilters(this.RepInfo.ReportFilters);
			}

            subReportSessCheck.Checked = this.RepInfo.SubReportsBySession;
            subReportDeviceCheck.Checked = this.RepInfo.SubReportsByDevice;

			sessionsList.Focus();	
		}

        switch(this.RepInfo.ReportTypeId)
        {
            case ReportHelper.ReportTypeEnum.DISTRIBUTION:
            case ReportHelper.ReportTypeEnum.SENSOR:
                subReportSessCheck.Visible = true;
                subReportDeviceCheck.Visible = true;
                    break;
            default:
                subReportSessCheck.Visible = false;
                subReportDeviceCheck.Visible = false;
                break;
        }
	}

    private void PopulateSessionList(List<SessionInfo> sessionFilters)
    {
        sessionsList.DataSource = Utility.GetActiveSessionsList();
        sessionsList.DataBind();

        if (sessionFilters != null)
        {
            foreach (SessionInfo curSession in sessionFilters)
            {
                if (sessionsList.Items.FindByValue(curSession.SessionId.ToString()) == null)
                    sessionsList.Items.Insert(0, new ListItem(curSession.SessionName, curSession.SessionId.ToString()));

                ((ListItem)sessionsList.Items.FindByValue(curSession.SessionId.ToString())).Selected = true;
            }
        }
    }

	private void PopulateCellList(List<CellInfo> cellFilters)
	{
		cellList.DataSource = Utility.GetCellList();
		cellList.DataBind();

		if (cellFilters != null)
		{
			foreach (CellInfo curCell in cellFilters)
			{
				if (cellList.Items.FindByValue(curCell.CellId.ToString()) == null)
					cellList.Items.Insert(0, new ListItem(curCell.CellName, curCell.CellId.ToString()));

				((ListItem)cellList.Items.FindByValue(curCell.CellId.ToString())).Selected = true;
			}
		}
	}

	protected void NextButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			TimeSpan startTime = TimeSpan.Zero;
			TimeSpan endTime = TimeSpan.Zero;

			if (startTimeField.SelectedDate != null && startTimeField.SelectedDate.HasValue)
			{
				startTime = new TimeSpan(((DateTime)startTimeField.SelectedDate).Hour, ((DateTime)startTimeField.SelectedDate).Minute, ((DateTime)startTimeField.SelectedDate).Second);
				if (startTime.Minutes % 30 != 0)
				{
					timeError.Visible = true;
					return;
				}
			}

			if (endTimeField.SelectedDate != null && endTimeField.SelectedDate.HasValue)
			{
				endTime = new TimeSpan(((DateTime)endTimeField.SelectedDate).Hour, ((DateTime)endTimeField.SelectedDate).Minute, ((DateTime)endTimeField.SelectedDate).Second);
				if (endTime.Minutes % 30 != 0)
				{
					timeError.Visible = true;
					return;
				}
			}

			if (StartDateTypeList.SelectedValue.Equals("fixed"))
			{
				this.RepInfo.RelativeStartTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;

				if (startDateField.SelectedDate.HasValue)
					this.RepInfo.FixedStartDate = ((DateTime)startDateField.SelectedDate).Add(startTime);
				else
					this.RepInfo.FixedStartDate = DateTime.MinValue;
			}
			else
			{
				//Relative Time
				if (!string.IsNullOrEmpty(RelativeStartList.SelectedValue))
				{
					this.RepInfo.RelativeStartTimeId = (ReportHelper.RelativeTimeEnum)Convert.ToInt32(RelativeStartList.SelectedValue);
					this.RepInfo.FixedStartDate = new DateTime(1900, 1, 1).Add(startTime);
				}
				else
				{
					this.RepInfo.FixedStartDate = DateTime.MinValue;
				}
			}

			if (EndDateTypeList.SelectedValue.Equals("fixed"))
			{
				this.RepInfo.RelativeEndTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;

				if (endDateField.SelectedDate.HasValue)
					this.RepInfo.FixedEndDate = ((DateTime)endDateField.SelectedDate).Add(endTime);
				else
					this.RepInfo.FixedEndDate = DateTime.MinValue;
			}
			else
			{
				//Relative Time
				if (!string.IsNullOrEmpty(RelativeEndList.SelectedValue))
				{
					this.RepInfo.RelativeEndTimeId = (ReportHelper.RelativeTimeEnum)Convert.ToInt32(RelativeEndList.SelectedValue);
					this.RepInfo.FixedEndDate = new DateTime(1900, 1, 1).Add(endTime);
				}
				else
				{
					this.RepInfo.FixedEndDate = DateTime.MinValue;
				}
			}
            
			this.RepInfo.AttachedSessions = new List<SessionInfo>();
			SessionInfo curSession = null;
			foreach (ListItem item in sessionsList.Items)
			{
				if (item.Selected)
				{
					curSession = new SessionInfo();
					curSession.SessionId = Convert.ToInt32(item.Value);
					curSession.SessionName = item.Text;

					this.RepInfo.AttachedSessions.Add(curSession);
				}
			}

			if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
			{
				this.RepInfo.CellFilters = new List<CellInfo>();
				CellInfo curCell = null;
				foreach (ListItem item in cellList.Items)
				{
					if (item.Selected)
					{
						curCell = new CellInfo();
						curCell.CellId = Convert.ToInt32(item.Value);
						curCell.CellName = item.Text;

						this.RepInfo.CellFilters.Add(curCell);
					}
				}
			}
			else
			{
				this.RepInfo.ReportFilters = this.reportFilterSelector.GetSelectedReportFilters();
			}

            switch (this.RepInfo.ReportTypeId)
            {
                case ReportHelper.ReportTypeEnum.DISTRIBUTION:
                case ReportHelper.ReportTypeEnum.SENSOR:
                    this.RepInfo.SubReportsBySession = subReportSessCheck.Checked;
                    this.RepInfo.SubReportsByDevice = subReportDeviceCheck.Checked;
                    break;
                default:
                    this.RepInfo.SubReportsBySession = false;
                    this.RepInfo.SubReportsByDevice = false;
                    break;
            }

			Utility.SetReportInfoForTransfer(this.RepInfo);
			
			//Close the window and redirect parent to the Run Report screen.
            string closeScript = "<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.location.href='" + this.RepInfo.RunPageName + "';\r\nCloseRadWindow(); </script>";
			Page.ClientScript.RegisterStartupScript(typeof(NewReportWizard_Format), "CloseScript", closeScript);
		}
	}

	protected void StartDateTypeList_IndexChanged(object sender, EventArgs e)
	{
		if (StartDateTypeList.SelectedValue.Equals("fixed"))
		{
			this.startDateField.Style.Add(HtmlTextWriterStyle.Display, "");
			this.RelativeStartList.Style.Add(HtmlTextWriterStyle.Display, "none");
		}
		else
		{
			this.startDateField.Style.Add(HtmlTextWriterStyle.Display, "none");
			this.RelativeStartList.Style.Add(HtmlTextWriterStyle.Display, "");
		}
	}

	protected void EndDateTypeList_IndexChanged(object sender, EventArgs e)
	{
		if (EndDateTypeList.SelectedValue.Equals("fixed"))
		{
			this.endDateField.Style.Add(HtmlTextWriterStyle.Display, "");
			this.RelativeEndList.Style.Add(HtmlTextWriterStyle.Display, "none");
		}
		else
		{
			this.endDateField.Style.Add(HtmlTextWriterStyle.Display, "none");
			this.RelativeEndList.Style.Add(HtmlTextWriterStyle.Display, "");
		}
	}

    protected void ValidateSubReport(object sender, ServerValidateEventArgs e)
    {
        if (subReportDeviceCheck.Checked)
        {
            Dictionary<string, List<string>> filters = this.reportFilterSelector.GetSelectedReportFilters();
            e.IsValid = (filters != null && filters.ContainsKey(DieboldConstants.DEVICE_FILTER_DIMENSION_NAME) && filters[DieboldConstants.DEVICE_FILTER_DIMENSION_NAME].Count > 0) ;
        }
        else
        {
            e.IsValid = true;
        }
    }
}
