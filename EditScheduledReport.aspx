﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditScheduledReport.aspx.cs" Inherits="EditScheduledReport" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">
    var reportList = new Array(<%= this.ReportsJSON %>);
    var subReportExpandList = '<%= Session["scheduledSubReportExpandList"] %>';

    function saveToFileCheckClick(e) {
	    var timeAmt = (e?400:0);
		if ($('#<%= saveToFileCheck.ClientID %>').attr('checked'))
			$('#saveToFileDiv').slideDown(timeAmt);
		else
		    $('#saveToFileDiv').slideUp(timeAmt);
	}
	function emailCheckClick(e) {
	    var timeAmt = (e?400:0);
		if ($('#<%= receiveEmailCheck.ClientID %>').attr('checked'))
			$('#emailDiv').slideDown(timeAmt);
		else
			$('#emailDiv').slideUp(timeAmt);
	}
	function printCheckClick(e) {
	    var timeAmt = (e?400:0);
		if ($('#<%= printCheck.ClientID %>').attr('checked'))
			$('#printDiv').slideDown(timeAmt);
		else
			$('#printDiv').slideUp(timeAmt);
	}
	function toggleReport(e) {
        if(e) {
            e.stopPropagation();
            e.preventDefault();
        }
        var parentRow = $(this);
        if($('TD', parentRow).size() == 0)
            parentRow = $(this).parents('tr:first');
        var rptId = $('.expandButton,.collapseButton', parentRow).attr('rptId');
        var shouldExpand = ($('.expandButton', parentRow).size() > 0);
        var shouldCollapse = ($('.collapseButton', parentRow).size() > 0);
        if(!shouldExpand && !shouldCollapse)
            shouldExpand = true;

        if(shouldExpand) {
            $('.expandButton', parentRow).removeClass('expandButton').addClass('collapseButton');
            parentRow.nextUntil('[rptId!=' + rptId + ']').show();
            subReportExpandList = subReportExpandList + ',' + rptId;
        } else {
            $('.collapseButton', parentRow).removeClass('collapseButton').addClass('expandButton');
            parentRow.nextUntil('[rptId!=' + rptId + ']').hide();
            if(subReportExpandList) {
                var regObj = new RegExp(',' + rptId,'g');
                subReportExpandList = subReportExpandList.replace(regObj, '');
                if(!subReportExpandList || subReportExpandList.length == 0)
                    subReportExpandList = ',0';
            }
        }
		$('<div></div>').load('EditScheduledReport.aspx', {subReportExpandList: subReportExpandList});
	    return false;
	}
    function buildReportRow(item, parentItem, isAlt) {
        var cellClass = (isAlt?'repeaterSubItemAlt':'repeaterSubItem');
        var rowItem = $('<tr></tr>').attr('rptId', item.id);
        var btnClass = 'expandButton';
        if(!item.sub || item.sub.length == 0)
            btnClass = 'noChildrenButton';
        else
            rowItem.hover(function() { $('TD' ,$(this)).css('background-color', '#bcdaf2'); }, function() { $('TD' ,$(this)).css('background-color', ''); });
        rowItem.appendTo(parentItem);
        $('<td style="padding-left:0px;padding-right:0px;width:14px;"></td>').addClass(cellClass).addClass(btnClass).attr('rptId', item.id).click(toggleReport).appendTo(rowItem);
        $('<td style="padding-left:0px;"></td>').addClass(cellClass).append($('<div style="width:300px;overflow:hidden;"></div>').text(item.name).attr('title', item.name)).click(toggleReport).appendTo(rowItem);
        if(item.sub && item.sub.length > 0) {
            $('<td></td>').addClass(cellClass).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).append($('<input type="checkbox" name="reportId" />').val(item.id).click(mainRptCheck)).appendTo(rowItem);
        } else {
            $('<td></td>').addClass(cellClass).text(item.orient).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).text(item.chartScale).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).text(item.dataScale).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).text(item.separate).click(toggleReport).appendTo(rowItem);
            $('<td></td>').addClass(cellClass).append($('<input type="checkbox" name="reportId" />').val(item.id)).appendTo(rowItem);
        }

        $.each(item.sub, function(subindex, subitem) {
            buildSubReportRow(subitem, parentItem, isAlt, item.id);
        });
    }
    function buildSubReportRow(item, parentItem, isAlt, rptId) {
        var cellClass = (isAlt?'repeaterSubItemAlt':'repeaterSubItem');
        var rowItem = $('<tr></tr>').attr('rptId', rptId);
        rowItem.hide().appendTo(parentItem);
        $('<td style="padding-left:0px;padding-right:0px;width:14px;">&nbsp;</td>').addClass(cellClass).appendTo(rowItem);
        $('<td style="padding-left:14px;"></td>').addClass(cellClass).append($('<div style="width:286px;overflow:hidden;"></div>').text(item.name).attr('title', item.name)).appendTo(rowItem);
        $('<td></td>').addClass(cellClass).text(item.orient).appendTo(rowItem);
        $('<td></td>').addClass(cellClass).text(item.chartScale).appendTo(rowItem);
        $('<td></td>').addClass(cellClass).text(item.dataScale).appendTo(rowItem);
        $('<td></td>').addClass(cellClass).text(item.separate).appendTo(rowItem);
        $('<td></td>').addClass(cellClass).append($('<input type="checkbox" name="subReportId" />').val(item.id).click(subRptCheck)).appendTo(rowItem);
    }
    var inCheckChange = false;
    function mainCheck(e) {
        if(!inCheckChange) {
            inCheckChange = true;
            var checked = ($(this).attr('checked'));
            $('TD input', $('#reportList')).attr('checked', checked);
            $(this).blur();
            inCheckChange = false;
        }
    }
    function minorCheck(e) {
        if(!inCheckChange) {
            inCheckChange = true;
            $('TH input:checkbox', $('#reportList')).attr('checked', false);
            $(this).blur();
            inCheckChange = false;
        }
    }
    function mainRptCheck(e) {
        if(!inCheckChange) {
            inCheckChange = true;
            var checked = ($(this).attr('checked'));
            var parentRow = $(this).parents('tr:first');
            $.each(parentRow.nextUntil('[rptId!=' + $(this).val() + ']'), function(index, item) {
                $('input:checkbox', $(item)).attr('checked', checked);
            });
            $(this).blur();
            inCheckChange = false;
        }
    }
    function subRptCheck(e) {
        if(!inCheckChange) {
            inCheckChange = true;
            var parentRow = $(this).parents('tr:first');
            var mainRptRow = null;
            $.each(parentRow.prevUntil('[rptId!=' + parentRow.attr('rptId') + ']'), function(index, item) {
                mainRptRow = $(item);
            });
            if(mainRptRow)
                $('input:checkbox', mainRptRow).attr('checked', false);
            $(this).blur();
            inCheckChange = false;
        }
    }
    function selectAction(e) {
        var action = $(this).val();
        if (action == "Add" || action == "Format" || action == "MoveTop"
             || action == "MoveUp" || action == "MoveDown" || action == "MoveBottom") {
            $('form').submit();
        } else if(action == "Remove") {
            if(confirm('Are you sure you wish to remove the selected items?'))
                $('form').submit();
        } else {
            if(action && action.length > 0)
                $(this).val('').blur();
        }
    }
    $(function () {
        $('#<%= saveToFileCheck.ClientID %>').click(saveToFileCheckClick);
		$('#<%= receiveEmailCheck.ClientID %>').click(emailCheckClick);
        $('#<%= printCheck.ClientID %>').click(printCheckClick);
        saveToFileCheckClick();
		emailCheckClick();
		printCheckClick();

		$('#<%= numCopies.ClientID %>').keydown(function(event) {
			// Allow only backspace and delete
			if (event.keyCode == 46 || event.keyCode == 8) {
				// let it happen, don't do anything
			}
			else {
				// Ensure that it is a number and stop the keypress
				if (event.keyCode < 48 || event.keyCode > 57) {
					event.preventDefault();
				}
			}
		});
		
		var isAlt = false;
        $.each(reportList, function(index, item) {
            $(buildReportRow(item, $('#reportList'), isAlt));
            isAlt = !isAlt;
        });

        var subArr = subReportExpandList.split(',');
        if(subArr && subArr.length > 0) {
            $.each(subArr, function(index, item) {
                $('.expandButton[rptId=' + item + ']').click();
            });
        }
        $('TH input:checkbox', $('#reportList')).click(mainCheck);
        $('TD input:checkbox', $('#reportList')).click(minorCheck);
        $('TH select', $('#reportList')).change(selectAction);
	});
</script>

<asp:panel id="DefaultPanel" runat="server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Schedule Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Edit Scheduled Report</div>
					<div id="NewSnapshotArea" runat="server">
						<table border="0" cellpadding="0" cellspacing="13" width="100%">
							<tr>
								<td class="rowHeading" style="width:150px;">Report Name</td>
								<td>
									<asp:textbox id="scheduledReportName" runat="server" width="250"></asp:textbox>
									<asp:requiredfieldvalidator id="v1" runat="server" controltovalidate="scheduledReportName" errormessage="* Required"></asp:requiredfieldvalidator>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Scheduled Time</td>
								<td>
									<div style="width:120px;">
										<telerik:radtimepicker id="timeField" runat="server" timepopupbutton-visible="true" width="120">
											<dateinput runat="server" dateformat="hh:mm tt"></dateinput>
											<timeview runat="server" interval="00:30:0" columns="4" width="300" height="400" timeformat="hh:mm tt" culture="en-US"></timeview>
										</telerik:radtimepicker>
									</div>
									<asp:requiredfieldvalidator id="v13" runat="server" controltovalidate="timeField" errormessage="* Required"></asp:requiredfieldvalidator>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Schedule Day(s)</td>
								<td>
									<asp:checkboxlist id="scheduleList" repeatlayout="table" repeatcolumns="4" repeatdirection="Horizontal" runat="server">
										<asp:listitem text="Sunday" value="sunday"></asp:listitem>
										<asp:listitem text="Monday" value="Monday"></asp:listitem>
										<asp:listitem text="Tuesday" value="Tuesday"></asp:listitem>
										<asp:listitem text="Wednesday" value="Wednesday"></asp:listitem>
										<asp:listitem text="Thursday" value="Thursday"></asp:listitem>
										<asp:listitem text="Friday" value="Friday"></asp:listitem>
										<asp:listitem text="Saturday" value="Saturday"></asp:listitem>
									</asp:checkboxlist>
								</td>
							</tr>
                            <tr>
								<td class="rowHeading" style="width:150px;">Save to PDF File</td>
								<td>
									<asp:checkbox id="saveToFileCheck" checked="false" causesvalidation="false" runat="server" text=" Save the report(s) PDF to a custom file location" />
									<br /><br />
									<div id="saveToFileDiv" style="display:none;">
										<asp:textbox id="saveToFilePathBox" width="650" runat="server"></asp:textbox>
										<div style="font-weight:normal;font-size:10px;color:#777;">Input full server file path i.e. "\\ServerName\DirectoryName\FileName.pdf</div>
                                        <div id="invalidExtensionMsg" runat="server" visible="false" style="color:#c00;">Invalid file type, files must end with .pdf.</div>
									</div>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Email Report</td>
								<td>
									<asp:checkbox id="receiveEmailCheck" checked="false" causesvalidation="false" runat="server" text=" Email the report(s) when prepared" />
									<br /><br />
									<div id="emailDiv" style="display:none;">
										<asp:textbox id="emailAddressBox" width="650" runat="server"></asp:textbox>
										<div style="font-weight:normal;font-size:10px;color:#777;">Seperate multiple email addresses with semi-colons</div>
                                        <div style="font-weight:normal;font-size:10px;color:#777;">For SharePoint, use email address: <EMAIL></div>
									</div>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Print Report</td>
								<td>
									<asp:checkbox id="printCheck" checked="false" causesvalidation="false" runat="server" text=" Print the report(s) when prepared" />
									<br /><br />
									<div id="printDiv" style="display:none;">
										<asp:textbox id="printerPath" width="300" runat="server"></asp:textbox>
										<div style="font-weight:normal;font-size:10px;color:#777;">Input full path to printer. i.e. "\\ServerName\PrinterName"</div>
										<br />
										# Copies: <asp:textbox id="numCopies" maxlength="3" width="30" runat="server"></asp:textbox>
									</div>
								</td>
							</tr>
						</table>
						
						<div style="padding-left:14px;padding-top:20px;">
						    <table id="reportList" cellpadding="0" cellspacing="0" width="100%" border="0">
						        <tr>
						            <th class="gridHeading" colspan="2">Included Reports</th>
						            <th class="gridHeading">Page Layout</th>
						            <th class="gridHeading">Chart Scale</th>
						            <th class="gridHeading">Data Scale</th>
						            <th class="gridHeading">Separate Data</th>
						            <th class="gridHeading">
    						            <input type="checkbox" />
						                <select id="SelectAction" name="SelectAction">
						                    <option value="">Action...</option>
						                    <option value="Add">Add More Reports</option>
						                    <option value="Format">Change Format</option>
						                    <option value="Remove">Remove Reports</option>
						                    <option value="MoveTop">Move Top</option>
						                    <option value="MoveUp">Move Up</option>
						                    <option value="MoveDown">Move Down</option>
						                    <option value="MoveBottom">Move Bottom</option>
						                </select>
						            </th>
						        </tr>
						    </table>
						</div>
						<br /><br />
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:80px;" class="leftPad" id="SaveBtnCell" runat="server"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
								<td style="width:90px;"><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to cancel without saving?');" href="ScheduledReports.aspx">Cancel</a></div></td>
								<td><div class="cancelButton"><asp:linkbutton runat="server" causesvalidation="false" visible="false" onclick="DeleteButton_Click" id="deleteBtn">Delete</asp:linkbutton></div></td>
							</tr>
						</table>
						<br /><br />
					</div>
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

