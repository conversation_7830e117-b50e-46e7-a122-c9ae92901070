using System;
using System.Collections;
using System.Runtime.Serialization;

[Serializable()]
public class SessionInfo : ISerializable
{
	public int SessionId = 0;
	public string SessionName = null;	
	public int SessionStatusId = 0;

	// Default constructor.
	public SessionInfo() { }

	// Deserialization constructor.
	public SessionInfo(SerializationInfo info, StreamingContext context)
	{
		SessionId = (int)info.GetValue("SessionId", typeof(int));
		SessionName = (string)info.GetValue("SessionName", typeof(string));
		SessionStatusId = (int)info.GetValue("SessionStatusId", typeof(int));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("SessionId", SessionId);
		info.AddValue("SessionName", SessionName);
		info.AddValue("SessionStatusId", SessionStatusId);
	}
}