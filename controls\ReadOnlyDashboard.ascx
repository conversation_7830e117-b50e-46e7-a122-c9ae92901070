<%@ Control Language="C#" AutoEventWireup="true" CodeFile="ReadOnlyDashboard.ascx.cs" Inherits="controls_ReadOnlyDashboard" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
	
<table style="padding-top:8px;background-color:#867f79;color:#ffffff;width:100%;" border="0" cellpadding="0" cellspacing="0">
	<tr>
		<td style="width:220px;padding-left:4px;">
			<div class="dashboardNavItemSelected">Shared Dashboard</div>
		</td>
		<td style="width:210px;">
			<a runat="server" href="~/MyDashboard.aspx"><div class="dashboardNavItem">My Dashboard</div></a>
		</td>
		<td style="width:250px;">&nbsp;</td>
		<td style="width:83px; padding-right:10px;"><div class="goButton" style="padding-left:14px;"><asp:linkbutton style="text-decoration:none;color:#dddddd;" visible="false" runat="server" onclick="AdminEditShared_Click" id="AdminButton">Customize</asp:linkbutton></div></td>
        <td>&nbsp;</td>
	</tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="8" style="width:2050px;">
	<tr>
		<td>
			<telerik:raddocklayout runat="server" id="RadDockLayout1" enableembeddedskins="false" skin="Diebold" onloaddocklayout="RadDockLayout1_LoadDockLayout">
				<telerik:raddockzone runat="server" id="RadDockZone1" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px; margin-right:15px; background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
				<telerik:raddockzone runat="server" id="RadDockZone2" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px; margin-right:15px; background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
                <telerik:raddockzone runat="server" id="RadDockZone3" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px; margin-right:15px; background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
                <telerik:raddockzone runat="server" id="RadDockZone4" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px; margin-right:15px; background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
                <telerik:raddockzone runat="server" id="RadDockZone5" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px; margin-right:15px; background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
				<div style="display:none">
					Hidden UpdatePanel, which is used to receive the new dock controls. 
					We will move them with script to the desired initial dock zone.
					<asp:updatepanel runat="server" id="UpdatePanel1">
						<contenttemplate>
						<triggers>
							<asp:asyncpostbacktrigger controlid="ButtonAddDock" eventname="Click" />
						</triggers>
						</contenttemplate>
					</asp:updatepanel>
				</div>
			</telerik:raddocklayout>
		</td>
	</tr>
</table>