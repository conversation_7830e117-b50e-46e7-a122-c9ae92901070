﻿<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Progress.aspx.cs" Inherits="NewReportWizard_Progress" %>
<%@ register tagprefix="rpt" tagname="DimensionSelector" src="~/controls/DimensionSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">
	function AddTab()
	{
		var btn = document.getElementById('<%= AddTabBtn.ClientID %>');
		btn.click();
	}
	function OnChanged(sender, args)
	{
	    sender.get_clientStateField().value = sender.saveClientState();
	}
</script>

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="NewSeriesButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series1Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series2Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series3Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="Series4Btn">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="DeleteSeriesButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="ContentUpdateDiv" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="ButtonUpdateDiv" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<asp:button style="display:none;" runat="server" id="AddTabBtn" causesvalidation="false" onclick="AddSeries_Click" />
	<table width="100%" border="0" cellpadding="0" cellspacing="8">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"> 
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				 
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px;padding-bottom:5px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
				
					<div id="ButtonUpdateDiv" runat="server" style="padding-left:14px; padding-right:14px; padding-bottom:0px; height:44px;">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series1Btn" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="1"><div id="Tab1" runat="server" class="tab_selected">Series 1</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series2Btn" visible="false" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="2"><div id="Tab2" runat="server" class="tab">Series 2</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series3Btn" visible="false" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="3"><div id="Tab3" runat="server" class="tab">Series 3</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="Series4Btn" visible="false" runat="server" oncommand="ChangeSelectedSeries_Command" commandargument="4"><div id="Tab4" runat="server" class="tab">Series 4</div></asp:linkbutton></td>
								<td>&nbsp;</td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="DeleteSeriesButton" visible="false" runat="server" onclick="DeleteSeries_Click" causesvalidation="false"><div id="Div1" runat="server" class="tab"><img style="padding-top:4px;" src="images/buttons/minus.png" border="0" />&nbsp;Delete Series</div></asp:linkbutton></td>
								<td style="width:103px;padding-left:0px;"><asp:linkbutton id="NewSeriesButton" visible="false" runat="server" onclick="AddSeries_Click"><div id="TabNew" runat="server" class="tab"><img style="padding-top:4px;" src="images/buttons/add.png" border="0" />&nbsp;Add Series</div></asp:linkbutton></td>
							</tr>
						</table>
					</div>
					<div style="padding-left:14px; padding-right:14px;">
						<div id="ContentUpdateDiv" runat="server" style="border:solid 2px #2388d8;">
							<table border="0" cellpadding="0" cellspacing="5">
								<tr>
									<td style="width:200px;"><div style="width:200px;" class="rowHeading">Series <asp:label id="SeriesNumLabel" runat="server"></asp:label> Name:</div></td>
									<td>
										<asp:textbox id="seriesName" width="244" cssclass="entryControl" runat="server"></asp:textbox>
										<asp:requiredfieldvalidator id="SeriesVal1" runat="server" controltovalidate="seriesName" cssclass="error" errormessage="* Required"></asp:requiredfieldvalidator>
										<br /><asp:customvalidator display="dynamic" id="Val" runat="server" style="padding-left:14px;" onservervalidate="ValidateSeriesNameCollision" cssclass="error" errormessage="* All series and spec label names must be unique."></asp:customvalidator>
									</td>
								</tr>
								<tr>
									<td style="padding-bottom:5px;width:200px;" valign="top">
										<div class="rowHeading" style="width:200px;">Select Dimension:</div>
										<div class="leftPad body" style="padding-top:15px;">
											All data items will display in the final chart unless they are filtered off by unchecking them in the list. 
										</div>
									</td>
									<td style="padding-left:14px;"><rpt:DimensionSelector id="dimensionSel" runat="server" isrequired="true"></rpt:DimensionSelector></td>
								</tr>
								<tr>
									<td style="width:200px;" valign="top"><div style="width:200px;" class="rowHeading">Rate Type:</div></td>
									<td> 
										<asp:dropdownlist id="rateTypeList" autopostback="true" onselectedindexchanged="rateTypeList_selectedIndexChanged" cssclass="entryControl" runat="server" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:dropdownlist>
										<asp:requiredfieldvalidator id="val3" runat="server" controltovalidate="rateTypeList" cssclass="error" errormessage="*"></asp:requiredfieldvalidator>
										<asp:customvalidator display="dynamic" id="v" runat="server" onservervalidate="ValidateRate" cssclass="error" errormessage="* 'By Statistic Value' is only valid with the Statistic dimension."></asp:customvalidator>
									</td>
								</tr>
								<tr>
									<td style="width:200px;" valign="top"><div style="width:200px;" class="rowHeading">Display Format:</div></td>
									<td> 
										<asp:dropdownlist id="formatTypeList" cssclass="entryControl" runat="server">
											<asp:listitem text="Standard" value=""></asp:listitem>
											<asp:listitem text="Numeric, no decimals" value="#,#"></asp:listitem>
											<asp:listitem text="Numeric, rounded to 1 decimal" value="#,##0.0"></asp:listitem>
											<asp:listitem text="Numeric, rounded to 2 decimals" value="#,##0.00"></asp:listitem>
											<asp:listitem text="Numeric, rounded to 3 decimals" value="#,##0.000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 4 decimals" value="#,##0.0000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 5 decimals" value="#,##0.00000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 6 decimals" value="#,##0.000000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 7 decimals" value="#,##0.0000000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 8 decimals" value="#,##0.00000000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 9 decimals" value="#,##0.000000000"></asp:listitem>
									        <asp:listitem text="Numeric, rounded to 10 decimals" value="#,##0.0000000000"></asp:listitem>
											<asp:listitem text="Hexidecimal" value="hex"></asp:listitem>
										</asp:dropdownlist>
									</td>
								</tr>
								<tr>
									<td style="width:200px;" valign="top"><div style="width:200px;" class="rowHeading">Workload Amount:</div></td>
									<td> 
										<asp:textbox id="workLoadField" runat="server" enabled="false" cssclass="entryControl"></asp:textbox>
										<asp:customvalidator display="dynamic" id="workLoadVal" runat="server" onservervalidate="ValidateWorkLoad" cssclass="error" errormessage="* Must be > 0"></asp:customvalidator>
									</td>
								</tr>
								<tr>
									<td style="width:200px;"><div style="width:200px;" class="rowHeading">Spec Label:</div></td>
									<td>
										<asp:textbox id="specLabelField" cssclass="entryControl" runat="server"></asp:textbox>
										<asp:customvalidator display="dynamic" id="specVal" runat="server" onservervalidate="ValidateSpecNameCollision" cssclass="error" errormessage="* All series and spec label names must be unique."></asp:customvalidator>
									</td>
								</tr>
								<tr>
									<td style="width:200px;"><div style="width:200px;" class="rowHeading">Spec Rate:</div></td>
									<td>
										<asp:textbox id="specRateField" cssclass="entryControl" runat="server"></asp:textbox>
										<asp:customvalidator display="dynamic" id="val5" runat="server" onservervalidate="ValidateSpecRate" cssclass="error" errormessage="* Must be > 0"></asp:customvalidator>
									</td>
								</tr>
							</table>
						</div>
						
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:200px;padding-top:5px;" valign="top"><div class="rowHeading">Options:</div></td>
								<td style="padding-top:5px;"> 
									<asp:checkbox id="normalizeCheck" class="entryControl" checked="true" runat="server" text=" Normalize to Spec" />
									&nbsp;&nbsp;&nbsp;&nbsp;
									<asp:checkbox id="splitByCell" cssclass="entryControl" runat="server" text="Split Series By Cell" />
                                    &nbsp;&nbsp;&nbsp;&nbsp;
									<asp:checkbox id="assumeOneFailureCheck" cssclass="entryControl" runat="server" text="Assume 1 Failure" />
								</td>
							</tr>
							<tr>
								<td style="width:200px;padding-top:5px;" valign="top"><div class="rowHeading">Grouping:</div></td>
								<td style="padding-top:5px;padding-left:10px;" valign="top">
									<asp:radiobuttonlist runat="server" repeatdirection="horizontal" id="groupingList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:radiobuttonlist>
									<asp:requiredfieldvalidator id="val1" runat="server" display="dynamic" controltovalidate="groupingList" cssclass="error"></asp:requiredfieldvalidator>
								</td>
							</tr>
						    <tr>
								<td style="width:200px;padding-top:5px;" valign="top"><div class="rowHeading">Y-Axis Scale (optional):</div></td>
								<td style="padding-top:5px;padding-left:10px;" valign="top">
								    <asp:textbox id="minYScale" width="60" runat="server"></asp:textbox> Min 
								    <asp:comparevalidator id="val6" runat="server" display="dynamic" controltovalidate="minYScale" operator="dataTypeCheck" type="double" errormessage="* Invalid format"></asp:comparevalidator>
                                    <span style="display:inline-block;width:15px;">&nbsp;</span>
								    <asp:textbox id="maxYScale" width="60" runat="server"></asp:textbox> Max
								    <asp:comparevalidator id="val7" runat="server" display="dynamic" controltovalidate="maxYScale" operator="dataTypeCheck" type="double" errormessage="* Invalid format"></asp:comparevalidator>
							    </td>
						    </tr>
						</table>
					</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
				</div>
			</td>
		</tr> 
	</table>
</asp:panel>

</asp:Content>

