using System;
using System.Data;
using System.Configuration;
using System.Runtime.Serialization;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

[Serializable()]
public class SelectedFieldOption : ISerializable
{
	public int FieldOptionId = 0;
	public string FieldOptionName = null;
	
	// Default constructor.
	public SelectedFieldOption() { }

	// Deserialization constructor.
	public SelectedFieldOption(SerializationInfo info, StreamingContext context)
    {
		FieldOptionId = (int)info.GetValue("FieldOptionId", typeof(int));
		FieldOptionName = (string)info.GetValue("FieldOptionName", typeof(string));
    }

    // Serialization function.
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
		info.AddValue("FieldOptionId", FieldOptionId);
		info.AddValue("FieldOptionName", FieldOptionName);
    }
}

