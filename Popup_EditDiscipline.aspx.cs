﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditDiscipline : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string ObjectId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DISCIPLINE_ID_KEY]))
			{
				this.ObjectId = Request.Params[DieboldConstants.DISCIPLINE_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetDiscipline", this.ObjectId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.nameField.Text = DataFormatter.getString(row, "DisciplineName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(this.ObjectId)) {
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertDiscipline", this.nameField.Text);
		}
		else {
			SqlHelper.ExecuteNonQuery("RPT_UpdateDiscipline", Convert.ToInt32(this.ObjectId), this.nameField.Text, this.isActive.Checked);
		}

		if (result == 0) {
			this.ErrorLabel.Visible = true;
		}
		else {
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditDiscipline.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditDiscipline.aspx';CloseRadWindow(); ", true);
		}
	}
}
