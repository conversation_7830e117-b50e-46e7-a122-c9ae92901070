<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_General.aspx.cs" Inherits="NewReportWizard_General" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
					
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>
