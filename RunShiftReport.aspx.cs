using System;
using System.Data;
using System.IO;
using System.Text;
using System.Web.UI;
using Telerik.Web.UI;

public partial class RunShiftReport : System.Web.UI.Page
{
    protected BaseReport ReportObj = null;

    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

	public bool IsReportLoaded
	{
		get { if (this.ViewState["ld"] != null) { return (bool)this.ViewState["ld"]; } else { return false; } }
		set { this.ViewState["ld"] = value; }
	}

	public int SnapshotId
    {
        get { if(this.ViewState["s"] != null) { return (int)this.ViewState["s"]; } else { return 0; } }
        set { this.ViewState["s"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SNAPSHOT_ID_KEY]))
			{
                this.SnapshotId = Convert.ToInt32(Request.QueryString[DieboldConstants.SNAPSHOT_ID_KEY]);
				LoadSnapshotData();
			}
						
            if (this.RepInfo == null)
                Response.Redirect("~/reportlibrary.aspx");

            string loadScript = "<script language='javascript'>\r\ndocument.forms[0].submit();\r\n</script>";
			Page.ClientScript.RegisterStartupScript(typeof(RunShiftReport), "LoadReportScript", loadScript);
        }

        if (Page.IsPostBack && this.IsReportLoaded == false)
        {
            InitialLoadingPanel.Visible = false;
            MainPanel.Visible = true;

			this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, null);

			if (this.SnapshotId == 0)
			{
				//require user to be admin to edit or save reports that are associated with sessions
				if (this.RepInfo.FolderId == 0 && this.RepInfo.ReportId != 0)
				{
					bool isAdmin = Utility.IsUserAdmin();
					EditBtnDiv.Visible = isAdmin;
					SaveBtnDiv.Visible = isAdmin;
				}

				this.ReportObj.LoadData();
			}
			else
			{
				LoadSnapshotData();
				this.ReportObj.BuildGridDisplay();
			}

            switch (this.RepInfo.ReportTypeId)
            {
                case ReportHelper.ReportTypeEnum.LEGACY_PARETO:
                case ReportHelper.ReportTypeEnum.LEGACY_PROGRESS:
                case ReportHelper.ReportTypeEnum.LEGACY_PRST:
                case ReportHelper.ReportTypeEnum.LEGACY_DISTRIBUTION:
                case ReportHelper.ReportTypeEnum.LEGACY_GENERAL:
                    viewTranButton.Visible = false;
                    break;
                default:
                    viewTranButton.Visible = true;
                    break;
            }

			reportNameLabel.Text = this.RepInfo.ReportName;

			if (this.RepInfo.StartDate != DateTime.MinValue)
			{
				dateLbl.Text = this.RepInfo.StartDate.ToString("MMM. d, yyyy h:mm tt");

				if (this.RepInfo.EndDate != DateTime.MinValue)
					dateLbl.Text += " - ";
				else
					dateLbl.Text += "<br />";
			}
			
			if (this.RepInfo.EndDate != DateTime.MinValue)
				dateLbl.Text += this.RepInfo.EndDate.ToString("MMM. d, yyyy h:mm tt") + "<br />";

			if (!string.IsNullOrEmpty(((ShiftReport)this.ReportObj).SettingsList))
				firmwareLbl.Text = "F/W: " + ((ShiftReport)this.ReportObj).SettingsList;

			SearchCriteria searchCriteria = new SearchCriteria(this.Page);
			searchCriteria.CalculateSearchCriteria(this.RepInfo, null);
			searchCriteria.SetToSession();

            DataSet ds = TransactionSource.GetTransactionsFromSessionParameters();
			DataView view = ds.Tables[0].DefaultView;
			view.Sort = "DeviceFullName, TranDate";
			lowerRep.DataSource = view; 
			lowerRep.DataBind();

			GridBindingPanel.DataBind();

			this.IsReportLoaded = true;
        }
    }

	private string priorDevice = null;
	private string priorSession = null;
	protected string FormatReportSeperator(object dataItem)
	{
		StringBuilder retVal = new StringBuilder();

		if (dataItem != null)
		{
			string device = DataFormatter.Format(dataItem, "DeviceFullName");
			string session = DataFormatter.Format(dataItem, "SessionName");
			if (string.Compare(priorDevice, device) != 0 || string.Compare(priorSession, session) != 0)
			{
				retVal.AppendLine("<tr class=\"shiftReportHeading\"><td style=\"border-top:solid 18px #000;\">&nbsp;</td><td style=\"border-top:solid 18px #000;\">&nbsp;</td><td style=\"border-top:solid 18px #000;\">Session</td><td style=\"border-top:solid 18px #000;\">");
				retVal.AppendLine(session);
				retVal.AppendLine("</td></tr>");
				retVal.AppendLine("<tr class=\"shiftReportHeading\"><td>Unit #</td><td>Date</td><td>TRX</td><td>Fault Description</td></tr>");

				priorDevice = device;
				priorSession = session;
			}
		}
		if (retVal.Length > 0)
			return retVal.ToString();
		else
			return null;
	}

	private void LoadSnapshotData()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadSnapshot", this.SnapshotId);
		this.RepInfo = null;

		this.SnapshotTable.Visible = true;
		this.EditBtnDiv.Visible = false;
		this.SaveBtnDiv.Visible = false;

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				this.RepInfo = Utility.LoadReportInfo(DataFormatter.getInt32(row, "ReportId"));
				this.RepInfo.ReportData = DataFormatter.getString(row, "ReportData");
				this.RepInfo.CellSetData = DataFormatter.getString(row, "CellSetData");
				this.SnapshotDesc.Text = DataFormatter.getString(row, "Description");
				this.SnapshotName.Text = DataFormatter.getString(row, "SnapshotName");
				this.SnapshotDate.Text = DataFormatter.getDateTime(row, "SnapshotDate").ToString();

				this.RepInfo.ChartTitle = DataFormatter.getString(row, "ChartTitle");
				this.RepInfo.FixedStartDate = DataFormatter.getDateTime(row, "StartDate");
				this.RepInfo.FixedEndDate = DataFormatter.getDateTime(row, "EndDate");
				this.RepInfo.ParetoInfo.SplitByCell = DataFormatter.getBool(row, "Pareto_SplitByCell");
				this.RepInfo.ParetoInfo.IncludeTotal = DataFormatter.getBool(row, "Pareto_IncludeTotal");
                this.RepInfo.ParetoInfo.ByStatisticValue = DataFormatter.getBool(row, "Pareto_ByStatValue");
                this.RepInfo.ParetoInfo.MaxColumns = DataFormatter.getInt32(row, "Pareto_MaxColumns");
				this.RepInfo.PRSTInfo.XaxisTypeId = (ReportHelper.XAxisTypeEnum)DataFormatter.getInt32(row, "PRST_XaxisTypeId");
				this.RepInfo.PRSTInfo.IncludeTrendline = DataFormatter.getBool(row, "PRST_IncludeTrendline");
				this.RepInfo.PRSTInfo.IncludeUncensoredSeries = DataFormatter.getBool(row, "PRST_IncludeUncensoredSeries");
				this.RepInfo.PRSTInfo.MTBFSpec = DataFormatter.getDecimal(row, "PRST_MTBFSpec");
				this.RepInfo.PRSTInfo.ProducersRisk = DataFormatter.getDecimal(row, "PRST_ProducersRisk");
				this.RepInfo.PRSTInfo.ConsumersRisk = DataFormatter.getDecimal(row, "PRST_ConsumersRisk");
				this.RepInfo.PRSTInfo.DiscriminationRatio = DataFormatter.getDecimal(row, "PRST_DiscriminationRatio");
				this.RepInfo.ProgressInfo.Normalize = DataFormatter.getBool(row, "Progress_Normalize");
				this.RepInfo.DistributionInfo.LowerSpecLimit = DataFormatter.getDecimal(row, "Distro_LowerSpecLimit");
				this.RepInfo.DistributionInfo.UpperSpecLimit = DataFormatter.getDecimal(row, "Distro_UpperSpecLimit");
				this.RepInfo.DistributionInfo.ShowNormalDistribution = DataFormatter.getBool(row, "Distro_ShowNormalDistribution");
				this.RepInfo.DistributionInfo.ShowHistogram = DataFormatter.getBool(row, "Distro_ShowHistogram");
			}
		}
	}

	protected void PrintButton_Click(object sender, EventArgs e)
	{
		Utility.SetReportInfoForTransfer(this.RepInfo);
		Response.Redirect("PrintShiftReport.aspx");
	}

	protected void SnapshotButton_Click(object sender, EventArgs e)
	{
		Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "SnapshotWindow")
			{
				win.NavigateUrl = "ReportSnapshot.aspx";
				win.VisibleOnPageLoad = true;
			}
		}
	}

    protected void ViewTranButton_Click(object sender, EventArgs e)
    {
		SearchCriteria searchCriteria = new SearchCriteria(this.Page);
		searchCriteria.CalculateSearchCriteria(this.RepInfo, null);
		searchCriteria.SetToSession();
		Response.Redirect("Search.aspx");
    }

   	protected void ExportButton_Click(object sender, EventArgs e)
	{
		Response.ClearHeaders();
		Response.ClearContent();
		Response.Charset = "";
		Response.AppendHeader("content-disposition", "attachment; filename=" + this.RepInfo.ReportName + ".xls");
		Response.ContentType = "application/vnd.ms-excel";

		StringWriter colWrite = new StringWriter();
		StringWriter rowWrite = new StringWriter();
		//HtmlTextWriter colHtmlWrite = new HtmlTextWriter(colWrite);
		HtmlTextWriter rowsHtmlWrite = new HtmlTextWriter(rowWrite);

		//ColumnHeaderLevelRepeater.RenderControl(colHtmlWrite);
		GridBindingPanel.RenderControl(rowsHtmlWrite);
		
		Response.Write(rowWrite.ToString());
		Response.End();
	}

    protected void SaveButton_Click(object sender, EventArgs e)
    {
        Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "SaveReportWindow")
				win.VisibleOnPageLoad = true;
		}
    }

    protected void EditButton_Click(object sender, EventArgs e)
    {
        Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "EditWindow")
			{
                win.NavigateUrl = this.RepInfo.WizardPageName;
				win.VisibleOnPageLoad = true;
			}
		}
    }

	protected void ScheduleButton_Click(object sender, EventArgs e)
	{
		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "ScheduleWindow")
			{
				win.NavigateUrl = "Popup_EditScheduledReport.aspx?r=" + this.RepInfo.ReportId;
				win.VisibleOnPageLoad = true;
			}
		}
	}
}
