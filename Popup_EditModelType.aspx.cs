using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditModelType : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string ModelTypeId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.MODEL_TYPE_KEY]))
			{
				this.ModelTypeId = Request.Params[DieboldConstants.MODEL_TYPE_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetModelType", this.ModelTypeId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.ModelTypeField.Text = DataFormatter.getString(row, "ModelType");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(ModelTypeId)) {
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertModelType", this.ModelTypeField.Text);
		}
		else {
			SqlHelper.ExecuteNonQuery("RPT_UpdateModelType", Convert.ToInt32(this.ModelTypeId), this.ModelTypeField.Text, this.isActive.Checked);
		}

		if (result == 0) {
			this.ErrorLabel.Visible = true;
		}
		else {
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditModelTypes.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditModelTypes.aspx';CloseRadWindow(); ", true);
		}
	}
}
