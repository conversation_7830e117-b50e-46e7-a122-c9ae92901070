<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditSession.aspx.cs" Inherits="Popup_EditSession" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Session</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Add/Edit Session</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="14" width="100%">
						<tr>
							<td class="rowHeading" style="width:150px;">Session Name</td>
							<td>
								<asp:textbox runat="server" width="230" id="sessionNameField"></asp:textbox>
								<asp:requiredfieldvalidator id="val1" runat="server" errormessage="* Required" controltovalidate="sessionNameField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Session Status</td>
							<td>
								<asp:dropdownlist id="statusList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Session Format</td>
							<td>
								<asp:dropdownlist id="sessionFormat" runat="server">
                                    <asp:listitem text="Standard" value="standard" selected="True"></asp:listitem>
                                    <asp:listitem text="Manual Transaction Volume Entry" value="manual"></asp:listitem>
                                    <asp:listitem text="Observations Only, No Transactions" value="observations"></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
                        <tr>
							<td class="rowHeading" style="width:150px;">Test Type</td>
							<td>
								<asp:dropdownlist id="testTypeList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server">
                                    <asp:listitem text="Select..." value=""></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
                        <tr>
							<td class="rowHeading" style="width:150px;">Target Media Count</td>
							<td>
								<asp:textbox id="targetMediaCount" runat="server"></asp:textbox>
							</td>
						</tr>
                        <tr>
							<td class="rowHeading" style="width:150px;">Target Media Type</td>
							<td>
								<asp:dropdownlist id="targetMediaType" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server">
                                    <asp:listitem text="Select..." value=""></asp:listitem>
                                    <asp:listitem text="Media" value="Media"></asp:listitem>
                                    <asp:listitem text="Transactions" value="Transactions"></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Last Modified By</td>
							<td>
								<asp:textbox runat="server" width="230" id="lastModifiedBy" enabled="false"></asp:textbox>
							</td>
                        </tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Last Modified Date</td>
							<td>
								<asp:textbox runat="server" width="230" id="lastModifiedDate" enabled="false"></asp:textbox>
							</td>
                        </tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Created By</td>
							<td>
								<asp:textbox runat="server" width="230" id="createdBy" enabled="false"></asp:textbox>
							</td>
                        </tr>
                        <tr>
                            <td class="rowHeading" style="width:150px;">Created Date</td>
							<td>
								<asp:textbox runat="server" width="230" id="createdDate" enabled="false"></asp:textbox>
							</td>
                        </tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />A session already exists with the specified name.<br /></div>
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

