using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

/// <summary>
/// Summary description for ObservationSource
/// </summary>
public class ObservationSource
{
	public static DataSet GetObservations(string observationText, DateTime startDateStr, DateTime endDateStr, int sessionId, bool includePastSessions)
	{
		return GetObservations(observationText, startDateStr, endDateStr, sessionId, includePastSessions, -1, -1, -1, -1, -1, -1, -1, -1, -1);
	}

	public static DataSet GetObservations(string observationText, DateTime startDateStr, DateTime endDateStr, int sessionId, bool includePastSessions,
											int advancedSessionId, int transactionIdStart, int transactionIdEnd, int cellId, int deviceTypeId,
											int deviceId, int failureLocationId, int failureTypeId, int investigationAreaId)
	{
		if (sessionId > 0)
		{
			return SqlHelper.ExecuteDataset("RPT_GetSessionById", sessionId);
		}
		else
		{
			bool doAdvancedSearch = false;

			string obvText = null;
			object startDate = null;
			object endDate = null;
			object sessionIdObj = null;
			object tranIdStart = null;
			object tranIdEnd = null;
			object cellIdObj = null;
			object deviceTypeIdObj = null;
			object deviceIdObj = null;
			object failureLocationIdObj = null;
			object failureTypeIdObj = null;
			object investigationAreaIdObj = null;

			if (!string.IsNullOrEmpty(observationText))
			{
				obvText = observationText;
				doAdvancedSearch = true;
			}
			if (!DateTime.MinValue.Equals(startDateStr) && !string.IsNullOrEmpty(startDateStr.ToString()))
			{
				startDate = startDateStr;
				doAdvancedSearch = true;
			}
			if (!DateTime.MinValue.Equals(endDateStr) && !string.IsNullOrEmpty(startDateStr.ToString()))
			{
				endDate = endDateStr;
				doAdvancedSearch = true;
			}
			//rearrange dates if needed
			if (startDate != null && endDate != null && endDate.ToString().CompareTo(startDate.ToString()) < 0)
			{
				object tmp = startDate;
				startDate = endDate;
				endDate = tmp;
			}
			if (advancedSessionId > 0)
			{
				sessionIdObj = advancedSessionId;
				doAdvancedSearch = true;
			}
			if (transactionIdStart > 0)
			{
				tranIdStart = transactionIdStart;
				doAdvancedSearch = true;
			}
			if (transactionIdEnd > 0)
			{
				tranIdEnd = transactionIdEnd;
				doAdvancedSearch = true;
			}
			if (cellId > 0)
			{
				cellIdObj = cellId;
				doAdvancedSearch = true;
			}
			if (deviceTypeId > 0)
			{
				deviceTypeIdObj = deviceTypeId;
				doAdvancedSearch = true;
			}
			if (deviceId > 0)
			{
				deviceIdObj = deviceId;
				doAdvancedSearch = true;
			}
			if (failureLocationId > 0)
			{
				failureLocationIdObj = failureLocationId;
				doAdvancedSearch = true;
			}
			if (failureTypeId > 0)
			{
				failureTypeIdObj = failureTypeId;
				doAdvancedSearch = true;
			}
			if (investigationAreaId > 0)
			{
				investigationAreaIdObj = investigationAreaId;
				doAdvancedSearch = true;
			}

			if (doAdvancedSearch)
			{
				return SqlHelper.ExecuteDataset("RPT_SearchObservations", obvText, startDate, endDate, includePastSessions, sessionIdObj, tranIdStart, tranIdEnd,
													cellIdObj, deviceTypeIdObj, deviceIdObj, failureLocationIdObj, failureTypeIdObj, investigationAreaIdObj);
			}
			else
			{
				return SqlHelper.ExecuteDataset("RPT_RecentObservations", includePastSessions); //Return Recent
			}
		}
	}
}
