<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="AdministrationNav.aspx.cs" inherits="AdministrationNav" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Administration</td>
						<td class="widgetTop" style="width:30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title">Administration Areas</div>
					<table border="0" cellpadding="0" cellspacing="0" style="padding-top:10px; padding-left:14px;">
						<tr>
							<td>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditSessions.aspx">Sessions</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditCells.aspx">Cells</a></div>
								<hr />
								<div class="goButton" style="padding-bottom:5px;"><a href="DeviceType.aspx">Device Types</a></div>
                                <div class="goButton" style="padding-bottom:5px;"><a href="EditDevices.aspx">Devices</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditProductLines.aspx">Product Lines</a></div>
                                <div class="goButton" style="padding-bottom:5px;"><a href="EditFamilyLines.aspx">Family Lines</a></div>
                                <div class="goButton" style="padding-bottom:5px;"><a href="EditModelTypes.aspx">Model Types</a></div>
								<hr />
								<div class="goButton" style="padding-bottom:5px;"><a href="EditDiscipline.aspx">Disciplines</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditFailureType.aspx">Failure Types</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditFailureLocation.aspx">Failure Locations</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditInvestigationArea.aspx">Investigation Areas</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditOperator.aspx">Operators</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditTestLocations.aspx">Test Locations</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EditTriageType.aspx">Triage Types</a></div>
                                <div class="goButton" style="padding-bottom:5px;"><a href="EditSolutionState.aspx">Solution States</a></div>
                                <div class="goButton" style="padding-bottom:5px;"><a href="EditTestType.aspx">Test Types</a></div>
								<hr />
								<div class="goButton" style="padding-bottom:5px;"><a href="Events.aspx">Event Types</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="Entity.aspx">Entity Items</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="Extensions.aspx">Extensions</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="EngineeringFields.aspx">Engineering Fields</a></div>
								<hr />
                                <div class="goButton" style="padding-bottom:5px;"><a href="QueryReports.aspx">Query Reports</a></div>
                                <hr />
								<div class="goButton" style="padding-bottom:5px;"><a href="SessionArchive.aspx">Archive Management</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="Reprocess.aspx">Reprocess Data Files</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="UploaderSetup.aspx">Uploader Setup</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="UploadFieldDefinition.aspx">Upload AMI Field Definition File</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="SourceControl.aspx">RDTool Source Control</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="ScriptLog.aspx">Cell Script Logs</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="ClientDownload/RDToolInstaller.zip">Download RDTool Installer Package</a></div>
								<div class="goButton" style="padding-bottom:5px;"><a href="ClientDownload/ManualConfigExtensionInstaller.zip">Download Manual Config Extension Installer</a></div>
							</td>
						</tr>
					</table>					
					<br />
					<br />
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

