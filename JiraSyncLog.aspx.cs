﻿using System;
using System.Web.UI;

public partial class JiraSyncLog : System.Web.UI.Page {
    
    protected void Page_Load(object sender, EventArgs e) {
        if (!Page.IsPostBack) {
            if (!string.IsNullOrEmpty(Request.Params["page"]))
                DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
        }

        BindData();
    }

    private void BindData() {
        DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_JiraSync");
        DataGrid1.DataBind();
    }
}