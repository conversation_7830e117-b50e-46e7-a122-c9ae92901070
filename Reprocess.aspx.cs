using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using QueueServiceClient;

public partial class Reprocess : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		sessionList.DataSource = Utility.GetActiveSessionsList();
		sessionList.DataBind();

		if (!Page.IsPostBack)
		{
			deviceTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
			deviceTypeList.DataSource = Utility.GetDeviceTypeList();
			deviceTypeList.DataBind();
		}
	}

	protected void uploaderVersionRadioChanged(object sender, EventArgs e)
	{
		if (uploaderVersion.SelectedValue == "1")
		{
			SessionRow.Visible = false;
			DeviceTypeRow.Visible = true;
			DeviceRow.Visible = true;
		}
		else
		{
			SessionRow.Visible = true;
			DeviceTypeRow.Visible = false;
			DeviceRow.Visible = false;
		}
	}

	protected void ExecuteButton_Click(object sender, EventArgs e)
	{
		Page.Validate();
		if (Page.IsValid)
		{
			UploadClient uploadClient = new UploadClient();
			
			List<int> selectedSessions = new List<int>();
			foreach (ListItem item in sessionList.Items)
			{
				if (item.Selected && !string.IsNullOrEmpty(item.Value))
					selectedSessions.Add(Convert.ToInt32(item.Value));
			}

			List<int> selectedDevices = new List<int>();
			foreach (ListItem item in devicesList.Items)
			{
				if (item.Selected && !string.IsNullOrEmpty(item.Value))
					selectedDevices.Add(Convert.ToInt32(item.Value));
			}
			
			DateTime startDate = DateTime.Now;
			DateTime endDate = DateTime.Now;
			TimeSpan startTime = TimeSpan.Zero;
			TimeSpan endTime = TimeSpan.Zero;

			if (startTimeField.SelectedDate != null)
				startTime = new TimeSpan(((DateTime)startDateField.SelectedDate).Hour, ((DateTime)startTimeField.SelectedDate).Minute, ((DateTime)startTimeField.SelectedDate).Second);

			if (startTime != TimeSpan.Zero)
				startDate = ((DateTime)startDateField.SelectedDate).Add(startTime);
			else
				startDate = (DateTime)startDateField.SelectedDate;

			if (endTimeField.SelectedDate != null)
				endTime = new TimeSpan(((DateTime)endTimeField.SelectedDate).Hour, ((DateTime)endTimeField.SelectedDate).Minute, ((DateTime)endTimeField.SelectedDate).Second);

			if (endTime != TimeSpan.Zero)
				endDate = ((DateTime)endDateField.SelectedDate).Add(endTime);
			else
				endDate = (DateTime)endDateField.SelectedDate;			

			bool retVal = true;
			if (uploaderVersion.SelectedValue == "1")
				retVal = uploadClient.RequestReprocessFiles(selectedDevices.ToArray(), startDate, endDate, includeXMLFile.Checked, includeEngFile.Checked);
			else
				retVal = uploadClient.RequestReprocessFilesBySession(selectedSessions.ToArray(), startDate, endDate, includeXMLFile.Checked, includeEngFile.Checked);

			if (retVal)
				Response.Redirect("ImportOverview.aspx");
			else
				this.ErrorLabel.Visible = true;
		}
	}

	protected void CancelButton_Click(object sender, EventArgs e)
	{
		Response.Redirect("AdministrationNav.aspx");
	}

	protected void DevceTypeList_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(deviceTypeList.SelectedValue))
		{
			devicesList.Items.Clear();
			devicesList.Items.Add(new ListItem("Select...", ""));
			devicesList.DataSource = Utility.GetDevicesList(Convert.ToInt32(deviceTypeList.SelectedValue));
			devicesList.DataBind();

			devicesList.Enabled = true;
		}
		else
		{
			devicesList.Enabled = false;
		}
	}

	protected void EnsureFileTypes(object sender, ServerValidateEventArgs e)
	{
		if (includeEngFile.Checked || includeXMLFile.Checked)
			e.IsValid = true;
		else
			e.IsValid = false;
	}
}
