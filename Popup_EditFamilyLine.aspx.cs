using System;
using System.Data;
using System.Web.UI;

public partial class Popup_EditFamilyLine : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string FamilyLineId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FAMILY_LINES_KEY]))
			{
				this.FamilyLineId = Request.Params[DieboldConstants.FAMILY_LINES_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFamilyLine", this.FamilyLineId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.FamilyLineField.Text = DataFormatter.getString(row, "FamilyLine");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(FamilyLineId)) {
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertFamilyLine", this.FamilyLineField.Text);
		}
		else {
			SqlHelper.ExecuteNonQuery("RPT_UpdateFamilyLine", Convert.ToInt32(this.FamilyLineId), this.FamilyLineField.Text, this.isActive.Checked);
		}

		if (result == 0) {
			this.ErrorLabel.Visible = true;
		}
		else {
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditFamilyLines.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditFamilyLines.aspx';CloseRadWindow(); ", true);
		}
	}
}
