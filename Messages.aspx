<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Messages.aspx.cs" Inherits="Messages" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<script language="javascript" type="text/javascript">
		function ToggleStartDatePopup() { $find("<%= startDateField.ClientID %>").showPopup(); }  
		function ToggleEndDatePopup() { $find("<%= endDateField.ClientID %>").showPopup(); }
		
		var lastToggleDiv = null;
		function toggleExpand(item, folderId)
		{	
			if(lastToggleDiv && item == lastToggleDiv)
				lastToggleDiv = null;
				
			if(lastToggleDiv != null)
			{
				if(lastToggleDiv.className=='collapseButton')
					lastToggleDiv.className='expandButton';
				
				if(lastToggleDiv.isSelected && lastToggleDiv.isSelected==true)
					lastToggleDiv.isSelected = false;
			}
				
			if(item.className=='expandButton'){ item.className='collapseButton';}
			else if(item.className=='collapseButton'){item.className='expandButton';}

			if(item.isSelected && item.isSelected==true)
				item.isSelected = false;
			else
				item.isSelected = true;

			lastToggleDiv=item;
		}
	</script> 
	
	<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="600" width="800" modal="true" title="Pictures" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="SubscriptionsWindow" openerelementid='<%# editSubscriptionsButton.ClientID %>' VisibleOnPageLoad="false" OffsetElementID="offsetElement"
				Top="30" Left="30" NavigateUrl="Subscription.aspx" Title="" Height="600" Width="650" >
			</telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>
	
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Message Center</td>
						<td class="widgetTop" style="width:30%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<table border="0" cellpadding="0" cellspacing="0" style="padding-top:10px; padding-left:14px;">
						<tr>
							<td style="width:115px;" id="NewMessageCell" runat="server"><div class="goButton" style="padding-bottom:5px;"><asp:linkbutton runat="server" id="NewMsgButton" onclick="NewMessage_Click">New Message</asp:linkbutton></div></td>
							<td style="width:150px;"><div class="goButton" style="padding-bottom:5px;"><asp:linkbutton runat="server" id="editSubscriptionsButton"></asp:linkbutton></div></td>
							<td><div class="goButton" style="padding-bottom:5px;"><a href="NotificationClient.aspx">Get Notification Client</a></div></td>
						</tr>
					</table>
					
					<asp:objectdatasource id="MessagesSource" runat="server" typename="MessageSource" selectmethod="GetMessagesByDate"></asp:objectdatasource>
					
					<div class="title">Messages</div>
					<table border="0" cellpadding="0" cellspacing="0" style="width:100%;">
						<tr>
							<td colspan="4" class="rowHeading">Date Range:</td>
						</tr>
						<tr>
							<td style="padding-top:10px;padding-bottom:10px;width:130px;">
								<telerik:RadDatePicker id="startDateField"  Runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" cssclass="entryControl" style="padding-top:1px;" Width="110px">        
									<calendar skin="Default2006" showrowheaders="false"></calendar>       
									<DatePopupButton Visible="False"></DatePopupButton>
									<DateInput onclick="ToggleStartDatePopup()"></DateInput>                           	                                             
								</telerik:RadDatePicker>
							</td>
							<td style="padding-top:10px;padding-bottom:10px;width:130px;">
								<telerik:RadDatePicker id="endDateField"  Runat="server" cssclass="entryControl" style="padding-top:1px;" Width="110px">        
									<calendar skin="Default2006" showrowheaders="false"></calendar>       
									<DatePopupButton Visible="False"></DatePopupButton>
									<DateInput onclick="ToggleEndDatePopup()"></DateInput>                           	                                             
								</telerik:RadDatePicker>
							</td>
							<td style="width:100px;padding-top:2px;padding-left:10px;"><div class="goButton"><asp:linkbutton runat="server" id="filterDatesButton" onclick="FilterDates_Click">Refresh</asp:linkbutton></div></td>
							<td>&nbsp;</td>
						</tr>
					</table>
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="cellHeading" style="width:30%;">Sent:</td>
							<td class="cellHeading" style="width:5%;">&nbsp;</td>
							<td class="cellHeading" style="width:55%;">Subject:</td>
							<td class="cellHeading" style="width:10%;">&nbsp;</td>
						</tr>
						<tr id="NoMessagesRow" runat="server" visible="false">
							<td colspan="5" style="padding:10px 10px 10px 14px;">
								<asp:label id="label1" runat="server">No messages were found.</asp:label>
							</td>
						</tr>
						<tr>
							<td colspan="5" style="padding: 10px 0px 10px 0px;">
								<ajaxToolkit:Accordion ID="MessagesAccordian" runat="Server" SelectedIndex="-1" HeaderSelectedCssClass="repeaterItemSelected"
									ContentCssClass="accordionContent" AutoSize="None" FadeTransitions="true" TransitionDuration="100" FramesPerSecond="40" 
									RequireOpenedPane="false" SuppressHeaderPostbacks="true" datasourceid="MessagesSource">
									<panes></panes>
									<headertemplate>
										<table width="100%" border="0" cellpadding="0" cellspacing="0">
											<tr>
												<td style="width:5px;">&nbsp;</td>
												<td>
													<div onclick="toggleExpand(this);" style="padding-left:14px;" id="expandDiv" runat="server" class="expandButton">
														<table width="100%" border="0" cellpadding="0" cellspacing="0">
															<tr>
																<td class='<%# ((Container.DisplayIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>' style='width:27%; <%# (Convert.ToDateTime(DataFormatter.FormatDate(Container.DataItem, "LastModified", "MM/dd/yyyy hh:mm:ss", "")) > this.LastLogin)?"font-weight:bold;" : "" %>'><%# DataFormatter.FormatDate(Container.DataItem, "LastModified", "MM/dd/yyyy", "")%></td>
																<td class='<%# ((Container.DisplayIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>' style="width:5%;"><%# (DataFormatter.FormatBool(Container.DataItem, "IsUrgent", "true", "false", "").Equals("true", StringComparison.OrdinalIgnoreCase)) ? "<img src=\"images/alert.gif\" border=\"0\"/>" : "<img src=\"images/alert_spacer.gif\" border=\"0\"/>"%></td>
																<td class='<%# ((Container.DisplayIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>' style='width:55%; <%# (Convert.ToDateTime(DataFormatter.FormatDate(Container.DataItem, "LastModified", "MM/dd/yyyy hh:mm:ss", "")) > this.LastLogin)?"font-weight:bold;" : "" %>'><%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "MessageSubject", "", true, false), 26, 35, true)%></td>
																<td class='<%# ((Container.DisplayIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>' style="width:10%;"><div class="goButton" runat="server" id="EditBtn" visible='<%# this.IsUserAdmin %>'><a onclick='<%# string.Format("cancelClickEvent(event);document.location.href=\"EditMessage.aspx?m={0}\"; return false;", DataFormatter.Format(Container.DataItem, "MessageId")) %>'>Edit</a></div></td>
															</tr>
														</table>
													</div>	
												</td>
											</tr>
										</table>
									</headertemplate>
									<contenttemplate>
										<table width="100%" cellpadding="0" cellspacing="0" style="border:solid 10px #9e9995;">
											<tr>
												<td style="width:130px;border-bottom:solid 1px #ffffff;" class="rowHeading">Subject</td>
												<td class='<%# ((Container.DisplayIndex % 2)==0?"repeaterSubItem":"repeaterSubItemAlt") %>'><%# DataFormatter.Format(Container.DataItem, "MessageSubject")%></td>
											</tr>
											<tr>
												<td style="width:130px; vertical-align:top;padding-bottom:5px;" class="rowHeading">Message</td>
												<td class='<%# ((Container.DisplayIndex % 2)==0?"repeaterSubItem":"repeaterSubItemAlt") %>' style="padding-bottom:5px;">
													<div style="height: 220px; border: solid 1px #999999;overflow:auto" >
														<%# DataFormatter.Format(Container.DataItem, "MessageBody")%>
													</div>
												</td>
											</tr>
										</table>																			       
									</contenttemplate>
								</ajaxToolkit:Accordion>
							</td>
						</tr>
					</table>
				</div>
			</td>
		</tr>
	</table>
	<script type="text/javascript" language="JavaScript">
		function cancelClickEvent(event)
		{
		    if(!event)
                event = window.event;

            if(event)
            {
                event.cancelBubble=true;
                if (event.stopPropagation)
                    event.stopPropagation();
                if (event.preventDefault)
                    event.preventDefault();
	            event.returnValue = false;
	        }
		}
	</script>
</asp:Content>

