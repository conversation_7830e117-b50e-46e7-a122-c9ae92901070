using System;
using System.Data;
using System.Collections.Generic;
using System.Configuration;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Xml;
using Microsoft.AnalysisServices.AdomdClient;

/// <summary>
/// Summary description for OLAPHelper
/// </summary>
public class OLAPHelper
{
    public static Dimension FindDimensionByUniqueName(bool isLegacy, string uniqueName)
    {
        CubeDef cube = getCube(isLegacy);
        return (Dimension)cube.GetSchemaObject(SchemaObjectType.ObjectTypeDimension, uniqueName);
    }

	public static Hierarchy FindHierarchyByUniqueName(bool isLegacy, string uniqueName)
    {
		CubeDef cube = getCube(isLegacy);
        return (Hierarchy)cube.GetSchemaObject(SchemaObjectType.ObjectTypeHierarchy, uniqueName);
    }

	public static Level FindLevelByUniqueName(bool isLegacy, string uniqueName)
    {
		CubeDef cube = getCube(isLegacy);
        return (Level)cube.GetSchemaObject(SchemaObjectType.ObjectTypeLevel, uniqueName);
    }

	public static Member FindMemberByUniqueName(bool isLegacy, string uniqueName)
    {
		CubeDef cube = getCube(isLegacy);
        return (Member)cube.GetSchemaObject(SchemaObjectType.ObjectTypeMember, uniqueName);
    }
    
    public static DimensionCollection GetDimensions(bool isLegacy)
    {
		CubeDef cube = getCube(isLegacy);
        return cube.Dimensions;
    }

    public static XmlReader ExecuteXmlReader(bool isLegacy, string mdxCommandText)
    {
        XmlReader retVal = null;

        AdomdConnection conn = null;
        
        if(isLegacy)
            conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString);
        else
            conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString);

        try
        {
            conn.Open();
        }
        catch
        {
            conn.Close(true);
            if (isLegacy)
                conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString);
            else
                conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString);
            conn.Open();
        }

        AdomdCommand cmd = conn.CreateCommand();
        cmd.CommandText = mdxCommandText;

        retVal = cmd.ExecuteXmlReader();

        return retVal;
    }

    public static CellSet ExecuteCellSet(bool isLegacy, string mdxCommandText)
    {
        CellSet retVal = null;

        AdomdConnection conn = null;

        if (isLegacy)
            conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString);
        else
            conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString);

        try
        {
            conn.Open();
        }
        catch
        {
            conn.Close(true);
            if (isLegacy)
                conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString);
            else
                conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString);
            conn.Open();
        }

        AdomdCommand cmd = conn.CreateCommand();
        cmd.CommandText = mdxCommandText;

        retVal = cmd.ExecuteCellSet();

        return retVal;
    }

    public static string BuildMdxLevelTuple(bool isLegacy, string levelUniqueName, List<string> memberUniqueNameList, bool ensureUnknownMember)
    {
        List<string> memberList = new List<string>();
		CubeDef cube = getCube(isLegacy);
        Level level = (Level)cube.GetSchemaObject(SchemaObjectType.ObjectTypeLevel, levelUniqueName);
        bool hasUnknown = false;

        if (memberUniqueNameList != null && memberUniqueNameList.Count > 0)
        {
            foreach (string memberUniqueName in memberUniqueNameList)
            {
                try
                {
                    Member curMem = (Member)cube.GetSchemaObject(SchemaObjectType.ObjectTypeMember, memberUniqueName);
                    if (!memberList.Contains(curMem.UniqueName))
                    {
                        if (curMem.Parent != null && memberList.Contains(curMem.Parent.UniqueName))
                            memberList.Remove(curMem.Parent.UniqueName);

                        memberList.Add(curMem.UniqueName);
                        if(curMem.UniqueName.EndsWith(".UNKNOWNMEMBER"))
                            hasUnknown = true;
                    }
                }
                catch { }
            }
        }

        if (ensureUnknownMember && !hasUnknown && memberList.Count > 0)
        {
            Member unknownMem = FindUnknownMember(level.GetMembers());
            if(unknownMem != null)
                memberList.Add(unknownMem.UniqueName);
        }

        if (memberList.Count > 0)
            return "{" + string.Join(",", memberList.ToArray()) + "}";
        else
            return level.UniqueName;
    }

    private static Member FindUnknownMember(MemberCollection coll)
    {
        Member retVal = null;
        foreach (Member curMem in coll)
        {
            if (curMem.UniqueName.EndsWith(".UNKNOWNMEMBER"))
                retVal = curMem;
            else if (curMem.ChildCount > 0)
                retVal = FindUnknownMember(curMem.GetChildren());

            if (retVal != null)
                break;
        }
        return retVal;
    }

    public static string BuildMdxWhereTuples(List<SessionInfo> sessionList, Dictionary<string, List<string>> reportFilters, bool ignoreDeviceFilter, string prefixIfNotEmpty, bool encloseInParenthesis)
    {
        string curStr = null;
        List<string> whereList = new List<string>();

        if (sessionList != null)
        {
            curStr = BuildMdxSessionTuple(false, sessionList);
            if (curStr != null)
                whereList.Add(curStr);
        }

        foreach (string dimKey in reportFilters.Keys)
        {
            if (!(ignoreDeviceFilter && dimKey != null && dimKey.StartsWith("[Device]")))
            {
                List<string> memberList = reportFilters[dimKey];
                if (memberList != null && memberList.Count > 0)
                {
                    whereList.Add("{" + string.Join(",", memberList.ToArray()) + "}");
                }
            }
        }

        if (whereList.Count > 0)
        {
            if(encloseInParenthesis)
                return string.Format("{0}({1})", prefixIfNotEmpty, string.Join(",", whereList.ToArray()));
            else
                return string.Format("{0}{1}", prefixIfNotEmpty, string.Join(",", whereList.ToArray()));
        }
        else
            return null;
    }

    public static string BuildMdxWhereTuples(bool isLegacy, List<SessionInfo> sessionList, List<CellInfo> cellList, List<DeviceInfo> deviceList)
    {
        string curStr = null;
        List<string> whereList = new List<string>();

        if (sessionList != null)
        {
            curStr = BuildMdxSessionTuple(isLegacy, sessionList);
            if (curStr != null)
                whereList.Add(curStr);
        }

        if (cellList != null)
        {
            curStr = BuildMdxCellTuple(isLegacy, cellList);
            if (curStr != null)
                whereList.Add(curStr);
        }

        if (deviceList != null)
        {
            curStr = BuildMdxDeviceTuple(isLegacy, deviceList);
            if (curStr != null)
                whereList.Add(curStr);
        }

        return string.Join(",", whereList.ToArray());
    }

    private static string BuildMdxSessionTuple(bool isLegacy, List<SessionInfo> sessionList)
    {
        List<string> memberList = new List<string>();
        foreach (SessionInfo session in sessionList)
        {
            try
            {
                string uniqueName = (isLegacy?"[Test Session].[Session].&[":"[Session].[Session].&[") + session.SessionId.ToString() + "]";
                if (!memberList.Contains(uniqueName))
                    memberList.Add(uniqueName);
            }
            catch { }
        }

        if (memberList.Count > 0)
            return "{" + string.Join(",", memberList.ToArray()) + "}";
        else
            return null;
    }

    public static string BuildMdxSessionTuple(bool isLegacy, List<int> sessionIdList)
    {
        List<string> memberList = new List<string>();
        foreach (int sessionId in sessionIdList)
        {
            try
            {
                string uniqueName = (isLegacy?"[Test Session].[Session].&[":"[Session].[Session].&[") + sessionId.ToString() + "]";
                if (!memberList.Contains(uniqueName))
                    memberList.Add(uniqueName);
            }
            catch { }
        }

        if (memberList.Count > 0)
            return "{" + string.Join(",", memberList.ToArray()) + "}";
        else
            return null;
    }

    public static string BuildMdxCellTuple(bool isLegacy, List<CellInfo> cellList)
    {
        List<string> memberList = new List<string>();
        foreach (CellInfo cell in cellList)
        {
            try
            {
                string uniqueName = (isLegacy ? "[Test Location].[Location-Cell].[Cell].&[" : "[Cell].[Location - Cell].[Cell].&[") + cell.CellId.ToString() + "]";
                if (!memberList.Contains(uniqueName))
                    memberList.Add(uniqueName);
            }
            catch { }
        }

        if (memberList.Count > 0)
            return "{" + string.Join(",", memberList.ToArray()) + "}";
        else
            return null;
    }

    public static string BuildMdxDeviceTupleFromFilters(Dictionary<string, List<string>> reportFilters)
    {
        string retVal = null;
        foreach (string dimKey in reportFilters.Keys)
        {
            if (dimKey != null && dimKey.StartsWith("[Device]"))
            {
                List<string> memberList = reportFilters[dimKey];
                if (memberList != null && memberList.Count > 0)
                {
                    retVal = "{" + string.Join(",", memberList.ToArray()) + "}";
                }
            }
        }

        return retVal;
    }
    
    public static string BuildMdxDeviceTuple(bool isLegacy, List<DeviceInfo> deviceList)
    {
        List<string> memberList = new List<string>();
        foreach (DeviceInfo device in deviceList)
        {
            try
            {
                string uniqueName = (isLegacy ? "[Device].[Device Type-Device].[Device].&[" : "[Device].[Type - Device].[Device].&[") + device.DeviceId.ToString() + "]";
                if (!memberList.Contains(uniqueName))
                    memberList.Add(uniqueName);
            }
            catch { }
        }

        if (memberList.Count > 0)
            return "{" + string.Join(",", memberList.ToArray()) + "}";
        else
            return null;
    }

    public static Dictionary<string, string> GetDevicesFromFilters(Dictionary<string, List<string>> reportFilters)
    {
        Dictionary<string, string> retVal = new Dictionary<string, string>();

        foreach (string dimKey in reportFilters.Keys)
        {
            if (dimKey != null && dimKey.StartsWith("[Device]"))
            {
                retVal = GetMembersFromUniqueNames(DieboldConstants.DEVICE_FILTER_DIMENSION_NAME, reportFilters[dimKey], false);
            }
        }

        return retVal;
    }

    public static Dictionary<string, string> GetMembersFromUniqueNames(string dimensionLevelUniqueName, List<string> uniqueMemberNames, bool groupByCaptionPrefix)
    {
        Dictionary<string, string> retVal = new Dictionary<string, string>();
        CubeDef cube = getCube(false);
        Level level = (Level)cube.GetSchemaObject(SchemaObjectType.ObjectTypeLevel, dimensionLevelUniqueName);

        if (uniqueMemberNames != null && uniqueMemberNames.Count > 0)
        {
            foreach (string memberUniqueName in uniqueMemberNames)
            {
                try
                {
                    Member curMem = (Member)cube.GetSchemaObject(SchemaObjectType.ObjectTypeMember, memberUniqueName);
                    if (!retVal.ContainsKey(curMem.UniqueName))
                    {
                        if (curMem.Parent != null && retVal.ContainsKey(curMem.Parent.UniqueName))
                            retVal.Remove(curMem.Parent.UniqueName);

                        if (!curMem.UniqueName.EndsWith(".UNKNOWNMEMBER"))
                        {
                            if (groupByCaptionPrefix)
                            {
                                string[] captionNameParts = curMem.Caption.Split(new string[] { ":" }, StringSplitOptions.RemoveEmptyEntries);
                                string captionPrefix = captionNameParts[0];
                                string groupKey = null;
                                foreach (string key in retVal.Keys)
                                {
                                    if (string.Compare(retVal[key], captionPrefix, true) == 0)
                                    {
                                        groupKey = key;
                                        break;
                                    }
                                }

                                if (groupKey == null)
                                {
                                    retVal.Add(curMem.UniqueName, captionPrefix);
                                }
                                else
                                {
                                    retVal.Remove(groupKey);
                                    retVal.Add(groupKey + "\n" + curMem.UniqueName, captionPrefix);
                                }
                            }
                            else
                            {
                                retVal.Add(curMem.UniqueName, curMem.Caption);
                            }
                        }
                    }
                }
                catch { }
            }
        }

        return retVal;
    }

    public static string GetTimeHier(ReportHelper.DateGroupingEnum groupingId)
    {
        string hierName = null;
        switch (groupingId)
        {
            case ReportHelper.DateGroupingEnum.BY_DATE:
                hierName = "Year-Month-Date";
                break;
            case ReportHelper.DateGroupingEnum.BY_WEEK:
                hierName = "Year-Week-Date";
                break;
            case ReportHelper.DateGroupingEnum.BY_DATETIME:
                hierName = "Year-Month-Date";
                break;
            case ReportHelper.DateGroupingEnum.BY_MONTH:
                hierName = "Year-Month-Date";
                break;
            case ReportHelper.DateGroupingEnum.BY_ALL_TIME:
            default:
                hierName = "Year-Month-Date";
                break;
        }
        return hierName;
    }

    public static string GetTimeLevel(ReportHelper.DateGroupingEnum groupingId)
    {
        return GetTimeLevel(groupingId, DateTime.MinValue, DateTime.MinValue);
    }

    public static string GetTimeLevel(ReportHelper.DateGroupingEnum groupingId, DateTime startDate, DateTime endDate)
    {
        string levelName = null;
        switch (groupingId)
        {
            case ReportHelper.DateGroupingEnum.BY_DATE:
                levelName = "Date";
                break;
            case ReportHelper.DateGroupingEnum.BY_WEEK:
                levelName = "Week";
                break;
            case ReportHelper.DateGroupingEnum.BY_DATETIME:
                levelName = "DateTime";
                break;
            case ReportHelper.DateGroupingEnum.BY_MONTH:
                levelName = "Month";
                break;
            case ReportHelper.DateGroupingEnum.BY_ALL_TIME:
            default:
                bool hasDate = false;
                if (!DateTime.MinValue.Equals(startDate))
                {
                    if (startDate.Day != 1)
                        hasDate = true;
                }
                if (!DateTime.MinValue.Equals(endDate))
                {
                    if (endDate.Day != endDate.AddDays(1 - endDate.Day).AddMonths(1).AddDays(-1).Day)
                        hasDate = true;
                }
                if (hasDate)
                    levelName = "Date";
                else
                    levelName = "Month";
                break;
        }
        return levelName;
    }

    public static string BuildMdxFilteredTime(ReportHelper.DateGroupingEnum groupingId, DateTime startDate, DateTime endDate, string setName)
    {
        StringBuilder retVal = new StringBuilder();
        string hierName = GetTimeHier(groupingId);
        string levelName = GetTimeLevel(groupingId, startDate, endDate);

        if (string.IsNullOrEmpty(setName))
            throw new ApplicationException("The setName parameter must not be null.");

        if (DateTime.MinValue.Equals(startDate))
            retVal.Append(string.Format("MEMBER [{0}_StartRank] AS '0'\r\n", setName));
        else
            retVal.Append(string.Format("MEMBER [{0}_StartRank] AS 'RANK(ANCESTOR([Time].[{1}].[DateTime].&[{3}], [Time].[{1}].[{2}]), [Time].[{1}].[{2}].Members)'\r\n", 
                setName, hierName, levelName, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        if (DateTime.MinValue.Equals(endDate))
            retVal.Append(string.Format("MEMBER [{0}_EndRank] AS '0'\r\n", setName));
        else
            retVal.Append(string.Format("MEMBER [{0}_EndRank] AS 'RANK(ANCESTOR([Time].[{1}].[DateTime].&[{3}], [Time].[{1}].[{2}]), [Time].[{1}].[{2}].Members)'\r\n",
                setName, hierName, levelName, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        if (groupingId == ReportHelper.DateGroupingEnum.BY_ALL_TIME)
        {
            retVal.Append(string.Format("SET {0} AS '[Time].[{1}].[All]'\r\n", setName, hierName));
        }
        else
        {
            retVal.Append(string.Format("SET {0} AS 'IIF([{0}_EndRank] < 1, SUBSET([Time].[{1}].[{2}].Members, IIF([{0}_StartRank] < 1, 1, [{0}_StartRank])-1), SUBSET([Time].[{1}].[{2}].Members, "
                + "IIF([{0}_StartRank] < 1, 1, [{0}_StartRank])-1, [{0}_EndRank] - IIF([{0}_StartRank] < 1, 1, [{0}_StartRank]) + 1))'\r\n",
                setName, hierName, levelName));
        }

        return retVal.ToString();
    }
    
    public static string BuildMdxFilteredTime(ReportHelper.DateGroupingEnum groupingId, DateTime startDate, DateTime endDate, string optionalSetName, string optionalFilter)
    {
        string retVal = null;
        string hierName = GetTimeHier(groupingId);
        string levelName = null;
        string startRank = null;
        string endRank = null;

        switch (groupingId)
        {
            case ReportHelper.DateGroupingEnum.BY_DATE:
                if (!DateTime.MinValue.Equals(startDate))
                {
                    startDate = startDate.Date;
                }
                if (!DateTime.MinValue.Equals(endDate))
                {
                    if (TimeSpan.Zero.Equals(endDate.TimeOfDay))
                        endDate = endDate.Date;
                    else
                        endDate = endDate.Date.AddDays(1);
                }
                levelName = "Date";
                break;
            case ReportHelper.DateGroupingEnum.BY_WEEK:
                if (!DateTime.MinValue.Equals(startDate))
                {
                    startDate = startDate.Date.AddDays(0 - (int)startDate.DayOfWeek);
                }
                if (!DateTime.MinValue.Equals(endDate))
                {
                    endDate = endDate.Date.AddDays(0 - (int)endDate.DayOfWeek + 7);
                }
                levelName = "Week";
                break;
            case ReportHelper.DateGroupingEnum.BY_DATETIME:
                levelName = "DateTime";
                break;
            case ReportHelper.DateGroupingEnum.BY_MONTH:
                if (!DateTime.MinValue.Equals(startDate))
                {
                    startDate = startDate.Date.AddDays(1 - startDate.Day);
                }
                if (!DateTime.MinValue.Equals(endDate))
                {
                    endDate = endDate.Date.AddDays(1 - endDate.Day).AddMonths(1);
                }
                levelName = "Month";
                break;
            case ReportHelper.DateGroupingEnum.BY_ALL_TIME:
            default:
                bool hasDate = false;
                if (!DateTime.MinValue.Equals(startDate))
                {
                    startDate = startDate.Date;
                    if (startDate.Day != 1)
                        hasDate = true;
                }
                if (!DateTime.MinValue.Equals(endDate))
                {
                    if (endDate.Day != endDate.AddDays(1 - endDate.Day).AddMonths(1).AddDays(-1).Day)
                        hasDate = true;
                    if (TimeSpan.Zero.Equals(endDate.TimeOfDay))
                        endDate = endDate.Date;
                    else
                        endDate = endDate.Date.AddDays(1);
                }
                if (hasDate)
                    levelName = "Date";
                else
                    levelName = "Month";
                break;
        }

        if (DateTime.MinValue.Equals(startDate))
            startRank = "0";
        else
            startRank = string.Format("RANK(ANCESTOR([Time].[{0}].[DateTime].&[{2}], [Time].[{0}].[{1}]), [Time].[{0}].[{1}].Members)",
               hierName, levelName, startDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"));

        if (DateTime.MinValue.Equals(endDate))
            endRank = "0";
        else
            endRank = string.Format("RANK(ANCESTOR([Time].[{0}].[DateTime].&[{2}], [Time].[{0}].[{1}]), [Time].[{0}].[{1}].Members)",
               hierName, levelName, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"));

        if (groupingId == ReportHelper.DateGroupingEnum.BY_ALL_TIME)
        {
            retVal = string.Format("[Time].[{0}].[All]", hierName);
        }
        else
        {
            retVal = string.Format("IIF({3} < 1, SUBSET([Time].[{0}].[{1}].Members, IIF({2} < 1, 1, {2})-1), SUBSET([Time].[{0}].[{1}].Members, "
                + "IIF({2} < 1, 1, {2})-1, {3} - IIF({2} < 1, 1, {2}) + 1))", hierName, levelName, startRank, endRank);
        }

        if (!string.IsNullOrEmpty(optionalFilter))
            retVal = string.Format("Filter({{{0}}}, {1})", retVal, optionalFilter);

        if (!string.IsNullOrEmpty(optionalSetName))
            retVal = string.Format(" SET {0} AS '{1}'\r\n", optionalSetName, retVal);
        else
            retVal = string.Format("{{{0}}}\r\n", retVal);

        return retVal;
    }

	public static string BuildMdxFilteredMeasure(string timeHier, DateTime startDate, DateTime endDate, string filtedTimeSetName, string measureName)
	{
		return BuildMdxFilteredMeasure(timeHier, startDate, endDate, filtedTimeSetName, measureName, "SUM");
	}

    // Note: Requires that BuildMdxFilteredTime is called first with filtedTimeSetName so that StartRank and EndRank members exists
    public static string BuildMdxFilteredMeasure(string timeHier, DateTime startDate, DateTime endDate, string filtedTimeSetName, string measureName, string aggregateFunction)
    {
        StringBuilder retVal = new StringBuilder();

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND [{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]) "
                + "AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{3}]),"
                + aggregateFunction + "(SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
                + "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), {4}),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), measureName));
        }

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ aggregateFunction + "(SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), measureName));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ aggregateFunction + "(SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), measureName));
        }

        retVal.Append(measureName);

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        return retVal.ToString();
    }

    private static CubeDef getCube(bool isLegacy)
    {
		if (isLegacy)
		{
			AdomdConnection conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString);
			try
			{
				conn.Open();
			}
			catch
			{
				conn.Close(true);
				conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["DataWarehouse"].ConnectionString);
				conn.Open();
			}

			CubeDef cube = conn.Cubes["Transactions"];
			if (cube == null)
				throw new ApplicationException("Invalid cube name - Transactions");

			return cube;
		}
		else
		{
			AdomdConnection conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString);
			try
			{
				conn.Open();
			}
			catch
			{
				conn.Close(true);
				conn = new AdomdConnection(ConfigurationManager.ConnectionStrings["ReportingAnalysis"].ConnectionString);
				conn.Open();
			}

			CubeDef cube = conn.Cubes["Reporting"];
			if (cube == null)
				throw new ApplicationException("Invalid cube name - Reporting");

			return cube;
		}
    }
}
