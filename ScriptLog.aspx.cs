﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QueueServiceClient;

public partial class ScriptLog : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
    {
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetCellScriptExecutionList");
		ds.Relations.Add("Cells", ds.Tables[0].Columns["ScriptName"], ds.Tables[1].Columns["ScriptName"]);

		Dictionary<string, string> registeredFileList = SourceControlClient.GetRegisteredFiles();
		List<CellScriptExecution> unRegisteredList = new List<CellScriptExecution>();
		List<CellScriptExecution> registeredList = new List<CellScriptExecution>();

		foreach (DataRow row in ds.Tables[0].Rows)
		{
			CellScriptExecution item = new CellScriptExecution();
			item.ScriptName = DataFormatter.getString(row, "ScriptName");

			bool foundMatch = false;
			foreach (string fileName in registeredFileList.Keys)
			{
				if (string.Compare(DataFormatter.getString(row, "ScriptName"), fileName, true) == 0)
				{
					foreach (DataRow cellRow in row.GetChildRows("Cells"))
					{
						CellScriptExecution childItem = new CellScriptExecution();
						childItem.CellId = DataFormatter.getInt32(cellRow, "CellId");
						childItem.CellName = DataFormatter.getString(cellRow, "CellName");
						childItem.ScriptName = DataFormatter.getString(cellRow, "ScriptName");
						childItem.LastExecutionDate = DataFormatter.getDateTime(cellRow, "LastExecutionDate");

						item.Children.Add(childItem);
					}
					registeredList.Add(item);
					foundMatch = true;
					break;
				}
			}

			if (!foundMatch)
			{
				foreach (DataRow cellRow in row.GetChildRows("Cells"))
				{
					CellScriptExecution childItem = new CellScriptExecution();
					childItem.CellId = DataFormatter.getInt32(cellRow, "CellId");
					childItem.CellName = DataFormatter.getString(cellRow, "CellName");
					childItem.ScriptName = DataFormatter.getString(cellRow, "ScriptName");
					childItem.LastExecutionDate = DataFormatter.getDateTime(cellRow, "LastExecutionDate");

					item.Children.Add(childItem);
				}
				unRegisteredList.Add(item);
			}
		}

		if (unRegisteredList.Count != 0)
		{
			unRegisteredFilesRep.DataSource = unRegisteredList;
			unRegisteredFilesRep.DataBind();
		}
		else
		{
			this.unregMessage.Visible = true;
		}

		if (registeredList.Count > 0)
		{
			registeredFilesRep.DataSource = registeredList;
			registeredFilesRep.DataBind();
		}
		else
		{
			this.regMessage.Visible = true;
		}
    }
}