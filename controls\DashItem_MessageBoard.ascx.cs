using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class controls_DashItem_MessageBoard : System.Web.UI.UserControl, IDashboardItem
{
    public bool UseMasterUser
    {
        get { if (this.ViewState["um"] != null) return (bool)this.ViewState["um"]; else return false; }
        set { this.ViewState["um"] = value; }
    }
    
    DashMessageInfo dashInfo = null;
	public DashMessageInfo DashInfo
	{
		get
		{
			if (dashInfo != null) { return dashInfo; }
			else
			{
				dashInfo = new DashMessageInfo();
                string ctrlXML = null;

                if (this.UseMasterUser)
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME));
                else
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, this.UserName));
                
				if (!string.IsNullOrEmpty(ctrlXML))
					dashInfo = (DashMessageInfo)XmlSerializable.LoadFromXmlText(typeof(DashMessageInfo), ctrlXML);

				return dashInfo;
			}
		}
		set { dashInfo = value; }
	}

	private string UserName = Utility.GetUserName();
	protected Guid UniqueControlGuid = Guid.Empty;

	protected void Page_Load(object sender, EventArgs e)
	{
	}

    private void LoadMessages()
    {
        string key = "MessageBoardWidget:" + UniqueControlGuid.ToString("N");

        List<MessageInfo> msgColl = (List<MessageInfo>) this.Session[key];
        if (!this.Page.IsPostBack || msgColl == null)
        {
            msgColl = new List<MessageInfo>();
            DataSet ds = MessageSource.GetRecentMessages();
            if (ds.Tables[0] != null)
            {
                if (this.DashInfo != null && this.DashInfo.NumMessages != 0)
                {
                    for (int i = 0; i < this.DashInfo.NumMessages; i++)
                    {
                        if (ds.Tables[0].Rows.Count > i)
                            msgColl.Add(Utility.LoadMessageInfo(ds.Tables[0].Rows[i]));
                    }
                }
                else
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        msgColl.Add(Utility.LoadMessageInfo(row));
                    }
                }
            }

            this.Session[key] = msgColl;
        }

        MessagesRepeater.DataSource = msgColl;
        MessagesRepeater.DataBind();
    }

    public void Initialize(Guid dockUniqueName, bool useMasterUser)
    {
        this.UseMasterUser = useMasterUser;
        this.UniqueControlGuid = dockUniqueName;
	}

	public void DisplayEdit()
	{
		RadAjaxManager manager = RadAjaxManager.GetCurrent(this.Parent.Page);
		if (manager != null)
			manager.ResponseScripts.Add(string.Format("document.getElementById('{0}').click();", this.HidEditButton.ClientID));
	}

	public void EditSettings()
	{
		if (this.DashInfo != null)
		{
			if (this.NumMessagesList.Items.FindByValue(this.DashInfo.NumMessages.ToString()) != null)
				this.NumMessagesList.SelectedValue = this.DashInfo.NumMessages.ToString();
		}
		SettingsPanel.Visible = true;
		DisplayPanel.Visible = false;
	}

	public void SaveSettings()
	{
		this.DashInfo.NumMessages = Convert.ToInt32(NumMessagesList.SelectedValue);

        if (this.UseMasterUser)
            SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME, XmlSerializable.ConvertToXml(this.DashInfo));
        else
            SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, UserName, XmlSerializable.ConvertToXml(this.DashInfo));

		SettingsPanel.Visible = false;
		DisplayPanel.Visible = true;

        string key = "MessageBoardWidget:" + UniqueControlGuid.ToString("N");
        this.Session[key] = null;
	}

	protected void SaveSettings_Click(object sender, EventArgs e)
	{
		SaveSettings();
	}

	protected void EditSettings_Click(object sender, EventArgs e)
	{
		EditSettings();
	}

	protected void CancelSettings_Click(object sender, EventArgs e)
	{
		SettingsPanel.Visible = false;
		DisplayPanel.Visible = true;
	}

	public void DisplayContent()
	{
        LoadMessages();
    }

	protected void ViewAll_Click(object sender, EventArgs e)
	{
		Response.Redirect("Messages.aspx");
	}

}
