using System;
using System.Data;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using QueueServiceClient;
using Telerik.Web.UI;
using Atlassian.Jira;
using System.Configuration;
using System.Threading.Tasks;
using System.Threading;
using System.Text;

public partial class EditObservation : System.Web.UI.Page
{
	public Int64 ObservationId
	{
		get { if (this.ViewState["o"] != null) return (Int64)this.ViewState["o"]; else return 0; }
		set { this.ViewState["o"] = value; }
	}

	public Int64 TranId
	{
		get { if (this.ViewState["t"] != null) return (Int64)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}

	public Dictionary<int, string> SelectedSolutionStates;

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			this.SelectedSolutionStates = new Dictionary<int, string>();

            if (!Utility.IsUserAdmin()) {
                this.observationField.Enabled = false;
                this.cumTranCountField.Enabled = false;
                this.cumMediaCountField.Enabled = false;
                this.moduleTypeList.Enabled = false;
                this.failureLocationList.Enabled = false;
                this.failureTypeList.Enabled = false;
                this.disciplineList.Enabled = false;
                this.investigationAreaList.Enabled = false;
                this.triageTypeList.Enabled = false;
                this.ownerList.Enabled = false;
                this.operatorList.Enabled = false;
                this.censorCheck.Enabled = false;
                this.projectedDate.Enabled = false;
                this.scrNumberField.Enabled = false;
                this.linkField.Enabled = false;
            }

            LoadDropDownData();

            this.dateField.SelectedDate = DateTime.Today;
            this.timeField.SelectedDate = DateTime.Now;

			if (!string.IsNullOrEmpty(Request.Params["o"]))
            {
                this.ObservationId = Convert.ToInt64(Request.Params["o"]);
				LoadObservation();
                this.transactionField.Enabled = false;

				//load a list of all selected solution state values
				DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservationSolutionStates", this.ObservationId);
				if (ds != null && ds.Tables[0] != null) {
					foreach (DataRow row in ds.Tables[0].Rows) {
						if (!SelectedSolutionStates.ContainsKey((int)row["SolutionStateId"])) {
							if ((bool)row["Pending"]) {
								this.SelectedSolutionStates.Add((int)row["SolutionStateId"], "pending");
							}
							else if ((bool)row["Complete"]) {
								this.SelectedSolutionStates.Add((int)row["SolutionStateId"], "complete");
							}
							else if ((bool)row["Skip"]) {
								this.SelectedSolutionStates.Add((int)row["SolutionStateId"], "skip");
							}
						}
					}
				}
            }
            else
            {
				if (!string.IsNullOrEmpty(Request.Params["t"]))
				{
					this.TranId = Convert.ToInt64(Request.Params["t"]);
					LoadTransaction();
					
					this.SettingsLink.HRef = string.Format("settings.aspx?t={0}", this.TranId);
					this.StatsLink.HRef = string.Format("statistics.aspx?t={0}", this.TranId);
				}

				TransactionNoRow.Visible = false;
				CumTranCountRow.Visible = true;
				CumMediaCountRow.Visible = true;
				picturesRow.Visible = false;
				settingsRow.Visible = false;
				statisticsRow.Visible = false;
                this.DelBtnDiv.Visible = false;
            }

			solutionStateRep.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_SolutionState");
			solutionStateRep.DataBind();

			LoadFileData();

            //launch Jira look up async
            RegisterAsyncTask(new PageAsyncTask(LookupItemInJira));
            Page.ExecuteRegisteredAsyncTasks();
        }
    }

    protected async Task LookupItemInJira() {
        try
        {
            Issue x = null;
            int timeout = 10000; //10 seconds
            DateTime lookupStart = DateTime.Now;
            System.Diagnostics.Debug.WriteLine("LookingUpJira");
            if (!string.IsNullOrEmpty(scrNumberField.Text)) {
                var task = JiraRestApi.GetIssueById(scrNumberField.Text);

                if (await Task.WhenAny(task, Task.Delay(timeout)) == task) {
                    //complete
                    x = task.Result;
                    //DateTime lookupEnd = DateTime.Now;
                    //jiraErrorDiv.InnerHtml = "JIRA sync completed in  " + (lookupEnd - lookupStart).TotalMilliseconds + " milliseconds.";
                    //jiraErrorDiv.Attributes["style"] = "background-color:#0a0; color:#fff; padding:10px;";
                    //jiraErrorDiv.Visible = true;
                }
                else {
                    //timeout
                    jiraErrorDiv.InnerHtml = "JIRA sync timeout occurred. Please ensure JIRA is online.";
                    jiraErrorDiv.Visible = true;
                }
            }
            else if (!string.IsNullOrEmpty(linkField.Text)) {
                var task = JiraRestApi.GetIssueByUrl(linkField.Text);
                if (await Task.WhenAny(task, Task.Delay(timeout)) == task) {
                    x = task.Result;
                }
                else {
                    jiraErrorDiv.InnerHtml = "JIRA sync timeout. Please ensure JIRA is online.";
                    jiraErrorDiv.Visible = true;
                }
            }
            else
            {
                var task = JiraRestApi.GetIssueByUrl("https://jerry.dieboldnixdorf.com/browse/PLAYHWRMV2-236");
                if (await Task.WhenAny(task, Task.Delay(timeout)) == task)
                {
                    System.Diagnostics.Debug.WriteLine("TasK Result");
                    System.Diagnostics.Debug.WriteLine(task.Result);
                    System.Diagnostics.Debug.WriteLine(JiraRestApi.GetIssueById("PLAYHWRMV2-236").Result);

                    x = task.Result;
                }
                else
                {
                    jiraErrorDiv.InnerHtml = "JIRA sync timeout. Please ensure JIRA is online.";
                    jiraErrorDiv.Visible = true;
                }
            }

            if (x != null)
            {
                //make sure issue number field and link field are filled in with correct data
                scrNumberField.Text = x.Key.Value;
                linkField.Text = ConfigurationManager.AppSettings["JiraRestApi_URL"] + "/browse/" + x.Key.Value;
                linkFieldActiveLink.Attributes.Add("href", linkField.Text);
                linkFieldActiveLink.Visible = true;

                //fill in Triage Name, if not exists, create a new one and select
                UpdateTriageNameFromJira(x.Summary);

                JiraUser jUser = null;
                var userTask = JiraRestApi.GetUser(x.Assignee);
                if (await Task.WhenAny(userTask, Task.Delay(timeout)) == userTask)
                {
                    //complete
                    jUser = userTask.Result;
                }
                else
                {
                    //timeout
                    jiraErrorDiv.InnerHtml = "JIRA sync timeout occurred. Please ensure JIRA is online.";
                    jiraErrorDiv.Visible = true;
                }

                string userName = x.Assignee;
                if (jUser != null)
                {
                    if (!string.IsNullOrEmpty(jUser.DisplayName) && jUser.DisplayName.Contains(" "))
                        userName = jUser.DisplayName;
                    else if (!string.IsNullOrEmpty(jUser.Email))
                        userName = jUser.Email;
                }

                //check if owner exists, if not create a new one, and select
                UpdateOwnerFromJira(userName);

                UpdateSolutionStatesFromJira(x.Status.Name);

                //get projected date
                //if (projectedDate.SelectedDate == null) {
                if (x.ResolutionDate != null)
                {
                    projectedDate.SelectedDate = x.ResolutionDate;
                }
                else if (x.DueDate != null)
                {
                    projectedDate.SelectedDate = x.DueDate;
                }
                //}

                foreach (ProjectComponent component in x.Components)
                {
                    //look for a match on Modules (Modules use Device Type List)
                    foreach (TypeCodeEntry moduleDevice in Utility.GetDeviceTypeList())
                    {
                        if (string.Compare(moduleDevice.Name, component.Name, true) == 0)
                        {
                            moduleTypeList.SelectedValue = moduleDevice.Code;
                            break;
                        }
                    }

                    //look for a match on Disciplines
                    foreach (TypeCodeEntry discipline in Utility.GetDisciplineList())
                    {
                        if (string.Compare(discipline.Name, component.Name, true) == 0)
                        {
                            disciplineList.SelectedValue = discipline.Code;
                            break;
                        }
                    }
                }
            }
        }
        catch (Exception ex) {
            if (ex.Message.Contains("Issue Does Not Exist")) {
                jiraErrorDiv.InnerHtml = "JIRA sync failed because specified issue number does not exist.";
            }
            else if (ex.Message.Contains("Unauthorized") || ex.Message.Contains("Forbidden")) {
                jiraErrorDiv.InnerHtml = "JIRA sync failed because user '" + ConfigurationManager.AppSettings["JiraRestApi_Username"] + "' is not authorized to view the requested issue.";
            }
            else {
                jiraErrorDiv.InnerHtml = "JIRA sync reported error: " + ex.Message;
            }
            jiraErrorDiv.Visible = true;
        }
    }

    private void UpdateSolutionStatesFromJira(string jiraStatus) {
        if (!string.IsNullOrEmpty(jiraStatus)) {
            HtmlInputRadioButton state1NotSetRadio = null;
            HtmlInputRadioButton state2NotSetRadio = null;
            HtmlInputRadioButton state3NotSetRadio = null;
            HtmlInputRadioButton state4NotSetRadio = null;
            HtmlInputRadioButton state5NotSetRadio = null;
            HtmlInputRadioButton state6NotSetRadio = null;
            HtmlInputRadioButton state7NotSetRadio = null;

            HtmlInputRadioButton state1PendingRadio = null;
            HtmlInputRadioButton state2PendingRadio = null;
            HtmlInputRadioButton state3PendingRadio = null;
            HtmlInputRadioButton state4PendingRadio = null;
            HtmlInputRadioButton state5PendingRadio = null;
            HtmlInputRadioButton state6PendingRadio = null;
            HtmlInputRadioButton state7PendingRadio = null;

            HtmlInputRadioButton state1CompleteRadio = null;
            HtmlInputRadioButton state2CompleteRadio = null;
            HtmlInputRadioButton state3CompleteRadio = null;
            HtmlInputRadioButton state4CompleteRadio = null;
            HtmlInputRadioButton state5CompleteRadio = null;
            HtmlInputRadioButton state6CompleteRadio = null;
            HtmlInputRadioButton state7CompleteRadio = null;

            HtmlInputRadioButton state1SkipRadio = null;
            HtmlInputRadioButton state2SkipRadio = null;
            HtmlInputRadioButton state3SkipRadio = null;
            HtmlInputRadioButton state4SkipRadio = null;
            HtmlInputRadioButton state5SkipRadio = null;
            HtmlInputRadioButton state6SkipRadio = null;
            HtmlInputRadioButton state7SkipRadio = null;

            foreach (RepeaterItem item in solutionStateRep.Items) {
                HiddenField hiddenId = (HiddenField)item.FindControl("SolutionStateId");
                if (hiddenId.Value == "1") {
                    state1NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state1SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state1CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state1PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
                else if (hiddenId.Value == "2") {
                    state2NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state2SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state2CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state2PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
                else if (hiddenId.Value == "3") {
                    state3NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state3SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state3CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state3PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
                else if (hiddenId.Value == "4") {
                    state4NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state4SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state4CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state4PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
                else if (hiddenId.Value == "5") {
                    state5NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state5SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state5CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state5PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
                else if (hiddenId.Value == "6") {
                    state6NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state6SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state6CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state6PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
                else if (hiddenId.Value == "7") {
                    state7NotSetRadio = ((HtmlInputRadioButton)item.FindControl("notset"));
                    state7SkipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));
                    state7CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
                    state7PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
                }
            }

            //clear previous selections
            state1NotSetRadio.Checked = false;
            state2NotSetRadio.Checked = false;
            state3NotSetRadio.Checked = false;
            state4NotSetRadio.Checked = false;
            state5NotSetRadio.Checked = false;
            state6NotSetRadio.Checked = false;
            state7NotSetRadio.Checked = false;

            state1PendingRadio.Checked = false;
            state2PendingRadio.Checked = false;
            state3PendingRadio.Checked = false;
            state4PendingRadio.Checked = false;
            state5PendingRadio.Checked = false;
            state6PendingRadio.Checked = false;
            state7PendingRadio.Checked = false;

            state1CompleteRadio.Checked = false;
            state2CompleteRadio.Checked = false;
            state3CompleteRadio.Checked = false;
            state4CompleteRadio.Checked = false;
            state5CompleteRadio.Checked = false;
            state6CompleteRadio.Checked = false;
            state7CompleteRadio.Checked = false;

            state1SkipRadio.Checked = false;
            state2SkipRadio.Checked = false;
            state3SkipRadio.Checked = false;
            state4SkipRadio.Checked = false;
            state5SkipRadio.Checked = false;
            state6SkipRadio.Checked = false;
            state7SkipRadio.Checked = false;

            switch (jiraStatus.ToUpper()) {
                case "SUBMITTED":
                case "TO DO":
                    state1CompleteRadio.Checked = true;

                    state2PendingRadio.Checked = true;
                    state3PendingRadio.Checked = true;
                    state4PendingRadio.Checked = true;
                    state5PendingRadio.Checked = true;
                    state6PendingRadio.Checked = true;
                    state7PendingRadio.Checked = true;

                    break;
                case "IN DEVELOPMENT":
                case "IN DEV":
                    state1CompleteRadio.Checked = true;
                    state2CompleteRadio.Checked = true;

                    state3PendingRadio.Checked = true;
                    state4PendingRadio.Checked = true;
                    state5PendingRadio.Checked = true;
                    state6PendingRadio.Checked = true;
                    state7PendingRadio.Checked = true;
                    break;
                case "READY FOR BUILD":
                    state1CompleteRadio.Checked = true;
                    state2CompleteRadio.Checked = true;
                    state3CompleteRadio.Checked = true;

                    state4PendingRadio.Checked = true;
                    state5PendingRadio.Checked = true;
                    state6PendingRadio.Checked = true;
                    state7PendingRadio.Checked = true;
                    break;
                case "IN FIXING":
                case "READY FOR QA":
                    state1CompleteRadio.Checked = true;
                    state2CompleteRadio.Checked = true;
                    state3CompleteRadio.Checked = true;
                    state4CompleteRadio.Checked = true;

                    state5PendingRadio.Checked = true;
                    state6PendingRadio.Checked = true;
                    state7PendingRadio.Checked = true;
                    break;
                case "BLOCKED":
                    state1CompleteRadio.Checked = true;
                    state2CompleteRadio.Checked = true;
                    state3CompleteRadio.Checked = true;
                    state4CompleteRadio.Checked = true;
                    state5CompleteRadio.Checked = true;
                    
                    state6PendingRadio.Checked = true;
                    state7PendingRadio.Checked = true;
                    break;
                case "IN QA":
                    state1CompleteRadio.Checked = true;
                    state2CompleteRadio.Checked = true;
                    state3CompleteRadio.Checked = true;
                    state4CompleteRadio.Checked = true;
                    state5CompleteRadio.Checked = true;
                    state6CompleteRadio.Checked = true;

                    state7PendingRadio.Checked = true;
                    break;
                case "REJECTED":
                case "DONE":
                case "CLOSED":
                    state1CompleteRadio.Checked = true;
                    state2CompleteRadio.Checked = true;
                    state3CompleteRadio.Checked = true;
                    state4CompleteRadio.Checked = true;
                    state5CompleteRadio.Checked = true;
                    state6CompleteRadio.Checked = true;
                    state7CompleteRadio.Checked = true;
                    break;
            }
        }
    }

    private void UpdateTriageNameFromJira(string jiraSummary) {
        try {
            string devTypeAndScrNumber = "";
            string fullIssueName = "";
            if (!string.IsNullOrEmpty(jiraSummary)) {
                //format: DEVICENAME JIRA_ISSUE_NO JIRA_DESCRIPTION

                //get current device type name
                List<TypeCodeEntry> databaseDeviceTypeList = Utility.GetDeviceTypeList();
                foreach (TypeCodeEntry curDevType in databaseDeviceTypeList) {
                    if (curDevType.Code == deviceTypeList.SelectedValue) {
                        devTypeAndScrNumber = curDevType.Name + " " + scrNumberField.Text;
                        fullIssueName = curDevType.Name + " " + scrNumberField.Text + " " + jiraSummary;
                    }
                }

                if (!string.IsNullOrEmpty(fullIssueName)) {
                    ListItem triageListItem = null;

                    //look for entire string match
                    foreach (ListItem exisitingItem in this.triageTypeList.Items) {
                        if (string.Compare(exisitingItem.Text.Trim(), fullIssueName.Trim(), true) == 0) {
                            triageListItem = exisitingItem;
                            break;
                        }
                    }

                    if (triageListItem == null) {
                        //look for partial name matches without the Jira summary
                        foreach (ListItem exisitingItem in this.triageTypeList.Items) {
                            if (exisitingItem.Text.StartsWith(devTypeAndScrNumber)) {
                                //update the on screen list and select
                                exisitingItem.Text = fullIssueName;
                                triageListItem = exisitingItem;

                                //update the name in the database with Jira summary name change
                                SqlHelper.ExecuteNonQuery("RPT_UpdateTriageTypeName", exisitingItem.Value, fullIssueName);
                                break;
                            }
                        }
                    }

                    if (triageListItem != null) {
                        this.triageTypeList.SelectedValue = triageListItem.Value;
                    }
                    else {
                        //insert a new Triage Type from Jira for anything we don't already have.
                        int newId = (int)SqlHelper.ExecuteScalar("RPT_InsertTriageType", fullIssueName);

                        this.triageTypeList.Items.Add(new ListItem(fullIssueName, newId.ToString()));
                        this.triageTypeList.SelectedValue = newId.ToString();
                    }
                }
            }
        }
        catch (Exception ex) {
            jiraErrorDiv.InnerHtml = "JIRA error: " + ex.Message;
            jiraErrorDiv.Visible = true;
        }
    }

    private void UpdateOwnerFromJira(string jiraReporterName) {
        try {
            if (!string.IsNullOrEmpty(jiraReporterName)) {
                string ownerName = "";

                if (jiraReporterName.Contains(", ")) {
                    //DISPLAY NAME - sometimes come through from Jira API 'last, first'
                    string[] nameParts = jiraReporterName.Split(new string[] { ", " }, StringSplitOptions.RemoveEmptyEntries);
                    if (nameParts.Length >= 1) {
                        string lastName = nameParts[0];
                        string firstName = nameParts[1];
                        firstName = char.ToUpper(firstName[0]) + firstName.Substring(1);
                        lastName = char.ToUpper(lastName[0]) + lastName.Substring(1);
                        ownerName = firstName + " " + lastName;
                    }
                }
                else if (jiraReporterName.Contains(" ")) {
                    //DISPLAY NAME - sometimes come through from Jira API 'First Last'
                    string[] nameParts = jiraReporterName.Split(new string[] { " " }, StringSplitOptions.RemoveEmptyEntries);
                    if (nameParts.Length >= 1) {
                        string firstName = nameParts[0];
                        string lastName = nameParts[1];
                        firstName = char.ToUpper(firstName[0]) + firstName.Substring(1);
                        lastName = char.ToUpper(lastName[0]) + lastName.Substring(1);
                        ownerName = firstName + " " + lastName;
                    }
                }
                else if (jiraReporterName.Contains("@")) {
                    //EMAIL FORMAT - <EMAIL>
                    string[] emailParts = jiraReporterName.Split(new string[] { "@" }, StringSplitOptions.RemoveEmptyEntries);
                    string namePortion = emailParts[0];

                    string[] nameParts = namePortion.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);
                    if (nameParts.Length >= 1) {
                        string firstName = nameParts[0];
                        string lastName = nameParts[1];
                        firstName = char.ToUpper(firstName[0]) + firstName.Substring(1);
                        lastName = char.ToUpper(lastName[0]) + lastName.Substring(1);
                        ownerName = firstName + " " + lastName;
                    }
                }
                else if (jiraReporterName.Contains(".")) {
                    //usernames from Jira API come through as 'first.last'
                    string[] nameParts = jiraReporterName.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);
                    if (nameParts.Length >= 1) {
                        string firstName = nameParts[0];
                        string lastName = nameParts[1];
                        firstName = char.ToUpper(firstName[0]) + firstName.Substring(1);
                        lastName = char.ToUpper(lastName[0]) + lastName.Substring(1);
                        ownerName = firstName + " " + lastName;
                    }
                }

                if (!string.IsNullOrEmpty(ownerName)) {
                    ListItem ownerListItem = null;
                    foreach (ListItem existingItem in this.ownerList.Items) {
                        if (string.Compare(existingItem.Text.Trim(), ownerName.Trim(), true) == 0) {
                            ownerListItem = existingItem;
                            break;
                        }
                    }

                    if (ownerListItem != null) {
                        this.ownerList.SelectedValue = ownerListItem.Value;
                    }
                    else {
                        int newId = (int)SqlHelper.ExecuteScalar("RPT_InsertOperator", ownerName, true);

                        this.ownerList.Items.Add(new ListItem(ownerName, newId.ToString()));
                        this.ownerList.SelectedValue = newId.ToString();
                    }
                }
            }
            else {
                SqlHelper.ExecuteNonQuery("RPT_UpdateObservationOwner", null, this.ObservationId);
            }
        }
        catch (Exception ex) {
            jiraErrorDiv.InnerHtml = "JIRA error: " + ex.Message;
            jiraErrorDiv.Visible = true;
        }
    }

    private void LoadObservation()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservation", this.ObservationId);
		this.DelBtnDiv.Visible = false; // Assume false

        if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
        {
			DataRow row = ds.Tables[0].Rows[0];
			
			if (DataFormatter.getBool(row, "IsManualVolumeSession"))
			{
				this.CumTranCountRow.Visible = true;
		 		this.CumMediaCountRow.Visible = true;
				this.TransactionNoRow.Visible = false;
			}

            if (DataFormatter.getBool(row, "IsObservationOnly")) {
                this.CumTranCountRow.Visible = false;
                this.CumMediaCountRow.Visible = false;
                this.TransactionNoRow.Visible = false;
            }

            if (DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "") != null)
            {
				this.dateField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "")));
				this.timeField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "hh:mm:ss tt", "")));
            }

			this.TranId = DataFormatter.getInt64(row, "TranId");

			UploadFileBtn.Attributes.Add("onclick", String.Format("window.radopen('Files.aspx?t={0}&o={1}', null);return false;", this.TranId, this.ObservationId));

			this.SettingsLink.HRef = string.Format("settings.aspx?t={0}", this.TranId);
			this.StatsLink.HRef = string.Format("statistics.aspx?t={0}", this.TranId);

            this.PicturesButton.InnerText = "View Pictures (" + DataFormatter.getInt32(row, "PictureCount") + ")";
			
			if (DataFormatter.getInt32(row, "PictureCount") == 0)
				PicturesButton.Attributes.Add("onclick", String.Format("window.radopen('files.aspx?t={0}&o={1}', null);return false;", this.TranId, this.ObservationId));
			else
				PicturesButton.Attributes.Add("onclick", String.Format("window.radopen('pictures.aspx?t={0}&o={1}', null);return false;", this.TranId, this.ObservationId));

			this.scrNumberField.Text = DataFormatter.getString(row, "SCRNumber");
			this.observationField.Text = DataFormatter.getString(row, "ObservationText");
            this.censorCheck.Checked = DataFormatter.getBool(row, "IsCensored");
            this.transactionField.Text = DataFormatter.getInt32(row, "RDToolTranId").ToString();
			this.transactionNotesField.Text = DataFormatter.getString(row, "Notes");
			this.linkField.Text = DataFormatter.getString(row, "Link");

			if (!string.IsNullOrEmpty(linkField.Text)) {
				linkFieldActiveLink.Attributes.Add("href", linkField.Text);
				linkFieldActiveLink.Visible = true;
			}

			if (DataFormatter.getInt32(row, "CumTranCount") != 0)
				this.cumTranCountField.Text = DataFormatter.getInt32(row, "CumTranCount").ToString();

			if (DataFormatter.getInt32(row, "CumMediaCount") != 0)
				this.cumMediaCountField.Text = DataFormatter.getInt32(row, "CumMediaCount").ToString();

            int cellId = DataFormatter.getInt32(row, "CellId");
            if (this.cellList.FindItemByValue(cellId.ToString()) != null)
            {
                this.cellList.SelectedValue = cellId.ToString();
                this.cellList.Enabled = false;
            }
            else if (cellId > 0)
            {
                this.cellList.Items.Insert(1, new RadComboBoxItem(DataFormatter.getString(row, "CellName"), cellId.ToString()));
                this.cellList.SelectedIndex = 1;
                this.cellList.Enabled = false;
            }

            int sessId = DataFormatter.getInt32(row, "SessionId");
            if (this.sessionList.FindItemByValue(sessId.ToString()) != null)
            {
                this.sessionList.SelectedValue = sessId.ToString();
                this.sessionList.Enabled = false;
            }
            else if (sessId > 0)
            {
                this.sessionList.Items.Insert(1, new RadComboBoxItem(DataFormatter.getString(row, "SessionName"), sessId.ToString()));
                this.sessionList.SelectedIndex = 1;
                this.sessionList.Enabled = false;
            }

            if (this.deviceTypeList.FindItemByValue(DataFormatter.getInt32(row, "DeviceTypeId").ToString()) != null)
            {
				this.deviceTypeList.SelectedValue = DataFormatter.getInt32(row, "DeviceTypeId").ToString();
                this.deviceTypeList.Enabled = false;

                deviceList.DataSource = Utility.GetDevicesList(Convert.ToInt32(deviceTypeList.SelectedValue));
                deviceList.DataBind();

                int devId = DataFormatter.getInt32(row, "DeviceId");
                if (this.deviceList.FindItemByValue(devId.ToString()) != null)
                {
                    this.deviceList.SelectedValue = devId.ToString();
                    this.deviceList.Enabled = false;
                }
                else if (devId > 0)
                {
                    this.deviceList.Items.Insert(1, new RadComboBoxItem(DataFormatter.getString(row, "SerialNumber"), devId.ToString()));
                    this.deviceList.SelectedIndex = 1;
                    this.deviceList.Enabled = false;
                }

                failureLocationList.DataSource = Utility.GetFailureLocationList(Convert.ToInt32(deviceTypeList.SelectedValue));
                failureLocationList.DataBind();

                if (this.failureLocationList.FindItemByValue(DataFormatter.getInt32(row, "FailureLocationId").ToString()) != null)
                    this.failureLocationList.SelectedValue = DataFormatter.getInt32(row, "FailureLocationId").ToString();

                if (Utility.IsUserAdmin()) {
                    failureLocationList.Enabled = !string.IsNullOrEmpty(deviceTypeList.SelectedValue);
                }
            }

			if (this.moduleTypeList.FindItemByValue(DataFormatter.getInt32(row, "ModuleDeviceTypeId").ToString()) != null)
				this.moduleTypeList.SelectedValue = DataFormatter.getInt32(row, "ModuleDeviceTypeId").ToString();

            if (this.failureTypeList.FindItemByValue(DataFormatter.getInt32(row, "FailureTypeId").ToString()) != null)
                this.failureTypeList.SelectedValue = DataFormatter.getInt32(row, "FailureTypeId").ToString();

            if (this.investigationAreaList.FindItemByValue(DataFormatter.getInt32(row, "InvestigationAreaId").ToString()) != null)
                this.investigationAreaList.SelectedValue = DataFormatter.getInt32(row, "InvestigationAreaId").ToString();

			if (this.triageTypeList.Items.FindByValue(DataFormatter.getInt32(row, "TriageTypeId").ToString()) != null)
				this.triageTypeList.SelectedValue = DataFormatter.getInt32(row, "TriageTypeId").ToString();

            if (this.disciplineList.FindItemByValue(DataFormatter.getInt32(row, "DisciplineId").ToString()) != null)
				this.disciplineList.SelectedValue = DataFormatter.getInt32(row, "DisciplineId").ToString();

            if (this.agilisList.FindItemByValue(DataFormatter.getInt32(row, "AgilisId").ToString()) != null)
                this.agilisList.SelectedValue = DataFormatter.getInt32(row, "AgilisId").ToString();

			int ownerId = DataFormatter.getInt32(row, "OwnerId");
            if (this.ownerList.Items.FindByValue(ownerId.ToString()) != null) {
				this.ownerList.SelectedValue = ownerId.ToString();
			}
			else if (ownerId > 0) {
                this.ownerList.Items.Add(new ListItem(DataFormatter.getString(row, "OwnerName"), ownerId.ToString()));
                this.ownerList.SelectedValue = ownerId.ToString();
			}

            int opId = DataFormatter.getInt32(row, "OperatorId");
            if (this.operatorList.Items.FindByValue(opId.ToString()) != null) {
                this.operatorList.SelectedValue = opId.ToString();
            }
            else if (opId > 0) {
                this.operatorList.Items.Add(new ListItem(DataFormatter.getString(row, "OperatorName"), opId.ToString()));
                this.operatorList.SelectedValue = opId.ToString();
            }

			if (DataFormatter.FormatDate(row, "ProjectedSolutionDate", "MM/dd/yyyy", "") != null) {
				this.projectedDate.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "ProjectedSolutionDate", "MM/dd/yyyy", "")));
			}

            // Show delete button if no RDToolNodeNumber (manually created observation)
            this.DelBtnDiv.Visible = (DataFormatter.getInt32(row, "RDToolNodeNumber", -1) < 0);
        }
        else
        {
            Response.Redirect("Search.aspx");
        }
	}

	private void LoadTransaction()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadTransaction", this.TranId);

		foreach (DataRow row in ds.Tables[0].Rows)
		{
			if (DataFormatter.getBool(row, "IsManualVolumeSession"))
			{
				if (DataFormatter.getInt32(row, "CumTranCount") != 0)
					this.cumTranCountField.Text = DataFormatter.getInt32(row, "CumTranCount").ToString();

				if (DataFormatter.getInt32(row, "CumMediaCount") != 0)
					this.cumMediaCountField.Text = DataFormatter.getInt32(row, "CumMediaCount").ToString();

				this.CumTranCountRow.Visible = true;
				this.CumMediaCountRow.Visible = true;
				this.TransactionNoRow.Visible = false;
			}

			if (DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "") != null)
			{
				this.dateField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "")));
				this.timeField.SelectedDate = Convert.ToDateTime((DataFormatter.FormatDate(row, "TranDate", "hh:mm:ss tt", "")));
			}

			this.transactionNotesField.Text = DataFormatter.getString(row, "Notes");

			int cellId = DataFormatter.getInt32(row, "CellId");
			if (this.cellList.FindItemByValue(cellId.ToString()) != null)
			{
				this.cellList.SelectedValue = cellId.ToString();
				this.cellList.Enabled = false;
			}
			else if (cellId > 0)
			{
				this.cellList.Items.Insert(1, new RadComboBoxItem(DataFormatter.getString(row, "CellName"), cellId.ToString()));
				this.cellList.SelectedIndex = 1;
				this.cellList.Enabled = false;
			}

			int sessId = DataFormatter.getInt32(row, "SessionId");
			if (this.sessionList.FindItemByValue(sessId.ToString()) != null)
			{
				this.sessionList.SelectedValue = sessId.ToString();
				this.sessionList.Enabled = false;
			}
			else if (sessId > 0)
			{
				this.sessionList.Items.Insert(1, new RadComboBoxItem(DataFormatter.getString(row, "SessionName"), sessId.ToString()));
				this.sessionList.SelectedIndex = 1;
				this.sessionList.Enabled = false;
			}	
		}
	}

	protected void DownloadAllBtn_Click(object sender, EventArgs e)
	{
		List<Int64> tranIdList = new List<Int64>();
		List<Int64> observationIdList = new List<Int64>();
		List<string> filePatternList = new List<string>();
		List<string> deviceTypeList = new List<string>();
		List<string> serialNumberList = new List<string>();
		List<string> cellNameList = new List<string>();
		List<string> sessionNameList = new List<string>();
		List<DateTime> tranDateList = new List<DateTime>();

		List<DataFile> dataFiles = LoadFileData();
		foreach (DataFile dataFile in dataFiles)
		{
			if (!tranIdList.Contains(dataFile.TranId))
			{
				tranIdList.Add(dataFile.TranId);
				observationIdList.Add(dataFile.ObservationId);
				deviceTypeList.Add(dataFile.DeviceTypeName);
				serialNumberList.Add(dataFile.SerialNumber);
				cellNameList.Add(this.cellList.SelectedItem.Text);
				sessionNameList.Add(this.sessionList.SelectedItem.Text);
				tranDateList.Add(dataFile.TranDate);
				filePatternList.Add("CELL=" + this.cellList.SelectedValue + "_SESSION=" + this.sessionList.SelectedValue + "_TRX=" + this.transactionField.Text);
			}
		}
		
		byte[] result = TxFiles.DownloadZipCollection(false, filePatternList, tranIdList, observationIdList, cellNameList, sessionNameList, tranDateList, deviceTypeList, serialNumberList);
		if (result != null && result.Length > 0)
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = "application/octet-stream";
			Response.AppendHeader("content-disposition", "attachment; filename=ReliabilityFiles_" + this.TranId.ToString() + "_" + DateTime.Now.Day + DateTime.Now.Month + DateTime.Now.Year + DateTime.Now.Hour + DateTime.Now.Minute + DateTime.Now.Second + ".zip;");
			Response.BinaryWrite(result);
			Response.End();
		}
	}
	
	protected void SaveButton_Click(object sender, EventArgs e)
	{
        //await LookupItemInJira();
        RegisterAsyncTask(new PageAsyncTask(LookupItemInJira));
        Page.ExecuteRegisteredAsyncTasks();

        ErrorDiv.Visible = ErrorDiv1.Visible = false; 
		Page.Validate();
		
        // RadComboBox controls don't seem to validate required selections when 'Select...' is in the list - this is a work around
        if (string.IsNullOrEmpty(this.cellList.SelectedValue))
            cellValidator.IsValid = false;

        if (string.IsNullOrEmpty(this.sessionList.SelectedValue))
            sessionValidator.IsValid = false;

        if (string.IsNullOrEmpty(this.deviceTypeList.SelectedValue))
            deviceTypeValidator.IsValid = false;

        if (string.IsNullOrEmpty(this.deviceList.SelectedValue))
            deviceValidator.IsValid = false;

        if (Page.IsValid)
		{
			object rdToolTranId = null;
			if (!string.IsNullOrEmpty(this.transactionField.Text))
				rdToolTranId = Int32.Parse(this.transactionField.Text);

			object tranId = null;
			if (this.TranId != 0)
				tranId = this.TranId;

			object cumTranCount = null;
			if (!string.IsNullOrEmpty(this.cumTranCountField.Text))
				cumTranCount = this.cumTranCountField.Text;

			object cumMediaCount = null;
			if (!string.IsNullOrEmpty(this.cumMediaCountField.Text))
				cumMediaCount = this.cumMediaCountField.Text;

			string linkString = linkField.Text;
			if (!string.IsNullOrEmpty(linkString) && (!linkString.ToLower().StartsWith("http://") || !linkString.ToLower().StartsWith("https://"))) {
				//linkString = "http://" + linkString;
			}

            TimeSpan time = new TimeSpan(((DateTime)timeField.SelectedDate).Hour, ((DateTime)timeField.SelectedDate).Minute, ((DateTime)timeField.SelectedDate).Second);
            DateTime transactionDate = ((DateTime)dateField.SelectedDate).Add(time);

			//Verify that the CumTranCount is valid between the previous and next dated observation or manual transaction volume entries.
			string errorReason = Utility.VerifyCumulativeValues(tranId, transactionDate, this.cellList.SelectedValue, this.sessionList.SelectedValue, this.cumTranCountField.Text, this.cumMediaCountField.Text);
			
			if (!string.IsNullOrEmpty(errorReason))
			{
				errorReason = errorReason + "<li>Ensure that you are not using the same 'Cumulative Tran Count' value, or same 'Cumulative Media Count' value from any of the Transaction Volume records that are already entered.</li>";
				ErrorMessage.Text = ErrorMessage1.Text = errorReason;
				ErrorDiv.Visible = ErrorDiv1.Visible = true;
			}
			else
			{
				if (this.ObservationId != 0)
				{
					//Update Observation
					int success = SqlHelper.ExecuteNonQuery("RPT_UpdateObservation", this.ObservationId, this.TranId, this.deviceList.SelectedValue, this.failureTypeList.SelectedValue,
								this.failureLocationList.SelectedValue, this.investigationAreaList.SelectedValue, transactionDate, this.scrNumberField.Text, this.severityValueHid.Value,
                                this.agilisList.SelectedValue, this.ownerList.SelectedValue, this.operatorList.SelectedValue, this.censorCheck.Checked, this.observationField.Text, this.transactionNotesField.Text, linkString,
								cumTranCount, cumMediaCount, this.moduleTypeList.SelectedValue, this.triageTypeList.SelectedValue, 
								this.disciplineList.SelectedValue, this.projectedDate.SelectedDate);

					//remove all prior Solution State Values
					SqlHelper.ExecuteNonQuery("RPT_ClearObservationSolutionStates", this.ObservationId);

					//insert new Solution State Values
					InsertSolutionStateValues();
				}
				else
				{
					//Insert New Observation
					this.ObservationId = (Int64)SqlHelper.ExecuteScalar("RPT_InsertObservation", this.cellList.SelectedValue, this.sessionList.SelectedValue, rdToolTranId,
								this.deviceList.SelectedValue, this.failureTypeList.SelectedValue, this.failureLocationList.SelectedValue, this.investigationAreaList.SelectedValue, transactionDate,
								this.scrNumberField.Text, this.severityValueHid.Value, this.agilisList.SelectedValue, this.ownerList.SelectedValue, this.operatorList.SelectedValue, this.censorCheck.Checked,
								this.observationField.Text, this.transactionNotesField.Text, linkString, cumTranCount, cumMediaCount, this.moduleTypeList.SelectedValue, this.triageTypeList.SelectedValue, 
								this.disciplineList.SelectedValue, this.projectedDate.SelectedDate);

					//insert new Solution State Values
					InsertSolutionStateValues();
				}
				Response.Redirect("Search.aspx");
			}
		}
	}

	protected void InsertSolutionStateValues() {
		foreach (RepeaterItem item in solutionStateRep.Items) {
			HiddenField hiddenId = (HiddenField)item.FindControl("SolutionStateId");
			HtmlInputRadioButton pendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
			HtmlInputRadioButton completeRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
			HtmlInputRadioButton skipRadio = ((HtmlInputRadioButton)item.FindControl("skip"));

			if (hiddenId != null && ((pendingRadio != null && pendingRadio.Checked) || (completeRadio != null && completeRadio.Checked) || (skipRadio != null && skipRadio.Checked))) {
				SqlHelper.ExecuteNonQuery("RPT_InsertObservationSolutionState", this.ObservationId, hiddenId.Value, pendingRadio.Checked, completeRadio.Checked, skipRadio.Checked);
			}
		}
	}

    protected bool IsSolutionStateDisabled() {
        return !Utility.IsUserAdmin();
    }


    protected void CancelButton_Click(object sender, EventArgs e)
	{
		Response.Redirect("Search.aspx");
	}

    protected void DeleteButton_Click(object sender, EventArgs e)
    {
        // Check for images and execution id
        DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservation", this.ObservationId);

        if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
        {
            DataRow row = ds.Tables[0].Rows[0];
            if ((DataFormatter.getInt32(row, "RDToolNodeNumber", -1) < 0))
            {
                SqlHelper.ExecuteNonQuery("RPT_DeleteObservation", this.ObservationId);
                Response.Redirect("Search.aspx");
            }
            else
            {
                string errorMessage = "Unable to delete observation. Observations created through RDTool can not be deleted.";
                string errorScript = "<script language='javascript'>\r\nalert('" + errorMessage + "');\r\n</script>";
                Page.ClientScript.RegisterStartupScript(typeof(EditObservation), "ErrorScript", errorScript);
            }
        }
        else
        {
            Response.Redirect("Search.aspx");
        }
    }

	protected bool IsSolutionStateCheck(int solutionStateId, string stateName) {
		if (this.SelectedSolutionStates.ContainsKey(solutionStateId)) {
			if (string.Compare("pending", stateName) == 0 && string.Compare(this.SelectedSolutionStates[solutionStateId], "pending") == 0)
				return true;
			else if (string.Compare("complete", stateName) == 0 && string.Compare(this.SelectedSolutionStates[solutionStateId], "complete") == 0)
				return true;
			else if (string.Compare("skip", stateName) == 0 && string.Compare(this.SelectedSolutionStates[solutionStateId], "skip") == 0)
				return true;
			else if (string.Compare("notset", stateName) == 0 && string.Compare(this.SelectedSolutionStates[solutionStateId], "notset") == 0)
				return true;
			else
				return false;
		}
		else {
			return false;
		}
	}

	private void LoadDropDownData()
	{
        cellList.Items.Add(new RadComboBoxItem("Select...", ""));
        cellList.DataSource = Utility.GetCellList();
        cellList.DataBind();

        sessionList.Items.Add(new RadComboBoxItem("Select...", ""));
        sessionList.DataSource = Utility.GetCustomSessionsList();
        sessionList.DataBind();

        deviceTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
        deviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
        deviceTypeList.DataBind();

		//modules use the same list as Device Types
		moduleTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
		moduleTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
		moduleTypeList.DataBind();

		//Populated on deviceTypeList selected index change
        deviceList.Items.Add(new RadComboBoxItem("Select...", ""));
        failureLocationList.Items.Add(new RadComboBoxItem("Select...", ""));

        ownerList.Items.Add(new ListItem("Select...", ""));
		//ownerList.Items.Add(new RadComboBoxItem("Select...", ""));
		ownerList.DataSource = Utility.GetOperatorList();
		ownerList.DataBind();

        operatorList.Items.Add(new ListItem("Select...", ""));
		operatorList.DataSource = Utility.GetOperatorList();
		operatorList.DataBind();

        failureTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
		failureTypeList.DataSource = Utility.GetFailureTypeList();
		failureTypeList.DataBind();

        investigationAreaList.Items.Add(new RadComboBoxItem("Select...", ""));
		investigationAreaList.DataSource = Utility.GetInvestigationAreaList();
		investigationAreaList.DataBind();

		disciplineList.Items.Add(new RadComboBoxItem("Select...", ""));
		disciplineList.DataSource = Utility.GetDisciplineList();
		disciplineList.DataBind();

		agilisList.Items.Add(new RadComboBoxItem("Select...", ""));
		agilisList.DataSource = Utility.GetAgilisList();
		agilisList.DataBind();

        triageTypeList.Items.Add(new ListItem("Select...", ""));
        //triageTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
		triageTypeList.DataSource = Utility.GetTriageTypeList();
		triageTypeList.DataBind();
	}

	protected void DevceTypeList_SelectedIndexChanged(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(deviceTypeList.SelectedValue))
		{
            deviceList.ClearSelection();
            deviceList.Text = null;
            deviceList.Items.Clear();
            deviceList.Items.Add(new RadComboBoxItem("Select...", ""));
            deviceList.DataSource = Utility.GetDevicesList(Convert.ToInt32(deviceTypeList.SelectedValue));            
            deviceList.DataBind();

            failureLocationList.ClearSelection();
            failureLocationList.Text = null;
            failureLocationList.Items.Clear();
            failureLocationList.Items.Add(new RadComboBoxItem("Select...", ""));
			failureLocationList.DataSource = Utility.GetFailureLocationList(Convert.ToInt32(deviceTypeList.SelectedValue));
			failureLocationList.DataBind();

            if (Utility.IsUserAdmin()) {
                deviceList.Enabled = true;
                failureLocationList.Enabled = true;
                RadAjaxManager1.ResponseScripts.Add(string.Format("window.setTimeout(function(){{document.getElementById('{0}').focus()}},100);", deviceList.ClientID));
            }
        }
		else
		{
			deviceList.Enabled = false;
			failureLocationList.Enabled = false;
		}
    }

	private List<DataFile> LoadFileData()
	{
		List<DataFile> engineeringFiles = new List<DataFile>();
		List<DataFile> xmlFiles = new List<DataFile>();

		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTransactionDevices", this.ObservationId, 0);
		if (ds.Tables[0] != null)
		{
			for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
			{
				DataRow row = ds.Tables[0].Rows[i];
				string deviceTypeName = DataFormatter.getString(row, "DeviceTypeName");
				string serialNumber = DataFormatter.getString(row, "SerialNumber");
				DateTime tranDate = DataFormatter.getDateTime(row, "TranDate");

				string filePattern = "CELL=" + DataFormatter.getInt32(row, "CellId").ToString() + "_SESSION=" + DataFormatter.getInt32(row, "SessionId").ToString() + "_TRX=" + DataFormatter.getInt32(row, "RDToolTranId").ToString();

				try
				{
					engineeringFiles = TxFiles.GetTxFileList(false, filePattern, this.TranId, this.ObservationId, deviceTypeName, serialNumber, tranDate);
					xmlFiles = TxFiles.GetTxFileList(true, filePattern, this.TranId, this.ObservationId, deviceTypeName, serialNumber, tranDate);
				}
				catch
				{
					//ignore errors, can occur when the Queue Service is offline.
				}
			}

			engRepeater.DataSource = engineeringFiles;
			engRepeater.DataBind();

			xmlRepeater.DataSource = xmlFiles;
			xmlRepeater.DataBind();

			if (engRepeater.Items.Count > 0)
			{
				NoFilesLabel.Visible = false;
				downloadAllRow.Visible = true;
			}
			else
			{
				NoFilesLabel.Visible = true;
				downloadAllRow.Visible = false;
			}

			if (xmlRepeater.Items.Count > 0)
				NoXMLLabel.Visible = false;
			else
				NoXMLLabel.Visible = true;
		}

		return engineeringFiles;
	}
}
