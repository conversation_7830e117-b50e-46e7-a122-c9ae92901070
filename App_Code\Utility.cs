using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Text;
using System.Web;
using System.Web.UI.WebControls;
using System.Security.Principal;
using WebSupergoo.ABCpdf9;
using Atlassian.Jira;

public class Utility
{
	public static string GetUserName()
	{
		return WindowsIdentity.GetCurrent().Name;
	}

    public static bool IsUserAuthorized() {
        bool retVal = false;

        if (HttpContext.Current.Request.Url.AbsoluteUri.Contains("Unauthorized")) {
            return true;
        }

        if (IsUserAdmin()) {
            return true;
        }

        WindowsPrincipal myPrincipal = new WindowsPrincipal(WindowsIdentity.GetCurrent());
        if (!string.IsNullOrEmpty(ConfigurationManager.AppSettings["Access_WindowsRoleName"])) {

            //if set to "ALL" allow access for all DIEBOLD_MASTER accounts
            if (string.Compare(ConfigurationManager.AppSettings["Access_WindowsRoleName"], "ALL", true) == 0) {
                return true;
            }

            //enables admin features for local testing
            //if (string.Compare(myPrincipal.Identity.Name, "KENTONVM\\Kenton Van Duyne") == 0)
            //   return true;

            if (myPrincipal.IsInRole(ConfigurationManager.AppSettings["Access_WindowsRoleName"]))
                retVal = true;
            else
                retVal = false;
        }
        else {
            throw new ApplicationException("The web.config setting for the [Access_WindowsRoleName] has not be specified.");
        }
        return retVal;
    }

	public static bool IsUserAdmin()
	{
		bool retVal = false;

		WindowsPrincipal myPrincipal = new WindowsPrincipal(WindowsIdentity.GetCurrent());
		if (!string.IsNullOrEmpty(ConfigurationManager.AppSettings["AdminFeatures_WindowsRoleName"]))
		{
			//enables admin features for local testing
			if (string.Compare(myPrincipal.Identity.Name, "KENTONVM\\Kenton Van Duyne") == 0)
				return true;

			if (myPrincipal.IsInRole(ConfigurationManager.AppSettings["AdminFeatures_WindowsRoleName"]))
				retVal = true;
			else
				retVal = false;
		}
		else
		{
			throw new ApplicationException("The web.config setting for the [AdminFeatures_WindowsRoleName] has not be specified.");
		}
		return retVal;
	}

    public static ReportInfo LoadReportTypeInfo(ReportInfo repInfo, int reportTypeId) {
        DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadReportType", reportTypeId);

        if (ds.Tables[0] != null) {
            foreach (DataRow row in ds.Tables[0].Rows) {
                repInfo.WizardPageName = DataFormatter.getString(row, "WizardPageName");
                repInfo.StoredProcName = DataFormatter.getString(row, "StoredProcName");
                repInfo.RunPageName = DataFormatter.getString(row, "RunPageName");
                repInfo.PrintPageName = DataFormatter.getString(row, "PrintPageName");
                repInfo.AllowFilterLevel3Notes = DataFormatter.getBool(row, "AllowFilterLevel3Notes");
                repInfo.AllowFilterFieldType = DataFormatter.getBool(row, "AllowFilterFieldType");
            }
        }
        return repInfo;
    }

    //public static void SyncObservationWithJiraIssue(int observationId) {
    //    Issue x = null;
    //    try {
    //        if (!string.IsNullOrEmpty(scrNumberField.Text)) {
    //            x = await JiraRestApi.GetIssueById(scrNumberField.Text);
    //        }
    //        else if (!string.IsNullOrEmpty(linkField.Text)) {
    //            x = await JiraRestApi.GetIssueByUrl(linkField.Text);
    //        }

    //        if (x != null) {
    //            //make sure issue number field and link field are filled in with correct data
    //            scrNumberField.Text = x.Key.Value;
    //            linkField.Text = "https://jerry.wincor-nixdorf.com/browse/" + x.Key.Value;
    //            linkFieldActiveLink.Attributes.Add("href", linkField.Text);
    //            linkFieldActiveLink.Visible = true;

    //            //fill in Triage Name, if not exists, create a new one and select
    //            UpdateTriageNameFromJira(x.Summary);

    //            //check if owner exists, if not create a new one, and select
    //            UpdateOwnerFromJira(x.Reporter);

    //            UpdateSolutionStatesFromJira(x.Status.Name);

    //            //get projected date
    //            if (projectedDate.SelectedDate == null) {
    //                if (x.ResolutionDate != null) {
    //                    projectedDate.SelectedDate = x.ResolutionDate;
    //                }
    //                else if (x.DueDate != null) {
    //                    projectedDate.SelectedDate = x.DueDate;
    //                }
    //            }
    //        }
    //    }
    //    catch (Exception ex) {
    //        if (ex.Message.Contains("Issue Does Not Exist")) {
    //            jiraErrorDiv.InnerHtml = "JIRA sync failed because specified issue number does not exist.";
    //        }
    //        else if (ex.Message.Contains("Unauthorized") || ex.Message.Contains("Forbidden")) {
    //            jiraErrorDiv.InnerHtml = "JIRA sync failed because user '" + ConfigurationManager.AppSettings["JiraRestApi_Username"] + "' is not authorized to view the requested issue.";
    //        }
    //        else {
    //            jiraErrorDiv.InnerHtml = "JIRA sync reported error: " + ex.Message;
    //        }
    //        jiraErrorDiv.Visible = true;
    //    }
    //}

    //private void UpdateSolutionStatesFromJira(string jiraStatus) {
    //    HtmlInputRadioButton state1PendingRadio = null;
    //    HtmlInputRadioButton state2PendingRadio = null;
    //    HtmlInputRadioButton state3PendingRadio = null;
    //    HtmlInputRadioButton state4PendingRadio = null;
    //    HtmlInputRadioButton state5PendingRadio = null;
    //    HtmlInputRadioButton state6PendingRadio = null;
    //    HtmlInputRadioButton state7PendingRadio = null;

    //    HtmlInputRadioButton state1CompleteRadio = null;
    //    HtmlInputRadioButton state2CompleteRadio = null;
    //    HtmlInputRadioButton state3CompleteRadio = null;
    //    HtmlInputRadioButton state4CompleteRadio = null;
    //    HtmlInputRadioButton state5CompleteRadio = null;
    //    HtmlInputRadioButton state6CompleteRadio = null;
    //    HtmlInputRadioButton state7CompleteRadio = null;

    //    foreach (RepeaterItem item in solutionStateRep.Items) {
    //        HiddenField hiddenId = (HiddenField)item.FindControl("SolutionStateId");
    //        if (hiddenId.Value == "1") {
    //            state1CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state1PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //        else if (hiddenId.Value == "2") {
    //            state2CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state2PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //        else if (hiddenId.Value == "3") {
    //            state3CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state3PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //        else if (hiddenId.Value == "4") {
    //            state4CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state4PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //        else if (hiddenId.Value == "5") {
    //            state5CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state5PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //        else if (hiddenId.Value == "6") {
    //            state6CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state6PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //        else if (hiddenId.Value == "7") {
    //            state7CompleteRadio = ((HtmlInputRadioButton)item.FindControl("complete"));
    //            state7PendingRadio = ((HtmlInputRadioButton)item.FindControl("pending"));
    //        }
    //    }

    //    switch (jiraStatus) {
    //        case "Submitted":
    //        case "To Do":
    //            state1CompleteRadio.Checked = true;

    //            state2PendingRadio.Checked = true;
    //            state3PendingRadio.Checked = true;
    //            state4PendingRadio.Checked = true;
    //            state5PendingRadio.Checked = true;
    //            state6PendingRadio.Checked = true;
    //            state7PendingRadio.Checked = true;
    //            break;
    //        case "In Development":
    //            state1CompleteRadio.Checked = true;
    //            state2CompleteRadio.Checked = true;

    //            state3PendingRadio.Checked = true;
    //            state4PendingRadio.Checked = true;
    //            state5PendingRadio.Checked = true;
    //            state6PendingRadio.Checked = true;
    //            state7PendingRadio.Checked = true;
    //            break;
    //        case "In Fixing":
    //            state1CompleteRadio.Checked = true;
    //            state2CompleteRadio.Checked = true;
    //            state3CompleteRadio.Checked = true;
    //            state4CompleteRadio.Checked = true;

    //            state5PendingRadio.Checked = true;
    //            state6PendingRadio.Checked = true;
    //            state7PendingRadio.Checked = true;
    //            break;
    //        case "Ready for Build":
    //            state1CompleteRadio.Checked = true;
    //            state2CompleteRadio.Checked = true;
    //            state3CompleteRadio.Checked = true;

    //            state4PendingRadio.Checked = true;
    //            state5PendingRadio.Checked = true;
    //            state6PendingRadio.Checked = true;
    //            state7PendingRadio.Checked = true;
    //            break;
    //        case "Ready for QA":
    //            state1CompleteRadio.Checked = true;
    //            state2CompleteRadio.Checked = true;
    //            state3CompleteRadio.Checked = true;
    //            state4CompleteRadio.Checked = true;

    //            state5PendingRadio.Checked = true;
    //            state6PendingRadio.Checked = true;
    //            state7PendingRadio.Checked = true;
    //            break;
    //        case "In QA":
    //        case "Blocked":
    //            state1CompleteRadio.Checked = true;
    //            state2CompleteRadio.Checked = true;
    //            state3CompleteRadio.Checked = true;
    //            state4CompleteRadio.Checked = true;
    //            state5CompleteRadio.Checked = true;
    //            state6CompleteRadio.Checked = true;

    //            state7PendingRadio.Checked = true;
    //            break;
    //        case "Done":
    //        case "Closed":
    //            state1CompleteRadio.Checked = true;
    //            state2CompleteRadio.Checked = true;
    //            state3CompleteRadio.Checked = true;
    //            state4CompleteRadio.Checked = true;
    //            state5CompleteRadio.Checked = true;
    //            state6CompleteRadio.Checked = true;
    //            state7CompleteRadio.Checked = true;
    //            break;
    //    }
    //}

    //private void UpdateTriageNameFromJira(string jiraSummary) {
    //    try {
    //        string issueName = "";
    //        if (!string.IsNullOrEmpty(jiraSummary)) {
    //            //format: DEVICENAME JIRA_ISSUE_NO JIRA_DESCRIPTION

    //            //get current device type name
    //            List<TypeCodeEntry> databaseDeviceTypeList = Utility.GetDeviceTypeList();
    //            foreach (TypeCodeEntry curDevType in databaseDeviceTypeList) {
    //                if (curDevType.Code == deviceTypeList.SelectedValue) {
    //                    issueName = curDevType.Name + " " + scrNumberField.Text + " " + jiraSummary;
    //                }
    //            }

    //            if (!string.IsNullOrEmpty(issueName)) {
    //                RadComboBoxItem item = this.triageTypeList.FindItemByText(issueName);
    //                if (item != null) {
    //                    item.Selected = true;
    //                }
    //                else {
    //                    int newId = (int)SqlHelper.ExecuteScalar("RPT_InsertTriageType", issueName);

    //                    //select newly inserted item
    //                    this.triageTypeList.Items.Add(new RadComboBoxItem(issueName, newId.ToString()));
    //                    this.triageTypeList.SelectedValue = newId.ToString();
    //                }
    //            }
    //        }
    //    }
    //    catch (Exception ex) {
    //        jiraErrorDiv.InnerHtml = "JIRA error: " + ex.Message;
    //        jiraErrorDiv.Visible = true;
    //    }
    //}

    //private void UpdateOwnerFromJira(string jiraReporterName) {
    //    try {
    //        if (!string.IsNullOrEmpty(jiraReporterName)) {
    //            //names from Jira API come through as 'last.first'
    //            if (jiraReporterName.Contains(".")) {
    //                string[] nameParts = jiraReporterName.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);
    //                string ownerName = "";
    //                if (nameParts.Length >= 1) {
    //                    string firstName = nameParts[1];
    //                    string lastName = nameParts[0];
    //                    firstName = char.ToUpper(firstName[0]) + firstName.Substring(1);
    //                    lastName = char.ToUpper(lastName[0]) + lastName.Substring(1);
    //                    ownerName = firstName + " " + lastName;
    //                }

    //                if (!string.IsNullOrEmpty(ownerName)) {
    //                    RadComboBoxItem item = this.ownerList.FindItemByText(ownerName);
    //                    if (item != null) {
    //                        item.Selected = true;
    //                    }
    //                    else {
    //                        int newId = (int)SqlHelper.ExecuteScalar("RPT_InsertOperator", ownerName, true);

    //                        //select newly inserted item
    //                        this.ownerList.Items.Add(new RadComboBoxItem(ownerName, newId.ToString()));
    //                        this.ownerList.SelectedValue = newId.ToString();
    //                    }
    //                }
    //            }
    //        }
    //    }
    //    catch (Exception ex) {
    //        jiraErrorDiv.InnerHtml = "JIRA error: " + ex.Message;
    //        jiraErrorDiv.Visible = true;
    //    }
    //}

    public static ReportInfo LoadReportInfo(int reportId)
	{
		ReportInfo rptInfo = null;

		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadReport", reportId);

		if (ds.Tables[0] != null)
		{
			rptInfo = new ReportInfo();
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				rptInfo.ReportId = DataFormatter.getInt32(row, "ReportId");
				rptInfo.ReportName = DataFormatter.getString(row, "ReportName");
				rptInfo.ChartTitle = DataFormatter.getString(row, "ReportTitle");
				rptInfo.ReportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(row, "ReportTypeId");
				rptInfo.ReportTypeName = DataFormatter.getString(row, "ReportTypeName");
                rptInfo.WizardPageName = DataFormatter.getString(row, "WizardPageName");
                rptInfo.RunPageName = DataFormatter.getString(row, "RunPageName");
                rptInfo.PrintPageName = DataFormatter.getString(row, "PrintPageName");
                rptInfo.StoredProcName = DataFormatter.getString(row, "StoredProcName");
                rptInfo.AllowFilterLevel3Notes = DataFormatter.getBool(row, "AllowFilterLevel3Notes");
                rptInfo.AllowFilterFieldType = DataFormatter.getBool(row, "AllowFilterFieldType");
                rptInfo.FolderId = DataFormatter.getInt32(row, "FolderId");
				rptInfo.FolderName = DataFormatter.getString(row, "FolderName");
				rptInfo.PromptSessions = DataFormatter.getBool(row, "PromptSessions");
				rptInfo.FixedStartDate = DataFormatter.getDateTime(row, "StartDate");
				rptInfo.RelativeStartTimeId = (ReportHelper.RelativeTimeEnum)DataFormatter.getInt32(row, "RelativeStartId");
				rptInfo.FixedEndDate = DataFormatter.getDateTime(row, "EndDate");
				rptInfo.RelativeEndTimeId = (ReportHelper.RelativeTimeEnum)DataFormatter.getInt32(row, "RelativeEndId");
				rptInfo.DimensionName = DataFormatter.getString(row, "DimensionName");
                rptInfo.SubReportsByDimension = DataFormatter.getBool(row, "SubReportsByDimension");
                rptInfo.SubReportsBySession = DataFormatter.getBool(row, "SubReportsBySession");
                rptInfo.SubReportsByDevice = DataFormatter.getBool(row, "SubReportsByDevice");
                rptInfo.MinXScale = DataFormatter.getString(row, "MinXScale");
                rptInfo.MaxXScale = DataFormatter.getString(row, "MaxXScale");
                rptInfo.MinYScale = DataFormatter.getString(row, "MinYScale");
                rptInfo.MaxYScale = DataFormatter.getString(row, "MaxYScale");

				if (!string.IsNullOrEmpty(DataFormatter.getString(row, "DimensionMembers")))
				{
					foreach (string dimMem in DataFormatter.getString(row, "DimensionMembers").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
						rptInfo.DimensionMembers.Add(dimMem);
				}

				if (!string.IsNullOrEmpty(DataFormatter.getString(row, "ReportFilters")))
					rptInfo.ReportFilters = BuildReportFilters(DataFormatter.getString(row, "ReportFilters"));
				
				rptInfo.ReportData = DataFormatter.getString(row, "ReportData");
				rptInfo.CellSetData = DataFormatter.getString(row, "CellSetData");
				rptInfo.LastSaveDate = DataFormatter.getDateTime(row, "LastSaveDate", DateTime.MinValue);

				switch (rptInfo.ReportTypeId)
				{
					case ReportHelper.ReportTypeEnum.SENSOR:
						rptInfo.ParetoInfo.GroupingId = (ReportHelper.DateGroupingEnum)DataFormatter.getInt32(row, "Pareto_GroupingId", (int)ReportHelper.DateGroupingEnum.BY_MONTH);
						break;
					case ReportHelper.ReportTypeEnum.PARETO:
					case ReportHelper.ReportTypeEnum.LEGACY_PARETO:
						rptInfo.ParetoInfo.IncludeTotal = DataFormatter.getBool(row, "Pareto_IncludeTotal");
                        rptInfo.ParetoInfo.SplitByCell = DataFormatter.getBool(row, "Pareto_SplitByCell");
                        rptInfo.ParetoInfo.ByStatisticValue = DataFormatter.getBool(row, "Pareto_ByStatValue");
                        rptInfo.ParetoInfo.MaxColumns = DataFormatter.getInt32(row, "Pareto_MaxColumns");
						rptInfo.ParetoInfo.GroupingId = (ReportHelper.DateGroupingEnum)DataFormatter.getInt32(row, "Pareto_GroupingId", (int)ReportHelper.DateGroupingEnum.BY_MONTH);

						rptInfo.ProgressInfo.Format1 = DataFormatter.getString(row, "Prog_Format1", "#,#");
						rptInfo.ProgressInfo.RateTypeId1 = (ReportHelper.RateTypeEnum)DataFormatter.getInt32(row, "Prog_RateTypeId1", 0);
						rptInfo.ProgressInfo.WorkLoad1 = DataFormatter.getDecimal(row, "Prog_WorkLoad1", decimal.MinValue);
						break;
					case ReportHelper.ReportTypeEnum.PRST:
					case ReportHelper.ReportTypeEnum.LEGACY_PRST:
						rptInfo.PRSTInfo.IncludeTrendline = DataFormatter.getBool(row, "PRST_IncludeTrendline");
						rptInfo.PRSTInfo.IncludeUncensoredSeries = DataFormatter.getBool(row, "PRST_IncludeUncensored");
						rptInfo.PRSTInfo.XaxisTypeId = (ReportHelper.XAxisTypeEnum)DataFormatter.getInt32(row, "PRST_XaxisTypeId", (int)ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS);
						rptInfo.PRSTInfo.MTBFSpec = DataFormatter.getDecimal(row, "PRST_MTBFSpec");
						rptInfo.PRSTInfo.CustomizeRatioRisk = DataFormatter.getBool(row, "PRST_Customize");
						rptInfo.PRSTInfo.DiscriminationRatio = DataFormatter.getDecimal(row, "PRST_DiscrimRatio", decimal.MinValue);
						rptInfo.PRSTInfo.ProducersRisk = DataFormatter.getDecimal(row, "PRST_ProducerRisk", decimal.MinValue);
						rptInfo.PRSTInfo.ConsumersRisk = DataFormatter.getDecimal(row, "PRST_ConsumerRisk", decimal.MinValue);
                        rptInfo.PRSTInfo.AllInOneFormat = DataFormatter.getBool(row, "PRST_AllInOneFormat");
                        rptInfo.PRSTInfo.NumberOfUnits = DataFormatter.getInt32(row, "PRST_NumberOfUnits");
                        break;
					case ReportHelper.ReportTypeEnum.PROGRESS:
					case ReportHelper.ReportTypeEnum.LEGACY_PROGRESS:
					case ReportHelper.ReportTypeEnum.SHIFT:
						rptInfo.ProgressInfo.Normalize = DataFormatter.getBool(row, "Prog_Normalize");
						rptInfo.ProgressInfo.SplitByCell = DataFormatter.getBool(row, "Prog_SplitByCell");
						rptInfo.ProgressInfo.AssumeOneFailure = DataFormatter.getBool(row, "Prog_AssumeOneFailure");
						rptInfo.ProgressInfo.GroupingId = (ReportHelper.DateGroupingEnum)DataFormatter.getInt32(row, "Prog_GroupingId", (int)ReportHelper.DateGroupingEnum.BY_MONTH);

						rptInfo.ProgressInfo.SeriesName1 = DataFormatter.getString(row, "Prog_SeriesName1");
						rptInfo.ProgressInfo.SeriesName2 = DataFormatter.getString(row, "Prog_SeriesName2");
						rptInfo.ProgressInfo.SeriesName3 = DataFormatter.getString(row, "Prog_SeriesName3");
						rptInfo.ProgressInfo.SeriesName4 = DataFormatter.getString(row, "Prog_SeriesName4");

						rptInfo.ProgressInfo.Format1 = DataFormatter.getString(row, "Prog_Format1");
						rptInfo.ProgressInfo.Format2 = DataFormatter.getString(row, "Prog_Format2");
						rptInfo.ProgressInfo.Format3 = DataFormatter.getString(row, "Prog_Format3");
						rptInfo.ProgressInfo.Format4 = DataFormatter.getString(row, "Prog_Format4");

						rptInfo.ProgressInfo.RateTypeId1 = (ReportHelper.RateTypeEnum)DataFormatter.getInt32(row, "Prog_RateTypeId1", (int)ReportHelper.RateTypeEnum.PER_MILLION_MEDIA);
						rptInfo.ProgressInfo.RateTypeId2 = (ReportHelper.RateTypeEnum)DataFormatter.getInt32(row, "Prog_RateTypeId2", (int)ReportHelper.RateTypeEnum.PER_MILLION_MEDIA);
						rptInfo.ProgressInfo.RateTypeId3 = (ReportHelper.RateTypeEnum)DataFormatter.getInt32(row, "Prog_RateTypeId3", (int)ReportHelper.RateTypeEnum.PER_MILLION_MEDIA);
						rptInfo.ProgressInfo.RateTypeId4 = (ReportHelper.RateTypeEnum)DataFormatter.getInt32(row, "Prog_RateTypeId4", (int)ReportHelper.RateTypeEnum.PER_MILLION_MEDIA);

						rptInfo.ProgressInfo.SpecLabel1 = DataFormatter.getString(row, "Prog_SpecLabel1");
						rptInfo.ProgressInfo.SpecLabel2 = DataFormatter.getString(row, "Prog_SpecLabel2");
						rptInfo.ProgressInfo.SpecLabel3 = DataFormatter.getString(row, "Prog_SpecLabel3");
						rptInfo.ProgressInfo.SpecLabel4 = DataFormatter.getString(row, "Prog_SpecLabel4");

						rptInfo.ProgressInfo.SpecRate1 = DataFormatter.getDecimal(row, "Prog_SpecRate1", decimal.MinValue);
						rptInfo.ProgressInfo.SpecRate2 = DataFormatter.getDecimal(row, "Prog_SpecRate2", decimal.MinValue);
						rptInfo.ProgressInfo.SpecRate3 = DataFormatter.getDecimal(row, "Prog_SpecRate3", decimal.MinValue);
						rptInfo.ProgressInfo.SpecRate4 = DataFormatter.getDecimal(row, "Prog_SpecRate4", decimal.MinValue);

						rptInfo.ProgressInfo.WorkLoad1 = DataFormatter.getDecimal(row, "Prog_WorkLoad1", decimal.MinValue);
						rptInfo.ProgressInfo.WorkLoad2 = DataFormatter.getDecimal(row, "Prog_WorkLoad2", decimal.MinValue);
						rptInfo.ProgressInfo.WorkLoad3 = DataFormatter.getDecimal(row, "Prog_WorkLoad3", decimal.MinValue);
						rptInfo.ProgressInfo.WorkLoad4 = DataFormatter.getDecimal(row, "Prog_WorkLoad4", decimal.MinValue);

						rptInfo.ProgressInfo.DimensionName1 = DataFormatter.getString(row, "Prog_DimensionName1");
						rptInfo.ProgressInfo.DimensionName2 = DataFormatter.getString(row, "Prog_DimensionName2");
						rptInfo.ProgressInfo.DimensionName3 = DataFormatter.getString(row, "Prog_DimensionName3");
						rptInfo.ProgressInfo.DimensionName4 = DataFormatter.getString(row, "Prog_DimensionName4");

						if (!string.IsNullOrEmpty(DataFormatter.getString(row, "Prog_DimensionMembers1")))
						{
							foreach (string dimMem in DataFormatter.getString(row, "Prog_DimensionMembers1").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
								rptInfo.ProgressInfo.DimensionMembers1.Add(dimMem);
						}
						if (!string.IsNullOrEmpty(DataFormatter.getString(row, "Prog_DimensionMembers2")))
						{
							foreach (string dimMem in DataFormatter.getString(row, "Prog_DimensionMembers2").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
								rptInfo.ProgressInfo.DimensionMembers2.Add(dimMem);
						}
						if (!string.IsNullOrEmpty(DataFormatter.getString(row, "Prog_DimensionMembers3")))
						{
							foreach (string dimMem in DataFormatter.getString(row, "Prog_DimensionMembers3").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
								rptInfo.ProgressInfo.DimensionMembers3.Add(dimMem);
						}
						if (!string.IsNullOrEmpty(DataFormatter.getString(row, "Prog_DimensionMembers4")))
						{
							foreach (string dimMem in DataFormatter.getString(row, "Prog_DimensionMembers4").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
								rptInfo.ProgressInfo.DimensionMembers4.Add(dimMem);
						}
						break;
					case ReportHelper.ReportTypeEnum.DISTRIBUTION:
					case ReportHelper.ReportTypeEnum.LEGACY_DISTRIBUTION:
						rptInfo.DistributionInfo.Target = DataFormatter.getDecimal(row, "Distrib_Target", decimal.MinValue);
						rptInfo.DistributionInfo.ShowHistogram = DataFormatter.getBool(row, "Show_Histogram");
						rptInfo.DistributionInfo.SplitByDevice = DataFormatter.getBool(row, "Distrib_SplitByDevice"); 
						rptInfo.DistributionInfo.ShowNormalDistribution = DataFormatter.getBool(row, "Show_Normal");
						rptInfo.DistributionInfo.LowerSpecLimit = DataFormatter.getDecimal(row, "Distrib_LowerSpecLimit", decimal.MinValue);
						rptInfo.DistributionInfo.UpperSpecLimit = DataFormatter.getDecimal(row, "Distrib_UpperSpecLimit", decimal.MinValue);
						rptInfo.DistributionInfo.FilterStartValue = DataFormatter.getDecimal(row, "Distrib_FilterStartValue", decimal.MinValue);
                        rptInfo.DistributionInfo.FilterEndValue = DataFormatter.getDecimal(row, "Distrib_FilterEndValue", decimal.MinValue);
						rptInfo.DistributionInfo.XAxisStartValue = DataFormatter.getDecimal(row, "Distrib_XAxisStartValue", decimal.MinValue);
						rptInfo.DistributionInfo.XAxisEndValue = DataFormatter.getDecimal(row, "Distrib_XAxisEndValue", decimal.MinValue);
						break;
					case ReportHelper.ReportTypeEnum.ENA:
					case ReportHelper.ReportTypeEnum.ECRM:
					case ReportHelper.ReportTypeEnum.AFD:
                    case ReportHelper.ReportTypeEnum.AFD_CONFIGURATION_MANAGER:
                    case ReportHelper.ReportTypeEnum.TBT:
					case ReportHelper.ReportTypeEnum.STATISTICS:
					case ReportHelper.ReportTypeEnum.BUBBLE:
						rptInfo.QueryInfo.MinTranId = DataFormatter.getInt64(row, "Query_MinTranId", 0);
						rptInfo.QueryInfo.MaxTranId = DataFormatter.getInt64(row, "Query_MaxTranId", 0);
						rptInfo.QueryInfo.AcceptLevel3Notes = DataFormatter.getBool(row, "Query_AcceptLevel3", false);
						rptInfo.QueryInfo.FieldTypeId = DataFormatter.getInt32(row, "Query_FieldTypeId");
						break;
				}

				if (ds.Tables[1] != null)
				{
					foreach (DataRow sessionRow in ds.Tables[1].Rows)
					{
						SessionInfo sesInfo = new SessionInfo();
						sesInfo.SessionId = DataFormatter.getInt32(sessionRow, "SessionId");
						sesInfo.SessionName = DataFormatter.getString(sessionRow, "SessionName");
						sesInfo.SessionStatusId = DataFormatter.getInt32(sessionRow, "SessionStatusId");

						rptInfo.AttachedSessions.Add(sesInfo);
					}
				}

				if (ds.Tables[2] != null)
				{
					foreach (DataRow cellRow in ds.Tables[2].Rows)
					{
						CellInfo cellInfo = new CellInfo();
						cellInfo.CellId = DataFormatter.getInt32(cellRow, "CellId");
						cellInfo.CellName = DataFormatter.getString(cellRow, "CellName");
						cellInfo.CellStatusId = DataFormatter.getInt32(cellRow, "CellStatusId");

						rptInfo.CellFilters.Add(cellInfo);
					}
				}
			}
		}
		return rptInfo;
	}

    public static SubReportInfo LoadSubReportInfo(int subReportId)
    {
        SubReportInfo subReportInfo = null;

        if (subReportId > 0)
        {
            DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadSubReport", subReportId);

            if (ds.Tables[0] != null)
            {
                subReportInfo = new SubReportInfo();
                foreach (DataRow row in ds.Tables[0].Rows)
                {
                    subReportInfo.SubReportId = DataFormatter.getInt32(row, "SubReportId");
                    subReportInfo.SubReportName = DataFormatter.getString(row, "SubReportName");
                    if (!string.IsNullOrEmpty(DataFormatter.getString(row, "DimensionMemberMdx")))
                    {
                        foreach (string dimMem in DataFormatter.getString(row, "DimensionMemberMdx").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
                            subReportInfo.DimensionMembers.Add(dimMem);
                    }

                    if (!string.IsNullOrEmpty(DataFormatter.getString(row, "DeviceMemberMdx")))
                    {
                        foreach (string devMem in DataFormatter.getString(row, "DeviceMemberMdx").Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries))
                            subReportInfo.DeviceFilters.Add(devMem);
                    }
                }

                if (ds.Tables[1] != null)
                {
                    foreach (DataRow sessionRow in ds.Tables[1].Rows)
                    {
                        SessionInfo sesInfo = new SessionInfo();
                        sesInfo.SessionId = DataFormatter.getInt32(sessionRow, "SessionId");
                        sesInfo.SessionName = DataFormatter.getString(sessionRow, "SessionName");
                        sesInfo.SessionStatusId = DataFormatter.getInt32(sessionRow, "SessionStatusId");

                        subReportInfo.AttachedSessions.Add(sesInfo);
                    }
                }
            }
        }

        return subReportInfo;
    }

	public static int SaveReportInfo(ReportInfo repInfo)
	{
		int retVal = repInfo.ReportId;
		object folderId = null;
		object paretoGroupingId = null;
		object paretoMaxColumns = null;
		object prstXaxisTypeId = null;
		object prstMTBFSpec = null;
		object prstDiscrim = null;
		object prstProducer = null;
		object prstConsumer = null;
        object prstNumberUnits = null;
		object progGroupingId = null;
		object progRateTypeId1 = null;
		object progRateTypeId2 = null;
		object progRateTypeId3 = null;
		object progRateTypeId4 = null;
		object progSpecRate1 = null;
		object progSpecRate2 = null;
		object progSpecRate3 = null;
		object progSpecRate4 = null;
		object progWorkLoad1 = null;
		object progWorkLoad2 = null;
		object progWorkLoad3 = null;
		object progWorkLoad4 = null;
		object distribTarget = null;
		object distribLowerSpec = null;
		object distribUpperSpec = null;
		object distribStartVal = null;
		object distribEndVal = null;
		object distribXAxisStartVal = null;
		object distribXAxisEndVal = null;
		object startDate = null;
		object relativeStartId = null;
		object endDate = null;
		object relativeEndId = null;

		if (repInfo.FixedStartDate != DateTime.MinValue)
			startDate = (DateTime)repInfo.FixedStartDate;

		if (repInfo.FixedEndDate != DateTime.MinValue)
			endDate = (DateTime)repInfo.FixedEndDate;

		if (repInfo.RelativeStartTimeId != 0)
			relativeStartId = (int)repInfo.RelativeStartTimeId;

		if (repInfo.RelativeEndTimeId != 0)
			relativeEndId = (int)repInfo.RelativeEndTimeId;

		//Pareto specific
		if (repInfo.ParetoInfo.GroupingId != 0)
			paretoGroupingId = (int)repInfo.ParetoInfo.GroupingId;

		if (repInfo.ParetoInfo.MaxColumns != 0)
			paretoMaxColumns = (int)repInfo.ParetoInfo.MaxColumns;

		//PRST specific
		if (repInfo.PRSTInfo.XaxisTypeId != 0)
			prstXaxisTypeId = (int)repInfo.PRSTInfo.XaxisTypeId;
		if (repInfo.PRSTInfo.MTBFSpec != Decimal.MinValue)
			prstMTBFSpec = (decimal)repInfo.PRSTInfo.MTBFSpec;
		if (repInfo.PRSTInfo.DiscriminationRatio != Decimal.MinValue)
			prstDiscrim = (decimal)repInfo.PRSTInfo.DiscriminationRatio;
		if (repInfo.PRSTInfo.ProducersRisk != Decimal.MinValue)
			prstProducer = (decimal)repInfo.PRSTInfo.ProducersRisk;
		if (repInfo.PRSTInfo.ConsumersRisk != Decimal.MinValue)
			prstConsumer = (decimal)repInfo.PRSTInfo.ConsumersRisk;
        if (repInfo.PRSTInfo.NumberOfUnits > 0)
            prstNumberUnits = (int)repInfo.PRSTInfo.NumberOfUnits;

        //Progress specific
        if (repInfo.ProgressInfo.GroupingId != 0)
			progGroupingId = (int)repInfo.ProgressInfo.GroupingId;

		if (repInfo.ProgressInfo.RateTypeId1 != 0)
			progRateTypeId1 = (int)repInfo.ProgressInfo.RateTypeId1;
		if (repInfo.ProgressInfo.RateTypeId2 != 0)
			progRateTypeId2 = (int)repInfo.ProgressInfo.RateTypeId2;
		if (repInfo.ProgressInfo.RateTypeId3 != 0)
			progRateTypeId3 = (int)repInfo.ProgressInfo.RateTypeId3;
		if (repInfo.ProgressInfo.RateTypeId4 != 0)
			progRateTypeId4 = (int)repInfo.ProgressInfo.RateTypeId4;

		if (repInfo.ProgressInfo.SpecRate1 != Decimal.MinValue)
			progSpecRate1 = (decimal)repInfo.ProgressInfo.SpecRate1;
		if (repInfo.ProgressInfo.SpecRate2 != Decimal.MinValue)
			progSpecRate2 = (decimal)repInfo.ProgressInfo.SpecRate2;
		if (repInfo.ProgressInfo.SpecRate3 != Decimal.MinValue)
			progSpecRate3 = (decimal)repInfo.ProgressInfo.SpecRate3;
		if (repInfo.ProgressInfo.SpecRate4 != Decimal.MinValue)
			progSpecRate4 = (decimal)repInfo.ProgressInfo.SpecRate4;

		if (repInfo.ProgressInfo.WorkLoad1 != Decimal.MinValue)
			progWorkLoad1 = (decimal)repInfo.ProgressInfo.WorkLoad1;
		if (repInfo.ProgressInfo.WorkLoad2 != Decimal.MinValue)
			progWorkLoad2 = (decimal)repInfo.ProgressInfo.WorkLoad2;
		if (repInfo.ProgressInfo.WorkLoad3 != Decimal.MinValue)
			progWorkLoad3 = (decimal)repInfo.ProgressInfo.WorkLoad3;
		if (repInfo.ProgressInfo.WorkLoad4 != Decimal.MinValue)
			progWorkLoad4 = (decimal)repInfo.ProgressInfo.WorkLoad4;

		//Distribution specific
		if (repInfo.DistributionInfo.Target != Decimal.MinValue)
			distribTarget = (decimal)repInfo.DistributionInfo.Target;
		if (repInfo.DistributionInfo.LowerSpecLimit != Decimal.MinValue)
			distribLowerSpec = (decimal)repInfo.DistributionInfo.LowerSpecLimit;
		if (repInfo.DistributionInfo.UpperSpecLimit != Decimal.MinValue)
			distribUpperSpec = (decimal)repInfo.DistributionInfo.UpperSpecLimit;
		if (repInfo.DistributionInfo.FilterStartValue != Decimal.MinValue)
			distribStartVal = (decimal)repInfo.DistributionInfo.FilterStartValue;
        if (repInfo.DistributionInfo.FilterEndValue != Decimal.MinValue)
			distribEndVal = (decimal)repInfo.DistributionInfo.FilterEndValue;
		if (repInfo.DistributionInfo.XAxisStartValue != Decimal.MinValue)
			distribXAxisStartVal = (decimal)repInfo.DistributionInfo.XAxisStartValue;
		if (repInfo.DistributionInfo.XAxisEndValue != Decimal.MinValue)
			distribXAxisEndVal = (decimal)repInfo.DistributionInfo.XAxisEndValue;

		if (repInfo.FolderId > 0)
			folderId = repInfo.FolderId;


		//Create new report from copy or create wizard.
		if (repInfo.ReportId == 0)
		{
			repInfo.ReportId = Convert.ToInt32(SqlHelper.ExecuteScalar("RPT_InsertReport", repInfo.ReportName, repInfo.ChartTitle, repInfo.ReportTypeId,
											folderId, repInfo.PromptSessions, startDate, relativeStartId, endDate, relativeEndId, repInfo.DimensionName,
											string.Join("\\n", repInfo.DimensionMembers.ToArray()),
											repInfo.ParetoInfo.IncludeTotal, paretoMaxColumns, paretoGroupingId, repInfo.ParetoInfo.SplitByCell, repInfo.ParetoInfo.ByStatisticValue,
											repInfo.PRSTInfo.IncludeTrendline, repInfo.PRSTInfo.IncludeUncensoredSeries, prstXaxisTypeId, prstMTBFSpec,
											repInfo.PRSTInfo.CustomizeRatioRisk, prstDiscrim, prstProducer, prstConsumer, 
                                            repInfo.PRSTInfo.AllInOneFormat, prstNumberUnits,
											repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SplitByCell, repInfo.ProgressInfo.AssumeOneFailure, repInfo.ProgressInfo.GroupingId,
											repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.SpecLabel1, progSpecRate1, progRateTypeId1, progWorkLoad1, repInfo.ProgressInfo.Format1,
											repInfo.ProgressInfo.DimensionName1, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers1.ToArray()),
											repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.SpecLabel2, progSpecRate2, progRateTypeId2, progWorkLoad2, repInfo.ProgressInfo.Format2,
											repInfo.ProgressInfo.DimensionName2, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers2.ToArray()),
											repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.SpecLabel3, progSpecRate3, progRateTypeId3, progWorkLoad3, repInfo.ProgressInfo.Format3,
											repInfo.ProgressInfo.DimensionName3, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers3.ToArray()),
											repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.SpecLabel4, progSpecRate4, progRateTypeId4, progWorkLoad4, repInfo.ProgressInfo.Format4,
											repInfo.ProgressInfo.DimensionName4, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers4.ToArray()),
											distribTarget, repInfo.DistributionInfo.ShowHistogram, repInfo.DistributionInfo.ShowNormalDistribution, 
											distribLowerSpec, distribUpperSpec, distribStartVal, distribEndVal, distribXAxisStartVal, distribXAxisEndVal, repInfo.DistributionInfo.SplitByDevice,
											repInfo.QueryInfo.MinTranId, repInfo.QueryInfo.MaxTranId, repInfo.QueryInfo.AcceptLevel3Notes, repInfo.QueryInfo.FieldTypeId,
											ConvertReportFiltersToString(repInfo.ReportFilters), repInfo.ReportData, repInfo.CellSetData,
                                            repInfo.SubReportsByDimension, repInfo.SubReportsBySession, repInfo.SubReportsByDevice, 
                                            repInfo.MinXScale, repInfo.MaxXScale, repInfo.MinYScale, repInfo.MaxYScale));
			retVal = repInfo.ReportId;
		}
		else
		{
			//Delete report sessions and reinsert later
			SqlHelper.ExecuteNonQuery("RPT_DeleteReportSessions", repInfo.ReportId);

			//Delete report cell filters and reinsert later
			SqlHelper.ExecuteNonQuery("RPT_DeleteReportCells", repInfo.ReportId);

			////Delete report device filters and reinsert later
			//SqlHelper.ExecuteNonQuery("RPT_DeleteReportDevices", repInfo.ReportId);
			SqlHelper.ExecuteNonQuery("RPT_UpdateReport", repInfo.ReportId, repInfo.ReportName, repInfo.ChartTitle, repInfo.ReportTypeId,
											folderId, repInfo.PromptSessions, startDate, relativeStartId, endDate, relativeEndId, repInfo.DimensionName,
											string.Join("\\n", repInfo.DimensionMembers.ToArray()),
                                            repInfo.ParetoInfo.IncludeTotal, paretoMaxColumns, paretoGroupingId, repInfo.ParetoInfo.SplitByCell, repInfo.ParetoInfo.ByStatisticValue,
											repInfo.PRSTInfo.IncludeTrendline, repInfo.PRSTInfo.IncludeUncensoredSeries, prstXaxisTypeId, prstMTBFSpec,
											repInfo.PRSTInfo.CustomizeRatioRisk, prstDiscrim, prstProducer, prstConsumer,
                                            repInfo.PRSTInfo.AllInOneFormat, prstNumberUnits,
											repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SplitByCell, repInfo.ProgressInfo.AssumeOneFailure, repInfo.ProgressInfo.GroupingId,
											repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.SpecLabel1, progSpecRate1, progRateTypeId1, progWorkLoad1, repInfo.ProgressInfo.Format1,
											repInfo.ProgressInfo.DimensionName1, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers1.ToArray()),
											repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.SpecLabel2, progSpecRate2, progRateTypeId2, progWorkLoad2, repInfo.ProgressInfo.Format2,
											repInfo.ProgressInfo.DimensionName2, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers2.ToArray()),
											repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.SpecLabel3, progSpecRate3, progRateTypeId3, progWorkLoad3, repInfo.ProgressInfo.Format3,
											repInfo.ProgressInfo.DimensionName3, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers3.ToArray()),
											repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.SpecLabel4, progSpecRate4, progRateTypeId4, progWorkLoad4, repInfo.ProgressInfo.Format4,
											repInfo.ProgressInfo.DimensionName4, string.Join("\\n", repInfo.ProgressInfo.DimensionMembers4.ToArray()),
											distribTarget, repInfo.DistributionInfo.ShowHistogram, repInfo.DistributionInfo.ShowNormalDistribution, 
											distribLowerSpec, distribUpperSpec, distribStartVal, distribEndVal, distribXAxisStartVal, distribXAxisEndVal, repInfo.DistributionInfo.SplitByDevice,
											repInfo.QueryInfo.MinTranId, repInfo.QueryInfo.MaxTranId, repInfo.QueryInfo.AcceptLevel3Notes, repInfo.QueryInfo.FieldTypeId,
											ConvertReportFiltersToString(repInfo.ReportFilters), repInfo.ReportData, repInfo.CellSetData,
                                            repInfo.SubReportsByDimension, repInfo.SubReportsBySession, repInfo.SubReportsByDevice,
                                            repInfo.MinXScale, repInfo.MaxXScale, repInfo.MinYScale, repInfo.MaxYScale);
		}

        //Insert Sessions if not having the user select each time via prompt
		if (!repInfo.PromptSessions)
		{
			foreach (SessionInfo session in repInfo.AttachedSessions)
			{
				SqlHelper.ExecuteNonQuery("RPT_InsertReportSession", repInfo.ReportId, session.SessionId);
			}
		}

		foreach (CellInfo cell in repInfo.CellFilters)
		{
			SqlHelper.ExecuteNonQuery("RPT_InsertReportCell", repInfo.ReportId, cell.CellId);
		}

        switch (repInfo.ReportTypeId)
        {
            case ReportHelper.ReportTypeEnum.DISTRIBUTION:
            case ReportHelper.ReportTypeEnum.SENSOR:
                StringBuilder sessionIdList = new StringBuilder();
                if (repInfo.SubReportsBySession)
                {
                    foreach (SessionInfo session in repInfo.AttachedSessions)
                    {
                        if (sessionIdList.Length > 0)
                            sessionIdList.Append(',');
                        sessionIdList.Append(session.SessionId.ToString());
                    }
                }

                StringBuilder dimensionNameList = new StringBuilder();
                StringBuilder dimensionMdxList = new StringBuilder();
                if (repInfo.SubReportsByDimension)
                {
                    bool groupByCaptionPrefix = (repInfo.ReportTypeId == ReportHelper.ReportTypeEnum.SENSOR);
                    Dictionary<string, string> dimensionList = OLAPHelper.GetMembersFromUniqueNames(repInfo.DimensionName, repInfo.DimensionMembers, groupByCaptionPrefix);
                    foreach (string key in dimensionList.Keys)
                    {
                        if (dimensionMdxList.Length > 0)
                            dimensionMdxList.Append('\n');
                        dimensionMdxList.Append(key);
                        if (dimensionNameList.Length > 0)
                            dimensionNameList.Append('\n');
                        dimensionNameList.Append(dimensionList[key]);
                    }
                }

                StringBuilder deviceNameList = new StringBuilder();
                StringBuilder deviceMdxList = new StringBuilder();
                if (repInfo.SubReportsByDevice)
                {
                    Dictionary<string, string> deviceList = OLAPHelper.GetDevicesFromFilters(repInfo.ReportFilters);
                    foreach (string key in deviceList.Keys)
                    {
                        if (deviceMdxList.Length > 0)
                            deviceMdxList.Append('\n');
                        deviceMdxList.Append(key);
                        if (deviceNameList.Length > 0)
                            deviceNameList.Append('\n');
                        deviceNameList.Append(deviceList[key]);
                    }
                }

                //Update subreport records
                SqlHelper.ExecuteNonQuery("RPT_RefreshSubReports", repInfo.ReportId, dimensionMdxList.ToString(), dimensionNameList.ToString(),
                    sessionIdList.ToString(), deviceMdxList.ToString(), deviceNameList.ToString());
                break;
        }

		return retVal;
	}

	public static string ConvertReportFiltersToString(Dictionary<string, List<string>> reportFilters)
	{
		StringBuilder sb = new StringBuilder();
		foreach (string key in reportFilters.Keys)
		{
			foreach (string member in reportFilters[key])
			{
				sb.Append(key + "\\t" + member + "\\n");
			}
		}
		return sb.ToString();
	}

	public static Dictionary<string, List<string>> BuildReportFilters(string reportFilters)
	{
		Dictionary<string, List<string>> tempFilters = new Dictionary<string, List<string>>();

		string[] sets = reportFilters.Split(new string[] { "\\n" }, StringSplitOptions.RemoveEmptyEntries);
		foreach (string set in sets)
		{
			string[] names = set.Split(new string[] { "\\t" }, StringSplitOptions.RemoveEmptyEntries);

			if (!tempFilters.ContainsKey(names[0]))
				tempFilters.Add(names[0], new List<string>());

			tempFilters[names[0]].Add(names[1]);
		}

		return tempFilters;
	}

	//public static string GetReportWizardPageName(ReportHelper.ReportTypeEnum reportTypeId)
	//{
	//	string pageName = "NewReportWizard_";
	//	switch (reportTypeId)
	//	{
	//		case ReportHelper.ReportTypeEnum.GENERAL:
	//		case ReportHelper.ReportTypeEnum.LEGACY_GENERAL:
	//			pageName += "Format.aspx"; //Skip Step 2
	//			break;
	//		case ReportHelper.ReportTypeEnum.PARETO:
	//		case ReportHelper.ReportTypeEnum.LEGACY_PARETO:
	//			pageName += "Pareto.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.PROGRESS:
	//		case ReportHelper.ReportTypeEnum.LEGACY_PROGRESS:
	//			pageName += "Progress.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.PRST:
	//		case ReportHelper.ReportTypeEnum.LEGACY_PRST:
	//			pageName += "PRST.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.DISTRIBUTION:
	//		case ReportHelper.ReportTypeEnum.LEGACY_DISTRIBUTION:
	//			pageName += "Distribution.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.SHIFT:
	//			pageName += "Shift.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.SENSOR:
	//			pageName += "Sensor.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.ENA:
	//		case ReportHelper.ReportTypeEnum.ECRM:
	//		case ReportHelper.ReportTypeEnum.AFD:
	//           case ReportHelper.ReportTypeEnum.AFD_CONFIGURATION_MANAGER:
	//           case ReportHelper.ReportTypeEnum.TBT:
	//		case ReportHelper.ReportTypeEnum.STATISTICS:
	//			pageName += "Query.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.BUBBLE:
	//			pageName += "Bubble.aspx";
	//			break;
	//	}
	//	return pageName;
	//}

	//public static string GetRunReportPageName(ReportHelper.ReportTypeEnum reportTypeId)
	//{
	//	string pageName = null;
	//	switch (reportTypeId)
	//	{
	//		case ReportHelper.ReportTypeEnum.PARETO:
	//		case ReportHelper.ReportTypeEnum.PRST:
	//		case ReportHelper.ReportTypeEnum.PROGRESS:
	//		case ReportHelper.ReportTypeEnum.DISTRIBUTION:
	//		case ReportHelper.ReportTypeEnum.LEGACY_PARETO:
	//		case ReportHelper.ReportTypeEnum.LEGACY_PRST:
	//		case ReportHelper.ReportTypeEnum.LEGACY_PROGRESS:
	//		case ReportHelper.ReportTypeEnum.LEGACY_DISTRIBUTION:
	//			pageName = "RunReport.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.GENERAL:
	//		case ReportHelper.ReportTypeEnum.LEGACY_GENERAL:
	//			pageName = "RunClientReport.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.SHIFT:
	//			pageName = "RunShiftReport.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.SENSOR:
	//			pageName = "RunSensorReport.aspx";
	//			break;
	//		case ReportHelper.ReportTypeEnum.ENA:
	//		case ReportHelper.ReportTypeEnum.ECRM:
	//		case ReportHelper.ReportTypeEnum.AFD:
	//           case ReportHelper.ReportTypeEnum.AFD_CONFIGURATION_MANAGER:
	//		case ReportHelper.ReportTypeEnum.TBT:
	//		case ReportHelper.ReportTypeEnum.STATISTICS:
	//		case ReportHelper.ReportTypeEnum.BUBBLE:
	//			pageName = "RunQuery.aspx";
	//			break;
	//	}
	//	return pageName;
	//}

	public static string GetPrintReportPageName(ReportHelper.ReportTypeEnum reportTypeId)
	{
		string pageName = null;
		switch (reportTypeId)
		{
			case ReportHelper.ReportTypeEnum.PARETO:
			case ReportHelper.ReportTypeEnum.PRST:
			case ReportHelper.ReportTypeEnum.PROGRESS:
			case ReportHelper.ReportTypeEnum.DISTRIBUTION:
			case ReportHelper.ReportTypeEnum.LEGACY_PARETO:
			case ReportHelper.ReportTypeEnum.LEGACY_PRST:
			case ReportHelper.ReportTypeEnum.LEGACY_PROGRESS:
			case ReportHelper.ReportTypeEnum.LEGACY_DISTRIBUTION:
				pageName = "PrintReport.aspx";
				break;
			case ReportHelper.ReportTypeEnum.GENERAL:
			case ReportHelper.ReportTypeEnum.LEGACY_GENERAL:
				throw new NotImplementedException("Printing & PDF generation is not supported for General Reports.");
			case ReportHelper.ReportTypeEnum.SHIFT:
				pageName = "PrintShiftReport.aspx";
				break;
			case ReportHelper.ReportTypeEnum.SENSOR:
				pageName = "PrintSensorReport.aspx";
				break;
			case ReportHelper.ReportTypeEnum.AFD:
			case ReportHelper.ReportTypeEnum.AFD_CONFIGURATION_MANAGER:
			case ReportHelper.ReportTypeEnum.ENA:
			case ReportHelper.ReportTypeEnum.ECRM:
			case ReportHelper.ReportTypeEnum.TBT:
			case ReportHelper.ReportTypeEnum.STATISTICS:
			case ReportHelper.ReportTypeEnum.BUBBLE:
				pageName = "PrintQueryReport.aspx";
				break;
		}
		return pageName;
	}

	private static CachedList cellList = new CachedList("RPT_GetList_Cell", "CellId", "CellName");
    public static List<TypeCodeEntry> GetCellList() { return cellList.GetList(); }

    private static CachedList cellStatusList = new CachedList("RPT_GetList_CellStatus", "CellStatusId", "CellStatusName");
    public static List<TypeCodeEntry> GetCellStatusList() { return cellStatusList.GetList(); }

    private static CachedList sessionList = new CachedList("RPT_GetList_Session", "SessionId", "SessionName");
    public static List<TypeCodeEntry> GetSessionsList() { return sessionList.GetList(); }

    private static CachedList activeSessionList = new CachedList("RPT_GetList_ActiveSession", "SessionId", "SessionName");
    public static List<TypeCodeEntry> GetActiveSessionsList() { return activeSessionList.GetList(); }

    private static CachedList volumeList = new CachedList("RPT_GetList_ManualVolumeSession", "SessionId", "SessionName");
    public static List<TypeCodeEntry> GetManualVolumeSessionList() { return volumeList.GetList(); }

    private static CachedList customSessionList = new CachedList("RPT_GetList_CustomSessions", "SessionId", "SessionName");
    public static List<TypeCodeEntry> GetCustomSessionsList() { return customSessionList.GetList(); }

    private static CachedList sessStatusList = new CachedList("RPT_GetList_SessionStatus", "SessionStatusId", "SessionStatusName");
    public static List<TypeCodeEntry> GetSessionStatusList() { return sessStatusList.GetList(); }

	private static CachedList testTypeList = new CachedList("RPT_GetList_TestType", "TestTypeID", "TestType");
	public static List<TypeCodeEntry> GetTestTypeList() { return testTypeList.GetList(); }

	private static CachedList deviceTypeList = new CachedList("RPT_GetList_DeviceType", "DeviceTypeId", "DeviceTypeName");
    public static List<TypeCodeEntry> GetDeviceTypeList() { return deviceTypeList.GetList(); }

    private static CachedList deviceTypeFullNameList = new CachedList("RPT_GetList_DeviceType", "DeviceTypeId", "FullName");
    public static List<TypeCodeEntry> GetDeviceTypeFullNameList() { return deviceTypeFullNameList.GetList(); }

    private static CachedList operatorList = new CachedList("RPT_GetList_Operator", "OperatorId", "OperatorName", false);
    public static List<TypeCodeEntry> GetOperatorList() { return operatorList.GetList(); }

	private static CachedList failureTypeList = new CachedList("RPT_GetList_FailureType", "FailureTypeId", "FailureTypeName", false);
    public static List<TypeCodeEntry> GetFailureTypeList() { return failureTypeList.GetList(); }

	private static CachedList investigationList = new CachedList("RPT_GetList_InvestigationArea", "InvestigationAreaId", "InvestigationAreaName", false);
    public static List<TypeCodeEntry> GetInvestigationAreaList() { return investigationList.GetList(); }

    private static CachedList severityList = new CachedList("RPT_GetList_Severity", "SeverityId", "SeverityName");
    public static List<TypeCodeEntry> GetSevertiyList() { return severityList.GetList(); }

    private static CachedList agilisList = new CachedList("RPT_GetList_Agilis", "AgilisId", "AgilisName");
    public static List<TypeCodeEntry> GetAgilisList() { return agilisList.GetList(); }

    private static CachedList reportTypeList = new CachedList("RPT_GetList_ReportType", "ReportTypeId", "ReportTypeName");
    public static List<TypeCodeEntry> GetReportTypeList() { return reportTypeList.GetList(); }

    private static CachedList queryReportList = new CachedList("RPT_GetList_QueryReport", "StoredProcName", "ReportName");
    public static List<TypeCodeEntry> GetQueryReportList() { return queryReportList.GetList(); }

    private static CachedList dimGroupList = new CachedList("RPT_GetList_Grouping", "GroupingId", "GroupingName");
    public static List<TypeCodeEntry> GetDimensionGroupByList() { return dimGroupList.GetList(); }

    private static CachedList rateTypeList = new CachedList("RPT_GetList_ProgressRateType", "ProgressRateTypeId", "ProgressRateTypeName");
    public static List<TypeCodeEntry> GetProgressRateTypeList() { return rateTypeList.GetList(); }

    private static CachedList paretoRateTypeList = new CachedList("RPT_GetList_ParetoRateType", "ProgressRateTypeId", "ProgressRateTypeName");
    public static List<TypeCodeEntry> GetParetoRateTypeList() { return paretoRateTypeList.GetList(); }

    private static CachedList parsingTypeList = new CachedList("RPT_GetList_ParsingType", "ParsingTypeCode", "ParsingTypeName");
    public static List<TypeCodeEntry> GetParsingTypeList() { return parsingTypeList.GetList(); }

    private static CachedList deviceStatusList = new CachedList("RPT_GetList_DeviceStatus", "DeviceStatusId", "DeviceStatusName");
    public static List<TypeCodeEntry> GetDeviceStatusList() { return deviceStatusList.GetList(); }

    private static CachedList fieldStatusList = new CachedList("RPT_GetList_FieldStatus", "FieldStatusId", "FieldStatusName");
    public static List<TypeCodeEntry> GetFieldStatusList() { return fieldStatusList.GetList(); }

	private static CachedList fieldTypeList = new CachedList("RPT_GetList_FieldType", "FieldTypeId", "FieldTypeName");
	public static List<TypeCodeEntry> GetFieldTypeList() { return fieldTypeList.GetList(); }

    private static CachedList aggregateList = new CachedList("RPT_GetList_AggregateType", "AggregateTypeCode", "AggregateTypeName");
    public static List<TypeCodeEntry> GetAggregateTypeList() { return aggregateList.GetList(); }

    private static CachedList testLocList = new CachedList("RPT_GetList_TestLocation", "TestLocationId", "TestLocationName");
    public static List<TypeCodeEntry> GetTestLocationList() { return testLocList.GetList(); }

    private static CachedList relTimeList = new CachedList("RPT_GetList_RelativeTime", "RelativeTimeId", "RelativeTimeName");
    public static List<TypeCodeEntry> GetRelativeTimeList() { return relTimeList.GetList(); }

	private static CachedList relTimePurgeList = new CachedList("RPT_GetList_RelativeTimePurge", "RelativeTimeId", "RelativeTimeName");
	public static List<TypeCodeEntry> GetRelativeTimePurgeList() { return relTimePurgeList.GetList(); }

	private static CachedList triageTypeList = new CachedList("RPT_GetList_TriageType", "TriageTypeId", "TriageTypeName", false);
	public static List<TypeCodeEntry> GetTriageTypeList() { return triageTypeList.GetList(); }

	private static CachedList disciplineList = new CachedList("RPT_GetList_Discipline", "DisciplineId", "DisciplineName", false);
	public static List<TypeCodeEntry> GetDisciplineList() { return disciplineList.GetList(); }

	private static CachedList solutionStateList = new CachedList("RPT_GetList_SolutionState", "SolutionStateId", "SolutionStateName", false);
	public static List<TypeCodeEntry> GetSolutionStateList() { return solutionStateList.GetList(); }

	private static CachedList productLineList = new CachedList("RPT_GetList_ProductLine", "ProductLineId", "ProductLineName", false);
	public static List<TypeCodeEntry> GetProductLineList() { return productLineList.GetList(); }

	private static CachedList familyLineList = new CachedList("RPT_GetList_FamilyLine", "FamilyLineId", "FamilyLineName", false);
	public static List<TypeCodeEntry> GetFamilyLineList() { return familyLineList.GetList(); }

	private static CachedList modelTypeList = new CachedList("RPT_GetList_ModelType", "ModelTypeId", "ModelTypeName", false);
	public static List<TypeCodeEntry> GetModelTypeList() { return modelTypeList.GetList(); }

	public static List<TypeCodeEntry> GetDevicesList(int deviceTypeId) { return GetTypeCodeList("RPT_GetList_Device", "DeviceId", "DeviceName", deviceTypeId); }
    public static List<TypeCodeEntry> GetEventTypeList(int deviceTypeId) { return GetTypeCodeList("RPT_GetList_EventTypeByDeviceType", "EventTypeId", "FullName", deviceTypeId); }
	public static List<TypeCodeEntry> GetFailureLocationList(int deviceCode) { return GetFilteredTypeCodeList("RPT_GetList_FailureLocation", "FailureLocationId", "FailureLocationName", deviceCode, false); }

    private static List<TypeCodeEntry> GetTypeCodeList(string taskName, string codeFieldName, string nameFieldName, int paramValue)
	{
        List<TypeCodeEntry> retVal = new List<TypeCodeEntry>();
		DataSet ds = SqlHelper.ExecuteDataset(taskName, paramValue);

		foreach (DataRow row in ds.Tables[0].Rows)
		{
			if (row.IsNull(codeFieldName) == false)
				retVal.Add(new TypeCodeEntry(row[codeFieldName].ToString(), DataFormatter.getString(row, nameFieldName)));
		}

		return retVal;
	}

	private static List<TypeCodeEntry> GetFilteredTypeCodeList(string taskName, string codeFieldName, string nameFieldName, int paramValue, bool includeInactive) {
		List<TypeCodeEntry> retVal = new List<TypeCodeEntry>();
		DataSet ds = SqlHelper.ExecuteDataset(taskName, paramValue, includeInactive);

		foreach (DataRow row in ds.Tables[0].Rows) {
			if (row.IsNull(codeFieldName) == false)
				retVal.Add(new TypeCodeEntry(row[codeFieldName].ToString(), DataFormatter.getString(row, nameFieldName)));
		}

		return retVal;
	}

	public static MessageInfo LoadMessageInfo(DataRow row)
	{
		MessageInfo msgInfo = new MessageInfo();
		msgInfo.MessageId = DataFormatter.getInt32(row, "MessageId");
		msgInfo.IsUrgent = DataFormatter.getBool(row, "IsUrgent");
		msgInfo.MessageBody = DataFormatter.getString(row, "MessageBody");
		msgInfo.LastModified = DataFormatter.getDateTime(row, "LastModified");
		msgInfo.MessageSubject = DataFormatter.getString(row, "MessageSubject");
		msgInfo.CreatedBy = DataFormatter.getString(row, "CreatedBy");
		msgInfo.SessionId = DataFormatter.getInt32(row, "SessionId");
		msgInfo.SessionName = DataFormatter.getString(row, "SessionName");
		return msgInfo;
	}

	public static void SetReportInfoForTransfer(ReportInfo repInfo)
	{
		HttpContext.Current.Items["ReportInfo"] = repInfo;
		HttpContext.Current.Session["ReportInfo"] = repInfo;
	}

    public static ReportInfo GetReportInfoFromTransfer()
    {
        ReportInfo repInfo = (ReportInfo)HttpContext.Current.Items["ReportInfo"];

        if (repInfo == null && HttpContext.Current.Session != null)
            repInfo = (ReportInfo)HttpContext.Current.Session["ReportInfo"];

        return repInfo;
    }

    public static void SetSubReportInfoForTransfer(SubReportInfo subReportInfo)
    {
        HttpContext.Current.Items["SubReportInfo"] = subReportInfo;
        HttpContext.Current.Session["SubReportInfo"] = subReportInfo;
    }

    public static SubReportInfo GetSubReportInfoFromTransfer()
    {
        SubReportInfo subReportInfo = (SubReportInfo)HttpContext.Current.Items["SubReportInfo"];

        if (subReportInfo == null && HttpContext.Current.Session != null)
            subReportInfo = (SubReportInfo)HttpContext.Current.Session["SubReportInfo"];

        return subReportInfo;
    }
    
    public static string VerifyCumulativeValues(object tranId, DateTime transactionDate, string cellId, string sessionId, string cumTranCountValue, string cumMediaCountValue)
	{
		//Verify that the CumTranCount is valid between the previous and next dated observation or manual transaction volume entries.
		DataSet ds = SqlHelper.ExecuteDataset("RPT_ValidateCumCounts", tranId, transactionDate, cellId, sessionId);
		StringBuilder sb = new StringBuilder();

		if (ds.Tables[0] != null && ds.Tables[0].Rows[0] != null)
		{
			DataRow row = ds.Tables[0].Rows[0];

			int minTran = DataFormatter.getInt32(row, "MinCumTranCount");
			int maxTran = DataFormatter.getInt32(row, "MaxCumTranCount");
			int minMedia = DataFormatter.getInt32(row, "MinCumMediaCount");
			int maxMedia = DataFormatter.getInt32(row, "MaxCumMediaCount");

			bool isMinTranManual = DataFormatter.getBool(row, "IsMinTranManual");
			bool isMaxTranManual = DataFormatter.getBool(row, "isMaxTranManual");
			bool isMinMediaManual = DataFormatter.getBool(row, "isMinMediaManual");
			bool isMaxMediaManual = DataFormatter.getBool(row, "isMaxMediaManual");

			DateTime MinTranDate = DataFormatter.getDateTime(row, "MinTranDate");
			DateTime MaxTranDate = DataFormatter.getDateTime(row, "MaxTranDate");
			DateTime MinMediaDate = DataFormatter.getDateTime(row, "MinMediaDate");
			DateTime MaxMediaDate = DataFormatter.getDateTime(row, "MaxMediaDate");

			if (!string.IsNullOrEmpty(cumTranCountValue) && !cumTranCountValue.Equals("0"))
			{
				if (minTran > 0 && Convert.ToInt32(cumTranCountValue) <= minTran)
				{
					if (isMinTranManual)
						sb.Append(string.Format("<li>The Cumulative <b>Transaction</b> count is <b>less than or equal to</b> a previously entered value from the Transaction Volume screen on date: '{0}'.</li>", MinTranDate.ToString("MM/dd/yyyy hh:mm tt")));
					else
						sb.Append(string.Format("<li>The Cumulative <b>Transaction</b> count is <b>less than or equal to</b> a previously entered value from the Observation screen on date: '{0}'.</li>", MinTranDate.ToString("MM/dd/yyyy hh:mm tt")));
				}

				if (maxTran > 0 && Convert.ToInt32(cumTranCountValue) >= maxTran)
				{
					if (isMaxTranManual)
						sb.Append(string.Format("<li>The Cumulative <b>Transaction</b> count is <b>equal to or greater than</b> than a more recent value entered from the Transaction Volume screen on date: '{0}'.</li>", MaxTranDate.ToString("MM/dd/yyyy hh:mm tt")));
					else
						sb.Append(string.Format("<li>The Cumulative <b>Transaction</b> count is <b>equal to or greater than</b> than a more recent value entered from the Observation screen on date: '{0}'.</li>", MaxTranDate.ToString("MM/dd/yyyy hh:mm tt")));
				}
			}

			if (!string.IsNullOrEmpty(cumMediaCountValue) && !cumMediaCountValue.Equals("0"))
			{
				if (minMedia > 0 && Convert.ToInt32(cumMediaCountValue) <= minMedia)
				{
					if (isMinMediaManual)
						sb.Append(string.Format("<li>The Cumulative <b>Media</b> count is <b>less than or equal to</b> a previously entered value from the Transaction Volume screen on date: '{0}'.</li>", MinMediaDate.ToString("MM/dd/yyyy hh:mm tt")));
					else
						sb.Append(string.Format("<li>The Cumulative <b>Media</b> count is <b>less than or equal to</b> a previously entered value from the Observation screen on date: '{0}'.</li>", MinMediaDate.ToString("MM/dd/yyyy hh:mm tt")));
				}

				if (maxMedia > 0 && Convert.ToInt32(cumMediaCountValue) >= maxMedia)
				{
					if (isMaxMediaManual)
						sb.Append(string.Format("<li>The Cumulative <b>Media</b> count is <b>equal to or greater than</b> than a more recent value entered from the Transaction Volume screen on date: '{0}'.</li>", MaxMediaDate.ToString("MM/dd/yyyy hh:mm tt")));
					else
						sb.Append(string.Format("<li>The Cumulative <b>Media</b> count is <b>equal to or greater than</b> than a more recent value entered from the Observation screen on date: '{0}'.</li>", MaxMediaDate.ToString("MM/dd/yyyy hh:mm tt")));
				}
			}
		}

		return sb.ToString();
	}

	public static string ConvertListBoxSelectionsToString(ListItemCollection listBoxCntrlItems, bool useValues)
	{
		StringBuilder retVal = new StringBuilder();
		if (listBoxCntrlItems != null)
		{
			bool isFirst = true;
			foreach (ListItem item in listBoxCntrlItems)
			{
				if (item.Selected)
				{
					if (!isFirst)
						retVal.Append(", ");

					if (useValues)
						retVal.Append(item.Value);
					else
						retVal.Append(item.Text);

					isFirst = false;
				}
			}
		}

		return retVal.ToString();
	}

	public static void SetListBoxSelectedValuesFromString(ListBox listBox, string selectedItems)
	{
		string[] itemCol = selectedItems.Split(new string[] { ", " }, StringSplitOptions.RemoveEmptyEntries);

		foreach (string item in itemCol)
		{
			if (listBox.Items.FindByValue(item) != null)
				listBox.Items.FindByValue(item).Selected = true;
		}
	}

    public static void ClearSearchSession(bool advancedOnly)
    {
        System.Web.SessionState.HttpSessionState Session = HttpContext.Current.Session;

        if (!advancedOnly)
        {
            Session[DieboldConstants.ADV_SEARCH_TEXT] = null;
            Session[DieboldConstants.ADV_SEARCH_SESSION] = null;
            Session[DieboldConstants.ADV_SEARCH_START_DATE] = null;
            Session[DieboldConstants.ADV_SEARCH_END_DATE] = null;
            Session[DieboldConstants.ADV_SEARCH_TRANSACTION_START] = null;
            Session[DieboldConstants.ADV_SEARCH_TRANSACTION_END] = null;
            Session[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY] = null;
        }

		Session[DieboldConstants.ADV_SEARCH_CELL] = null;
		Session[DieboldConstants.ADV_SEARCH_CELL_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] = null;
		Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_DEVICE] = null;
		Session[DieboldConstants.ADV_SEARCH_DEVICE_NAME] = null;
        Session[DieboldConstants.ADV_SEARCH_EVENT] = null;
		Session[DieboldConstants.ADV_SEARCH_EVENT_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] = null;
		Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] = null;
		Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] = null;
		Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA_NAME] = null;
        Session[DieboldConstants.ADV_SEARCH_SETTING] = null;
		Session[DieboldConstants.ADV_SEARCH_SETTING_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_SETTING_TREE] = null;
        Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] = null;
		Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] = null;
		Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE] = null;
		Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] = null;
		Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] = null;
		Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] = null;
		Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] = null;
		Session[DieboldConstants.ADV_SEARCH_OWNER] = null;
		Session[DieboldConstants.ADV_SEARCH_OWNER_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_OPERATOR] = null;
		Session[DieboldConstants.ADV_SEARCH_OPERATOR_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_DISCIPLINE] = null;
		Session[DieboldConstants.ADV_SEARCH_DISCIPLINE_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] = null;
		Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE_NAME ] = null;
		Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] = null;
		Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE_NAME] = null;
		Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] = null;
		Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE_NAME] = null;
	}

    public static string FormatFieldNameList(List<SelectedField> fieldList, bool includeFields, bool includeOptions, string fieldSeparator, string optionSetPrefix, string optionSearator)
    {
        StringBuilder retVal = new StringBuilder();
        bool isFirstField = true;
        bool isFirstOption = true;

        foreach (SelectedField field in fieldList)
        {
            if (includeFields)
            {
                if (!isFirstField)
                    retVal.Append(fieldSeparator);

                retVal.Append(field.FieldName);
            }

            if (includeOptions)
            {
                isFirstOption = true;
                foreach (SelectedFieldOption option in field.SelectedFieldOptions)
                {
                    if (isFirstOption && includeFields)
                        retVal.Append(optionSetPrefix);
                    else if (isFirstOption && !isFirstField)
                        retVal.Append(optionSearator);
                    else if (!isFirstOption)
                        retVal.Append(optionSearator);

                    retVal.Append(option.FieldOptionName);
                    isFirstOption = false;
                }
            }

            isFirstField = false;
        }

        return retVal.ToString();
    }

    public static string FormatFieldIdList(List<SelectedField> fieldList, bool includeFields, bool includeOptions, string fieldSeparator, string optionSetPrefix, string optionSearator)
    {
        StringBuilder retVal = new StringBuilder();
        bool isFirstField = true;
        bool isFirstOption = true;

        foreach (SelectedField field in fieldList)
        {
            if (includeFields)
            {
                if (!isFirstField)
                    retVal.Append(fieldSeparator);

                retVal.Append(field.FieldId);
            }

            if (includeOptions)
            {
                isFirstOption = true;
                foreach (SelectedFieldOption option in field.SelectedFieldOptions)
                {
                    if (isFirstOption && includeFields)
                        retVal.Append(optionSetPrefix);
                    else if (isFirstOption && retVal.Length > 0)
                        retVal.Append(optionSearator);
                    else if (!isFirstOption)
                        retVal.Append(optionSearator);

                    retVal.Append(option.FieldOptionId);
                    isFirstOption = false;
                }
            }

            isFirstField = false;
        }

        return retVal.ToString();
    }

	public static DateTime CalculateNextScheduledReportDate(DateTime scheduledDate, CheckBoxList scheduleList)
	{
		bool runSunday = scheduleList.Items[0].Selected;
		bool runMonday = scheduleList.Items[1].Selected;
		bool runTuesday = scheduleList.Items[2].Selected;	
		bool runWednesday = scheduleList.Items[3].Selected;
		bool runThursday = scheduleList.Items[4].Selected;
		bool runFriday = scheduleList.Items[5].Selected;
		bool runSaturday = scheduleList.Items[6].Selected;

		double dayOffset = 0;

		DateTime curDate = DateTime.Now;
		if (scheduledDate > curDate)
		{
			//remove current time
			curDate = curDate.AddHours(-curDate.Hour);
			curDate = curDate.AddMinutes(-curDate.Minute);
			curDate = curDate.AddSeconds(-curDate.Second);
			curDate = curDate.AddMilliseconds(-curDate.Millisecond);

			//reset to scheduled time, round to hours & minutes...removing seconds and milliseconds
			curDate = curDate.AddHours(scheduledDate.Hour);
			curDate = curDate.AddMinutes(scheduledDate.Minute);

			scheduledDate = curDate;
		}

		//if time is passed for today, start looking for the next schedule date tomorrow
		if (scheduledDate.TimeOfDay < DateTime.Now.TimeOfDay)
			dayOffset = 1;

		bool dateFound = false;
		while (!dateFound && dayOffset < 14)
		{
			switch (scheduledDate.AddDays(dayOffset).DayOfWeek)
			{
				case DayOfWeek.Sunday:
					if (runSunday)
						dateFound = true;
					break;
				case DayOfWeek.Monday:
					if (runMonday)
						dateFound = true;
					break;
				case DayOfWeek.Tuesday:
					if (runTuesday)
						dateFound = true;
					break;
				case DayOfWeek.Wednesday:
					if (runWednesday)
						dateFound = true;
					break;
				case DayOfWeek.Thursday:
					if (runThursday)
						dateFound = true;
					break;
				case DayOfWeek.Friday:
					if (runFriday)
						dateFound = true;
					break;
				case DayOfWeek.Saturday:
					if (runSaturday)
						dateFound = true;
					break;
			}

			if (!dateFound)
				dayOffset++;
		}

		return scheduledDate.AddDays(dayOffset);
	}

    public static object GetDataField(object dataObject, string fieldName)
    {
        object evalObj = null;
        if (dataObject is DataRowView)
            dataObject = ((DataRowView)dataObject).Row;

        if (dataObject is DataRow)
        {
            if (((DataRow)dataObject).IsNull(fieldName) == false)
            {
                evalObj = ((DataRow)dataObject)[fieldName];
            }
        }
        else if (dataObject is SqlDataReader)
        {
            int ord = ((SqlDataReader)dataObject).GetOrdinal(fieldName);
            if (((SqlDataReader)dataObject).IsDBNull(ord) == false)
            {
                evalObj = ((SqlDataReader)dataObject).GetValue(ord);
            }
        }
        else if (dataObject is DbDataRecord)
        {
            int ord = ((DbDataRecord)dataObject).GetOrdinal(fieldName);
            if (((DbDataRecord)dataObject).IsDBNull(ord) == false)
            {
                evalObj = ((DbDataRecord)dataObject).GetValue(ord);
            }
        }

        return evalObj;
    }

    public static string FormatJsonString(string text) { return FormatJsonString(null, text); }

    public static string FormatJsonString(object dataObject, string fieldName)
    {
        string retVal = null;

        if (dataObject != null)
        {
            object evalObj = GetDataField(dataObject, fieldName);

            if (evalObj != null)
                retVal = evalObj.ToString();
        }
        else
        {
            retVal = fieldName;
        }

        if (retVal != null)
            retVal = retVal.Replace("\\", "\\\\").Replace("\r", "\\r").Replace("\n", "\\n").Replace("'", "\\'");

        return retVal;
    }

	public static string GeneratePDFThumbnail(string sourceImagePDFPath, string destImageFilePath, int maxThumbSizeWidth, int maxThumbSizeHeight, bool enforceSquare)
	{
		string retVal = null;

		using (Doc pdfDoc = new Doc())
		{
			pdfDoc.Read(sourceImagePDFPath);
			pdfDoc.Rendering.DotsPerInch = 300;

			using (Bitmap pdfBitmap = pdfDoc.Rendering.GetBitmap())
			{
				int shtWidth = 0;
				int shtHeight = 0;
				int x = 1;
				int y = 1;

				Scale(pdfBitmap.Height, pdfBitmap.Width, maxThumbSizeHeight, maxThumbSizeWidth, ref shtHeight, ref shtWidth);
				if (shtWidth < maxThumbSizeWidth)
					x = (maxThumbSizeWidth - shtWidth) / 2;

				if (shtHeight < maxThumbSizeHeight)
					y = (maxThumbSizeHeight - shtHeight) / 2;

				using (Bitmap bitmap = new Bitmap(shtWidth, shtHeight, PixelFormat.Format24bppRgb))
				{
					using (Graphics graphicBox = Graphics.FromImage(bitmap))
					{
						graphicBox.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
						graphicBox.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
						graphicBox.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
						graphicBox.Clear(Color.Transparent);
						graphicBox.DrawImage(pdfBitmap, 0, 0, shtWidth, shtHeight);

						int finalWidth = shtWidth;
						int finalHeight = shtHeight;
						if (enforceSquare)
						{
							if (maxThumbSizeWidth > 0)
								finalWidth = maxThumbSizeWidth;
							if (maxThumbSizeHeight > 0)
								finalHeight = maxThumbSizeHeight;
						}
						else
						{
							x = 0;
							y = 0;
						}

						using (Bitmap finalThumbnail = new Bitmap(finalWidth, finalHeight, PixelFormat.Format32bppArgb))
						{
							using (Graphics finalGraphicBox = Graphics.FromImage(finalThumbnail))
							{
								finalGraphicBox.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
								finalGraphicBox.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
								finalGraphicBox.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
								finalGraphicBox.Clear(Color.Transparent);
								finalGraphicBox.DrawImage(bitmap, x, y, shtWidth, shtHeight);

								//ensure we are saving as a .png file
								FileInfo file = new FileInfo(destImageFilePath);
								destImageFilePath = file.FullName.Substring(0, file.FullName.Length - file.Extension.Length) + ".png";

								if (File.Exists(destImageFilePath))
									File.Delete(destImageFilePath);

								finalThumbnail.Save(destImageFilePath, ImageFormat.Png);

								//return just the file name
								retVal = file.Name.Substring(0, file.Name.Length - file.Extension.Length) + ".png";
							}
						}
					}
				}
			}
		}
		return retVal;
	}

	public static void Scale(int originalHeight, int originalWidth, int maxScaledHeight, int maxScaledWidth, ref int newHeight, ref int newWidth)
	{
		if (originalHeight != 0 && originalWidth != 0)
		{
			// Set proportionate dimensions
			if (maxScaledHeight <= 0 && maxScaledWidth <= 0)
			{
				newHeight = originalHeight;
				newWidth = originalWidth;
				return;
			}

			if ((maxScaledHeight > 0 && originalHeight > originalWidth) || (maxScaledWidth <= 0))
			{
				newHeight = maxScaledHeight;
				newWidth = Convert.ToInt32(Math.Floor(Convert.ToDouble(originalWidth) / (Convert.ToDouble(originalHeight) / Convert.ToDouble(newHeight))));
			}
			else
			{
				newWidth = maxScaledWidth;
				newHeight = Convert.ToInt32(Math.Floor(Convert.ToDouble(originalHeight) / (Convert.ToDouble(originalWidth) / Convert.ToDouble(newWidth))));
			}

			// If overflow occurs reset size by opposite dimension
			if (maxScaledHeight > 0 && newHeight > maxScaledHeight)
			{
				newHeight = maxScaledHeight;
				newWidth = Convert.ToInt32(Math.Floor(Convert.ToDouble(originalWidth) / (Convert.ToDouble(originalHeight) / Convert.ToDouble(newHeight))));
			}
			else if (maxScaledWidth > 0 && newWidth > maxScaledWidth)
			{
				newWidth = maxScaledWidth;
				newHeight = Convert.ToInt32(Math.Floor(Convert.ToDouble(originalHeight) / (Convert.ToDouble(originalWidth) / Convert.ToDouble(newWidth))));
			}
		}
	}
}
