using System;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public partial class RunSensorReport : System.Web.UI.Page
{
    protected BaseReport ReportObj = null;
	List<SensorChartInfo> chartColl = null;

    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

	public bool IsReportLoaded
	{
		get { if (this.ViewState["ld"] != null) { return (bool)this.ViewState["ld"]; } else { return false; } }
		set { this.ViewState["ld"] = value; }
	}

    public SubReportInfo SubReportInfo
    {
        get { if (this.ViewState["sr"] != null) { return (SubReportInfo)this.ViewState["sr"]; } else { return null; } }
        set { this.ViewState["sr"] = value; }
    }

	protected void Page_Load(object sender, EventArgs e)
    {
        //this.chartCntrl.CommandFired += new Chart.CommandFiredEventHandler(chartCntrl_CommandFired);

        if (!Page.IsPostBack)
        {
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]))
            {
                this.SubReportInfo = Utility.LoadSubReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]));
            }
            
            if (this.RepInfo == null)
                Response.Redirect("~/reportlibrary.aspx");

            string loadScript = "<script language='javascript'>\r\ndocument.forms[0].submit();\r\n</script>";
            Page.ClientScript.RegisterStartupScript(this.GetType(), "LoadReportScript", loadScript);
        }

		if (Page.IsPostBack && chartColl == null)
		{
			InitialLoadingPanel.Visible = false;
			MainPanel.Visible = true;

            this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, this.SubReportInfo);

			//require user to be admin to edit or save reports that are associated with sessions
			if (this.RepInfo.FolderId == 0 && this.RepInfo.ReportId != 0)
			{
				bool isAdmin = Utility.IsUserAdmin();
				EditBtnDiv.Visible = isAdmin;
				SaveBtnDiv.Visible = isAdmin;
			}

			this.ReportObj.LoadData();

			chartColl = new List<SensorChartInfo>();
			TupleCollection colTuples = ((SensorReport)this.ReportObj).GetCellSet().Axes[0].Set.Tuples;

			// Iterate over chart columns to determine which when to begin a new chart
			string previousSensor = null;
			for (int col = 0; col < colTuples.Count; col++)
			{
                Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
				string[] seriesNameParts = colTuple.Members[0].Caption.Split(new string[] { ":" }, StringSplitOptions.RemoveEmptyEntries);
				string sensorName = seriesNameParts[0];

				if (!string.IsNullOrEmpty(previousSensor))
				{
					if (!previousSensor.Equals(sensorName))
					{
						SensorChartInfo chartInfo = new SensorChartInfo();

						if (chartColl.Count == 0)
						{
							chartInfo.StartColumnIndex = 0;
							chartInfo.EndColumnIndex = col - 1;
						}
						else
						{
							chartInfo.StartColumnIndex = ((SensorChartInfo)chartColl[chartColl.Count - 1]).EndColumnIndex + 1;
							chartInfo.EndColumnIndex = col - 1;
						}

						chartColl.Add(chartInfo);
					}
				}

				previousSensor = sensorName;
			}

			//Add in final value
			SensorChartInfo cInfo = new SensorChartInfo();
			
			if (chartColl.Count > 0)
				cInfo.StartColumnIndex = ((SensorChartInfo)chartColl[chartColl.Count - 1]).EndColumnIndex + 1;
			else
				cInfo.StartColumnIndex = 0;
			
			cInfo.EndColumnIndex = colTuples.Count - 1;
			chartColl.Add(cInfo);

            if (this.SubReportInfo != null)
                reportNameLabel.Text = this.SubReportInfo.SubReportName;
            else
    			reportNameLabel.Text = this.RepInfo.ReportName;
		}

		//bind chart each page refresh
		ChartRepeater.DataSource = chartColl;
		ChartRepeater.DataBind();
    }

	protected void ChartRepeater_ItemDataBound(object sender, RepeaterItemEventArgs e)
	{
		if (e.Item != null && e.Item.DataItem != null)
		{
			if (e.Item.FindControl("chartCntrl") != null)
			{
				Chart chartObj = (Chart)e.Item.FindControl("chartCntrl");
				this.ReportObj.InitializeChart(chartObj, "RunSensorReport");

				((SensorReport)this.ReportObj).PopulateChart(chartObj, true, ((SensorChartInfo)chartColl[e.Item.ItemIndex]).StartColumnIndex, ((SensorChartInfo)chartColl[e.Item.ItemIndex]).EndColumnIndex);

                chartObj.RenderType = RenderType.ImageTag;
                chartObj.ImageType = ChartImageType.Jpeg;
                chartObj.MapEnabled = false;
                chartObj.UI.Toolbar.Enabled = false;
                chartObj.UI.ContextMenu.Enabled = false;

                //GridBindingPanel.DataBind();
			}
			else
			{
				throw new ApplicationException("Unable to find the chart object.");
			}
		}
	}

	protected void PrintButton_Click(object sender, EventArgs e)
	{
        Utility.SetReportInfoForTransfer(this.RepInfo);
        Utility.SetSubReportInfoForTransfer(this.SubReportInfo);
        Response.Redirect("PrintSensorReport.aspx");
	}

	protected void ViewTranButton_Click(object sender, EventArgs e)
	{
	    SearchCriteria searchCriteria = new SearchCriteria(this.Page);
        searchCriteria.CalculateSearchCriteria(this.RepInfo, this.SubReportInfo);
	    searchCriteria.SetToSession();        
	    Response.Redirect("Search.aspx");
	}

	protected void ExportButton_Click(object sender, EventArgs e)
	{
	//    Response.ClearHeaders();
	//    Response.ClearContent();
	//    Response.Charset = "";
	//    Response.AppendHeader("content-disposition", "attachment; filename=" + this.RepInfo.ReportName + ".xls");
	//    Response.ContentType = "application/vnd.ms-excel";

	//    StringWriter colWrite = new StringWriter();
	//    StringWriter rowWrite = new StringWriter();
	//    HtmlTextWriter rowsHtmlWrite = new HtmlTextWriter(rowWrite);

	//    GridBindingPanel.RenderControl(rowsHtmlWrite);
		
	//    Response.Write(rowWrite.ToString());
	//    Response.End();
	}

	void chartCntrl_CommandFired(object sender, CommandFiredArgs e)
	{
	    //if (e.Command.CommandType == ChartCommandType.SelectPaletteDefault)
	   // {
	    //    this.chartCntrl.Titles["Title1"].Font = new Font("Arial", 11, FontStyle.Bold);
	   // }
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "SaveReportWindow")
				win.VisibleOnPageLoad = true;
		}
	}

	protected void EditButton_Click(object sender, EventArgs e)
	{
		Utility.SetReportInfoForTransfer(this.RepInfo);

		foreach (RadWindow win in RadWindowManager1.Windows)
		{
			if (win.ID == "EditWindow")
			{
                win.NavigateUrl = this.RepInfo.WizardPageName;
				win.VisibleOnPageLoad = true;
			}
		}
	}
	
	protected void ScheduleButton_Click(object sender, EventArgs e)
	{
	    foreach (RadWindow win in RadWindowManager1.Windows)
	    {
	        if (win.ID == "ScheduleWindow")
	        {
                if (this.SubReportInfo != null)
                    win.NavigateUrl = "Popup_EditScheduledReport.aspx?r=" + this.RepInfo.ReportId + "&s=" + +this.SubReportInfo.SubReportId;
                else
                    win.NavigateUrl = "Popup_EditScheduledReport.aspx?r=" + this.RepInfo.ReportId;
	            win.VisibleOnPageLoad = true;
	        }
	    }
	}
}
