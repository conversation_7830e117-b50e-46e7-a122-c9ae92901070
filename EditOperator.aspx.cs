using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class EditOperator : System.Web.UI.Page
{
	public bool IncludeInactive
	{
		get { if (this.Session[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY] != null) return (bool)this.Session[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY]; else return false; }
		set { this.Session[DieboldConstants.SESSION_INCLUDE_INACTIVE_KEY] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			//ManageClosedDisplay();
			
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}

		BindData();
	}

	//private void ManageClosedDisplay()
	//{
	//	if (this.IncludeInactive)
	//	{
	//		this.includeInactiveLink.Text = "Exclude Inactive";
	//		IncludeInactiveToggle.Attributes.Add("class", "cancelButtonTop");
	//	}
	//	else
	//	{
	//		this.includeInactiveLink.Text = "Include Inactive";
	//		IncludeInactiveToggle.Attributes.Add("class", "addButtonTop");
	//	}
	//}

	protected void IncludeInactive_Toggle(object sender, EventArgs e)
	{
		this.IncludeInactive = !this.IncludeInactive;
		//ManageClosedDisplay();
		BindData();
	}

	private void BindData()
	{
		//DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_Operator", IncludeInactive); 
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_Operator", true);
		DataGrid1.DataBind();
	}
}
