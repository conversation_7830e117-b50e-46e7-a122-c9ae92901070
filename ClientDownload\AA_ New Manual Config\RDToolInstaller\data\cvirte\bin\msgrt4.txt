933 30165
0   "No error has occurred"
1   "The User Interface Manager could not be opened"
2   "The system font could not be loaded"
3   "The operation attempted cannot be performed while a pop-up panel is active"
4   "Panel, pop-up, or menu bar handle is invalid"
5   "Attempted to position panel at an invalid location"
6   "Attempted to make an inoperable control the active control"
7   "The operation requires that a panel be active"
8   "The operation requires that a pop-up be active"
9   "The operation requires that a menu bar be active"
10   "The control is not the type expected by the function"
11   "The id passed was not a valid menu item id"
12   "Out of memory"
13   "Invalid control ID"
14   "Value is invalid or out of range"
15   "File is not a User Interface file or has been corrupted"
16   "File format is out-of-date"
17   "Image is corrupted or format is not supported"
18   "No user event possible in current configuration"
19   "Unable to open file"
20   "Error reading file"
21   "Error writing file"
22   "Error closing file"
23   "Panel state file has invalid format"
24   "Resource ID not found in user interface resource file"
25   "Error occurred during hardcopy output"
26   "Invalid default directory specified in FileSelectPopup function"
27   "Operation is invalid for specified object"
28   "Unable to find specified string in menu"
29   "Palette menu items can only be added to the end of the menu"
30   "Too many menus in the menu bar"
31   "Separators can't have checkmarks"
32   "Separators can't have submenus"
34   "The menu item can't be a separator"
35   "The item already has a submenu"
36   "The item does not have a submenu"
37   "The id passed must be a menu id, a menu item id, or NULL"
38   "The id passed must be a menu id or a menu item id"
39   "The id passed was not a submenu id"
40   "The id passed was not a valid menu id"
41   "The handle is not a menu bar handle"
42   "The handle is not a panel handle"
43   "The operation attempted cannot be performed while this pop-up panel is active"
44   "The specified Control/Panel/Menu doesn't have the specified attribute"
45   "The control type passed was not a valid type"
46   "The attribute passed is not valid"
47   "The fill option must be set to fill above or fill below to paint ring slide's fill color"
48   "The fill option must be set to fill above or fill below to paint numeric slide's fill color"
49   "The control passed is not a ring slide"
50   "The control passed is not a numeric slide"
51   "The control passed is not a ring slide with inc/dec arrows"
52   "The control passed is not a numeric slide with inc/dec arrows"
53   "The data type passed is not a valid data type for the control"
54   "The attribute passed is not valid for the data type of the control"
55   "The index passed is out of range"
56   "There are no items in the list control"
57   "The buffer passed was too small for the operation"
58   "The control does not have a value"
59   "The value passed is not in the list control"
60   "The control passed must be a list control"
61   "The control passed must be a list control or binary switch"
62   "The data type of the control passed must be set to string"
63   "That attribute is not a settable attribute"
64   "The value passed is not a valid control mode for this control"
65   "A NULL pointer was passed when a non-NULL pointer was expected"
66   "The text bg color on a menu ring can not be set or gotten"
67   "The ring control passed must be one of the menu ring styles"
68   "Text can not be colored transparent"
69   "A value can not be converted to the specified data type"
70   "Invalid tab order position for control"
71   "The tab order position of an indicator-only-control cannot be set"
72   "Invalid number"
73   "There is no menu bar installed for the panel"
74   "The control passed is not a text box"
75   "Invalid scroll mode for chart"
76   "Invalid image type for picture"
77   "The attribute is valid for child panels only\nSome attributes of top level panels are determined by the host operating system."
78   "The list control passed is not in check mode"
79   "The control values could not be completely loaded into the panel\nbecause the panel has changed."
80   "Maximum value must be greater than minimum value"
81   "Graph doesn't have that many cursors"
82   "Invalid plot"
83   "New cursor position is outside plot area"
84   "The length of the string exceeds the limit"
85   "The callback function, %s, specified in the UIR file, does not have the required prototype."
86   "The callback function, %s, specified in the UIR file, " "is not a known function.  If you are using an external " "compiler, you must include the UIR callbacks object or " "source file in the executable or DLL."
87   "Graph cannot be in this mode without cursors"
88   "Invalid axis scaling mode for chart"
89   "The font passed is not in the meta font table"
90   "The attribute value passed is not valid"
91   "Too many files are open"
92   "Unexpectedly reached end of file"
93   "Input/Output error"
94   "File not found"
95   "File access permission denied"
96   "File access is not enabled"
97   "Disk is full"
98   "File already exists"
99   "File is already open"
100   "Badly formed pathname"
101   "File is damaged"
102   "Sorry, the format of the resource file is too old to read"
103   "File is damaged"
104   "The operation could not be performed"
105   "The control passed is not a ring knob, dial or gauge"
106   "The control passed is not a numeric knob, dial or gauge"
107   "The count passed is out of range"
108   "The keycode is not valid"
109   "The control passed is not a ring slide with a frame"
110   "Panel background cannot be colored transparent"
111   "Title background cannot be colored transparent"
112   "Not enough memory for printing"
113   "The shortcut key passed is reserved"
114   "The format of the file is newer than this version of CVI"
115   "System printing error"
116   "Driver printing error"
117   "The deferred callback queue is full"
118   "The mouse cursor passed is not valid"
119   "Cannot print while another printing operation is still active"
120   "Out of Windows GDI space"
121   "The panel must be visible"
122   "The control must be visible"
123   "The attribute is not valid for the type of plot"
124   "Intensity plots cannot use transparent colors"
125   "Color is invalid"
126   "The callback function, %s, differs only by a leading underscore from another " "function or variable.  Change one of the names for proper linking."
127   "Bitmap is invalid"
128   "There is no image in the control"
129   "This panel operation can be performed (if a top-level panel) " "only in the thread in which the panel was created, or " "(if a child panel) only in the thread in which the top-level " "parent panel was created."
130   "Panel not found in .tui file"
131   "Menu bar not found in .tui file"
132   "Control style not found in .tui file"
133   "Missing tag or value in .tui file"
134   "Error reading or parsing .sub file"
135   "There are no printers installed in the system"
136   "The beginning cell must be in the search range"
137   "The cell type passed is not valid for this operation"
138   "Cell type or data type is mismatched"
139   "Controls of the type passed do not have a menu"
140   "Built-in control menu items cannot be discarded"
141   "Separators can't have bold font"
142   "You must pass your callback function's eventData2 parameter to this function"
143   "ActiveX control error"
144   "The CAObjHandle passed does not refer to an ActiveX control"
145   "ActiveX control not registered on this computer"
400   "File selection restricted to current directory"
401   "Selection limited to files with \".%s\" extension"
402   "The directory '%s'\ndoes not exist"
403   "Directory name must not include drive specifier"
404   "Directory already exists"
405   "does not exist in directory"
406   "Unable to make directory\n\"%s\""
407   "File __Name:"
408   "List Files of __Type:"
409   "Dri__ves:"
410   "Make Directory"
411   "__Directories:"
412   "Directory (exclude drive):"
413   "Drive %s is not ready"
500   "An unspecified error has occurred, probably due to corrupted data"
501   "Sample Text"
503   "C"
504   "S"
505   "I"
506   "Goto..."
507   "Find..."
508   "Sort..."
509   "Untitled Control"
510   "Untitled Panel"
511   "The file is not a .tui file."
1200   "OK"
1201   "Yes"
1202   "No"
1203   "Cancel"
1204   "Untitled"
1205   "Cannot find file"
1206   "Unable to open file"
1207   "Unable to open library file: \"%s\"\n" "Would you like to remove this library from the User Libraries list?"
1208   "Error reading file"
1209   "already exists.  Do you want to overwrite it?"
1210   "already exist.  Do you want to overwrite them?"
1211   "Maximum line length exceeded"
1212   "Ins"
1213   "Ovr"
1214   "Incorrect format or corrupted data in file"
1215   "LabWindows/CVI Message"
1216   "Display"
1217   "Select"
1218   "Run-time Warning"
1219   "Non-Fatal Run-time Error"
1220   "Fatal Run-time Error"
1221   "Run-time Message"
1222   "File \"%s\", line %d, column %d:\n\n%s"
1223   "Fin__d What:"
1224   "__Case Sensitive"
1225   "W__hole Cell"
1226   "Search __Vertically"
1227   "__Selected Cells Only"
1228   "__Wrap"
1229   "Find __Prev"
1230   "Find __Next"
1231   "Must specify search pattern"
1232   "The string '%s'\n"
1233   "has no matches"
1234   " in the selected area"
1235   " from the active cell\nto the first cell"
1236   " from the active cell\nto the last cell"
1237   "Invalid pathname in file"
1238   "Unable to load DLL."
1239   "Cannot add font resource"
1240   "<< Waiting For Input >>"
1241   "<< Waiting For Key >>"
1242   "__Full Path Names"
1243   "return value"
1244   "Default"
1245   "Unable to obtain application path"
1246   "Invalid file extension"
1247   "Path does not exist"
1248   "Invalid drive letter"
1249   "The tag \"%s\" is missing from section [%s],\n" "or it has no value."
1250   "The value for tag \"%s\" in section [%s]\n" "is invalid."
1251   "The value for tag \"%s\" in section [%s]\n" "is invalid. {%s}"
1252   "Error while loading section [%s].\n" "{%s}"
1253   "Invalid 32-bit number"
1254   "Invalid unsigned 32-bit number"
1255   "Invalid 16-bit number"
1256   "Invalid unsigned 16-bit number"
1257   "Invalid 8-bit number"
1258   "Invalid unsigned 8-bit number"
1259   "Invalid float number"
1260   "Invalid double number"
1261   "Invalid color value"
1262   "Invalid boolean value"
1263   "not found"
1264   "Unable to change to the directory of the project."
1265   "Error in call to %s.\n\n"
1266   "    Parent Panel Handle:  %d\n"
1267   "    UIR File Name:  %s\n"
1268   "    Panel Resource ID:  %d\n"
1269   "    Menu Bar Resource ID:  %d\n"
1270   "    hInstance:  0x%08x,  %s\n"
1271   "\nError Code:  %d\n"
1272   "&Unminimize"
1273   "&All Windows"
1274   "<unnamed window>"
1275   "DOS Compatibility Window"
1276   "Dynamic Memory Information: "
1277   "Total number of block allocated: %d\nTotal number of bytes allocated: %d"
1278   "Block#    Address     Size        Values"
1279   "%6lu    %08lx    %8lu   "
1280   " %02lx"
1281   "Selected Files:"
1282   "File %s is already selected."
1283   "Remove All"
1284   "Directory __Name:"
1285   "%s\nis not a directory."
1286   "__OK"
1287   "__Select"
1288   "__Select All"
1289   "__Remove"
1290   "__Discard"
1291   "__Save"
1292   "__Load"
1293   "__Make Directory"
1294   "__Yes"
1295   "__No"
1296   "__Print..."
1297   "__Properties..."
1298   "__Change Printer..."
1299   "You must be in a valid directory to exit."
1300   "&Save"
1301   "&Add"
1302   "&OK"
1303   "&Select"
1304   "&Load"
1305   "N__etwork..."
1306   "D&one"
1307   "Directory &History:"
1308   "&Remove"
1309   "Re&move All"
1310   "You must select at least one file."
1311   "File extension limited to '%s'"
1312   "Cannot paint because Conform to System Colors is enabled for this panel.\nDo you want to disable it?"
1313   "Pathname exceeds maximum length of %ld characters."
1314   "__All Files (*.*)..."
1315   "__Standard Input/Output"
1316   "Standard I/O"
1317   "This Beta of LabWindows/CVI has expired."
6000   ".*"
6001   ".c"
6002   ".a"
6003   ".o"
6004   ".lib"
6005   ".obj"
6006   ".nidobj"
6007   ".dll"
6008   ".pth"
6200   "Caps"
6201   "Cmd"
6202   "Ctrl"
6203   "Mk"
6204   "Opt"
6205   "Shift"
6206   "vkCtrlAltSysRequest"
6207   "vkBreak"
6208   "vkDown"
6209   "vkEnd"
6210   "vkF1"
6211   "vkF10"
6212   "vkF11"
6213   "vkF12"
6214   "vkF2"
6215   "vkF3"
6216   "vkF4"
6217   "vkF5"
6218   "vkF6"
6219   "vkF7"
6220   "vkF8"
6221   "vkF9"
6222   "vkHome"
6223   "vkInsert"
6224   "vkLeft"
6225   "vkPageDown"
6226   "vkPageUp"
6227   "vkRight"
6228   "vkUp"
6229   "Clear"
6230   "Enter"
6231   "Break"
6232   "Up"
6233   "Down"
6234   "Left"
6235   "Right"
6236   "Ins"
6237   "Del"
6238   "Home"
6239   "End"
6240   "PgUp"
6241   "PgDown"
6242   "Help"
6243   "F1"
6244   "F2"
6245   "F3"
6246   "F4"
6247   "F5"
6248   "F6"
6249   "F7"
6250   "F8"
6251   "F9"
6252   "F10"
6253   "F11"
6254   "F12"
6255   "Ctrl-Alt-SysRequest"
6256   "Esc"
6257   "Tab"
6258   "Del"
6259   "BkSp"
6260   "\021"
6261   "M+"
6262   "Ctrl+"
6263   "Shift+"
6264   "Caps+"
6265   "Left"
6266   "Center"
6267   "Right"
6268   "Top Left"
6269   "Top Center"
6270   "Top Right"
6271   "Center Left"
6272   "Center Center"
6273   "Center Right"
6274   "Bottom Left"
6275   "Bottom Center"
6276   "Bottom Right"
6277   "Cancel"
6278   "Si__ze:"
6279   "__Font:"
6280   "__Justification:"
6281   "__Text Color:"
6282   "__Bold"
6283   "__Italic"
6284   "__Underline"
6285   "__Strikeout"
6286   "int"
6287   "unsigned int"
6288   "double"
6289   "float"
6290   "short int"
6291   "unsigned short int"
6292   "char"
6293   "unsigned char"
6294   "Select Color"
6295   "Red"
6296   "Blue"
6297   "Green"
6298   "Sort Table Cells"
6299   "Orientation"
6300   "Sort by __Ascending Row Values"
6301   "Sort by __Descending Row Values"
6302   "Sort by Ascending __Column Values"
6303   "Sort by Descending Column __Values"
6304   "Key"
6305   "__Key Row:"
6306   "__Key Column:"
6307   "Find in Table Cells"
6308   "Goto Table Cell"
6309   "Destination"
6310   "__Row:"
6311   "__Column:"
6312   "Out of Range"
6313   "Min Value: "
6314   "Max Value: "
6315   "Default Value: "
6316   "Data Type: "
6317   "Row: "
6318   "Column: "
6319   "Ring Select Popup"
8000   "Aborted load of library \"%s\"."
8001   "Aborted load of object module \"%s\"."
8002   "Aborted load of member \"%s\" from library \"%s\"."
8003   "Unexpected EOF."
8004   "Bad OMF record at position %d: OMF record type %s."
8005   "Bad name: OMF record position %d: OMF record type %s."
8006   "Only object modules produced by WATCOM C 386 fully supported."
8007   "Bad checksum: OMF record position %d: OMF record type %s."
8008   "Bad location code: OMF record position %d: OMF record type %s."
8009   "Bad method: OMF record position %d: OMF record type %s."
8010   "Object module contains unsupported FAR pointers."
8011   "Absolute segments not supported: segment name \"%s\"."
8012   "Private segments not supported: segment name \"%s\"."
8013   "Unsupported segment combination type %d: segment name \"%s\"."
8014   "Unknown or unsupported OMF record at position %d: OMF record type %s."
8015   "Segment must be USE32: segment name \"%s\"."
8016   "Segment must be of class \"CODE\", \"DATA\", \"BSS\", or \"STACK\": segment name \"%s\"."
8017   "Conflicting GRPDEF's: group name \"%s\"."
8018   "Read error."
8019   "Assertion failure in file \"%s\" at line %d."
8020   "COFF Name too long."
8021   "Bad COFF Library header."
8022   "Bad COFF Library member header."
8023   "Too many nested '#pragma DisableProtoChecking'."
8024   "Missing matching '#pragma DisableProtoChecking'"
8025   "Compile Error At Unknown Coordinates:\n%s"
8026   "Bad library format encountered while reading external module: '%s'."
8027   "Bad magic number encountered while reading external module: '%s'."
8028   "Bad relocation record encountered while reading external module: '%s'."
8029   "Bad BSS section encountered while reading external module: '%s'."
8030   "Elf library is out of date."
8031   "Error in Elf library encountered while reading external module: '%s'."
8032   "Cannot link variable '%s' referenced in '%s' to import library '%s' without '%s' keyword in declaration."
8033   "Symbol '%s' defined in modules '%s' and '%s'.  " "In Borland mode, multiple modules must not contain " "uninitialized definitions of the same global variable.  " "Borland creates a separate variable for each definition.  " "LabWindows/CVI and other linkers resolve all definitions to the same variable.  " "If you want separate variables, use different names or the \"static\" keyword.  " "If you want one variable, change all definitions except one to \"extern\" declarations."
8034   "Bad header encountered while reading external module: '%s'."
8035   "Insufficient user data memory for project."
8036   "Multiply defined symbol '%s' in modules '%s' and '%s'."
8037   "Failed to open external module."
8038   "No data section found for external module: '%s'."
8039   "No data relocation section found for external module: '%s'."
8040   "No symbol table found for external module: '%s'."
8041   "No string table found for external module: '%s'."
8042   "No text section found for external module: '%s'."
8043   "No text relocation section found for external module: '%s'."
8044   "%d undefined symbols: %s"
8045   "Link Error: %s"
8046   "Windows unable to load DLL \"%s\"."
8047   "Windows could not find the DLL \"%s\" or one\nof the DLLs which is statically linked to it."
8048   "Could not find the function or variable" "\n'%s' in the DLL \"%s\". Make sure" "\nthat the most recent versions of both the import library" "\nand the DLL are being used and are in their proper" "\ndirectories."
8049   "DLL Init Error: %s"
9000   "Illegal separator character or illegal position of separator character."
9001   "Missing format string integer."
9002   "Number of arguments exceed the maximum supported."
9003   "Format string integer is too big."
9004   "Repeat value not valid with s/l format specifiers."
9005   "l format specifier not valid in Fmt/FmtOut/FmtFile."
9006   "Missing right bracket (])."
9007   "b modifier must precede o modifier."
9008   "Invalid size for an integer."
9009   "d modifier not valid in Fmt/FmtOut/FmtFile."
9010   "Byte ordering is invalid."
9011   "z modifier only valid if rep is present."
9012   "Unknown modifier."
9013   "Unexpected end of format string."
9014   "c modifier valid only with l format specifier."
9015   "w modifier not valid with l format specifier."
9016   "Invalid size for a real."
9017   "Unknown specifier."
9018   "h modifier is only valid with d, i, n, o, u, and x specifiers."
9019   "l modifier is only valid with e, f, g, d, i, n, o, u, and x specifiers."
9020   "L modifier is only valid with e, f, and g specifiers."
9021   "l modifier is only valid with d, i, n, o, u, and x specifiers."
9022   "# flag is valid only with o, x, e, f, and g specifiers."
9023   "0 flag is not valid with c, s, p, and n modifiers."
9024   "Reference parameter expected."
9025   "Value parameter expected."
9026   "Uninitialized string."
9027   "Parameter type incompatible with format specifier."
9028   "Too many parameters."
9029   "Not enough parameters or invalid parameter type."
9030   "Simple/Array conflict with format specifier."
9031   "Attempt to write beyond end of array."
9032   "Attempt to write beyond end of string."
9033   "Parameter matched to '*' must be an integer."
9034   "Attempt to read beyond end of array."
9035   "Attempt to read beyond end of string."
9036   "'z' modifier required to match string parameter."
9037   "Specified width is too small to read the number."
9038   "Valid integer not found."
9039   "Overflow occurred during the conversion of the int. The absolute value is too big for the size."
9040   "Overflow occurred during the conversion of the int. The signed value is too big for the size."
9041   "Overflow occurred during the conversion of the float. The number is too big for type float."
9042   "Maximum field width has been exceeded."
9043   "Negative field width specified."
9044   "Valid floating point number not found."
9045   "Uninitialized pointer."
9046   "Pointer is invalid."
9047   "Pointer points to freed memory."
9048   "Null pointer."
9049   "Illegal value matched to asterisk."
9050   "Error at or near character %d in the format string."
9051   "Error at or near character %d in the format string: %s"
9052   "Parameter type mismatch; expecting %s%s but found %s%s."
9053   "Out of memory for user protection information."
9100   "In conditional breakpoint expression"
9101   "Missing return value."
9102   "Invalid argument type: found '%t', expected '%t'."
9103   "Missing argument to variable argument function."
9104   "Too many arguments to variable argument function."
9105   "Pointer arithmetic involving invalid pointer."
9106   "Pointer arithmetic involving null pointer."
9107   "Pointer arithmetic involving uninitialized pointer."
9108   "Pointer arithmetic involving address of nonarray object."
9109   "Pointer arithmetic involving pointer to freed memory."
9110   "Out-of-bounds pointer arithmetic: " "%u bytes (%u elements) past end of array."
9111   "Out-of-bounds pointer arithmetic: " "%u bytes (%u elements) before start of array."
9112   "Pointer arithmetic involving pointer to a function."
9113   "Array index (%u) too large (maximum: %u)."
9114   "Negative array index (%d)."
9115   "Assignment of invalid pointer value."
9116   "Assignment of uninitialized pointer value."
9117   "Assignment of out-of-bounds pointer: %u bytes past end of array."
9118   "Assignment of out-of-bounds pointer: %u bytes before start of array."
9119   "Assignment of pointer to freed memory."
9120   "Dereference of invalid pointer expression."
9121   "Dereference of null pointer."
9122   "Dereference of uninitialized pointer."
9123   "(Dereference of ignored pointer [%x].)"
9124   "Dereference of pointer to freed memory."
9125   "Dereference of out-of-bounds pointer: " "%u bytes (%u elements) past end of array."
9126   "Dereference of out-of-bounds pointer: " "%u bytes (%u elements) before start of array."
9127   "Dereference of unaligned pointer."
9128   "Dereference of function pointer used as data."
9129   "Dereference of data pointer used as a function."
9130   "Dereference of a %d byte object where only %d bytes exist."
9131   "Parameter type does not match argument type."
9132   "No room in structure for member '%s'."
9133   "Comparison involving uninitialized pointer."
9134   "Comparison involving invalid pointer."
9135   "Comparison involving null pointer."
9136   "Pointer comparison involving address of nonarray object."
9137   "Comparison of pointers to different objects."
9138   "Comparison of pointers to freed memory."
9139   "Subtraction involving uninitialized pointer."
9140   "Subtraction involving invalid pointer."
9141   "Subtraction involving null pointer."
9142   "Pointer subtraction involving address of nonarray object."
9143   "Subtraction of pointers to different objects."
9144   "Subtraction of pointers to freed memory."
9145   "Not enough space for casting expression to 'pointer to %t'."
9146   "Attempt to free invalid pointer expression."
9147   "Attempt to free uninitialized pointer."
9148   "Attempt to free pointer to freed memory."
9149   "Attempt to free pointer to memory not allocated by malloc() or calloc()."
9150   "Attempt to realloc invalid pointer expression."
9151   "Attempt to realloc uninitialized pointer."
9152   "Attempt to realloc pointer to freed memory."
9153   "Attempt to realloc pointer to memory not allocated by malloc() or calloc()."
9154   "Dynamic memory is corrupt."
9155   "Library function error (%s == %d [0x%x])."
9156   "Function %s: (%s == %d [0x%x])."
9157   "Library function error (%s == %d [0x%x]).\n%s"
9158   "Function %s: (%s == %d [0x%x]).\n%s"
9159   ""
9160   "Illegal argument(s) to library function."
9161   "Null pointer argument to library function."
9162   "Invalid pointer argument to library function."
9163   "Uninitialized pointer argument to library function."
9164   "Pointer to free memory passed to library function."
9165   "Out-of-bounds pointer argument (before start of array)."
9166   "Out-of-bounds pointer argument (past end of array)."
9167   "Scalar argument to library function, expected array."
9168   "Array argument to library function, expected scalar."
9169   "Missing terminating null in string argument."
9170   "Argument too small."
9171   "Argument must be character."
9172   "Number of points is too large for current waveform buffer."
9173   "Non-terminated address list."
9174   "VXI address must be a multiple of 2 for word transfer."
9175   "VXI address must be a multiple of 4 for longword transfer."
9176   "InitVXIlibrary must be called before other VXI functions."
9177   "Argument 2 must be 0, 1 or 2."
9178   "Argument 3 must be 0, 1 or 2."
9179   "Argument 4 must be 0 or 1."
9180   "Assertion error: %s."
9181   "Type of argument does not match type specified."
9182   "Argument must be a power of 2."
9183   "Argument must be an open stream."
9184   "Argument must be a function pointer to the correct type of callback function."
9185   "Array argument too small (%u bytes).  Argument must contain at least %u bytes (%u elements)."
9186   "%s"
9187   "A Run-State Change Callback caused a protection fault."
9188   "Unrecoverable Internal Error.\n" "Program will be aborted."
9189   "An Exception occurred while loading %s."
9190   "An Exception occurred while unloading %s."
12500   "Print"
12501   "Printer Info"
12502   "__Name:"
12503   "Status:"
12504   "Type:"
12505   "Where:"
12506   "Comment:"
12507   "Filter:"
12508   "E__ject page after printing"
12509   "Print to __file"
12510   "Graphics Options"
12511   "__Width:"
12512   "__Height:"
12513   "Hori__zontal offset:"
12514   "__Vertical offset:"
12515   "Force blac__k & white"
12516   "Scale to sc__reen"
12517   "Visible area onl__y"
12518   "Use Bi__tmap Printing"
12519   "Entire paper"
12520   "Specify in millimeters/10"
12521   "Proportional to width"
12522   "Proportional to height"
12523   "Centered"
12524   "Text Options"
12525   "Show line n__umbers"
12526   "Show page num__bers"
12527   "Show __date"
12528   "Show ti__me"
12529   "Show fi__le name"
12530   "Wrap te__xt"
12531   "Sele__ction only"
12532   "T__ab interval:"
12533   "Charact__ers per line:"
12534   "L__ines per page:"
12535   "S__elect Font"
12536   "__Show Graphics Options"
12537   "__Show Text Options"
12538   "Idle"
12539   "Busy"
12540   "Door open"
12541   "Error"
12542   "Initializing"
12543   "Input/Output cctive"
12544   "Manual feed"
12545   "Out of toner"
12546   "Not available"
12547   "Offline"
12548   "Out of memory"
12549   "Bin Full"
12550   "Cannot print current page"
12551   "Paper jam"
12552   "Out of paper"
12553   "Paper problem"
12554   "Paused"
12555   "Deleting job"
12556   "Printing"
12557   "Processing"
12558   "Low toner"
12559   "User intervention required"
12560   "document(s) waiting"
12561   "Waiting"
12562   "Warming up"
12563   "Orientation"
12564   "__Portrait"
12565   "__Landscape"
12566   "Resolution"
12567   "__Horizontal:"
12568   "__Vertical:"
12569   "__Copies:"
12570   "Print in __grayscale"
12571   "Print Output File"
12572   "Print Output File for %s"
12573   "Printing %s"
12574   "Page"
12575   "A local variable was referenced before being initialized."
12576   "Local '%s' was referenced before being initialized."
12577   "Break"
12578   "Continue"
12579   "Quit"
12580   "Unable to launch CVI environment."
12581   "More..."
12582   "Decimal"
12583   "Hex"
12584   "Octal"
12585   "Binary"
13600   "PM"
13601   "AM"
13602   "%m/%d/%y"
13603   "%H:%M:%S"
13604   "%b %d %H:%M:%S %Y"
13605   "Sun"
13606   "Mon"
13607   "Tue"
13608   "Wed"
13609   "Thu"
13610   "Fri"
13611   "Sat"
13612   "Sunday"
13613   "Monday"
13614   "Tuesday"
13615   "Wednesday"
13616   "Thursday"
13617   "Friday"
13618   "Saturday"
13619   "Jan"
13620   "Feb"
13621   "Mar"
13622   "Apr"
13623   "May"
13624   "Jun"
13625   "Jul"
13626   "Aug"
13627   "Sep"
13628   "Oct"
13629   "Nov"
13630   "Dec"
13631   "January"
13632   "February"
13633   "March"
13634   "April"
13635   "May"
13636   "June"
13637   "July"
13638   "August"
13639   "September"
13640   "October"
13641   "November"
13642   "December"
13643   ":(1986)040102+0:110102-0:(1967)040102-0:110102-0"
13644   "Usage: cvirt5 appname [argfile]\n" "  where\n" "    -  appname is the full pathname of the application\n" "    -  argfile is name of the file (without the path) containing the\n" "       command line arguments for the application.\n" "    -  argfile is in the same directory as the application.\n" "    -  the first three characters of argfile are \"CVI\",\n" "       followed by the arguments."
13645   "%s is not a valid LabWindows/CVI Executable."
13646   "Could not open"
13647   "Error Reading File"
13648   "Could not open command line arguments file"
13649   "This application executable was created by LabWindows/CVI\n" "version %s, which is not compatible with version %s of the\n" "LabWindows/CVI Run-Time Engine.  You need a more recent\n" "version of the Run-Time Engine to run this application."
13650   "Too many processes are currently running."
13651   "Could not run executable."
13652   "Execute permission denied for HyperHelp"
13653   "The path or one of the directories names is too long."
13654   "The HHPATH environment variable does not contain a valid path."
13655   "HyperHelp executable '%s' not found.\nSet the \"HHPATH\" environment variable to the correct path."
14450   "Unknown error number"
14500   "No error"
14501   "Invalid parameter"
14502   "Invalid display"
14503   "Invalid window"
14504   "Invalid property"
14505   "Invalid property type"
14506   "Unable to connect to X server"
14507   "Too many X server connections"
14508   "Duplicate property name"
14509   "Duplicate property type name"
14510   "Property is used by an active callback"
14511   "Property type is used by an active property"
14512   "Property type mismatch"
14513   "Property unit mismatch"
14514   "Invalid index"
14515   "Property size mismatch"
14516   "Arithmetic overflow"
14517   "Function is not a valid callback"
14518   "Property not found on window"
14519   "Insufficient memory"
14520   "General X error (cause unknown)"
14521   "Connection to X server broken"
14550   "No Error"
14551   "Unable to register service"
14552   "Unable to establish connection"
14553   "Existing server"
14554   "Failed to connect"
14555   "Server not registered"
14556   "Too many connections"
14557   "Read failed"
14558   "Write failed"
14559   "Invalid parameter"
14560   "Insufficient memory"
14561   "Timeout error"
14562   "No connection established"
14563   "General IO error"
14564   "Connection closed"
14565   "Unable to load Winsock DLL"
14566   "Incorrect Winsock DLL version"
14567   "Network subsystem not ready"
14568   "Connections still open"
14569   "Disconnect pending"
14570   "Information not available"
14600   "No error"
14601   "Unable to register service"
14602   "Existing server"
14603   "Failed to connect"
14604   "Server not registered"
14605   "Too many conversations"
14606   "Invalid parameter"
14607   "Insufficient memory"
14608   "No connection established"
14609   "Not in thread in which server was registered"
14610   "Not in thread in which client connection was established"
14611   "Advise ACK time out"
14612   "DDE_FBUSY flag is set"
14613   "XTYP_REQUEST ACK time out"
14614   "DdeInitialize has not been called"
14615   "Server and client handle misuse"
14616   "Execute ACK time out"
14617   "Data formats mismatched"
14618   "Memory low"
14619   "Memory allocation failed"
14620   "Transaction failed"
14621   "Conversation not established"
14622   "Poke ACK time out"
14623   "Internal postmessage failed"
14624   "Server died before completion"
14625   "System internal error"
14626   "Unadvise ACK time out"
14627   "Transaction ID is invalid"
14650   "No error"
14651   "Unknown system error"
14652   "Invalid port number"
14653   "Port not open"
14654   "Unknown I/O error"
14655   "Unexpected internal error"
14656   "No port found"
14657   "Cannot open port"
14658   "Out of memory"
14659   "Unable to allocate system resources"
14660   "Invalid parameter"
14661   "Invalid baud rate"
14662   "Invalid parity"
14663   "Invalid number of data bits"
14664   "Invalid number of stop bits"
14665   "Bad file handle"
14666   "File I/O error"
14667   "Invalid count - must be 0 or greater"
14668   "Invalid interrupt level"
14669   "I/O operation timed out"
14670   "Break time must be a positive value"
14671   "Input queue size must be 0 or greater"
14672   "Output queue size must be 0 or greater"
14673   "General I/O error"
14674   "Buffer parameter is NULL"
14675   "Package sent but no ACK received"
14676   "Packet not sent within retry limit"
14677   "Packet not received within retry limit"
14678   "Unexpected EOT received"
14679   "Unable to read packet number"
14680   "Inconsistent packet number"
14681   "Unable to read packet data"
14682   "Unable to read checksum"
14683   "Checksum error"
14684   "Packet size greater than input queue size"
14685   "File open error"
14686   "File read error"
14687   "Did not receive initial NAK"
14688   "Did not receive ACK after EOT was sent"
14689   "File write error"
14690   "Did not receive SOH or EOT when expected"
14691   "Transfer was cancelled because CAN was received"
14692   "Invalid start delay"
14693   "Invalid maximum number of retries"
14694   "Invalid wait period"
14695   "Invalid packet size"
14696   "Unable to read CRC"
14697   "CRC error"
14698   "A NULL pointer was passed when a non-NULL pointer was expected"
14800   "No such file or directory"
14801   "General I/O failure"
14802   "Insufficient memory"
14803   "Access denied"
14804   "File exists"
14805   "Invalid argument"
14806   "Too many open files"
14807   "No space left on device"
14808   "Domain error"
14809   "Range error"
14810   "File name too long"
14811   "Bad file handle"
14812   "No error"
14850   "No error"
14851   "File not found"
14852   "General I/O failure"
14853   "Bad file handle"
14854   "Insufficient memory"
14855   "File exists"
14856   "Access denied"
14857   "Invalid argument"
14858   "Too many open files"
14859   "Disk is full"
14860   "File name too long"
14900   "Invalid status code."
14901   "Invalid handle."
14902   "Invalid parameter."
14903   "Invalid attribute ID."
14904   "Invalid event ID."
14905   "Out of memory."
14906   "The time limit expired."
14907   "Another callback is registered for this event (you can only register one callback per event)."
14908   "Invalid callback ID."
14909   "Invalid thread ID."
14910   "System error occurred."
14911   "This thread or another thread is currently reading from the thread safe queue."
14912   "This thread or another thread is currently writing to the thread safe queue."
14913   "The thread safe queue is full."
14914   "The thread safe queue is empty."
14915   "You read more data than was available in the thread safe queue."
14916   "You wrote more data than space was available in the thread safe queue (memory outside the queue might have been overwritten, putting your system in an unstable state)."
14917   "The thread safe queue is configured to automatically flush when full.  You cannot write more items to it than it can hold."
14918   "This type of access to the queue is not permitted."
14919   "The thread currently holding the write pointer cannot resize the thread safe queue."
14920   "You must release the pointer from the same thread that got it."
14921   "You cannot set this attribute while threads exist in the thread pool."
14922   "Invalid thread priority."
14923   "Invalid function ID."
14924   "The function ID you passed is owned by the thread pool.  This operation can only be performed on a function ID that you own."
14925   "The function attribute is not available at this time."
14926   "You can only perform this operation on a thread pool thread."
14927   "You cannot perform this operation on the default thread pool."
14928   "You cannot reduce the maximum number of threads in the thread pool."
14929   "You must release the thread safe variable from the same thread that got it."
14930   "You must release the lock from the same thread that got it."
14931   "You cannot discard a lock that is still owned."
14932   "You must get the lock before releasing it."
14933   "Invalid status code."
