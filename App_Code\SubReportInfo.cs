﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class SubReportInfo : ISerializable
{
    public int SubReportId = 0;
	public string SubReportName = null;
    public List<string> DimensionMembers = new List<string>();
    public List<SessionInfo> AttachedSessions = new List<SessionInfo>();
    public List<string> DeviceFilters = new List<string>();

	// Default constructor.
    public SubReportInfo() { }

	// Deserialization constructor.
    public SubReportInfo(SerializationInfo info, StreamingContext context)
	{
        SubReportId = (int)info.GetValue("SubReportId", typeof(int));
        SubReportName = (string)info.GetValue("SubReportName", typeof(string));
        DimensionMembers = (List<string>)info.GetValue("DimensionMembers", typeof(List<string>));
        AttachedSessions = (List<SessionInfo>)info.GetValue("AttachedSessions", typeof(List<SessionInfo>));
        DeviceFilters = (List<string>)info.GetValue("DeviceFilters", typeof(List<string>));
    }

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
        info.AddValue("SubReportId", SubReportId);
        info.AddValue("SubReportName", SubReportName);
        info.AddValue("DimensionMembers", DimensionMembers);
        info.AddValue("AttachedSessions", AttachedSessions);
        info.AddValue("DeviceFilters", DeviceFilters);
    }
}
