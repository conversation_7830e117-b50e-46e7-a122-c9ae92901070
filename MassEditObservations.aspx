﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="MassEditObservations.aspx.cs" Inherits="MassEditObservations" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">
    <script type="text/javascript">
        var activeTextBox = '';
        var tempScrollTop = $(window).scrollTop();
        var tempScrollLeft = $(window).scrollLeft();

        function colorSolutionStateList(dropdownCntrl) {
            var selVal = dropdownCntrl.val();
            if (selVal == 'pending') {
                dropdownCntrl.css('background-color', 'yellow');
            }
            else if (selVal == 'complete') {
                dropdownCntrl.css('background-color', 'lightgreen');
            }
            else if (selVal == 'skip') {
                dropdownCntrl.css('background-color', 'lightblue');
            } else {
                dropdownCntrl.css('background-color', '');
            }
        }
        function closeNotes() {
            var newText = $('#notesEditBox').val();
            $(activeTextBox).val(newText);
            $(activeTextBox).attr('title', newText);
            $('#notesPopup').hide();
            $('#modal').hide();
            $(window).scrollTop(tempScrollTop);
            $(window).scrollLeft(tempScrollLeft);
        }
        function performSave() {
            if (confirm('SAVE all changes?')) {
                $('#dataDiv').hide();
                $('#savingDiv').show();
                $('#<%= serverSaveBtn.ClientID %>').click();
            }
        }
        $(function () {
            //color solution state lists
            $.each($('.solState'), function (index, item) {
                colorSolutionStateList($(item));
            });
            $('.solState').change(function () { colorSolutionStateList($(this)); });

            //notes popup
            $('.notesField').click(function () {
                tempScrollTop = $(window).scrollTop();
                tempScrollLeft = $(window).scrollLeft();
                activeTextBox = this;
                $('#modal').show();
                $('#notesPopup').show();
                $('#notesEditBox').val($(this).val()).focus();
            });
            $('#notesEditCloseBtn').click(closeNotes);

            //add hover over text for drop down lists
            $('.dropList > option').each(function (index, item) {
                $(item).attr('title', $(item).text());
            });

            $('#dataDiv').show();
            $('#savingDiv').hide();
        });
    </script>
    

    <asp:panel id="DefaultPanel" runat="server">

    <div id="modal" style="display:none;position: absolute;top: 0;left: 0;width: 100%;height: 100%;z-index: 10;background-color: rgba(0,0,0,0.5);"></div>

    <div id="notesPopup" style="display:none;width:420px;height:340px;position:absolute;top:0px;bottom:0px;left:0px;right:0px;margin:auto;padding:30px;background-color:#e2e2e2;z-index:20;">
        <textarea id="notesEditBox" rows="20" cols="30" style="width:400px;height:300px;"></textarea>
        <div id="notesEditCloseBtn" style="padding: 10px 0;cursor:pointer;background-color:#2388d8;border-radius:8px; width:150px;text-align:center;margin-top: 10px;color: #fff;"">Close</div>
    </div>

    <table width="100%" border="0" cellpadding="0" cellspacing="15">
	    <tr>
		    <td>
			    <table width="100%" border="0" cellpadding="0" cellspacing="0">
				    <tr>
					    <td class="widgetTitle">Reliability Observations</td>
					    <td class="widgetTop" style="width:50%;">&nbsp;</td>
					    <td class="widgetTop" style="text-align:right;">&nbsp;</td>
				    </tr>
			    </table>				
			    <div class="widget">
				    <div id="ErrorDiv" runat="server" visible="false" style="margin:10px 14px 0px 14px; border:solid 1px #ff0000; padding:10px 10px 10px 14px;">
					    <asp:label id="ErrorMessage" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				    </div>
				    <br />

                    <div id="savingDiv" style="margin:20px; border:solid 5px #2388d8; padding:20px; border-radius:6px; display:none;">
                        <h1 style="float:left;width:105px;">Saving...</h1> &nbsp; 
                        <div style="float:left;width:100px;margin-top:5px;"><img src="images/ajax-loader.gif" alt="" /></div>
                        <div style="clear:both;"></div>
                        <h2>Please be patient, the saving process can take several minutes depending on the number of observations in the set.</h2>
                        <h2>The page will redirect automatically when saving is complete.</h2>
                    </div>

                    <div id="dataDiv">
				        <table width="100%" border="1" cellpadding="0" cellspacing="0">
                            <asp:repeater id="obsRepeater" runat="server" onitemdatabound="obsRepeater_ItemDataBound">
                                <headertemplate>
                                    <tr>
                                        <%--<td class="rowHeading" style="padding:0px 5px;">Id</td>--%>
                                        <td class="rowHeading" style="padding:0px 5px;">Date</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Session</td>
							            <td class="rowHeading" style="padding:0px 5px;">Cell</td>
							            <td class="rowHeading" style="padding:0px 5px;">Tran No</td>
							            <%--<td class="rowHeading" style="padding:0px 5px;">Device Type</td>--%>
							            <td class="rowHeading" style="padding:0px 5px;">Device</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Observation</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Failure Type</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Investigation Area</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Triage Name</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Module Type</td>
							            <td class="rowHeading" style="padding:0px 5px;">Discipline</td>
                                        <td class="rowHeading" style="padding:0px 5px;" title="1: To Do">1: To Do</td>
                                        <td class="rowHeading" style="padding:0px 5px;" title="2: In Dev">2: In Dev</td>
							            <td class="rowHeading" style="padding:0px 5px;" title="3: Ready Build">3: Ready Build</td>
							            <td class="rowHeading" style="padding:0px 5px;" title="4: Ready QA">4: Ready QA</td>
							            <td class="rowHeading" style="padding:0px 5px;" title="5: Blocked">5: Blocked</td>
							            <td class="rowHeading" style="padding:0px 5px;" title="6: In QA">6: In QA</td>
							            <td class="rowHeading" style="padding:0px 5px;" title="7: Done">7: Done</td>
                                        <td class="rowHeading" style="padding:0px 5px;width:90px;">Projected Solution Date</td>
							            <td class="rowHeading" style="padding:0px 5px;">Notes</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Censor</td>							
                                        <td class="rowHeading" style="padding:0px 5px;">Link</td>
							            <td class="rowHeading" style="padding:0px 5px;">Failure Location</td>
							            <td class="rowHeading" style="padding:0px 5px;">SCR/TI No</td>
                                        <td class="rowHeading" style="padding:0px 5px;">Owner</td>
							            <td class="rowHeading" style="padding:0px 5px;">Operator</td>
					                </tr>
                                </headertemplate>
                                <itemtemplate>
                                    <tr>
                                        <%--<td>
                                            <a href='<%# "EditObservation.aspx?o=" + DataBinder.Eval(Container.DataItem, "ObservationId") %>' target="_blank"><%# DataBinder.Eval(Container.DataItem, "ObservationId") %></a>
							            </td>--%>
                                        <td>
                                            <asp:textbox id="tranDate" runat="server" width="115" enabled="false" text='<%# DataFormatter.FormatDate(Container.DataItem, "TranDate", "MM/dd/yy hh:mm tt", "")%>' tooltip='<%# DataFormatter.FormatDate(Container.DataItem, "TranDate", "MM/dd/yy hh:mm tt", "")%>'></asp:textbox>
                                            <asp:hiddenfield id="obsId" runat="server" value='<%# DataBinder.Eval(Container.DataItem, "ObservationId") %>' />
                                            <asp:hiddenfield id="tranId" runat="server" value='<%# DataBinder.Eval(Container.DataItem, "TranId") %>' />
							            </td>
						                <td>
                                            <asp:textbox id="sessionName" runat="server" width="140" enabled="false" text='<%# DataBinder.Eval(Container.DataItem, "SessionName") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "SessionName") %>'></asp:textbox>
							            </td>
                                        <td>
                                            <asp:textbox id="cellName" runat="server" width="140" enabled="false" text='<%# DataBinder.Eval(Container.DataItem, "CellName") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "CellName") %>'></asp:textbox>
							            </td>
							            <td>
								            <asp:textbox id="transactionNo" runat="server" width="60" enabled="false" text='<%# DataBinder.Eval(Container.DataItem, "RDToolTranId") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "RDToolTranId") %>'></asp:textbox>
							            </td>
							            <%--<td>
								            <asp:textbox id="deviceTypeName" runat="server" width="80" enabled="false" text='<%# DataBinder.Eval(Container.DataItem, "DeviceTypeName") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "DeviceTypeName") %>'></asp:textbox>
							            </td>--%>
							            <td>
								            <asp:textbox id="deviceName" runat="server" width="120" enabled="false" text='<%# DataBinder.Eval(Container.DataItem, "DeviceFullName") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "DeviceFullName") %>'></asp:textbox>
                                        </td>
                                        <td>
								            <asp:textbox id="observation" runat="server" width="120" enabled="false" text='<%# DataBinder.Eval(Container.DataItem, "ObservationText") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "ObservationText") %>'></asp:textbox>
							            </td>
                                        
							            <td>
                                            <asp:dropdownlist id="failureTypeList" width="110" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# failureTypes %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="investigationAreaList" width="100" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# investigationAreas %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
                                        <td>
                                            <asp:dropdownlist cssclass="dropList" id="triageTypeList" runat="server" width="175" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# triageTypes %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="moduleTypeList" width="100" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# moduleTypes %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="disciplineList" width="80" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# disciplines %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
                                        <td>
                                            <asp:dropdownlist id="solutionState1" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="solutionState2" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="solutionState3" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="solutionState4" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="solutionState5" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="solutionState6" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="solutionState7" cssclass="solState" width="65" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# solutionStateValues %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
								            <telerik:RadDatePicker id="projectedDate" class="projected-date" runat="server" style="padding-top:1px;" width="100">        
									            <calendar skin="Default2006" showrowheaders="false"></calendar>       
									            <DatePopupButton Visible="true"></DatePopupButton>
								            </telerik:RadDatePicker>
							            </td>
                                        <td>
								            <asp:textbox id="tranNotes" cssclass="notesField" runat="server" width="100" text='<%# DataBinder.Eval(Container.DataItem, "Notes") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "Notes") %>'></asp:textbox>
							            </td>
                                        <td>
                                            <asp:checkbox id="censorCheck" runat="server" checked='<%# DataBinder.Eval(Container.DataItem, "IsCensored") %>'></asp:checkbox>
							            </td>
                                        <td>
								            <asp:textbox id="linkField" runat="server" width="100" text='<%# DataBinder.Eval(Container.DataItem, "Link") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "Link") %>'></asp:textbox>
							            </td>
                                        <td>
                                            <asp:dropdownlist id="failureLocationList" width="150" runat="server" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:textbox id="scrNumberField" runat="server" width="50" text='<%# DataBinder.Eval(Container.DataItem, "SCRNumber") %>' tooltip='<%# DataBinder.Eval(Container.DataItem, "SCRNumber") %>'></asp:textbox>
							            </td>
                                        <td>
                                            <asp:dropdownlist id="ownerList" runat="server" width="125" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# owners %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>
							            <td>
                                            <asp:dropdownlist id="operatorList" runat="server" width="125" datatextfield="Name" datavaluefield="Code" appenddatabounditems="true" datasource="<%# owners %>">
                                                <asp:listitem text="" value=""></asp:listitem>
                                            </asp:dropdownlist>
							            </td>							        
						            </tr>
                                </itemtemplate>
                            </asp:repeater>
				        </table>
				        <br />
				    
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
					        <tr>	
						        <td style="width:80px;" class="leftPad">
                                    <div class="goButton"><a onclick="performSave();">Save</a></div>
                                    <asp:button style="display:none;" runat="server" id="serverSaveBtn" onclick="SaveButton_Click"></asp:button>
						        </td>
						        <td style="width:120px;"><div class="cancelButton"><asp:linkbutton runat="server" id="CancelButton" onclick="CancelButton_Click" onclientclick="return confirm('Are you sure you want to CANCEL and lose any changes?');" causesvalidation="false">Cancel</asp:linkbutton></div></td>
						        <td>&nbsp;</td>
					        </tr>
				        </table>
                    </div>
                        
                    <div id="ErrorDiv1" runat="server" visible="false" style="margin:10px 14px 10px 14px; border:solid 1px #ff0000; padding:10px 10px 10px 14px;">
					    <asp:label id="ErrorMessage1" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				    </div>
				
				    <br />
				    <br />
			    </div>
		    </td>
	    </tr>
    </table>

    <telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	    <asp:Image id="Image1" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
    </telerik:RadAjaxLoadingPanel>

    </asp:panel>
</asp:Content>
