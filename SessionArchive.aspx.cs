using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class SessionArchive : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetSessionData");
		DataGrid1.DataBind();
	}

	public string FormatByte(string bytes) { return FormatByte(Convert.ToInt32(bytes)); }
	public string FormatByte(int bytes)
	{
		//if (bytes > 1000000)
		//	return ((decimal)bytes / (1024 * 1024)).ToString("#,##0.#") + " GB";
		if (bytes > 1000)
			return ((decimal)bytes / 1024).ToString("#,##0.#") + " MB";
		else
			return bytes.ToString("#,##0") + " KB";
	}
}
