using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Messages : System.Web.UI.Page
{
	public DateTime LastLogin
	{
		get { if (HttpContext.Current.Session["LastMsgLogin"] != null) return (DateTime)HttpContext.Current.Session["LastMsgLogin"]; else return DateTime.MinValue; }
		set { HttpContext.Current.Session["LastMsgLogin"] = value; }
	}

	public bool IsUserAdmin
	{
		get { if (this.ViewState["ia"] != null) return (bool)this.ViewState["ia"]; else return false; }
		set { this.ViewState["ia"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
	{
		string userName = Utility.GetUserName();
		if (!Page.IsPostBack)
		{
			this.IsUserAdmin = Utility.IsUserAdmin();
			NewMessageCell.Visible = this.IsUserAdmin;
			
			this.startDateField.SelectedDate = DateTime.Now.AddDays(-30);
			this.endDateField.SelectedDate = DateTime.Now;

			if (this.LastLogin == DateTime.MinValue)
			{
				object lastLoginObj = SqlHelper.ExecuteScalar("RPT_GetMessagesLastLogin", userName);
				if (lastLoginObj != null && lastLoginObj != DBNull.Value)
				{
					this.LastLogin = (Convert.ToDateTime(lastLoginObj));
				}
			}
			MessagesSource.SelectParameters.Add("startDate", TypeCode.DateTime, startDateField.SelectedDate.ToString());
			MessagesSource.SelectParameters.Add("endDate", TypeCode.DateTime, ((DateTime)endDateField.SelectedDate).AddDays(1).ToString());
			MessagesSource.DataBind();
        }

        if (HasSubscription())
            editSubscriptionsButton.Text = "Edit Subscriptions";
        else
            editSubscriptionsButton.Text = "Subscribe";
		
		//update timestamp for last login now that previous login time is stored in the session.
		SqlHelper.ExecuteNonQuery("RPT_UpdateSubscriptionLastLogin", userName, DateTime.Now);
    }

    private bool HasSubscription()
    {
        DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadUserSubscription", Utility.GetUserName());
        return (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0);
    }

	protected void NewMessage_Click(object sender, EventArgs e)
	{
		Response.Redirect("EditMessage.aspx");
	}

	protected void FilterDates_Click(object sender, EventArgs e)
	{
		MessagesSource.SelectParameters.Clear();
		
		MessagesSource.SelectParameters.Add("startDate", TypeCode.DateTime, startDateField.SelectedDate.ToString());
		MessagesSource.SelectParameters.Add("endDate", TypeCode.DateTime, endDateField.SelectedDate.ToString());
		MessagesSource.DataBind();
	}


}
