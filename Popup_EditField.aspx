<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditField.aspx.cs" Inherits="Popup_EditField" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
    <script type="text/javascript">
        $(function () {
            $('#optionsHelp').click(function () {
                $('#optionExampleDiv').show();
            });
            $('#optionExampleDiv').click(function () {
                $('#optionExampleDiv').hide();
            });

            $('#fieldHelp').click(function () {
                $('#fieldExampleDiv').show();
            });
            $('#fieldExampleDiv').click(function () {
                $('#fieldExampleDiv').hide();
            });
        });
    </script>
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr id="ContentRow" runat="server">
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Field Info</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:12px;">Edit Field</div>
					<div style="padding-left:10px;">
						<table border="0" cellpadding="0" cellspacing="4" width="100%">
							<tr>
								<td class="rowHeading" style="width:150px;">Field Name</td>
								<td>
									<asp:textbox runat="server" width="250" id="fieldNameBox"></asp:textbox>
									<asp:requiredfieldvalidator id="val" runat="server" errormessage="* Required" controltovalidate="fieldNameBox" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
								</td>					
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Field Status</td>
								<td>
									<asp:dropdownlist id="fieldStatusList" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Track Data</td>
								<td>
									<div class="goButton" runat="server" id="TrackDataDiv"><asp:linkbutton runat="server" onclick="TrackDataButton_Click" id="TrackDataButton">Enable</asp:linkbutton></div>
									<div class="minusButton" runat="server" id="DisableTrackingDiv"><asp:linkbutton runat="server" onclientclick="return confirm('Disabling this will cause all related data to be deleted. This action cannot be undone. \r\n\r\nAre you sure you wish to disable?');" onclick="DisableTrackingButton_Click" id="DisableTrackingButton">Disable</asp:linkbutton></div>
									<div runat="server" id="PendingDisableDiv" visible="false"><b>Disabling Tracking</b></div>
								</td>
							</tr>
							<tr id="TrackingPropertiesRow" runat="server">
								<td colspan="2" style="padding-left:14px;padding-bottom:5px;">
									<%--<div style="overflow:auto;height:180px;width:717px;">--%>
										<table cellpadding="0" cellspacing="4" width="700" style="border:solid 2px #2388d8;">
											<tr>
												<td class="rowHeading" style="width:150px;">Track As</td>
												<td>
													<asp:dropdownlist id="trackAsList" enabled="false" runat="server">
														<asp:listitem text="Media Count" value="media"></asp:listitem>
														<asp:listitem text="Setting" value="setting"></asp:listitem>
														<asp:listitem text="Statistic" selected="true" value="stat"></asp:listitem>
													</asp:dropdownlist>
												</td>
											</tr>
											<tr>
												<td class="rowHeading" style="width:150px;">Options</td>
												<td>
													<asp:dropdownlist id="optionsParsingTypeList" enabled="false" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist> &nbsp;<img id="optionsHelp" src="images/question_mark_icon.gif" alt="Click for Description" />
												</td>
											</tr>
											<tr>
												<td class="rowHeading" style="width:150px;">Data</td>
												<td>
													<asp:dropdownlist id="dataParsingTypeList" enabled="false" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist> &nbsp;<img id="fieldHelp" src="images/question_mark_icon.gif" alt="Click for Description" />
												</td>
											</tr>
											<tr>
												<td class="rowHeading" style="width:150px;">Aggregate Type</td>
												<td>
													<asp:dropdownlist id="aggregateTypeList" enabled="false" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:dropdownlist>
												</td>
											</tr>
											<tr>
												<td class="rowHeading" style="width:150px;" valign="top">Event Filters</td>
												<td>
													<asp:listbox id="eventFilterList" visible="false" rows="6" selectionmode="multiple" appenddatabounditems="true" datavaluefield="Code" datatextfield="Name" runat="server"></asp:listbox>
													<asp:label id="selectedEventFiltersList" runat="server"></asp:label>
												</td>
											</tr>
							                <tr>
								                <td class="rowHeading" style="width:150px;">Track Command Number</td>
								                <td><asp:checkbox id="reportCommandNumber" runat="server" /></td>
							                </tr>
										</table>
									<%--</div>--%>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Field Type</td>
								<td><asp:label runat="server" id="fieldTypeNameLabel"></asp:label></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Device Type</td>
								<td><asp:label id="deviceTypeLabel" runat="server"></asp:label></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Entity Id</td>
								<td><asp:label id="entityIdLabel" runat="server"></asp:label>&nbsp;&nbsp;&nbsp;<div style="display:inline; padding-top:3px;" class="goButton"><asp:linkbutton runat="server" onclick="EditEntityButton_Click" id="EditEntityId_Button">Edit</asp:linkbutton></div></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Field is a Sensor</td>
								<td><asp:checkbox id="isSensorBox" runat="server" /></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Bit Mask</td>
								<td><asp:label id="bitMaskLabel" runat="server"></asp:label></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Library Name</td>
								<td><asp:label id="libraryNameLabel" runat="server"></asp:label></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">RD Tool Name</td>
								<td><asp:label id="rdToolNameLabel" runat="server"></asp:label></td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Engineering Field Id</td>
								<td><asp:label id="engineeringFieldIdLabel" runat="server"></asp:label></td>
							</tr>
						</table>
					</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />An event already exists with the specified Id.<br /></div>
				</div>
			</td>
		</tr>
		<tr id="ErrorRow" runat="server" visible="false">
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Error</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<br />
					<div class="title">Unable to identify a Device Type Id.</div>
					<div style="padding-left:14px;">
						Please select a Device Type from the list before adding items.
						<br /><br />
						<div class="cancelButton"><a href="javascript:CloseRadWindow();">Close</a></div>
					</div>
					<br />
				</div>
			</td>
		</tr>
	</table>

    <div id="fieldExampleDiv" style="position:absolute;top:0px;left:20px;display:none;box-shadow:20px;">
        <img src="images/field_tracking.png" alt="field_tracking" />
    </div>
    <div id="optionExampleDiv" style="position:absolute;top:40px;left:150px;display:none;box-shadow:20px;">
        <img src="images/option_tracking.png" alt="option_tracking" />
    </div>

</asp:Content>

