<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="ScheduledReports.aspx.cs" Inherits="ScheduledReports" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
	var openedRow = null;
	function RowClick(sender, eventArgs) {
		curRowClicked = eventArgs.get_itemIndexHierarchical();
		if (openedRow && curRowClicked != openedRow) {
			$find("<%= RadGrid1.MasterTableView.ClientID %>").updateItem(openedRow); //colapse previous row
			$find("<%= RadGrid1.MasterTableView.ClientID %>").editItem(curRowClicked); //expand row
			openedRow = curRowClicked;
		}
		else if (openedRow) {
			$find("<%= RadGrid1.MasterTableView.ClientID %>").updateItem(openedRow); //colapse previous row
			openedRow = null;
		}
		else {
			openedRow = eventArgs.get_itemIndexHierarchical();
			$find("<%= RadGrid1.MasterTableView.ClientID %>").editItem(openedRow); //expand row
		}
	}
    
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= RadGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
</script>

<telerik:radwindowmanager runat="server" id="RadWindowManager" height="630" width="620" modal="true" title="Diebold Reporting" 
	behaviors="Close,Reload" reloadonshow="true" visiblestatusbar="false" destroyonclose="true"></telerik:radwindowmanager>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Scheduled Reports</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				
				<table border="0" cellpadding="0" cellspacing="0" style="padding-left:10px;">
					<tr>
						<td style="width:185px;"><div class="goButton" style="margin:10px 0px;"><a href="EditScheduledReport.aspx">New Scheduled Report</a></div></td>
					</tr>
				</table>
				<telerik:radgrid id="RadGrid1" allowmultirowselection="false" 
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="repeaterItem" />
					<alternatingitemstyle cssclass="repeaterItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings enablerowhoverstyle="true" resizing-allowcolumnresize="true">
						<selecting allowrowselect="true" />
						<clientevents onrowclick="RowClick" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="ScheduledReportId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="Report Name" datafield="ReportName" sortexpression="ReportName" uniquename="ReportName">
								<itemtemplate>
									<div class="expandCollapseButton">&nbsp;&nbsp;&nbsp;&nbsp;<%# DataFormatter.Format(Container.DataItem, "ReportName")%></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Run Time" sortexpression="NextScheduledDate" uniquename="NextScheduledDate">
								<headerstyle width="10%" />
								<itemtemplate><%# DataFormatter.FormatDate(Container.DataItem, "NextScheduledDate", "h:mm tt", "")%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Sun" sortexpression="Sunday" uniquename="Sunday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Sunday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : "" %>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Mon" sortexpression="Monday" uniquename="Monday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Monday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Tues" sortexpression="Tuesday" uniquename="Tuesday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Tuesday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Wed" sortexpression="Wednesday" uniquename="Wednesday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Wednesday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Thur" sortexpression="Thursday" uniquename="Thursday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Thursday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Fri" sortexpression="Friday" uniquename="Friday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Friday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Sat" sortexpression="Saturday" uniquename="Saturday">
								<headerstyle width="6%" />
								<itemtemplate><%# DataFormatter.FormatBool(Container.DataItem, "Saturday", "true", "false", "").Equals("true") ? "<img style='padding-left:6px;' src='images/check.png' alt='Selected' border='0' />" : ""%>&nbsp;</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><a href="<%# string.Format("EditScheduledReport.aspx?{0}={1}&page={2}", DieboldConstants.SCHEDULED_REPORT_ID_KEY, DataFormatter.Format(Container.DataItem, "ScheduledReportId", ""), RadGrid1.CurrentPageIndex) %>">Edit</a></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
						
						<editformsettings editformtype="template">
							<formtemplate>
								<table width="100%" cellpadding="0" cellspacing="0" style="border: solid 10px #9e9995;">
									<tr>
										<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">
											Save to File Path</td>
										<td style="padding-left:10px;">
											<%# DataFormatter.Format(Container.DataItem, "SaveToFilePath")%>
										</td>
									</tr>
                                    <tr>
										<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">
											Email List</td>
										<td style="padding-left:10px;">
											<%# GetEmailList(Container.DataItem)%>
										</td>
									</tr>
									<tr>
										<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">
											Printer Path</td>
										<td style="padding-left:10px;">
											<%# DataFormatter.Format(Container.DataItem, "PrinterPath")%>
										</td>
									</tr>
									<tr>
										<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">
											Number of Copies</td>
										<td style="padding-left:10px;">
											<%# DataFormatter.Format(Container.DataItem, "NumCopies")%>
										</td>
									</tr>
									<tr>
										<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">
											Included Reports</td>
										<td style="padding-left:10px;">
											<%# GetReports(Container.DataItem)%>
										</td>
									</tr>
                                    <tr>
										<td style="width: 150px; border-bottom: solid 1px #ffffff;" class="rowHeading">
											Status</td>
										<td style="padding-left:10px;">
											<%# DataFormatter.Format(Container.DataItem, "StatusMessage")%>
										</td>
									</tr>
								</table>
							</formtemplate>
						</editformsettings>
					</mastertableview>
				</telerik:radgrid>
				
			</div>
		</td>
	</tr>
</table>

</asp:Content>


