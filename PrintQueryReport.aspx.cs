﻿using System;
using System.Data;
using System.Drawing;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Xml;
using Telerik.Web.UI;

public partial class PrintQueryReport : System.Web.UI.Page
{
	protected BaseReport ReportObj = null;

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	public int SnapshotId
	{
		get { if (this.ViewState["s"] != null) { return (int)this.ViewState["s"]; } else { return 0; } }
		set { this.ViewState["s"] = value; }
	}

	public SubReportInfo SubReportInfo
	{
		get { if (this.ViewState["sr"] != null) { return (SubReportInfo)this.ViewState["sr"]; } else { return null; } }
		set { this.ViewState["sr"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
			else
				this.RepInfo = Utility.GetReportInfoFromTransfer();

			if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SNAPSHOT_ID_KEY]))
			{
				this.SnapshotId = Convert.ToInt32(Request.QueryString[DieboldConstants.SNAPSHOT_ID_KEY]);
				LoadSnapshotData();
			}

			if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]))
				this.SubReportInfo = Utility.LoadSubReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.SUBREPORT_ID_KEY]));
			else
				this.SubReportInfo = Utility.GetSubReportInfoFromTransfer();

			if (this.RepInfo == null)
				Response.Redirect("~/reportlibrary.aspx");

			this.ReportObj = ReportHelper.LoadReportObject(this.RepInfo, this.SubReportInfo);

			if (this.SnapshotId == 0)
			{
				this.ReportObj.LoadData();
			}
			else
			{
				LoadSnapshotData();
				this.ReportObj.BuildGridDisplay();
			}

			GridBindingPanel.DataBind();

			//PDF mode, set parameters and render for PDF
			if (!string.IsNullOrEmpty(Request.QueryString["pdf"]))
			{
				this.PrintControlsPanel.Style.Add("display", "none");

				//if request is from the Queue Service, return the PDF immediately
				if (!string.IsNullOrEmpty(Request.QueryString["qs"]))
					PDFButton_Click(null, null);
			}
		}
	}

	private void LoadSnapshotData()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadSnapshot", this.SnapshotId);
		this.RepInfo = null;

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				this.RepInfo = Utility.LoadReportInfo(DataFormatter.getInt32(row, "ReportId"));
				this.RepInfo.ReportData = DataFormatter.getString(row, "ReportData");
				this.RepInfo.CellSetData = DataFormatter.getString(row, "CellSetData");
				this.RepInfo.ChartTitle = DataFormatter.getString(row, "ChartTitle");
				this.RepInfo.FixedStartDate = DataFormatter.getDateTime(row, "StartDate");
				this.RepInfo.FixedEndDate = DataFormatter.getDateTime(row, "EndDate");
				this.RepInfo.ParetoInfo.SplitByCell = DataFormatter.getBool(row, "Pareto_SplitByCell");
				this.RepInfo.ParetoInfo.IncludeTotal = DataFormatter.getBool(row, "Pareto_IncludeTotal");
				this.RepInfo.ParetoInfo.ByStatisticValue = DataFormatter.getBool(row, "Pareto_ByStatValue");
				this.RepInfo.ParetoInfo.MaxColumns = DataFormatter.getInt32(row, "Pareto_MaxColumns");
				this.RepInfo.PRSTInfo.XaxisTypeId = (ReportHelper.XAxisTypeEnum)DataFormatter.getInt32(row, "PRST_XaxisTypeId");
				this.RepInfo.PRSTInfo.IncludeTrendline = DataFormatter.getBool(row, "PRST_IncludeTrendline");
				this.RepInfo.PRSTInfo.IncludeUncensoredSeries = DataFormatter.getBool(row, "PRST_IncludeUncensoredSeries");
				this.RepInfo.PRSTInfo.MTBFSpec = DataFormatter.getDecimal(row, "PRST_MTBFSpec");
				this.RepInfo.PRSTInfo.ProducersRisk = DataFormatter.getDecimal(row, "PRST_ProducersRisk");
				this.RepInfo.PRSTInfo.ConsumersRisk = DataFormatter.getDecimal(row, "PRST_ConsumersRisk");
				this.RepInfo.PRSTInfo.DiscriminationRatio = DataFormatter.getDecimal(row, "PRST_DiscriminationRatio");
				this.RepInfo.ProgressInfo.Normalize = DataFormatter.getBool(row, "Progress_Normalize");
				this.RepInfo.DistributionInfo.LowerSpecLimit = DataFormatter.getDecimal(row, "Distro_LowerSpecLimit");
				this.RepInfo.DistributionInfo.UpperSpecLimit = DataFormatter.getDecimal(row, "Distro_UpperSpecLimit");
				this.RepInfo.DistributionInfo.ShowNormalDistribution = DataFormatter.getBool(row, "Distro_ShowNormalDistribution");
				this.RepInfo.DistributionInfo.ShowHistogram = DataFormatter.getBool(row, "Distro_ShowHistogram");
			}
		}
	}

	protected void PDFButton_Click(object sender, EventArgs e)
	{
		PrintControlsPanel.Visible = false;

		int subReportId = 0;
		if (this.SubReportInfo != null)
			subReportId = this.SubReportInfo.SubReportId;

		string tempFileName = ReportHelper.GeneratePDF(this.RepInfo, this.RepInfo.PrintPageName, "landscape", "", "", false, subReportId);

		string displayName = this.RepInfo.ReportName + "_" + DateTime.Now.Ticks + ".pdf";
		if (this.SubReportInfo != null)
			displayName = this.SubReportInfo.SubReportName + "_" + DateTime.Now.Ticks + ".pdf";

		try
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = "application/pdf";
			Response.AppendHeader("content-disposition", "attachment; filename=" + displayName + ";");

			Response.WriteFile(tempFileName);
			Response.Flush();
		}
		finally
		{
			if (!string.IsNullOrEmpty(tempFileName) && File.Exists(tempFileName))
				File.Delete(tempFileName);
		}

		Response.End();
	}
}
