<%@ Control Language="C#" AutoEventWireup="true" CodeFile="DashItem_SessionSummary.ascx.cs" Inherits="controls_DashItem_SessionSummary" %>
<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="dcwc" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<script language="javascript" type="text/javascript">
	var CYCLE_INTERVAL = 10000;
	var playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %> = 0;
	function toggleSelectedDiv<%= "_" + this.UniqueControlGuid.ToString("N") %>(ctrlId)
	{	
		var hidList = document.getElementById('<%= hidDivList.ClientID %>');
		if (hidList != null)
		{
			for(var i=0;i<hidList.options.length;i++)
			{
				if (hidList.options[i] && hidList.options[i].text && hidList.options[i].value)
				{
					var newPanel = document.getElementById(hidList.options[i].value);
					var newNav = document.getElementById(hidList.options[i].text);

					if(hidList.options[i].text == ctrlId)
					{
						hidList.options[i].selected = true;
						
						if (newPanel)
							newPanel.style.display='';
							
						if (newNav)
							newNav.className='pagingDiv_selected';
					} else {
						if (newPanel)
							newPanel.style.display='none';
							
						if (newNav)
							newNav.className='pagingDiv';
					}
				}
			}
		}
	}
	var aniKill<%= "_" + this.UniqueControlGuid.ToString("N") %> = 0
	function togglePlayPause<%= "_" + this.UniqueControlGuid.ToString("N") %>(value)
	{
		var btn = document.getElementById('<%= "playbutton_" + this.UniqueControlGuid.ToString("N") %>');
		
		if(!value)
		{
			value=(btn.src.indexOf("images/pause.gif")>0)?'play':'pause';
		}
			
		if(playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %> && playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %> != 0)
			clearInterval(playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %>);
		playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %> = 0;
		
		if (value=='pause')
		{
			if(btn)
				btn.src = "images/pause.gif";
		}
		else
		{
			playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %> = setInterval('<%= "toggleNextDiv_" + this.UniqueControlGuid.ToString("N") + "()" %>', CYCLE_INTERVAL);
			if(btn)
				btn.src = "images/play.gif?" + aniKill<%= "_" + this.UniqueControlGuid.ToString("N") %>;

			aniKill<%= "_" + this.UniqueControlGuid.ToString("N") %>=aniKill<%= "_" + this.UniqueControlGuid.ToString("N") %>+1;
		}
	}
	function toggleNextDiv<%= "_" + this.UniqueControlGuid.ToString("N") %>()
	{
		var hidList = document.getElementById('<%= hidDivList.ClientID %>');
		if (hidList != null)
		{
			if (hidList.selectedIndex + 1 < hidList.length)
				toggleSelectedDiv<%= "_" + this.UniqueControlGuid.ToString("N") %>(hidList.options[hidList.selectedIndex + 1].text);
			else if (hidList.options[0].text != null)
				toggleSelectedDiv<%= "_" + this.UniqueControlGuid.ToString("N") %>(hidList.options[0].text);
		} else {
			<%= "togglePlayPause_" + this.UniqueControlGuid.ToString("N") + "('pause')" %>;
		}
	}		
	playInterval<%= "_" + this.UniqueControlGuid.ToString("N") %> = setInterval('<%= "toggleNextDiv_" + this.UniqueControlGuid.ToString("N") + "()" %>', CYCLE_INTERVAL);
</script>

<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="610" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
	<windows><telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="~/promptsessions.aspx" Title="" Height="400" Width="600" ></telerik:radwindow></windows>
</telerik:radwindowmanager>

<telerik:RadAjaxManagerProxy ID="RadAjaxManagerProxy1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="SaveButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="CancelButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="HidEditButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:RadAjaxManagerProxy>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<telerik:radcodeblock id="panelCodeBlock" runat="server">
<asp:panel id="AjaxPanel" runat="server">
	<asp:panel id="DisplayPanel" runat="server">
		<!-- stores names of report swapping divs -->
		<asp:dropdownlist id="hidDivList" runat="server" style="display:none;" ></asp:dropdownlist>
		<div style="padding:5px 0px 0px 5px;">
			<!-- Session Panel Buttons -->
			<asp:placeholder id="HeaderPlaceHolder" runat="server"></asp:placeholder>
			<img onclick='<%= "togglePlayPause_" + this.UniqueControlGuid.ToString("N") + "();" %>' style="vertical-align:middle;" id='<%= "playButton_" + this.UniqueControlGuid.ToString("N") %>' src="images/play.gif" alt="Play" />
		</div>
		<asp:repeater id="SessionsRepeater" runat="server" enableviewstate="true" onitemdatabound="SessionsRepeater_ItemDataBound">
			<itemtemplate>
				<div runat="server" id="SessionDisplayDiv">
					<div class="title" style="padding-left:10px;padding-bottom:2px;"><%# DataFormatter.Format(Container.DataItem, "SessionName") %></div>
					<asp:HiddenField ID="SessionId" runat="server" Value='<%# DataFormatter.Format(Container.DataItem, "SessionId") %>' />
                    <dcwc:Chart ID="chartCntrl" runat="server" Width="380px" Palette="Dundas" Height="50px" BackColor="White" BorderLineStyle="NotSet"
                        RenderType="ImageTag" ImageType="jpeg" MapEnabled="false">
                        <ChartAreas><dcwc:ChartArea Name="Transactions" BackColor="White" BorderColor="26, 59, 105" BorderStyle="NotSet" ShadowOffset="0">
                            <AxisY>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisY>
                            <AxisX>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisX>
                            <AxisX2>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisX2>
                            <AxisY2>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisY2>
                        </dcwc:ChartArea>
                        <dcwc:ChartArea Name="Media" BackColor="White" BorderColor="26, 59, 105" BorderStyle="NotSet" ShadowOffset="0">
                            <AxisY>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisY>
                            <AxisX>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisX>
                            <AxisX2>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisX2>
                            <AxisY2>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisY2>
                        </dcwc:ChartArea>
                        <dcwc:ChartArea Name="Observations" BackColor="White" BorderColor="26, 59, 105" BorderStyle="NotSet" ShadowOffset="0">
                            <AxisY>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisY>
                            <AxisX>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisX>
                            <AxisX2>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisX2>
                            <AxisY2>
                                <MajorGrid LineColor="Silver" Enabled="false" />
                                <MinorGrid LineColor="Silver" Enabled="false" />
                            </AxisY2>
                        </dcwc:ChartArea>
                        </ChartAreas>
                        <Titles>
                            <dcwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></dcwc:Title>
                        </Titles>
                        <Legends>
                            <dcwc:Legend Enabled="False" BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
                            </dcwc:Legend>
                        </Legends>
                        <UI>
                            <Toolbar Enabled="False"></Toolbar>
                            <ContextMenu Enabled="False"></ContextMenu>
                        </UI>
                        <BorderSkin SkinStyle="None" />
                    </dcwc:Chart>
					
					<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
						<tr>
						    <td style="width:30%;padding-bottom:0px;padding-top:0px;" class="title"><center><asp:Label ID="TranCount" runat="server"></asp:Label></center></td>
						    <td style="width:30%;padding-bottom:0px;padding-top:0px;" class="title"><center><asp:Label ID="MediaCount" runat="server"></asp:Label></center></td>
						    <td style="width:30%;padding-bottom:0px;padding-top:0px;" class="title"><center><asp:Label ID="ObsCount" runat="server"></asp:Label></center></td>
						</tr>
						<tr>
						    <td style="width:30%;font-size:10px;"><center><asp:linkbutton ID="TranLink" runat="server" OnCommand="RunRpt" CommandName="Transaction" CommandArgument='<%# DataFormatter.Format(Container.DataItem, "SessionId") %>' CausesValidation="false">Transactions</asp:linkbutton></center></td>
						    <td style="width:30%;font-size:10px;"><center><asp:linkbutton ID="MediaLink" runat="server" OnCommand="RunRpt" CommandName="Media" CommandArgument='<%# DataFormatter.Format(Container.DataItem, "SessionId") %>' CausesValidation="false">Media</asp:linkbutton></center></td>
						    <td style="width:30%;font-size:10px;"><center><asp:linkbutton ID="ObsrvLink" runat="server" OnCommand="RunRpt" CommandName="Observation" CommandArgument='<%# DataFormatter.Format(Container.DataItem, "SessionId") %>' CausesValidation="false">Observations</asp:linkbutton></center></td>
						</tr>
				    </table>
					<br />

					<table style="padding-top:0px;width:100%;" border="0" cellpadding="0" cellspacing="0">
						<tr><td class="rowHeading">Reports:</td></tr>
						<tr>
							<td>
								<div style="height:70px;width:381px; border-top: solid 1px #999999;border-bottom: solid 1px #999999; overflow:auto" >
									<asp:repeater id="ReportRepeater" enableviewstate="true" runat="server" datasource='<%# ((System.Data.DataRowView)Container.DataItem).CreateChildView("Children") %>'>
										<headertemplate><table style="padding-bottom:5px;" cellpadding="0" cellspacing="0"></headertemplate>
										<itemtemplate>
											<tr id="Tr5" runat="server" visible='<%# !string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "ReportName")) %>'>
												<td style="width:250px;padding-left:14px;"><asp:linkbutton runat="server" oncommand="RunButton_Command" commandargument='<%# DataFormatter.Format(Container.DataItem, "ReportId") %>' id="Linkbutton2" causesvalidation="false" tooltip=' <%# DataFormatter.Format(Container.DataItem, "ReportName") %>'><%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "ReportName"), 22, 27)%></asp:linkbutton></td>
											</tr>
										</itemtemplate>
										<footertemplate></table></footertemplate>
									</asp:repeater>
									<asp:label id="NoReportsLabel" visible="false" runat="server" style="padding-left:14px;">There are no saved reports for this session.</asp:label>
								</div>
							</td>
						</tr>
					</table>
				</div>
			</itemtemplate>
		</asp:repeater>
	</asp:panel>

	<asp:panel id="SettingsPanel" runat="server" visible="false" cssclass="body">
		<div class="title" style="padding-left:10px;padding-bottom:2px;padding-top:0px;">Edit Settings</div>
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td class="rowHeading">Report Sessions: <span style="font-weight:normal;">(hold Ctrl to select multiple)</span></td>
				</tr>
				<tr>
					<td style="padding:10px 0px 10px 0px;">
						<asp:listbox id="CustomizeSessionsList" width="350" rows="8" selectionmode="multiple" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" runat="server" cssclass="entryControl"></asp:listbox>
					</td>
				</tr>
				<tr>
					<td>
						<asp:checkbox id="allSessionsCheck" checked="true" runat="server" cssclass="entryControl" text="View All Sessions" />
						<br />
						<span style="font-size:9px;padding-left:18px;"><b>Note:</b> If unchecked, any future sessions will not be displayed.</span>
					</td>
				</tr>
			</table>
			<br />
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td style="width:100px;" class="leftPad"><div class="goButton"><asp:linkbutton style="padding-left:14px;" runat="server" onclick="SaveSettings_Click" id="SaveButton" validationgroup="settingsPanel">Save</asp:linkbutton></div></td>
					<td><div class="cancelButton"><asp:linkbutton runat="server" style="padding-left:14px;" onclick="CancelSettings_Click" id="CancelButton" causesvalidation="false">Cancel</asp:linkbutton></div></td>
				</tr>
			</table>
			<br />
	</asp:panel>
	<asp:button runat="server" onclick="EditSettings_Click" id="HidEditButton" style="display:none;" usesubmitbehavior="false" causesvalidation="false"></asp:button>
</asp:panel>
</telerik:radcodeblock>
