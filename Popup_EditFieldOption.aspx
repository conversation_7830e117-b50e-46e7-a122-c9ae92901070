<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditFieldOption.aspx.cs" Inherits="Popup_EditFieldOption" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr id="ContentRow" runat="server">
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Field Option</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Add/Edit Field Option</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="14" width="100%">
						<tr>
							<td class="rowHeading" style="width:175px;">RD Tool Name</td>
							<td>
								<asp:textbox runat="server" width="150" id="rdToolNameField"></asp:textbox>
								<asp:requiredfieldvalidator id="Requiredfieldvalidator1" runat="server" errormessage="* Required" controltovalidate="rdToolNameField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:175px;">Display Name</td>
							<td>
								<asp:textbox runat="server" width="150" id="displayNameField"></asp:textbox>
								<asp:requiredfieldvalidator id="val1" runat="server" errormessage="* Required" controltovalidate="displayNameField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:175px;">Track Data</td>
							<td>
								<asp:checkbox visible="false" checked="true" id="IncludeTrackingCheck" runat="server" text="Add Tracking" />
								<div class="goButton" runat="server" id="TrackDataDiv"><asp:linkbutton runat="server" onclick="TrackDataButton_Click" id="TrackDataButton">Add Tracking</asp:linkbutton></div>
								<div class="minusButton" visible="false" runat="server" id="DisableTrackingDiv"><asp:linkbutton runat="server" onclientclick="return confirm('Removing tracking from this will cause all related data to be deleted. This action cannot be undone. \r\n\r\nAre you sure you wish to remove tracking?');" onclick="DisableTrackingButton_Click" id="DisableTrackingButton">Remove Tracking</asp:linkbutton></div>
								<div runat="server" id="PendingDisableDiv" visible="false"><b>Disabling Tracking</b></div>
							</td>
						</tr>
							<tr>
								<td class="rowHeading" style="width:175px;">Enable For ManualConfig</td>
								<td><asp:checkbox id="isManualConfig" runat="server" /></td>
							</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><asp:linkbutton causesvalidation="false" runat="server" onclientclick="return confirm('Are you sure you wish to close the window without saving?');" onclick="CancelButton_Click" id="CancelButton">Cancel</asp:linkbutton></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />An event already exists with the specified Id.<br /></div>
				</div>
			</td>
		</tr>
		<tr id="ErrorRow" runat="server" visible="false">
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Error</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<br />
					<div class="title">Unable to identify a Device Type Id.</div>
					<div style="padding-left:14px;">
						Please select a Device Type from the list before adding Event Types.
						<br /><br />
						<div class="cancelButton"><a href="javascript:CloseRadWindow();">Close</a></div>
					</div>
					<br />
				</div>
			</td>
		</tr>
	</table>

</asp:Content>


