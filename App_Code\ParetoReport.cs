using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

/// <summary>
/// Summary description for ParetoReport
/// </summary>
public class ParetoReport : BaseReport
{
    public ParetoReport(ReportInfo repInfo) : base(repInfo, false) { }

    private List<decimal> totals = null;

    public override void InitializeChart(Chart chart, string pageOrControlName)
    {
        base.InitializeChart(chart, pageOrControlName);
        chart.Customize += new CustomizeEventHandler(chart_Customize);
    }

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        string mdxSelect = null;
        string mdxCategory = null;
        string mdxFromWhere = null;
        string timeHier = OLAPHelper.GetTimeHier(repInfo.ParetoInfo.GroupingId);
        DateTime startDate = repInfo.StartDate;
        DateTime endDate = repInfo.EndDate;
        bool splitByDevice = this.repInfo.ParetoInfo.SplitByCell;
        string seriesName = "Pareto Count";
        string orderSeriesName = "Pareto Order";
        string aggregateFunction = "SUM";
        string measureName = null;

        if (repInfo.DimensionName.StartsWith("[Statistic]."))
        {
            if (this.repInfo.ParetoInfo.ByStatisticValue)
                measureName = "[Measures].[Stat Value Cnt]";
            else
                measureName = "[Measures].[Stat Tran Cnt]";
        }
        else if (repInfo.DimensionName.StartsWith("[Event Type]."))
            measureName = "[Measures].[Event Cnt]";
        else if (repInfo.DimensionName.StartsWith("[Setting]."))
            measureName = "[Measures].[Transaction Cnt]";
        else
            measureName = "[Measures].[Observation Cnt]";

        mdxSetup.Append(string.Format("WITH {0}\r\n", OLAPHelper.BuildMdxFilteredTime(repInfo.ParetoInfo.GroupingId, startDate, endDate, "FilteredTime")));

        mdxSetup.Append("MEMBER [Stat Value Cnt] AS ' IIF([Measures].[Stat Tran Count]>0,");
        mdxSetup.Append(BuildFilteredAggregate(timeHier, startDate, endDate));
        mdxSetup.Append(",NULL) ', FORMAT_STRING = \"#,#\" \r\n");

        mdxSetup.Append("MEMBER [Transaction Cnt] AS ' ");
        mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, startDate, endDate, "FilteredTime", "[Measures].[Transaction Count]", aggregateFunction));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");
        mdxSetup.Append("MEMBER [Media Cnt] AS ' ");
        mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, startDate, endDate, "FilteredTime", "[Measures].[Media Count]", aggregateFunction));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");
        mdxSetup.Append("MEMBER [Observation Cnt] AS ' ");
        mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, startDate, endDate, "FilteredTime", "[Measures].[Observation Count]", aggregateFunction));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");
        mdxSetup.Append("MEMBER [Stat Tran Cnt] AS ' ");
        mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, startDate, endDate, "FilteredTime", "[Measures].[Stat Tran Count]"));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");

        mdxSetup.Append(BuildParetoSeries(timeHier, startDate, endDate, seriesName, measureName, repInfo.ProgressInfo.RateTypeId1,
            repInfo.DimensionName, repInfo.DimensionMembers, repInfo.ProgressInfo.WorkLoad1, repInfo.ProgressInfo.Format1, false));

        mdxSetup.Append(BuildParetoSeries(timeHier, startDate, endDate, orderSeriesName, measureName, repInfo.ProgressInfo.RateTypeId1,
            repInfo.DimensionName, repInfo.DimensionMembers, repInfo.ProgressInfo.WorkLoad1, repInfo.ProgressInfo.Format1, true));
        
        mdxSelect = string.Format("SELECT NON EMPTY (FilteredTime, [Measures].[{0}]) on columns, \r\n", seriesName);

        if (splitByDevice)
        {
            string deviceList = OLAPHelper.BuildMdxDeviceTupleFromFilters(repInfo.ReportFilters);
            if (string.IsNullOrEmpty(deviceList))
                deviceList = "[Device].[Type - Device].[Device]";

            if (this.repInfo.ParetoInfo.ByStatisticValue)
                mdxSetup.Append(string.Format("SET FilteredDevices AS 'Filter({0}, SUM(FilteredTime, [Measures].[Stat Tran Count]) > 0)'\r\n", deviceList));
            else
                mdxSetup.Append(string.Format("SET FilteredDevices AS 'Filter({0}, SUM(FilteredTime, {1}) > 0)'\r\n", deviceList, measureName.Replace(" Cnt", " Count")));

            mdxCategory = string.Format("NON EMPTY (Order({0}, [Measures].[{1}], BDESC), FilteredDevices) on rows \r\n",
                OLAPHelper.BuildMdxLevelTuple(false, repInfo.DimensionName, repInfo.DimensionMembers, false), orderSeriesName);
        }
        else
        {
            mdxCategory = string.Format("NON EMPTY Order({0}, [Measures].[{1}], BDESC) on rows \r\n",
                OLAPHelper.BuildMdxLevelTuple(false, repInfo.DimensionName, repInfo.DimensionMembers, false), orderSeriesName);
        }

        mdxFromWhere = string.Format("FROM [Reporting] WHERE ({0})", OLAPHelper.BuildMdxWhereTuples(repInfo.AttachedSessions, repInfo.ReportFilters, splitByDevice, " ", false));

        string mdx = mdxSetup.ToString() + mdxSelect + mdxCategory + mdxFromWhere;

        if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Request.Params["showmdx"])) {
            throw new ApplicationException(mdx);
        }
        ExecuteMdx(mdx);
        BuildTotals();
        BuildGridDisplay();
    }

    private string BuildParetoSeries(string timeHier, DateTime startDate, DateTime endDate, string seriesName, string measureName,
            ReportHelper.RateTypeEnum rateTypeId, string dimensionName,
            List<string> dimensionMembers, decimal workloadAmount, string formatString, bool shouldSumSeries)
    {
        StringBuilder retVal = new StringBuilder();

        if (!string.IsNullOrEmpty(seriesName) && !string.IsNullOrEmpty(measureName) && !string.IsNullOrEmpty(dimensionName))
        {
            string normalizeString = null;
            string rateMeasure = null;

            if (workloadAmount <= 0)
                workloadAmount = 0;

            if (!string.IsNullOrEmpty(formatString))
                formatString = string.Format("FORMAT_STRING = \"{0}\"", formatString);

            switch (rateTypeId)
            {
                case ReportHelper.RateTypeEnum.OCCURRANCES:
                case ReportHelper.RateTypeEnum.BY_STAT_VALUE:
                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
                case ReportHelper.RateTypeEnum.PER_WORKLOAD_MEDIA: // Per Workload Media
                case ReportHelper.RateTypeEnum.INVERSE_PER_MILLION_MEDIA: // Inverse Per Million Media
                case ReportHelper.RateTypeEnum.INVERSE_PER_WORKLOAD_MEDIA: // Inverse Per Workload Media
                    rateMeasure = "[Measures].[Media Cnt]";

                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
                case ReportHelper.RateTypeEnum.PER_WORKLOAD_TRANSACTIONS: // Per Workload Transactions
                case ReportHelper.RateTypeEnum.INVERSE_PER_MILLION_TRANSACTIONS: // Inverse Per Million Transactions
                case ReportHelper.RateTypeEnum.INVERSE_PER_WORKLOAD_TRANSACTIONS: // Inverse Per Workload Transactions
                    rateMeasure = "[Measures].[Transaction Cnt]";

                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                case ReportHelper.RateTypeEnum.INVERSE_PERCENTAGE_OF_MEDIA: // Inverse Percentage of Media
                    rateMeasure = "[Measures].[Media Cnt]";

                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.000\\%\"";
                    else
                        formatString = formatString.Substring(0, formatString.Length - 1) + "\\%\"";
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                case ReportHelper.RateTypeEnum.INVERSE_PERCENTAGE_OF_TRANSACTIONS: // Inverse Percentage of Transactions
                    rateMeasure = "[Measures].[Transaction Cnt]";

                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.000\\%\"";
                    else
                        formatString = formatString.Substring(0, formatString.Length - 1) + "\\%\"";
                    break;
                case ReportHelper.RateTypeEnum.MEDIA_PER_INCIDENT: // Media per Incident
                case ReportHelper.RateTypeEnum.INVERSE_MEDIA_PER_INCIDENT: // Inverse Media per Incident
                    rateMeasure = "[Measures].[Media Cnt]";

                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                case ReportHelper.RateTypeEnum.TRANSACTIONS_PER_INCIDENT: // Transactions per Incident
                case ReportHelper.RateTypeEnum.INVERSE_TRANSACTIONS_PER_INCIDENT: // Inverse Transactions per Incident
                    rateMeasure = "[Measures].[Transaction Cnt]";

                    if (string.IsNullOrEmpty(formatString))
                        formatString = "FORMAT_STRING = \"#,##0.0\"";
                    break;
                default:
                    throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
            }

            // Add the transformed member
            switch (rateTypeId)
            {
                case ReportHelper.RateTypeEnum.OCCURRANCES:
                case ReportHelper.RateTypeEnum.BY_STAT_VALUE:
                    if(shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0, ({1} * 1) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0, ({1} * 1) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
                case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
                    if (shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0, ({1} * (1000000/{2})) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, ({1} * (1000000/{2})) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.INVERSE_PER_MILLION_MEDIA: // Inverse Per Million Media
                case ReportHelper.RateTypeEnum.INVERSE_PER_MILLION_TRANSACTIONS: // Inverse Per Million Transactions
                    if (shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0, (1/({1} * (1000000/{2}))) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, (1/({1} * (1000000/{2}))) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                    if (shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0, ({1} / {2} * 100) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, ({1} / {2} * 100) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.INVERSE_PERCENTAGE_OF_MEDIA: // Inverse Percentage of Media
                case ReportHelper.RateTypeEnum.INVERSE_PERCENTAGE_OF_TRANSACTIONS: // Inverse Percentage of Transactions
                    if (shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0, (1/({1} / {2} * 100)) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, (1/({1} / {2} * 100)) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.MEDIA_PER_INCIDENT: // Media per Incident
                case ReportHelper.RateTypeEnum.TRANSACTIONS_PER_INCIDENT: // Transactions per Incident
                    if(shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0, ({2} / {1}) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, ({2} / {1}) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.INVERSE_MEDIA_PER_INCIDENT: // Inverse Media per Incident
                case ReportHelper.RateTypeEnum.INVERSE_TRANSACTIONS_PER_INCIDENT: // Inverse Transactions per Incident
                    if(shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0, (1/({2} / {1})) {3} ,NULL))', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, (1/({2} / {1})) {3} ,NULL)', {4}\r\n", seriesName, measureName, rateMeasure, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.PER_WORKLOAD_MEDIA: //Per Workload Media
                case ReportHelper.RateTypeEnum.PER_WORKLOAD_TRANSACTIONS: //Per Workload Transactions
                    if (shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0 AND {3}>0, ({1} * ({3}/{2})) {4} ,NULL))', {5}\r\n", seriesName, measureName, rateMeasure, workloadAmount, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0 AND {3}>0, ({1} * ({3}/{2})) {4} ,NULL)', {5}\r\n", seriesName, measureName, rateMeasure, workloadAmount, normalizeString, formatString));
                    break;
                case ReportHelper.RateTypeEnum.INVERSE_PER_WORKLOAD_MEDIA: // Inverse Per Workload Media
                case ReportHelper.RateTypeEnum.INVERSE_PER_WORKLOAD_TRANSACTIONS: // Inverse Per Workload Transactions
                    if (shouldSumSeries)
                        retVal.Append(string.Format("MEMBER [{0}] AS ' SUM(FilteredTime, IIF({1}>0 AND {2}>0 AND {3}>0, (1/({1} * ({3}/{2}))) {4} ,NULL))', {5}\r\n", seriesName, measureName, rateMeasure, workloadAmount, normalizeString, formatString));
                    else
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0 AND {3}>0, (1/({1} * ({3}/{2}))) {4} ,NULL)', {5}\r\n", seriesName, measureName, rateMeasure, workloadAmount, normalizeString, formatString));
                    break;
                default:
                    throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
            }
        }

        return retVal.ToString();
    }

    private string BuildFilteredAggregate(string timeHier, DateTime startDate, DateTime endDate)
    {
        StringBuilder retVal = new StringBuilder();
        string filtedTimeSetName = "FilteredTime";

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
                + "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), "
                + "[Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));
        }

        retVal.Append("SUM([Aggregate Type].[Global Level Function].&[Sum], [Measures].[Stat Value Sum])");

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        retVal.Append(" + ");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MAX((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
                + "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), "
                + "[Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MAX((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MAX((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));
        }

        retVal.Append("MAX([Aggregate Type].[Global Level Function].&[Max], [Measures].[Stat Value Max])");

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        retVal.Append(" + ");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MIN((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
                + "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), "
                + "[Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MIN((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MIN((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));
        }

        retVal.Append("MIN([Aggregate Type].[Global Level Function].&[Min], [Measures].[Stat Value Min])");

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        retVal.Append(" + (");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
                + "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));
        }

        retVal.Append("SUM([Aggregate Type].[Global Level Function].&[AverageOfChildren], [Measures].[Stat Value Sum])");

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        retVal.Append(" / ");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
                + "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss")));
        }

        retVal.Append("SUM([Aggregate Type].[Global Level Function].&[AverageOfChildren], [Measures].[Stat Tran Count])");

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        retVal.Append(")");
        return retVal.ToString();
    }

    private void BuildTotals()
    {
        this.totals = new List<decimal>();

        TupleCollection colTuples = this.cellSet.Axes[1].Set.Tuples;
        HierarchyCollection colHierarchies = this.cellSet.Axes[1].Set.Hierarchies;
        TupleCollection rowTuples = this.cellSet.Axes[0].Set.Tuples;
        HierarchyCollection rowHierarchies = this.cellSet.Axes[0].Set.Hierarchies;

        for (int col = 0; col < colTuples.Count; col++)
        {
            if (repInfo.ParetoInfo.MaxColumns == 0 || col < repInfo.ParetoInfo.MaxColumns)
            {
                decimal curTotal = 0;
                for (int row = 0; row < rowTuples.Count; row++)
                {
                    Cell cell = this.cellSet.Cells[row, col];
                    decimal value = ((cell.Value == null || string.IsNullOrEmpty(cell.Value.ToString())) ? 0 : decimal.Parse(cell.Value.ToString(), System.Globalization.NumberStyles.Any));
                    curTotal += value;
                }
                this.totals.Add(curTotal);
            }
            else
            {
                break;
            }
        }
    }

    public override void BuildGridDisplay()
    {
        if (this.totals == null)
            BuildTotals();
        base.BuildGridDisplay();

        if (repInfo.ParetoInfo.IncludeTotal)
        {
            for (int x = 0; x < this.RowDisplayHeaders.Count; x++)
            {
                List<string> headers = this.RowDisplayHeaders[x];
                if (x == 0)
                    headers.Insert(0, "Total");
                else
                    headers.Insert(0, "");
            }

            List<string> totalsRow = new List<string>();
            for (int x = 0; x < this.totals.Count; x++)
            {
                totalsRow.Add(this.totals[x].ToString("#,##0"));
            }
            this.RowDisplayData.Insert(0, totalsRow);
        }

        while (this.RowDisplayHeaders.Count > 1)
            this.RowDisplayHeaders.RemoveAt(1);
    }

    public override void PopulateChart(Chart chart, bool includeToolTips)
    {
        if (this.totals == null)
            BuildTotals();

        TupleCollection rowTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection colTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[1].Set.Hierarchies;
        double maxY = 0;

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
        {
            if (!priorSeries.ContainsKey(series.Name))
                priorSeries.Add(series.Name, series);
        }

        chart.Series.Clear();

		if (this.repInfo.ParetoInfo.SplitByCell)
        {
			// Series per Cell and Date Grouping
            // StackedGroupName matching Date Grouping
            // Must have a point for each category, cell and date grouping combination
            List<string> categoryList = new List<string>();
            List<string> cellList = new List<string>();
            List<string> legendList = new List<string>();
            Dictionary<string, int> columnLookup = new Dictionary<string, int>();
            Dictionary<string, double> maxLookup = new Dictionary<string, double>();

            for (int col = 0; col < colTuples.Count; col++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
                string catName = colTuple.Members[0].Caption;
                int catCnt = 0;
                string finalCatName = catName;
                string cellName = colTuple.Members[1].Caption;

                string colKey = string.Format("{0}~{1}", finalCatName, cellName);

                if (!cellList.Contains(cellName))
                    cellList.Add(cellName);

                while (columnLookup.ContainsKey(colKey))
                {
                    catCnt++;
                    finalCatName = string.Format("{0} ({1})", catName, catCnt);
                    colKey = string.Format("{0}~{1}", finalCatName, cellName);
                }

                if (!categoryList.Contains(finalCatName))
                {
                    if (repInfo.ParetoInfo.MaxColumns == 0 || categoryList.Count < repInfo.ParetoInfo.MaxColumns)
                        categoryList.Add(finalCatName);
                }

                columnLookup.Add(colKey, col);
            }

            for (int row = 0; row < rowTuples.Count; row++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];
                string dateGroup = rowTuple.Members[0].Caption;

                foreach (string cellName in cellList)
                {
                    Series series = null;
                    string seriesName = cellName;

                    if (priorSeries.ContainsKey(seriesName))
                    {
                        series = priorSeries[seriesName];
                        priorSeries.Remove(series.Name);
                        series.Points.Clear();
                    }
                    else
                    {
                        series = new Series(seriesName);
                        series["DrawingStyle"] = "Cylinder";
                        series.BorderColor = Color.FromArgb(26, 59, 105);
                        series.ShadowOffset = 2;
                        series.Type = SeriesChartType.StackedColumn;
                    }

                    if (string.Compare(dateGroup, "Total") == 0)
                    {
                        if (!repInfo.ParetoInfo.IncludeTotal)
                            continue;
                    }

                    series["StackedGroupName"] = dateGroup;
                    if (legendList.Contains(cellName))
                    {
                        series.Name = string.Format("{0}~{1}", cellName, row);
                        series.ShowInLegend = false;
                    }
                    else
                    {
                        series.ShowInLegend = true;
                        legendList.Add(cellName);
                    }

                    foreach (string category in categoryList)
                    {
                        DataPoint dp = null;
                        string formattedValue = null;
                        string colKey = string.Format("{0}~{1}", category, cellName);
                        string maxKey = string.Format("{0}~{1}", category, dateGroup);
                        if (columnLookup.ContainsKey(colKey))
                        {
                            Cell cell = cellSet.Cells[row, columnLookup[colKey]];
                            object valObj = cell.Value;
                            formattedValue = cell.FormattedValue;

                            if (valObj == null)
                                dp = new DataPoint(0, 0);
                            else
                                dp = new DataPoint(0, double.Parse(cell.Value.ToString()));
                        }
                        else
                        {
                            dp = new DataPoint(0, 0);
                            formattedValue = "0";
                        }

                        if (!maxLookup.ContainsKey(maxKey))
                            maxLookup.Add(maxKey, 0.0);
                        maxLookup[maxKey] += dp.GetValueY(0);

                        dp.AxisLabel = string.Format("{0}", category);
                        if (includeToolTips)
                            dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}\r\n{3}", formattedValue, category, cellName, dateGroup);

                        series.Points.Add(dp);
                    }

                    chart.Series.Add(series);
                }
            }

            maxY = 0.0;
            foreach (double curMax in maxLookup.Values)
            {
                maxY = (maxY > curMax) ? maxY : curMax;
            }
        }
        else
        {
            // Add total series
            if (repInfo.ParetoInfo.IncludeTotal)
            {
                Series series = null;
                string seriesName = "Total";

                if (priorSeries.ContainsKey(seriesName))
                {
                    series = priorSeries[seriesName];
                    priorSeries.Remove(series.Name);
                    series.Points.Clear();
                }
                else
                {
                    series = new Series(seriesName);
                    series["DrawingStyle"] = "Cylinder";
                    series.BorderColor = Color.FromArgb(26, 59, 105);
                    series.ShadowOffset = 2;

                    series.Type = SeriesChartType.Column;
                }

                for (int col = 0; col < colTuples.Count; col++)
                {
                    if (repInfo.ParetoInfo.MaxColumns == 0 || col < repInfo.ParetoInfo.MaxColumns)
                    {
                        Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
                        DataPoint dp = null;
                        dp = new DataPoint(0, (int) this.totals[col]); ;

                        maxY = (maxY > dp.GetValueY(0)) ? maxY : dp.GetValueY(0);
                        dp.AxisLabel = string.Format("{0}", colTuple.Members[0].Caption);

                        if (includeToolTips)
                            dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}", this.totals[col].ToString("#,##0"), colTuple.Members[0].Caption, series.Name);

                        series.Points.Add(dp);
                    }
                    else
                    {
                        break;
                    }
                }
                chart.Series.Add(series);
            }

            // Build chart series data
            for (int row = 0; row < rowTuples.Count; row++)
            {
                Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];

                Series series = null;
                string seriesName = rowTuple.Members[0].Caption;

                if (priorSeries.ContainsKey(seriesName))
                {
                    series = priorSeries[seriesName];
                    priorSeries.Remove(series.Name);
                    series.Points.Clear();
                }
                else
                {
                    series = new Series(seriesName);
                    series["DrawingStyle"] = "Cylinder";
                    series.BorderColor = Color.FromArgb(26, 59, 105);
                    series.ShadowOffset = 2;

                    series.Type = SeriesChartType.Column;
                }

                if (string.Compare(series.Name, "Total") == 0)
                {
                    if (!repInfo.ParetoInfo.IncludeTotal)
                        continue;
                }

                for (int col = 0; col < colTuples.Count; col++)
                {
                    if (repInfo.ParetoInfo.MaxColumns == 0 || col < repInfo.ParetoInfo.MaxColumns)
                    {
                        Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];
                        Cell cell = cellSet.Cells[row, col];

                        object valObj = cell.Value;
                        DataPoint dp = null;
                        if (valObj == null)
                            dp = new DataPoint(0, 0);
                        else
                            dp = new DataPoint(0, double.Parse(cell.Value.ToString())); ;

                        maxY = (maxY > dp.GetValueY(0)) ? maxY : dp.GetValueY(0);
                        dp.AxisLabel = string.Format("{0}", colTuple.Members[0].Caption);

                        if (includeToolTips)
                            dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}", cell.FormattedValue, colTuple.Members[0].Caption, series.Name);

                        series.Points.Add(dp);
                    }
                    else
                    {
                        break;
                    }
                }

                chart.Series.Add(series);
            }
        }

		double minX = 0.0;
		double maxX = 0.0;
		double curXVal = 0.0;
		foreach (Series series in chart.Series)
		{
			foreach (DataPoint point in series.Points)
			{
				curXVal = point.XValue;
				minX = Math.Min(minX, curXVal);
				maxX = Math.Max(maxX, curXVal);
			}
		}

        if (!string.IsNullOrEmpty(repInfo.MinYScale))
            chart.ChartAreas[0].AxisY.Minimum = double.Parse(repInfo.MinYScale, System.Globalization.NumberStyles.Any);
        else
            chart.ChartAreas[0].AxisY.Minimum = 0;

        if (!string.IsNullOrEmpty(repInfo.MaxYScale))
            chart.ChartAreas[0].AxisY.Maximum = double.Parse(repInfo.MaxYScale, System.Globalization.NumberStyles.Any);
        else
            chart.ChartAreas[0].AxisY.Maximum = RoundAxis(maxY, 100);

        chart.ChartAreas[0].AxisX.Interval = 1;
        chart.ChartAreas[0].AxisX.Margin = true;

        ResetAxisMarks(chart.ChartAreas[0].AxisY);

        chart.Titles["Title1"].Text = repInfo.ChartTitle;

        if (string.IsNullOrEmpty(chart.ChartAreas[0].AxisY.Title))
        {
            chart.ChartAreas[0].AxisY.Title = "Rate";
            chart.ChartAreas[0].AxisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
        }

		chart.Annotations.Clear();
		//if Splitting by Device, and Rate is "Per Million", show note on chart
		if (this.repInfo.ParetoInfo.SplitByCell)
		{
			switch (this.repInfo.ProgressInfo.RateTypeId1)
			{
				case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA:
				case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS:
				case ReportHelper.RateTypeEnum.INVERSE_PER_MILLION_MEDIA:
				case ReportHelper.RateTypeEnum.INVERSE_PER_MILLION_TRANSACTIONS:
					Dundas.Charting.WebControl.Axis axisX = chart.ChartAreas[0].AxisX;
					Dundas.Charting.WebControl.Axis axisY = chart.ChartAreas[0].AxisY;
					double yCoor = axisY.Maximum;
					double xCoor = maxX;

					SixSigma.AddTextAnnotation("NOTE: Rates shown are accurate per device per category.  \r\nStacked totals are not representative of overall rates.",
						axisX, axisY, xCoor, yCoor, Color.Red, new Font("Arial", 8, FontStyle.Bold), chart);

					break;
			}
		}
    }

    private void chart_Customize(Chart chart)
    {
        Dictionary<string, Color> colorLookup = new Dictionary<string, Color>();
        foreach (Series series in chart.Series)
        {
            if (series.ShowInLegend && series.Name.IndexOf("~") < 0)
            {
                colorLookup.Add(series.Name, series.Color);
            }
        }

        foreach (Series series in chart.Series)
        {
            if (!series.ShowInLegend && series.Name.IndexOf("~") >= 0)
            {
                string baseName = series.Name.Substring(0, series.Name.IndexOf("~"));
                if (colorLookup.ContainsKey(baseName))
                    series.Color = colorLookup[baseName];
            }
        }
    }


}
