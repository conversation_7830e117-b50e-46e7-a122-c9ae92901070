using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Events : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		this.EntityNavBar.ItemChangedEvent += new CommandEventHandler(EntityNavBar_ItemChangedEvent);
		this.NewEventButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditEventType.aspx?{0}={1}', null); return false;", DieboldConstants.DEVICE_TYPE_KEY, this.EntityNavBar.DeviceTypeId));
		
		EventTypeGrid.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_EventTypeByDeviceType", this.EntityNavBar.DeviceTypeId);
		EventTypeGrid.DataBind();

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				EventTypeGrid.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
	}

	void EntityNavBar_ItemChangedEvent(object sender, CommandEventArgs e)
	{
		EventTypeGrid.CurrentPageIndex = 0;
		this.NewEventButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditEventType.aspx?{0}={1}', null); return false;", DieboldConstants.DEVICE_TYPE_KEY, this.EntityNavBar.DeviceTypeId));
		
		if (e.CommandName.Equals(DieboldConstants.COMMAND_DEVICE_TYPE_CHANGED) && e.CommandArgument != null)
		{
			EventTypeGrid.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_EventTypeByDeviceType", e.CommandArgument);
			EventTypeGrid.DataBind();
		}
	}
}
