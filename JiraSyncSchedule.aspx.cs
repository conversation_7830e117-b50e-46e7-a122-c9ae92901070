using QueueServiceClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using System.Web.UI;

public partial class JiraSyncSchedule : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_JiraSchedule");
		RadGrid1.DataSource = ds;
		RadGrid1.DataBind();

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				RadGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
	}

    protected string GetScheduledDateString(string relativeDate, string fixedDateString) {
        string retVal = null;
        if (!string.IsNullOrEmpty(relativeDate)) {
            retVal = relativeDate;
        }
        else {
            DateTime fixedDate = Convert.ToDateTime(fixedDateString);
            retVal = fixedDate.ToString("M/d/yyyy h:mm tt");
        }
        return retVal;
    }
    
    protected void ExecuteSync_Command(object sender, System.Web.UI.WebControls.CommandEventArgs e) {
        Utility.ClearSearchSession(true);

        //load Schedule
        DataSet ds1 = SqlHelper.ExecuteDataset("RPT_LoadJiraSyncSchedule", Convert.ToInt32(e.CommandArgument));
        foreach (DataRow row in ds1.Tables[0].Rows) //only loads one row.
        {
            Session[DieboldConstants.ADV_SEARCH_SESSION] = DataFormatter.getInt32(row, "SessionId").ToString();
        }

        List<Int64> obsIdList = new List<long>();
        DataSet ds = TransactionSource.GetTransactionsFromSessionParameters();
        if (ds.Tables != null && ds.Tables[0] != null) {
            foreach (DataRow row in ds.Tables[0].Rows) {
                Int64 obsId = DataFormatter.getInt64(row, "ObservationId");
                if (obsId > 0 && !obsIdList.Contains(obsId)) {
                    obsIdList.Add(obsId);
                }
            }
        }

        //call to Queue Service to start syncing process
        UploadClient uploadClient = new UploadClient();
        uploadClient.JiraSyncObservations(obsIdList);

        Response.Redirect("JiraSyncLog.aspx");
    }
}
