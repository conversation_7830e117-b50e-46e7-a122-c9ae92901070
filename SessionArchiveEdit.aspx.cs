using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Text;

public partial class SessionArchiveEdit : System.Web.UI.Page
{
	public int SessionId
	{
		get { if (this.ViewState["s"] != null) return (int)this.ViewState["s"]; else return 0; }
		set { this.ViewState["s"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Utility.IsUserAdmin())
			Response.Redirect("Default.aspx");

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SESSION_ID_KEY]))
				this.SessionId = Convert.ToInt32(Request.Params[DieboldConstants.SESSION_ID_KEY]);
			else
				throw new ApplicationException("Unable to identify the requested session id.");

			LoadData();
		}
	}

	private void LoadData()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetSession", this.SessionId);

		if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
		{
			DataRow row = ds.Tables[0].Rows[0];

			SessionNameLabel.Text = DataFormatter.getString(row, "SessionName");

			//Transactions
			if (DataFormatter.getDateTime(row, "TranKeepDataAfter") != DateTime.MinValue)
				TranKeepDataAfterDate.FixedDate = DataFormatter.getDateTime(row, "TranKeepDataAfter");
			if (DataFormatter.getDateTime(row, "TranKeepRollupAfter") != DateTime.MinValue)
				TranKeepRollupAfterDate.FixedDate = DataFormatter.getDateTime(row, "TranKeepRollupAfter");

			TranKeepDataAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "TranKeepDataRelativeId");
			TranKeepRollupAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "TranKeepRollupRelativeId");

			TranDataDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "TranDataDiskSpace"));
			TranRowCount.Text = DataFormatter.getInt32(row, "TranRowCount").ToString("#,###");
			TranRollupDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "TranRollupDiskSpace"));
			TranRollupCount.Text = DataFormatter.getInt32(row, "TranRollupCount").ToString("#,###");

			//Status Entities
			if (DataFormatter.getDateTime(row, "StatusKeepDataAfter") != DateTime.MinValue)
				StatusKeepDataAfterDate.FixedDate = DataFormatter.getDateTime(row, "StatusKeepDataAfter");
			if (DataFormatter.getDateTime(row, "StatusKeepRollupAfter") != DateTime.MinValue)
				StatusKeepRollupAfterDate.FixedDate = DataFormatter.getDateTime(row, "StatusKeepRollupAfter");

			StatusKeepDataAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "StatusKeepDataRelativeId");
			StatusKeepRollupAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "StatusKeepRollupRelativeId"); 
			
			StatusDataDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "StatusDataDiskSpace"));
			StatusRowCount.Text = DataFormatter.getInt32(row, "StatusRowCount").ToString("#,###");
			StatusRollupDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "StatusRollupDiskSpace"));
			StatusRollupCount.Text = DataFormatter.getInt32(row, "StatusRollupCount").ToString("#,###");

			//Info Entites
			if (DataFormatter.getDateTime(row, "InfoKeepDataAfter") != DateTime.MinValue)
				InfoKeepDataAfterDate.FixedDate = DataFormatter.getDateTime(row, "InfoKeepDataAfter");
			if (DataFormatter.getDateTime(row, "InfoKeepRollupAfter") != DateTime.MinValue)
				InfoKeepRollupAfterDate.FixedDate = DataFormatter.getDateTime(row, "InfoKeepRollupAfter");

			InfoKeepDataAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "InfoKeepDataRelativeId");
			InfoKeepRollupAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "InfoKeepRollupRelativeId"); 
			
			InfoDataDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "InfoDataDiskSpace"));
			InfoRowCount.Text = DataFormatter.getInt32(row, "InfoRowCount").ToString("#,###");
			InfoRollupDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "InfoRollupDiskSpace"));
			InfoRollupCount.Text = DataFormatter.getInt32(row, "InfoRollupCount").ToString("#,###");

			//Metric Entites
			if (DataFormatter.getDateTime(row, "MetricKeepDataAfter") != DateTime.MinValue)
				MetricKeepDataAfterDate.FixedDate = DataFormatter.getDateTime(row, "MetricKeepDataAfter");
			if (DataFormatter.getDateTime(row, "MetricKeepRollupAfter") != DateTime.MinValue)
				MetricKeepRollupAfterDate.FixedDate = DataFormatter.getDateTime(row, "MetricKeepRollupAfter");

			MetricKeepDataAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "MetricKeepDataRelativeId");
			MetricKeepRollupAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "MetricKeepRollupRelativeId"); 
			
			MetricDataDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "MetricDataDiskSpace"));
			MetricRowCount.Text = DataFormatter.getInt32(row, "MetricRowCount").ToString("#,###");
			MetricRollupDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "MetricRollupDiskSpace"));
			MetricRollupCount.Text = DataFormatter.getInt32(row, "MetricRollupCount").ToString("#,###");

			//Result Data
			if (DataFormatter.getDateTime(row, "ResultKeepDataAfter") != DateTime.MinValue)
				ResultKeepDataAfterDate.FixedDate = DataFormatter.getDateTime(row, "ResultKeepDataAfter");
			if (DataFormatter.getDateTime(row, "ResultKeepRollupAfter") != DateTime.MinValue)
				ResultKeepRollupAfterDate.FixedDate = DataFormatter.getDateTime(row, "ResultKeepRollupAfter");

			ResultKeepDataAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "ResultKeepDataRelativeId");
			ResultKeepRollupAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "ResultKeepRollupRelativeId"); 
			
			ResultDataDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "ResultDataDiskSpace"));
			ResultRowCount.Text = DataFormatter.getInt32(row, "ResultRowCount").ToString("#,###");
			ResultRollupDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "ResultRollupDiskSpace"));
			ResultRollupCount.Text = DataFormatter.getInt32(row, "ResultRollupCount").ToString("#,###");

			//Engineering Data
			if (DataFormatter.getDateTime(row, "EngKeepDataAfter") != DateTime.MinValue)
				EngKeepDataAfterDate.FixedDate = DataFormatter.getDateTime(row, "EngKeepDataAfter");
			if (DataFormatter.getDateTime(row, "EngKeepRollupAfter") != DateTime.MinValue)
				EngKeepRollupAfterDate.FixedDate = DataFormatter.getDateTime(row, "EngKeepRollupAfter");

			EngKeepDataAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "EngKeepDataRelativeId");
			EngKeepRollupAfterDate.RelativeTimeId = DataFormatter.getInt32(row, "EngKeepRollupRelativeId"); 
			
			EngDataDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "EngDataDiskSpace"));
			EngRowCount.Text = DataFormatter.getInt32(row, "EngRowCount").ToString("#,###");
			EngRollupDiskSpace.Text = FormatByte(DataFormatter.getInt32(row, "EngRollupDiskSpace"));
			EngRollupCount.Text = DataFormatter.getInt32(row, "EngRollupCount").ToString("#,###");

			if (DataFormatter.getInt32(row, "TotalMaxSpace") != 0)
			{
				TranDiskGraph.Style.Add(HtmlTextWriterStyle.Width, Math.Round((double)100 * (DataFormatter.getInt32(row, "TranDataDiskSpace") + DataFormatter.getInt32(row, "TranRollupDiskSpace")) / DataFormatter.getInt32(row, "TotalMaxSpace"), 0).ToString() + "%");
				TranDiskGraphLabel.Text = Math.Round((double)100 * (DataFormatter.getInt32(row, "TranDataDiskSpace") + DataFormatter.getInt32(row, "TranRollupDiskSpace")) / DataFormatter.getInt32(row, "SessionDiskSpace"), 2).ToString() + "%";
				StatusDiskGraph.Style.Add(HtmlTextWriterStyle.Width, Math.Round((double)100 * (DataFormatter.getInt32(row, "StatusDataDiskSpace") + DataFormatter.getInt32(row, "StatusRollupDiskSpace")) / DataFormatter.getInt32(row, "TotalMaxSpace"), 0).ToString() + "%");
				StatusDiskGraphLabel.Text = Math.Round((double)100 * (DataFormatter.getInt32(row, "StatusDataDiskSpace") + DataFormatter.getInt32(row, "StatusRollupDiskSpace")) / DataFormatter.getInt32(row, "SessionDiskSpace"), 2).ToString() + "%";
				InfoDiskGraph.Style.Add(HtmlTextWriterStyle.Width, Math.Round((double)100 * (DataFormatter.getInt32(row, "InfoDataDiskSpace") + DataFormatter.getInt32(row, "InfoRollupDiskSpace")) / DataFormatter.getInt32(row, "TotalMaxSpace"), 0).ToString() + "%");
				InfoDiskGraphLabel.Text = Math.Round((double)100 * (DataFormatter.getInt32(row, "InfoDataDiskSpace") + DataFormatter.getInt32(row, "InfoRollupDiskSpace")) / DataFormatter.getInt32(row, "SessionDiskSpace"), 2).ToString() + "%";
				MetricDiskGraph.Style.Add(HtmlTextWriterStyle.Width, Math.Round((double)100 * (DataFormatter.getInt32(row, "MetricDataDiskSpace") + DataFormatter.getInt32(row, "MetricRollupDiskSpace")) / DataFormatter.getInt32(row, "TotalMaxSpace"), 0).ToString() + "%");
				MetricDiskGraphLabel.Text = Math.Round((double)100 * (DataFormatter.getInt32(row, "MetricDataDiskSpace") + DataFormatter.getInt32(row, "MetricRollupDiskSpace")) / DataFormatter.getInt32(row, "SessionDiskSpace"), 2).ToString() + "%";
				ResultDiskGraph.Style.Add(HtmlTextWriterStyle.Width, Math.Round((double)100 * (DataFormatter.getInt32(row, "ResultDataDiskSpace") + DataFormatter.getInt32(row, "ResultRollupDiskSpace")) / DataFormatter.getInt32(row, "TotalMaxSpace"), 0).ToString() + "%");
				ResultDiskGraphLabel.Text = Math.Round((double)100 * (DataFormatter.getInt32(row, "ResultDataDiskSpace") + DataFormatter.getInt32(row, "ResultRollupDiskSpace")) / DataFormatter.getInt32(row, "SessionDiskSpace"), 2).ToString() + "%";
				EngDiskGraph.Style.Add(HtmlTextWriterStyle.Width, Math.Round((double)100 * (DataFormatter.getInt32(row, "EngDataDiskSpace") + DataFormatter.getInt32(row, "EngRollupDiskSpace")) / DataFormatter.getInt32(row, "TotalMaxSpace"), 0).ToString() + "%");
				EngDiskGraphLabel.Text = Math.Round((double)100 * (DataFormatter.getInt32(row, "EngDataDiskSpace") + DataFormatter.getInt32(row, "EngRollupDiskSpace")) / DataFormatter.getInt32(row, "SessionDiskSpace"), 2).ToString() + "%";
			}
			else
			{
				TranDiskGraph.Visible = false;
				StatusDiskGraph.Visible = false;
				InfoDiskGraph.Visible = false;
				MetricDiskGraph.Visible = false;
				ResultDiskGraph.Visible = false;
				EngDiskGraph.Visible = false;
			}
		}
	}

	public string FormatByte(string bytes) { return FormatByte(Convert.ToInt32(bytes)); }
	public string FormatByte(int bytes)
	{
		//if (bytes > 1000000)
		//	return ((decimal)bytes / (1024 * 1024)).ToString("#,##0.#") + " GB";
		if (bytes > 1000)
			return ((decimal)bytes / 1024).ToString("#,##0.#") + " MB";
		else
			return bytes.ToString("#,##0") + " KB";
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		if (ValidatePage())
		{
			ErrorDiv.Visible = ErrorDiv1.Visible = false;

			object tranData = null;
			object tranRollup = null;
			object statusData = null;
			object statusRollup = null;
			object infoData = null;
			object infoRollup = null;
			object metricData = null;
			object metricRollup = null;
			object resultData = null;
			object resultRollup = null;
			object engData = null;
			object engRollup = null;

			if (TranKeepDataAfterDate.FixedDate != DateTime.MinValue)
				tranData = TranKeepDataAfterDate.FixedDate;
			if (TranKeepRollupAfterDate.FixedDate != DateTime.MinValue)
				tranRollup = TranKeepRollupAfterDate.FixedDate;

			if (StatusKeepDataAfterDate.FixedDate != DateTime.MinValue)
				statusData = StatusKeepDataAfterDate.FixedDate;
			if (StatusKeepRollupAfterDate.FixedDate != DateTime.MinValue)
				statusRollup = StatusKeepRollupAfterDate.FixedDate;

			if (InfoKeepDataAfterDate.FixedDate != DateTime.MinValue)
				infoData = InfoKeepDataAfterDate.FixedDate;
			if (InfoKeepRollupAfterDate.FixedDate != DateTime.MinValue)
				infoRollup = InfoKeepRollupAfterDate.FixedDate;

			if (MetricKeepDataAfterDate.FixedDate != DateTime.MinValue)
				metricData = MetricKeepDataAfterDate.FixedDate;
			if (MetricKeepRollupAfterDate.FixedDate != DateTime.MinValue)
				metricRollup = MetricKeepRollupAfterDate.FixedDate;

			if (ResultKeepDataAfterDate.FixedDate != DateTime.MinValue)
				resultData = ResultKeepDataAfterDate.FixedDate;
			if (ResultKeepRollupAfterDate.FixedDate != DateTime.MinValue)
				resultRollup = ResultKeepRollupAfterDate.FixedDate;

			if (EngKeepDataAfterDate.FixedDate != DateTime.MinValue)
				engData = EngKeepDataAfterDate.FixedDate;
			if (EngKeepRollupAfterDate.FixedDate != DateTime.MinValue)
				engRollup = EngKeepRollupAfterDate.FixedDate;

			SqlHelper.ExecuteNonQuery("RPT_UpdateSessionPurge", this.SessionId,
					tranData, TranKeepDataAfterDate.RelativeTimeId, tranRollup, TranKeepRollupAfterDate.RelativeTimeId,
					statusData, StatusKeepDataAfterDate.RelativeTimeId, statusRollup, StatusKeepRollupAfterDate.RelativeTimeId,
					infoData, InfoKeepDataAfterDate.RelativeTimeId, infoRollup, InfoKeepRollupAfterDate.RelativeTimeId,
					metricData, MetricKeepDataAfterDate.RelativeTimeId, metricRollup, MetricKeepRollupAfterDate.RelativeTimeId,
					resultData, ResultKeepDataAfterDate.RelativeTimeId, resultRollup, ResultKeepRollupAfterDate.RelativeTimeId,
					engData, EngKeepDataAfterDate.RelativeTimeId, engRollup, EngKeepRollupAfterDate.RelativeTimeId);

			if (!string.IsNullOrEmpty(Request.Params["page"]) && Request.Params["page"] != "0")
				Response.Redirect(string.Format("SessionArchive.aspx?page={0}", Request.Params["page"]));
			else
				Response.Redirect("sessionArchive.aspx");
		}
		else
		{
			ErrorMessage1.Text = ErrorMessage.Text;
			ErrorDiv.Visible = ErrorDiv1.Visible = true;
		}
	}

	private bool ValidatePage()
	{
		bool isValid = true;
		StringBuilder errorMessage = new StringBuilder();

		//if (TranKeepDataAfterDate.FixedDate != null && TranKeepDataAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (TranKeepRollupAfterDate.FixedDate != null && TranKeepRollupAfterDate.FixedDate !=
		//        DateTime.MinValue && DateTime.Compare((DateTime)TranKeepDataAfterDate.FixedDate, (DateTime)TranKeepRollupAfterDate.FixedDate) < 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Transaction & Observation: The rollup date: '{0}' must be earlier than the transaction date: '{1}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (StatusKeepDataAfterDate.FixedDate != null && StatusKeepDataAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepDataAfterDate.FixedDate, (DateTime)StatusKeepDataAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Status Entity: The transaction date: '{0}' must come after the Transaction & Observation transaction date: '{1}'.</li><br />", ((DateTime)StatusKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (StatusKeepDataAfterDate.FixedDate == null || StatusKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Status Entity: Transaction date is blank.  It must have a value greater than or equal to the Transaction & Observation transaction date: '{0}'.</li><br />", ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (InfoKeepDataAfterDate.FixedDate != null && InfoKeepDataAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepDataAfterDate.FixedDate, (DateTime)InfoKeepDataAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Info Entity: The transaction date: '{0}' must come after the Transaction & Observation transaction date: '{1}'.</li><br />", ((DateTime)InfoKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (InfoKeepDataAfterDate.FixedDate == null || InfoKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Info Entity: Transaction date is blank.  It must have a value greater than or equal to the Transaction & Observation transaction date: '{0}'.</li><br />", ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (MetricKeepDataAfterDate.FixedDate != null && MetricKeepDataAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepDataAfterDate.FixedDate, (DateTime)MetricKeepDataAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Metric Entity: The transaction date: '{0}' must come after the Transaction & Observation transaction date: '{1}'.</li><br />", ((DateTime)MetricKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (MetricKeepDataAfterDate.FixedDate == null || MetricKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Metric Entity: Transaction date is blank.  It must have a value greater than or equal to the Transaction & Observation transaction date: '{0}'.</li><br />", ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (ResultKeepDataAfterDate.FixedDate != null && ResultKeepDataAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepDataAfterDate.FixedDate, (DateTime)ResultKeepDataAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Result Data: The transaction date: '{0}' must come after the Transaction & Observation transaction date: '{1}'.</li><br />", ((DateTime)ResultKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (ResultKeepDataAfterDate.FixedDate == null || ResultKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Result Entity: Transaction date is blank.  It must have a value greater than or equal to the Transaction & Observation transaction date: '{0}'.</li><br />", ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (EngKeepDataAfterDate.FixedDate != null && EngKeepDataAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepDataAfterDate.FixedDate, (DateTime)EngKeepDataAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Engineering Data: The transaction date: '{0}' must come after the Transaction & Observation transaction date: '{1}'.</li><br />", ((DateTime)EngKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (EngKeepDataAfterDate.FixedDate == null || EngKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Eng Entity: Transaction date is blank.  It must have a value greater than or equal to the Transaction & Observation transaction date: '{0}'.</li><br />", ((DateTime)TranKeepDataAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		//if (TranKeepRollupAfterDate.FixedDate != null && TranKeepRollupAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (TranKeepDataAfterDate.FixedDate == null || TranKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Transaction & Observation: Transaction date is blank.  It must have a value greater than or equal to the rollup date: '{0}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (StatusKeepRollupAfterDate.FixedDate != null && StatusKeepRollupAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepRollupAfterDate.FixedDate, (DateTime)StatusKeepRollupAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Status Entity: The rollup date: '{0}' must come after the Transaction & Observation rollup date: '{1}'.</li><br />", ((DateTime)StatusKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (StatusKeepRollupAfterDate.FixedDate == null || StatusKeepRollupAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Status Entity: Rollup date is blank.  It must have a value greater than or equal to the Transaction & Observation rollup date: '{0}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (InfoKeepRollupAfterDate.FixedDate != null && InfoKeepRollupAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepRollupAfterDate.FixedDate, (DateTime)InfoKeepRollupAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Info Entity: The rollup date: '{0}' must come after the Transaction & Observation rollup date: '{1}'.</li><br />", ((DateTime)InfoKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (InfoKeepRollupAfterDate.FixedDate == null || InfoKeepRollupAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Info Entity: Rollup date is blank.  It must have a value greater than or equal to the Transaction & Observation rollup date: '{0}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (MetricKeepRollupAfterDate.FixedDate != null && MetricKeepRollupAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepRollupAfterDate.FixedDate, (DateTime)MetricKeepRollupAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Metric Entity: The rollup date: '{0}' must come after the Transaction & Observation rollup date: '{1}'.</li><br />", ((DateTime)MetricKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (MetricKeepRollupAfterDate.FixedDate == null || MetricKeepRollupAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Metric Entity: Rollup date is blank.  It must have a value greater than or equal to the Transaction & Observation rollup date: '{0}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (ResultKeepRollupAfterDate.FixedDate != null && ResultKeepRollupAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepRollupAfterDate.FixedDate, (DateTime)ResultKeepRollupAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Result Data: The rollup date: '{0}' must come after the Transaction & Observation rollup date: '{1}'.</li><br />", ((DateTime)ResultKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (ResultKeepRollupAfterDate.FixedDate == null || ResultKeepRollupAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Result Data: Rollup date is blank.  It must have a value greater than or equal to the Transaction & Observation rollup date: '{0}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }

		//    if (EngKeepRollupAfterDate.FixedDate != null && EngKeepRollupAfterDate.FixedDate != DateTime.MinValue &&
		//        DateTime.Compare((DateTime)TranKeepRollupAfterDate.FixedDate, (DateTime)EngKeepRollupAfterDate.FixedDate) > 0)
		//    {
		//        errorMessage.Append(string.Format("<li>Engineering Data: The rollup date: '{0}' must come after the Transaction & Observation rollup date: '{1}'.</li><br />", ((DateTime)EngKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy"), ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//    else if (EngKeepRollupAfterDate.FixedDate == null || EngKeepRollupAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Engineering Data: Rollup date is blank.  It must have a value greater than or equal to the Transaction & Observation rollup date: '{0}'.</li><br />", ((DateTime)TranKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		//if (StatusKeepRollupAfterDate.FixedDate != null && StatusKeepRollupAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (StatusKeepDataAfterDate.FixedDate == null || StatusKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Status Entity: Transaction date is blank.  It must have a value greater than or equal to the Status Entity rollup date: '{0}'.</li><br />", ((DateTime)StatusKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		//if (InfoKeepRollupAfterDate.FixedDate != null && InfoKeepRollupAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (InfoKeepDataAfterDate.FixedDate == null || InfoKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Info Entity: Transaction date is blank.  It must have a value greater than or equal to the Info Entity rollup date: '{0}'.</li><br />", ((DateTime)InfoKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		//if (MetricKeepRollupAfterDate.FixedDate != null && MetricKeepRollupAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (MetricKeepDataAfterDate.FixedDate == null || MetricKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Metric Entity: Transaction date is blank.  It must have a value greater than or equal to the Metric Entity rollup date: '{0}'.</li><br />", ((DateTime)MetricKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		//if (ResultKeepRollupAfterDate.FixedDate != null && ResultKeepRollupAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (ResultKeepDataAfterDate.FixedDate == null || ResultKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Result Data: Transaction date is blank.  It must have a value greater than or equal to the Result Entity rollup date: '{0}'.</li><br />", ((DateTime)ResultKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		//if (EngKeepRollupAfterDate.FixedDate != null && EngKeepRollupAfterDate.FixedDate != DateTime.MinValue)
		//{
		//    if (EngKeepDataAfterDate.FixedDate == null || EngKeepDataAfterDate.FixedDate == DateTime.MinValue)
		//    {
		//        errorMessage.Append(string.Format("<li>Engineering Data: Transaction date is blank.  It must have a value greater than or equal to the Eng Entity rollup date: '{0}'.</li><br />", ((DateTime)EngKeepRollupAfterDate.FixedDate).Date.ToString("MM/dd/yyyy")));
		//        isValid = false;
		//    }
		//}

		if (!ConfirmBox.Text.ToUpper().Equals("YES") && !ConfirmBox2.Text.ToUpper().Equals("YES"))
		{
			errorMessage.Append("<li>Please confirm your changes by typing \"YES\" into the text box before saving.</li><br />");
			isValid = false;
		}

		ErrorMessage.Text = errorMessage.ToString();
		return isValid;
	}

	protected void FillTransactionsButton_Click(object sender, EventArgs e)
	{
		StatusKeepDataAfterDate.FixedDate = TranKeepDataAfterDate.FixedDate;
		InfoKeepDataAfterDate.FixedDate = TranKeepDataAfterDate.FixedDate;
		MetricKeepDataAfterDate.FixedDate = TranKeepDataAfterDate.FixedDate;
		ResultKeepDataAfterDate.FixedDate = TranKeepDataAfterDate.FixedDate;
		EngKeepDataAfterDate.FixedDate = TranKeepDataAfterDate.FixedDate;
	}

	protected void FillRollupButton_Click(object sender, EventArgs e)
	{
		StatusKeepRollupAfterDate.FixedDate = TranKeepRollupAfterDate.FixedDate;
		InfoKeepRollupAfterDate.FixedDate = TranKeepRollupAfterDate.FixedDate;
		MetricKeepRollupAfterDate.FixedDate = TranKeepRollupAfterDate.FixedDate;
		ResultKeepRollupAfterDate.FixedDate = TranKeepRollupAfterDate.FixedDate;
		EngKeepRollupAfterDate.FixedDate = TranKeepRollupAfterDate.FixedDate;
	}
}
