﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.IO;

public partial class UploadFrame : System.Web.UI.Page
{
	private string ImageDir = ConfigurationManager.AppSettings["DashboardImagesPath"];
	public const int MAX_FILE_SIZE = (10000 * 1024); // 10 MB

    protected void Page_Load(object sender, EventArgs e)
    {

    }

	protected void UploadBtn_Click(object sender, EventArgs e)
	{
		Session["uploadedfile"] = UploadFile(this.fileUploadCntrl);
	}

	private string UploadFile(FileUpload currentFile)
	{
		if ((currentFile != null) && (currentFile.PostedFile != null) && (currentFile.PostedFile.ContentLength > 0))
		{
			bool isPicture = false;
			bool isPDF = false;
			string physDir = Server.MapPath(this.ImageDir);
			string fileName = Path.GetFileName(currentFile.PostedFile.FileName).Replace(' ', '_');
			string[] fileNameParts = fileName.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);

			switch (fileNameParts[1].ToLower())
			{
				case "jpg":
				case "jpeg":
				case "png":
				case "gif":
				case "bmp":
					isPicture = true;
					break;
				case "pdf":
					isPDF = true;
					break;
			}

			if (isPicture || isPDF)
			{
				if (!string.IsNullOrEmpty(physDir) && !Directory.Exists(physDir))
					Directory.CreateDirectory(physDir);

				//if (currentFile.PostedFile.ContentLength > MAX_FILE_SIZE)
				//{
				//	popupError("Your file size exceeds the " + MAX_FILE_SIZE / 1024 + " Mb size limit. Please lower the file size and try again.");
				//	return null;
				//}

				string targetPath = physDir + fileName;
				int collisionSuffix = 1;
				string collisionPath = targetPath;

				while (File.Exists(collisionPath))
				{
					collisionSuffix++;
					string[] pathParts = targetPath.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);
					collisionPath = pathParts[0] + "(" + collisionSuffix.ToString() + ")" + "." + pathParts[1];
				}

				if (collisionSuffix > 1)
				{
					targetPath = collisionPath;
					fileName = fileNameParts[0] + "(" + collisionSuffix + ")" + "." + fileNameParts[1];
				}

				//Save the image to the Server
				currentFile.PostedFile.SaveAs(targetPath);

				//if they uploaded a PDF, generate an image of the document to be displayed
				if (isPDF)
				{
					fileName = Utility.GeneratePDFThumbnail(targetPath, targetPath, 375, 0, false); 
				}

				successLbl.Visible = true;	

				return fileName;
			}
			else
			{
				popupError("Invalid file type, supported types are: .jpg, .png, .gif, .bmp, .pdf");
			}
		}
		return null;
	}

	private void popupError(string errorMessage)
	{
		if (!string.IsNullOrEmpty(errorMessage))
		{
			string errorScript = "<script language='javascript'>\r\nalert('" + errorMessage + "');\r\n</script>";
			Page.ClientScript.RegisterStartupScript(typeof(FileUpload), "ErrorScript", errorScript);
		}
	}
}
