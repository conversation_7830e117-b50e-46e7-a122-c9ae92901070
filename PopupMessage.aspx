<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="PopupMessage.aspx.cs" Inherits="PopupMessage" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Message</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					
					<div class="title" style="padding-bottom:2px;"><img id="AlertImg" runat="server" src="~/images/alert.gif" border="0" alt="Urgent" /><asp:label style="vertical-align:middle;padding-left:5px;" id="subjectLabel" runat="server"></asp:label></div>
					<div class="title" style="font-weight:normal;padding-bottom:2px;padding-top:0px;padding-left:19px;"><asp:label id="dateLabel" runat="server"></asp:label></div>
					<div class="title" style="font-weight:normal;padding-bottom:2px;padding-top:0px;"><asp:label id="typeLabel" runat="server"></asp:label></div>
					<br />
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td class="leftPad">&nbsp;</td>
							<td><asp:label id="bodyLabel" runat="server"></asp:label></td>
						</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="padding-left:14px;"><div class="goButton"><a href="javascript:CloseRadWindow();">Close</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

