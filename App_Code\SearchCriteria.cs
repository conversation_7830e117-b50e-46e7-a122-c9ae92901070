using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Web.UI;
using Microsoft.AnalysisServices.AdomdClient;

public class SearchCriteria
{
	public SearchCriteria(Page page)
	{
		this.Page = page;
	}

	private Page page = null;
	public Page Page
	{
		get { return page; }
		set { page = value; }
	}

	private string sessionIdList = null;
	public string SessionIdList
	{
		get { return sessionIdList; }
		set { sessionIdList = value; }
	}

	private DateTime searchStartDate = DateTime.MinValue;
	public DateTime SearchStartDate
	{
		get { return searchStartDate; }
		set { searchStartDate = value; }
	}

	private DateTime searchEndDate = DateTime.MinValue;
	public DateTime SearchEndDate
	{
		get { return searchEndDate; }
		set { searchEndDate = value; }
	}

	private string cell = null;
	public string Cell
	{
		get { return cell; }
		set { cell = value; }
	}

	private string cellName = null;
	public string CellName
	{
		get { return cellName; }
		set { cellName = value; }
	}

	private string device = null;
	public string Device
	{
		get { return device; }
		set { device = value; }
	}

	private string deviceName = null;
	public string DeviceName
	{
		get { return deviceName; }
		set { deviceName = value; }
	}

	private string deviceType = null;
	public string DeviceType
	{
		get { return deviceType; }
		set { deviceType = value; }
	}

	private string deviceTypeName = null;
	public string DeviceTypeName
	{
		get { return deviceTypeName; }
		set { deviceTypeName = value; }
	}

	private string setting = null;
	public string Setting
	{
		get { return setting; }
		set { setting = value; }
	}

	private string settingName = null;
	public string SettingName
	{
		get { return settingName; }
		set { settingName = value; }
	}

	private List<SelectedField> settingTree = new List<SelectedField>();
	public List<SelectedField> SettingTree
	{
		get { return settingTree; }
		set { settingTree = value; }
	}

	private string setting2 = null;
	public string Setting2
	{
		get { return setting2; }
		set { setting2 = value; }
	}

	private string settingName2 = null;
	public string SettingName2
	{
		get { return settingName2; }
		set { settingName2 = value; }
	}

	private List<SelectedField> settingTree2 = new List<SelectedField>();
	public List<SelectedField> SettingTree2
	{
		get { return settingTree2; }
		set { settingTree2 = value; }
	}

	private string setting3 = null;
	public string Setting3
	{
		get { return setting3; }
		set { setting3 = value; }
	}

	private string settingName3 = null;
	public string SettingName3
	{
		get { return settingName3; }
		set { settingName3 = value; }
	}

	private List<SelectedField> settingTree3 = new List<SelectedField>();
	public List<SelectedField> SettingTree3
	{
		get { return settingTree3; }
		set { settingTree3 = value; }
	}

	private string observationsOnly = Boolean.FalseString;
	public string ObservationsOnly
	{
		get { return observationsOnly; }
		set { observationsOnly = value; }
	}

	private string failureType = null;
	public string FailureType
	{
		get { return failureType; }
		set { failureType = value; }
	}

	private string failureTypeName = null;
	public string FailureTypeName
	{
		get { return failureTypeName; }
		set { failureTypeName = value; }
	}

    private string triageType = null;
    public string TriageType
    {
        get { return triageType; }
        set { triageType = value; }
    }

    private string triageTypeName = null;
    public string TriageTypeName
    {
        get { return triageTypeName; }
        set { triageTypeName = value; }
    }

    private string moduleDeviceType = null;
	public string ModuleDeviceType {
		get { return moduleDeviceType; }
		set { moduleDeviceType = value; }
	}

	private string moduleDeviceTypeName = null;
	public string ModuleDeviceTypeName {
		get { return moduleDeviceTypeName; }
		set { moduleDeviceTypeName = value; }
	}

	private string investigationArea = null;
	public string InvestigationArea
	{
		get { return investigationArea; }
		set { investigationArea = value; }
	}

	private string investigationAreaName = null;
	public string InvestigationAreaName
	{
		get { return investigationAreaName; }
		set { investigationAreaName = value; }
	}

	private string eventType = null;
	public string EventType
	{
		get { return eventType; }
		set { eventType = value; }
	}

	private string eventTypeName = null;
	public string EventTypeName
	{
		get { return eventTypeName; }
		set { eventTypeName = value; }
	}

	private string failureLocation = null;
	public string FailureLocation
	{
		get { return failureLocation; }
		set { failureLocation = value; }
	}

	private string failureLocationName = null;
	public string FailureLocationName
	{
		get { return failureLocationName; }
		set { failureLocationName = value; }
	}

    private string mediaNumber = null;
    public string MediaNumber
    {
        get { return mediaNumber; }
        set { mediaNumber = value; }
    }

    private string fileNumber = null;
    public string FileNumber
    {
        get { return fileNumber; }
        set { fileNumber = value; }
    }

    private string commandNumber = null;
    public string CommandNumber
    {
        get { return commandNumber; }
        set { commandNumber = value; }
    }

    private string statisticFields = null;
	public string StatisticFields
	{
		get { return statisticFields; }
		set { statisticFields = value; }
	}

	private string statisticFieldOptions = null;
	public string StatisticFieldOptions
	{
		get { return statisticFieldOptions; }
		set { statisticFieldOptions = value; }
	}

	private string statisticName = null;
	public string StatisticName
	{
		get { return statisticName; }
		set { statisticName = value; }
	}

	private List<SelectedField> statisticTree = new List<SelectedField>();
	public List<SelectedField> StatisticTree
	{
		get { return statisticTree; }
		set { statisticTree = value; }
	}

	private Decimal minQuantizedValue = Decimal.MinValue;
	public Decimal MinQuantizedValue
	{
		get { return minQuantizedValue; }
		set { minQuantizedValue = value; }
	}

	private Decimal maxQuantizedValue = Decimal.MinValue;
	public Decimal MaxQuantizedValue
	{
		get { return maxQuantizedValue; }
		set { maxQuantizedValue = value; }
	}

	private string solutionState = null;
	public string SolutionState {
		get { return solutionState; }
		set { solutionState = value; }
	}

	private string solutionStateNames = null;
	public string SolutionStateNames {
		get { return solutionStateNames; }
		set { solutionStateNames = value; }
	}

	public void CalculateSearchCriteria(ReportInfo repInfo, SubReportInfo subReportInfo)
	{
		Utility.ClearSearchSession(false);

		// Sessions
		List<string> sessionIdList = new List<string>();
        if (subReportInfo != null && subReportInfo.AttachedSessions.Count > 0)
        {
            foreach (SessionInfo info in subReportInfo.AttachedSessions)
                sessionIdList.Add(info.SessionId.ToString());
        }
        else
        {
            foreach (SessionInfo info in repInfo.AttachedSessions)
                sessionIdList.Add(info.SessionId.ToString());
        }

		if (sessionIdList.Count > 0)
			this.SessionIdList = string.Join(", ", sessionIdList.ToArray());

		// Date Range
		if (!DateTime.MinValue.Equals(repInfo.StartDate))
			this.SearchStartDate = repInfo.StartDate;

		if (!DateTime.MinValue.Equals(repInfo.EndDate))
			this.SearchEndDate = repInfo.EndDate;

		// Dimension Filters
		Regex r = new Regex(@"&\[(?<ID>\d{1,10})\]", RegexOptions.Compiled);
		foreach (string dimensionUniqueName in repInfo.ReportFilters.Keys)
		{
			List<string> memberList = repInfo.ReportFilters[dimensionUniqueName];
            if (string.Compare(dimensionUniqueName, DieboldConstants.DEVICE_FILTER_DIMENSION_NAME, true) == 0)
            {
                if (subReportInfo != null && subReportInfo.DeviceFilters.Count > 0)
                    memberList = subReportInfo.DeviceFilters;
            }
			List<SelectedField> selectedFields = GetSelectedFieldList(memberList);
			SelectedField root = null;

			if (selectedFields.Count > 0)
			{
				switch (dimensionUniqueName)
				{
					case "[Cell].[Status - Cell].[Status]":
						this.Cell = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
						this.CellName = Utility.FormatFieldNameList(selectedFields, false, true, null, null, ", ");
						break;

					case "[Device].[Type - Device].[Type]":
						this.Device = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
						this.DeviceName = Utility.FormatFieldNameList(selectedFields, false, true, null, null, ", ");

						this.DeviceType = selectedFields[0].FieldId.ToString();
						this.DeviceTypeName = selectedFields[0].FieldName;
						break;

                    case "[Failure Type].[Failure Type].[Failure Type]":
                        this.FailureType = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
                        this.FailureTypeName = Utility.FormatFieldNameList(selectedFields, false, true, null, null, ", ");
                        break;

                    case "[Triage Type].[Hierarchy].[Triage Type ID]":
                        this.TriageType = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
                        this.TriageTypeName = Utility.FormatFieldNameList(selectedFields, false, true, null, null, ", ");
                        break;

                    case "[Setting].[Device - Setting - Option].[Device]":
						root = GetRootField(memberList);
						if (root != null)
						{
							this.Setting = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
							this.SettingName = Utility.FormatFieldNameList(selectedFields, true, true, "; ", " - ", ", ");
							this.SettingTree = selectedFields;

							this.DeviceType = root.FieldId.ToString();
							this.DeviceTypeName = root.FieldName;
						}
						break;
					case "[Setting 2].[Device - Setting - Option].[Device]":
						root = GetRootField(memberList);
						if (root != null)
						{
							this.Setting2 = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
							this.SettingName2 = Utility.FormatFieldNameList(selectedFields, true, true, "; ", " - ", ", ");
							this.SettingTree2 = selectedFields;

							this.DeviceType = root.FieldId.ToString();
							this.DeviceTypeName = root.FieldName;
						}
						break;
					case "[Setting 3].[Device - Setting - Option].[Device]":
						root = GetRootField(memberList);
						if (root != null)
						{
							this.Setting3 = Utility.FormatFieldIdList(selectedFields, false, true, null, null, ", ");
							this.SettingName3 = Utility.FormatFieldNameList(selectedFields, true, true, "; ", " - ", ", ");
							this.SettingTree3 = selectedFields;

							this.DeviceType = root.FieldId.ToString();
							this.DeviceTypeName = root.FieldName;
						}
						break;

                    case "[Media Number].[Media Number].[Media Number]":
                        this.MediaNumber = Utility.FormatFieldIdList(selectedFields, true, false, ", ", null, null);
                        break;

                    case "[File Number].[File Number].[File Number]":
                        this.FileNumber = Utility.FormatFieldIdList(selectedFields, true, false, ", ", null, null);
                        break;

                    case "[Command Number].[Command Number].[Command Number]":
                        this.CommandNumber = Utility.FormatFieldIdList(selectedFields, true, false, ", ", null, null);
                        break;
                    default:
                        throw new NotSupportedException(string.Format("Dimension '{0}' not supported for transaction search.", dimensionUniqueName));

                }
			}
		}

		// Series Filter
		if (repInfo.DimensionMembers != null && repInfo.DimensionMembers.Count > 0)
		{
			List<SelectedField> seriesFields = GetSelectedFieldList(repInfo.DimensionMembers);
			SelectedField root = GetRootField(repInfo.DimensionMembers);
            if (subReportInfo != null && subReportInfo.DimensionMembers.Count > 0)
            {
                seriesFields = GetSelectedFieldList(subReportInfo.DimensionMembers);
                root = GetRootField(subReportInfo.DimensionMembers);
            }
            if (seriesFields != null && seriesFields.Count > 0)
			{
				switch (repInfo.DimensionName)
				{
					case "[Agilis].[Agilis].[Agilis]":
					case "[Operator].[Operator].[Operator]":
					case "[Severity].[Severity].[Severity]":
						this.ObservationsOnly = Boolean.TrueString;
						break;
					case "[Failure Type].[Failure Type].[Failure Type]":
						this.ObservationsOnly = Boolean.TrueString;
						this.FailureType = Utility.FormatFieldIdList(seriesFields, true, false, ", ", null, null);
						this.FailureTypeName = Utility.FormatFieldNameList(seriesFields, true, false, ", ", null, null);
						break;
                    case "[Triage Type].[Hierarchy].[Triage Type ID]":
                        this.ObservationsOnly = Boolean.TrueString;
                        this.TriageType = Utility.FormatFieldIdList(seriesFields, true, false, ", ", null, null);
                        this.TriageTypeName = Utility.FormatFieldNameList(seriesFields, true, false, ", ", null, null);
                        break;
                    case "[Investigation Area].[Investigation Area].[Investigation Area]":
						this.ObservationsOnly = Boolean.TrueString;
						this.InvestigationArea = Utility.FormatFieldIdList(seriesFields, true, false, ", ", null, null);
						this.InvestigationAreaName = Utility.FormatFieldNameList(seriesFields, true, false, ", ", null, null);
						break;
					case "[Event Type].[Device - Event].[Device]":
						if (root != null)
						{
							this.EventType = Utility.FormatFieldIdList(seriesFields, false, true, null, null, ", ");
							this.EventTypeName = Utility.FormatFieldNameList(seriesFields, false, true, null, null, ", ");

							this.DeviceType = root.FieldId.ToString();
							this.DeviceTypeName = root.FieldName;
						}
						break;
					case "[Failure Location].[Device - Location].[Device]":
						if (root != null)
						{
							this.ObservationsOnly = Boolean.TrueString;
							this.FailureLocation = Utility.FormatFieldIdList(seriesFields, false, true, null, null, ", ");
							this.FailureLocationName = Utility.FormatFieldNameList(seriesFields, false, true, null, null, ", ");

							this.DeviceType = root.FieldId.ToString();
							this.DeviceTypeName = root.FieldName;
						}
						break;
					case "[Statistic].[Device - Event - Statistic - Option].[Device]":
					case "[Distribution].[Device - Type - Field - Option].[Device]":
						if (root != null)
						{
							this.StatisticFields = Utility.FormatFieldIdList(seriesFields, true, false, ", ", null, null); ;
							this.StatisticFieldOptions = Utility.FormatFieldIdList(seriesFields, false, true, null, null, ", ");
							this.StatisticName = Utility.FormatFieldNameList(seriesFields, true, true, "; ", " - ", ", ");
							this.StatisticTree = seriesFields; //object list for rebuilding tree

							this.DeviceType = root.FieldId.ToString();
							this.DeviceTypeName = root.FieldName;
						}
						break;
                    default:
                        throw new NotSupportedException(string.Format("Dimension series '{0}' not supported for search.", repInfo.DimensionName));
                }
			}
		}
	}

	private List<SelectedField> GetSelectedFieldList(List<string> memberList)
	{
		List<SelectedField> retVal = new List<SelectedField>();
		Regex r = new Regex(@"&\[(?<ID>\d{1,10})\]$", RegexOptions.Compiled);
		Match m = null;

		foreach (string memberUniqueName in memberList)
		{
			try
			{
				Member member = OLAPHelper.FindMemberByUniqueName(false, memberUniqueName);
				if (member != null)
				{
					if (member.ChildCount > 0 || member.Parent == null || member.Parent.Type == MemberTypeEnum.All)
					{
						m = r.Match(member.UniqueName);
						if (m.Success)
						{
							SelectedField tempField = new SelectedField();
							tempField.FieldName = member.Name;
							tempField.FieldId = Int32.Parse(m.Result("${ID}"));
							tempField.IsSelected = true;
							retVal.Add(tempField);
						}
					}
					else
					{
						SelectedField parentField = null;
						Member parentMember = member.Parent;
						int fieldId = 0;
						m = r.Match(parentMember.UniqueName);
						if (m.Success)
						{
							fieldId = Int32.Parse(m.Result("${ID}"));
							foreach (SelectedField tempField in retVal)
							{
								if (tempField.FieldId == fieldId)
								{
									parentField = tempField;
									break;
								}
							}
						}
						else
						{
							foreach (SelectedField tempField in retVal)
							{
								if (string.Compare(tempField.FieldName, parentMember.Name) == 0)
								{
									parentField = tempField;
									break;
								}
							}
						}

						if (parentField == null)
						{
							parentField = new SelectedField();
							parentField.FieldName = parentMember.Name;
							parentField.FieldId = fieldId;
							parentField.IsSelected = false;
							retVal.Add(parentField);
						}

						m = r.Match(member.UniqueName);
						if (m.Success)
						{
							SelectedFieldOption option = new SelectedFieldOption();
							option.FieldOptionName = member.Name;
							option.FieldOptionId = Int32.Parse(m.Result("${ID}"));
							parentField.SelectedFieldOptions.Add(option);
						}
					}
				}
			}
			catch
			{
				// ignore members not found

			}
		}

		return retVal;
	}

	private SelectedField GetRootField(List<string> memberList)
	{
		SelectedField retVal = null;
		Regex r = new Regex(@"&\[(?<ID>\d{1,10})\]", RegexOptions.Compiled);
		Match m = null;

		foreach (string memberUniqueName in memberList)
		{
			try
			{
				Member member = OLAPHelper.FindMemberByUniqueName(false, memberUniqueName);
				if (member != null)
				{
					Member parentMember = member.Parent;
					while (parentMember != null && parentMember.Parent != null && parentMember.Parent.Type != MemberTypeEnum.All)
						parentMember = parentMember.Parent;

					if (parentMember != null)
					{
						retVal = new SelectedField();
						retVal.FieldName = parentMember.Name;
						m = r.Match(parentMember.UniqueName);
						if (m.Success)
						{
							retVal.FieldId = Int32.Parse(m.Result("${ID}"));
						}
						break;
					}
				}
			}
			catch
			{
				// ignore members not found
			}
		}

		return retVal;
	}

	public void SetToSession()
	{
		if (!string.IsNullOrEmpty(this.SessionIdList))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SESSION] = this.SessionIdList;

		if (!DateTime.MinValue.Equals(this.SearchStartDate))
			this.Page.Session[DieboldConstants.ADV_SEARCH_START_DATE] = this.SearchStartDate;

		if (!DateTime.MinValue.Equals(this.SearchEndDate))
			this.Page.Session[DieboldConstants.ADV_SEARCH_END_DATE] = this.SearchEndDate;

		if (!string.IsNullOrEmpty(this.Cell))
			this.Page.Session[DieboldConstants.ADV_SEARCH_CELL] = this.Cell;

		if (!string.IsNullOrEmpty(this.CellName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_CELL_NAME] = this.CellName;

		if (!string.IsNullOrEmpty(this.Device))
			this.Page.Session[DieboldConstants.ADV_SEARCH_DEVICE] = this.Device;

		if (!string.IsNullOrEmpty(this.DeviceName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_DEVICE_NAME] = this.DeviceName;

		if (!string.IsNullOrEmpty(this.DeviceType))
			this.Page.Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] = this.DeviceType;

		if (!string.IsNullOrEmpty(this.DeviceTypeName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE_NAME] = this.DeviceTypeName;

		//Setting filter 1
		if (!string.IsNullOrEmpty(this.Setting))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING] = this.Setting;

		if (!string.IsNullOrEmpty(this.SettingName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING_NAME] = this.SettingName;

		if (this.SettingTree.Count > 0)
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING_TREE] = this.SettingTree;

		//Setting filter 2
		if (!string.IsNullOrEmpty(this.Setting2))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING2] = this.Setting2;

		if (!string.IsNullOrEmpty(this.SettingName2))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING_NAME2] = this.SettingName2;

		if (this.SettingTree2.Count > 0)
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING_TREE2] = this.SettingTree2;

		//Setting filter 3
		if (!string.IsNullOrEmpty(this.Setting3))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING3] = this.Setting3;

		if (!string.IsNullOrEmpty(this.SettingName3))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING_NAME3] = this.SettingName3;

		if (this.SettingTree3.Count > 0)
			this.Page.Session[DieboldConstants.ADV_SEARCH_SETTING_TREE3] = this.SettingTree3;

		if (!string.IsNullOrEmpty(this.ObservationsOnly))
			this.Page.Session[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY] = this.ObservationsOnly;

		if (!string.IsNullOrEmpty(this.FailureType))
			this.Page.Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] = this.FailureType;

		if (!string.IsNullOrEmpty(this.FailureTypeName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE_NAME] = this.FailureTypeName;

        if (!string.IsNullOrEmpty(this.TriageType))
            this.Page.Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] = this.TriageType;

        if (!string.IsNullOrEmpty(this.TriageTypeName))
            this.Page.Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE_NAME] = this.TriageTypeName;

        if (!string.IsNullOrEmpty(this.ModuleDeviceType))
			this.Page.Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] = this.ModuleDeviceType;

		if (!string.IsNullOrEmpty(this.ModuleDeviceTypeName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE_NAME] = this.ModuleDeviceTypeName;

		if (!string.IsNullOrEmpty(this.InvestigationArea))
			this.Page.Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] = this.InvestigationArea;

		if (!string.IsNullOrEmpty(this.InvestigationAreaName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA_NAME] = this.InvestigationAreaName;

		if (!string.IsNullOrEmpty(this.EventType))
			this.Page.Session[DieboldConstants.ADV_SEARCH_EVENT] = this.EventType;

		if (!string.IsNullOrEmpty(this.EventTypeName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_EVENT_NAME] = this.EventTypeName;

        if (!string.IsNullOrEmpty(this.MediaNumber))
            this.Page.Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] = this.MediaNumber;

        if (!string.IsNullOrEmpty(this.FileNumber))
            this.Page.Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER] = this.FileNumber;

        if (!string.IsNullOrEmpty(this.CommandNumber))
            this.Page.Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] = this.CommandNumber;

        if (!string.IsNullOrEmpty(this.FailureLocation))
			this.Page.Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] = this.FailureLocation;

		if (!string.IsNullOrEmpty(this.FailureLocationName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION_NAME] = this.FailureLocationName;

		if (!string.IsNullOrEmpty(this.StatisticFields))
			this.Page.Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] = this.StatisticFields;

		if (!string.IsNullOrEmpty(this.StatisticFieldOptions))
			this.Page.Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] = this.StatisticFieldOptions;

		if (!string.IsNullOrEmpty(this.StatisticName))
			this.Page.Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME] = this.StatisticName;

		if (this.StatisticTree.Count > 0)
			this.Page.Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE] = this.StatisticTree;

		if (this.MinQuantizedValue != Decimal.MinValue)
			this.Page.Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] = this.MinQuantizedValue;

		if (this.MaxQuantizedValue != Decimal.MinValue)
			this.Page.Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] = this.MaxQuantizedValue;

		if (!string.IsNullOrEmpty(this.SolutionState))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] = this.SolutionState;

		if (!string.IsNullOrEmpty(this.SolutionStateNames))
			this.Page.Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE_NAME] = this.SolutionStateNames;
	}
}
