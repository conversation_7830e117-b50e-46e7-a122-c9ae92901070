using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using QueueServiceClient;

public class QueueServiceSource
{
	public static List<ErrorResult> GetErrorList()
	{
		return QueueErrors.GetErrorList();
	}
}

