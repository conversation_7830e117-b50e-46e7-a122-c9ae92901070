using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class PopupMessage : System.Web.UI.Page
{
	public int MessageId
	{
		get { if (this.ViewState["m"] != null) return (int)this.ViewState["m"]; else return 0; }
		set { this.ViewState["m"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!string.IsNullOrEmpty(Request.Params["m"]))
		{
			this.MessageId = Convert.ToInt32(Request.Params["m"]);
			LoadMessage();
		}
    }

	private void LoadMessage()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadMessage", this.MessageId);

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				dateLabel.Text = DataFormatter.FormatDate(row, "LastModified", "M/dd/yyyy h:mmtt", "");
				subjectLabel.Text = DataFormatter.getString(row, "MessageSubject");
				bodyLabel.Text = DataFormatter.Format(row, "MessageBody", "" ,true, true);

				if (DataFormatter.getBool(row, "IsUrgent"))
					AlertImg.Visible = true;
				else
					AlertImg.Visible = false;
			}
		}
	}
}
