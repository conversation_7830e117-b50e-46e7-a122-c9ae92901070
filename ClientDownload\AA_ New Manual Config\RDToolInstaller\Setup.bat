::Start of program, hide commands and clear screen.
ECHO OFF
CLS 

ECHO This installer will install the following programs...
ECHO     .NET Framework 3.5 SP1
ECHO     .NET Framework 4.0
ECHO     WinZip 11.2
ECHO     WinZip Command Line 23
ECHO     RDTool *********
ECHO     Uploader 3.0
ECHO.
ECHO If .NET requires the machine to reboot after installation, please do so
<PERSON>HO and run this installation file again once reboot has completed.
ECHO Press 1 then enter, to continue.
ECHO Press Q then enter, to quit.
ECHO.

:LOOP
:: SET /P prompts for input and sets the variable to whatever the user types
SET /P Choice=
:: Grab substring of input starting at 0 and 1 character long
IF NOT '%Choice%'=='' SET Choice=%Choice:~0,1%
ECHO.
IF /I '%Choice%'=='1' GOTO INSTALL_START
IF /I '%Choice%'=='Q' EXIT
ECHO "%Choice%" is not valid. Please try again.
ECHO.
GOTO LOOP

:INSTALL_START
ECHO Installing the .NET Framework 3.5 SP1...
"%~dp0data\dotnetfx35.exe" /qb
ECHO .NET Framework 3.5 SP1 installation complete.
ECHO.

ECHO Installing the .NET Framework 4.0...
"%~dp0data\dotNetFx40.exe" /qb
ECHO .NET Framework 4.0 installation complete.
ECHO.

ECHO Installing WinZip...
msiexec /I "%~dp0data\winzip11.2\winzip112.msi" /qb
ECHO WinZip installation complete.
ECHO.

ECHO Installing WinZip Command Line...
"%~dp0data\winzip11.2\wzcline23.exe" /qb
ECHO WinZip Command Line installation complete.
ECHO.

ECHO Running WinZip registration file...
"%~dp0data\winzip11.2\WinZip.wzmul"
ECHO WinZip registration complete.
ECHO.

ECHO Installing RDTool...
ECHO During installion, please change the install directory to C:\Program Files\Diebold\AMI\RDTool
PaUSE
"%~dp0data\RDToolAMI_v*********.exe" /qb
ECHO RDTool installation complete.
ECHO.

ECHO Installing Uploader...
"%~dp0data\Uploader\Uploader.msi"
ECHO Uploader installation complete.
ECHO.

ECHO Creating directory c:\RDToolEngineeringZips...
md "C:\RDToolEngineeringZips"
ECHO.

ECHO Creating directory C:\Program Files\Diebold\AMI\UploadStaging...
md "C:\Program Files\Diebold\AMI\UploadStaging"
ECHO.

ECHO Copying supporing files into RDTool Directory
copy "%~dp0data\SupportingFiles\*.*" "C:\Program Files\Diebold\AMi\RDTool\"
ECHO.

ECHO Creating directory C:\Windows\System32\cvirte...
md "C:\Windows\System32\cvirte"
ECHO.

ECHO Creating directory C:\Windows\System32\cvirte\bin...
md "C:\Windows\System32\cvirte\bin"
ECHO.

ECHO Creating directory C:\Windows\System32\cvirte\fonts...
md "C:\Windows\System32\cvirte\fonts"
ECHO.

ECHO Copying cvirte bin files into Windows\System32\cvirte\bin Directory
copy "%~dp0data\cvirte\bin\*.*" "C:\Windows\System32\cvirte\bin"
ECHO.

ECHO Copying cvirte fonts into Windows\System32\cvirte\fonts Directory
copy "%~dp0data\cvirte\fonts\*.*" "C:\Windows\System32\cvirte\fonts"
ECHO.

ECHO Installation has completed successfully.
PAUSE 
EXIT
