using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class EditSolutionState : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_SolutionState", true);
		DataGrid1.DataBind();

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
	}
}
