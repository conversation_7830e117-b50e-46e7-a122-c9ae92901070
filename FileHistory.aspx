﻿<%@ Page Title="File History" Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="FileHistory.aspx.cs" Inherits="FileHistory" %>
<%@ Register tagprefix="tagprefix" Namespace="QueueServiceClient" %>

<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">
	<asp:panel id="DefaultPanel" runat="server" >
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">File History</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;"></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;"><asp:label runat="server" id="fileNameLabel"></asp:label></div>
					<br />
					<asp:repeater id="fileHistoryRep" runat="server">
						<headertemplate>
							<table width="100%" border="0" cellpadding="10">
						</headertemplate>
						<itemtemplate>
							<tr>
								<td class="repeaterItemAlt" style="line-height:20px;">Commited by <b><%# ((SourceControlHistoryInfo)Container.DataItem).Author %></b> on <b><%# ((SourceControlHistoryInfo)Container.DataItem).CommitDate.ToString("MM/dd/yyyy") %></b> with comments:</td>
								<td class="repeaterItemAlt" style="line-height:20px;"><div class='goButton'><asp:linkbutton runat="server" id="downloadBtn" oncommand="DownloadBtn_Click" commandargument='<%# ((SourceControlHistoryInfo)Container.DataItem).RepoKey %>'>Download File</asp:linkbutton></div></td>
							</tr>
							<tr>
								<td class="repeaterItemAlt" style="line-height:20px;" colspan="2"><%# ((SourceControlHistoryInfo)Container.DataItem).Comment %></td>
							</tr>
						</itemtemplate>
						<alternatingitemtemplate>
							<tr>
								<td class="repeaterItem" style="line-height:20px;">Commited by <b><%# ((SourceControlHistoryInfo)Container.DataItem).Author %></b> on <b><%# ((SourceControlHistoryInfo)Container.DataItem).CommitDate.ToString("MM/dd/yyyy") %></b> with comments:</td>
								<td class="repeaterItem" style="line-height:20px;"><div class='goButton'><asp:linkbutton runat="server" id="downloadBtn" oncommand="DownloadBtn_Click" commandargument='<%# ((SourceControlHistoryInfo)Container.DataItem).RepoKey %>'>Download File</asp:linkbutton></div></td>
							</tr>
							<tr>
								<td class="repeaterItem" style="line-height:20px;" colspan="2"><%# ((SourceControlHistoryInfo)Container.DataItem).Comment %></td>
							</tr>
						</alternatingitemtemplate>
						<footertemplate>
							</table>
						</footertemplate>
					</asp:repeater>
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

