﻿<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="JiraSyncLog.aspx.cs" Inherits="JiraSyncLog" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= DataGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
</script>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Jira Sync Log</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">
                        <table cellpadding="0" cellspacing="0" border="0">
                            <tr>
								<td style="width:10px;">&nbsp;</td>
								<td style="width:107px;">
									<div class="goButtonTop"><a href="JiraSyncSchedule.aspx">Schedule Sync</a></div>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<telerik:radgrid id="DataGrid1" allowmultirowselection="false" 
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="gridItem" />
					<alternatingitemstyle cssclass="gridItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings resizing-allowcolumnresize="true">
						<selecting allowrowselect="false" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="SyncId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="# Items" sortexpression="ObservationCount" uniquename="ObservationCount">
								<headerstyle width="10%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "ObservationCount", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Schedule" sortexpression="SessionName" uniquename="SessionName">
								<headerstyle width="10%" />
								<itemtemplate>
									<a href="EditJiraSyncSchedule.aspx?jsi=<%# DataFormatter.Format(Container.DataItem, "ScheduleId", "")%>"><%# DataFormatter.Format(Container.DataItem, "SessionName", "")%></a>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Start" sortexpression="StartDate" uniquename="StartDate">
								<headerstyle width="10%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "StartDate", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="End" sortexpression="EndDate" uniquename="EndDate">
								<headerstyle width="10%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "EndDate", "")%>&nbsp;
								</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Complete" sortexpression="ItemCount" uniquename="ItemCount">
								<headerstyle width="10%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "ItemCount", "")%>&nbsp;
								</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Run Time (seconds)" sortexpression="SecondsRunTime" uniquename="SecondsRunTime">
								<headerstyle width="10%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "SecondsRunTime", "")%>&nbsp;
								</itemtemplate>
							</telerik:gridtemplatecolumn>
                            <telerik:gridtemplatecolumn headertext="Status" sortexpression="Status" uniquename="Status">
								<headerstyle width="45%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "Status", "")%>&nbsp;
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><a href="<%# string.Format("JiraSyncItemLog.aspx?{0}={1}&page={2}", DieboldConstants.JIRA_SYNC_ID_KEY, DataFormatter.Format(Container.DataItem, "SyncId", ""), DataGrid1.CurrentPageIndex) %>">Items</a></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>
			</div>
		</td>
	</tr>
</table>

</asp:Content>


