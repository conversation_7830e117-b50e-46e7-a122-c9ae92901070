<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Statistics.aspx.cs" Inherits="Statistics" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= DataGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
</script>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Statistics</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">
						<table cellpadding="0" cellspacing="0" border="0">
							<tr>
								<td style="width: 65px;">
									<div class="goButtonTop">
										<asp:linkbutton runat="server" id="exportButton" onclick="ExportButton_Click">Export</asp:linkbutton></div>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<%--<div style="padding:14px 14px 0px 14px;"><div class="goButton"><a runat="server" id="NewFieldButton">Add Field</a></div></div>--%>
				
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td id="LibraryNameSelection" runat="server">
							<div class="title">Entity Type</div>
							<telerik:radcombobox id="EntityList" runat="server" width="225px" allowcustomtext="false" markfirstmatch="true" enablescreenboundarydetection="true"
								 appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" autopostback="true"
								 onselectedindexchanged="EntityList_SelectedIndexChanged">
							</telerik:radcombobox>
						</td>
					</tr>	
				</table>
				<br />
				
				<telerik:radgrid id="DataGrid1" allowmultirowselection="false" onitemdatabound="DataGrid1_OnItemDataBound"
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="gridItem" />
					<alternatingitemstyle cssclass="gridItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings resizing-allowcolumnresize="true">
						<selecting allowrowselect="false" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="FieldId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridboundcolumn datafield="DeviceTypeName" headertext="Device Type" sortexpression="DeviceTypeName" uniquename="DeviceTypeName"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="SerialNumber" headertext="Device" sortexpression="SerialNumber" uniquename="SerialNumber"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="FieldName" headertext="Field Name" sortexpression="FieldName" uniquename="FieldName"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="FieldOptionName" headertext="Field Option" sortexpression="FieldOptionName" uniquename="FieldOptionName"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="FileNumber" headertext="File Number" sortexpression="FileNumber" uniquename="FileNumber"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="MediaNumber" headertext="Media Number" sortexpression="MediaNumber" uniquename="MediaNumber"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="CommandNumber" headertext="Command Number" sortexpression="CommandNumber" uniquename="CommandNumber"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="SequenceNumber" headertext="Sequence Number" sortexpression="SequenceNumber" uniquename="SequenceNumber"></telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="EventTypeName" headertext="Event Type" sortexpression="EventTypeName" uniquename="EventTypeName"></telerik:gridboundcolumn>
							<telerik:gridtemplatecolumn headertext="Data Value" sortexpression="DataValue" uniquename="DataValue">
								<itemtemplate>
									<%# DataFormatter.FormatDecimal(Container.DataItem, "DataValue", "#,##0.##", "0") %>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Quantized Value" sortexpression="QuantizedValue" uniquename="QuantizedValue">
								<itemtemplate>
									<%# DataFormatter.FormatDecimal(Container.DataItem, "QuantizedValue", "#,##0.##", "0") %>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>	
			</div>
		</td>
	</tr>
</table>



</asp:Content>

