using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditFieldEntityVersion : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public int VersionMappingId
	{
		get { if (this.ViewState["v"] != null) return (int)this.ViewState["v"]; else return 0; }
		set { this.ViewState["v"] = value; }
	}

	public string FieldId
	{
		get { return (string)this.ViewState["f"]; }
		set { this.ViewState["f"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FIELD_ID_KEY]))
		{
			this.FieldId = Request.Params[DieboldConstants.FIELD_ID_KEY];
			this.FieldIdLabel.Text = this.FieldId;
		}

		BindData();
	}

	private void BindData()
	{
		if (!Page.IsPostBack)
		{
			//Page Number is for redirecting back.
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];
		}

		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFieldEntityVersions", this.FieldId);

		if (ds.Tables != null && ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
			FieldNameLabel.Text = DataFormatter.getString(ds.Tables[0].Rows[0], "FieldName");
		
		DataGrid1.DataSource = ds;
		DataGrid1.DataBind();
	}

	protected void EditButton_Command(object sender, CommandEventArgs e)
	{
		ErrorLabel.Text = "";
		if (e.CommandArgument != null && !string.IsNullOrEmpty(e.CommandArgument.ToString()))
		{
			DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFieldEntityVersionById", e.CommandArgument.ToString());
			DataRow row = null;

			if (ds.Tables != null && ds.Tables[0] != null && ds.Tables[0].Rows[0] != null)
				row = ds.Tables[0].Rows[0];

			if (row != null)
			{
				this.VersionMappingId = DataFormatter.getInt32(row, "VersionMappingId");
				this.EntityIdField.Text = DataFormatter.getInt32(row, "EntityId").ToString();
				this.MinVersionField.Text = VersionNumber.ConverToString(DataFormatter.getInt64(row, "MinVersion"));
				
				if (DataFormatter.getInt64(row, "MaxVersion") != 0)
					this.MaxVersionField.Text = VersionNumber.ConverToString(DataFormatter.getInt64(row, "MaxVersion"));

				EditDiv.Visible = true;
			}
		}
	}

	protected void DeleteButton_Command(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null && !string.IsNullOrEmpty(e.CommandArgument.ToString()))
		{
			SqlHelper.ExecuteNonQuery("RPT_DeleteFieldEntityVersion", e.CommandArgument.ToString());

			this.VersionMappingId = 0;
			EditDiv.Visible = false;
			BindData();
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		object maxV = null;
		if (!string.IsNullOrEmpty(this.MaxVersionField.Text.Trim()))
			maxV = VersionNumber.ConverToInt64(this.MaxVersionField.Text.Trim());
		
		//Validate Min and Max across all other matching Entities
		DataSet ds = SqlHelper.ExecuteDataset("RPT_ValidateFieldEntityVersions", this.VersionMappingId, this.FieldId, EntityIdField.Text.Trim(), VersionNumber.ConverToInt64(MinVersionField.Text.Trim()), maxV);
		if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
		{
			ErrorLabel.Text = string.Format("Overlap of Min & Max Version within Field: {0}", DataFormatter.getString(ds.Tables[0].Rows[0], "FieldName"));
		}
		else
		{
			if (this.VersionMappingId == 0)
				SqlHelper.ExecuteNonQuery("RPT_InsertFieldEntityVersion", this.FieldId, EntityIdField.Text.Trim(), VersionNumber.ConverToInt64(MinVersionField.Text.Trim()), maxV);
			else
				SqlHelper.ExecuteNonQuery("RPT_UpdateFieldEntityVersion", this.VersionMappingId, this.FieldId, EntityIdField.Text.Trim(), VersionNumber.ConverToInt64(MinVersionField.Text.Trim()), maxV);

			this.VersionMappingId = 0;
			EditDiv.Visible = false;
			BindData();
		}
	}

	protected void AddVersion_Click(object sender, EventArgs e)
	{
		ErrorLabel.Text = "";
		EditDiv.Visible = true;
		this.VersionMappingId = 0;
		this.EntityIdField.Text = "";
		this.MinVersionField.Text = "";
		this.MaxVersionField.Text = "";

		this.EntityIdField.Focus();
	}

	protected void CancelButton_Click(object sender, EventArgs e)
	{
		this.VersionMappingId = 0;
		EditDiv.Visible = false;
	}

	protected void BackButton_Click(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(this.PageNo))
			Response.Redirect(string.Format("Popup_EditField.aspx?{0}={1}&page={2}", DieboldConstants.FIELD_ID_KEY, this.FieldId, this.PageNo));
		else
			Response.Redirect(string.Format("Popup_EditField.aspx?{0}={1}", DieboldConstants.FIELD_ID_KEY, this.FieldId));
	}
}
