<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="SessionArchive.aspx.cs" Inherits="SessionArchive" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= DataGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
</script>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Archive Data</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<%--<div style="padding:14px;"><div class="goButton"><a onclick="window.radopen('Popup_EditFailureType.aspx', null); return false;">Add Failure Type</a></div></div>--%>
				<div class="title">Session Resource Usage</div>
				<telerik:radgrid id="DataGrid1" allowmultirowselection="false" 
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="gridItem" />
					<alternatingitemstyle cssclass="gridItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings resizing-allowcolumnresize="true">
						<selecting allowrowselect="false" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="SessionId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="Session Id" sortexpression="SessionId" uniquename="SessionId">
								<headerstyle width="12%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "SessionId", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Session Name" sortexpression="SessionName" uniquename="SessionName">
								<headerstyle width="30%" />
								<itemtemplate>
									<asp:label id="sessionNameLabel" runat="server" tooltip='<%# DataFormatter.Format(Container.DataItem, "SessionName", "") %>'><%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "SessionName", ""), 26, 32) %></asp:label>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="% Database Space" sortexpression="SessionDiskSpace" uniquename="SessionDiskSpace">
								<headerstyle width="20%" />
								<itemtemplate>
									<div style="background-color:#cfc9c5; width:100%; border:solid 1px #0077cc;">
										<div style="background-color:#0077cc; color:#ffffff; font-weight:bold; padding:2px 0px 2px 1px; width:<%# Math.Round(((double)100 * ((int)DataBinder.Eval(Container.DataItem, "SessionDiskSpace")) / ((int)DataBinder.Eval(Container.DataItem, "MaxSessionDiskSpace"))), 0).ToString() %>%">
											<%# Math.Round((Convert.ToDecimal(DataFormatter.Format(Container.DataItem, "SessionDiskSpace", "")) / Convert.ToDecimal(DataFormatter.Format(Container.DataItem, "TotalDiskSpace", "")) * 100), 2) + "%"%>
										</div> 
									</div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Database Space" sortexpression="SessionDiskSpace" uniquename="SessionDiskSpace">
								<headerstyle width="18%" />
								<itemtemplate>
									<%# FormatByte(DataFormatter.Format(Container.DataItem, "SessionDiskSpace", "")) %>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Status" sortexpression="SessionStatusName" uniquename="SessionStatusName">
								<headerstyle width="10%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "SessionStatusName", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><asp:linkbutton runat="server" id="editLink" postbackurl='<%# string.Format("SessionArchiveEdit.aspx?{0}={1}&page={2}", DieboldConstants.SESSION_ID_KEY, DataFormatter.Format(Container.DataItem, "SessionId", ""), DataGrid1.CurrentPageIndex) %>'>Edit</asp:linkbutton></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>
				
			</div>
		</td>
	</tr>
</table>

</asp:Content>

