using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class controls_DashItem_UserInfo : System.Web.UI.UserControl, IDashboardItem
{
	protected void Page_Load(object sender, EventArgs e)
	{
		string userName = Utility.GetUserName();
		if (userName.Contains("\\"))
		{
			string[] nameParts = userName.Split(new string[] { "\\" }, StringSplitOptions.RemoveEmptyEntries);
			userNameLbl.Text = nameParts[nameParts.Length - 1];
		}
		else
		{
			userNameLbl.Text = userName;
		}

		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadDashboard", userName);
		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				lastLoginDate.Text = DataFormatter.FormatDate(row, "LastLoginDate", "MM/dd/yyyy h:mm tt", "");
			}
		}
	}

	public void Initialize(Guid dockUniqueName, bool useMasterUser)
	{

	}

	public void DisplayEdit()
	{
	}

	public void DisplayContent()
	{
	}


}
