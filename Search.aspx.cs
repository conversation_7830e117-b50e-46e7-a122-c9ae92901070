using System;
using System.Data;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;
using QueueServiceClient;
using System.ComponentModel;

public partial class Search : System.Web.UI.Page {
    public bool IncludePastSessions
    {
        get { if (this.ViewState["i"] != null) return (bool)this.ViewState["i"]; else return false; }
        set { this.ViewState["i"] = value; }
    }

    public bool IsUserAdmin
    {
        get { if (this.ViewState["ia"] != null) return (bool)this.ViewState["ia"]; else return false; }
        set { this.ViewState["ia"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e) {
        if (!Page.IsPostBack) {
            this.IsUserAdmin = Utility.IsUserAdmin();
            newObserDiv.Visible = this.IsUserAdmin;

            sessionList.DataSource = Utility.GetActiveSessionsList();
            searchField.Focus();

            // Key "cs" passed in on the Query String from the main nav button to clear past search filters from the session
            if (!string.IsNullOrEmpty(Request["cs"]) && Request["cs"].Equals("1"))
                ClearSession();

            this.DataBind();
            LoadAdvancedSearchBar();
        }
    }

    protected void SearchButton_Click(object sender, EventArgs e) {
        //only allow mass edit of observations
        massEditButtonContainer.Visible = onlyShowObservationsCheck.Checked;

        SaveSearchParamsToSession();

        RadGrid1.DataBind();
        searchField.Focus();
    }

    protected void DownloadEngBtn_Click(object sender, EventArgs e) {
        ExportDataFiles(false);
    }

    protected void DownloadXMLBtn_Click(object sender, EventArgs e) {
        ExportDataFiles(true);
    }

    private void ExportDataFiles(bool getXMLFiles) {
        List<Int64> tranIdList = new List<Int64>();
        List<Int64> observationIdList = new List<Int64>();
        List<string> filePatternList = new List<string>();
        List<string> deviceTypeList = new List<string>();
        List<string> serialNumberList = new List<string>();
        List<string> cellNameList = new List<string>();
        List<string> sessionNameList = new List<string>();
        List<DateTime> tranDateList = new List<DateTime>();

        DataSet ds = GetCurrentSearchDataSet(true, false);

        if (ds.Tables[0].Rows.Count > 0) {
            Dictionary<Int64, Int64> tranLookup = new Dictionary<Int64, Int64>();
            foreach (DataRow row in ds.Tables[0].Rows) {
                Int64 tranId = DataFormatter.getInt64(row, "TranId");
                if (!tranLookup.ContainsKey(tranId)) {
                    tranIdList.Add(tranId);
                    observationIdList.Add(DataFormatter.getInt64(row, "ObservationId"));
                    deviceTypeList.Add(DataFormatter.getString(row, "DeviceTypeName"));
                    serialNumberList.Add(DataFormatter.getString(row, "SerialNumber"));
                    cellNameList.Add(DataFormatter.getString(row, "CellName"));
                    sessionNameList.Add(DataFormatter.getString(row, "SessionName"));
                    tranDateList.Add(DataFormatter.getDateTime(row, "TranDate"));
                    filePatternList.Add("CELL=" + DataFormatter.getInt32(row, "CellId").ToString() + "_SESSION=" + DataFormatter.getInt32(row, "SessionId").ToString() + "_TRX=" + DataFormatter.getInt32(row, "RDToolTranId").ToString());
                    tranLookup.Add(tranId, tranId);
                }
            }

            byte[] result = TxFiles.DownloadZipCollection(getXMLFiles, filePatternList, tranIdList, observationIdList, cellNameList, sessionNameList, tranDateList, deviceTypeList, serialNumberList);
            if (result != null && result.Length > 0) {
                Response.ClearContent();
                Response.ClearHeaders();
                Response.ContentType = "application/octet-stream";
                Response.AppendHeader("content-disposition", "attachment; filename=ReliabilityFiles_" + DateTime.Now.Day + DateTime.Now.Month + DateTime.Now.Year + DateTime.Now.Hour + DateTime.Now.Minute + DateTime.Now.Second + ".zip;");
                Response.BinaryWrite(result);
                Response.End();
            }
        }
    }

    private void SaveSearchParamsToSession() {
        if (!string.IsNullOrEmpty(this.searchField.Text))
            Session[DieboldConstants.ADV_SEARCH_TEXT] = this.searchField.Text;
        else
            ClearSessionItem(DieboldConstants.ADV_SEARCH_TEXT);

        if (!string.IsNullOrEmpty(this.sessionList.SelectedValue))
            Session[DieboldConstants.ADV_SEARCH_SESSION] = Utility.ConvertListBoxSelectionsToString(sessionList.Items, true);
        else
            ClearSessionItem(DieboldConstants.ADV_SEARCH_SESSION);

        if (this.startDateField.SelectedDate != null && (DateTime)this.startDateField.SelectedDate != DateTime.MinValue) {
            TimeSpan time = TimeSpan.Zero;

            if (startTimeField.SelectedDate != null && startTimeField.SelectedDate != DateTime.MinValue)
                time = new TimeSpan(((DateTime)startTimeField.SelectedDate).Hour, ((DateTime)startTimeField.SelectedDate).Minute, ((DateTime)startTimeField.SelectedDate).Second);

            DateTime startDate = ((DateTime)startDateField.SelectedDate).Add(time);
            Session[DieboldConstants.ADV_SEARCH_START_DATE] = startDate;
        }
        else {
            ClearSessionItem(DieboldConstants.ADV_SEARCH_START_DATE);
        }

        if (this.endDateField.SelectedDate != null && (DateTime)this.endDateField.SelectedDate != DateTime.MinValue) {
            TimeSpan time = TimeSpan.Zero;

            if (endTimeField.SelectedDate != null && endTimeField.SelectedDate != DateTime.MinValue)
                time = new TimeSpan(((DateTime)endTimeField.SelectedDate).Hour, ((DateTime)endTimeField.SelectedDate).Minute, ((DateTime)endTimeField.SelectedDate).Second);

            DateTime endDate = ((DateTime)endDateField.SelectedDate).Add(time);
            Session[DieboldConstants.ADV_SEARCH_END_DATE] = endDate;
        }
        else {
            ClearSessionItem(DieboldConstants.ADV_SEARCH_END_DATE);
        }

        if (!string.IsNullOrEmpty(this.trxIdStartField.Text))
            Session[DieboldConstants.ADV_SEARCH_TRANSACTION_START] = this.trxIdStartField.Text;
        else
            ClearSessionItem(DieboldConstants.ADV_SEARCH_TRANSACTION_START);

        if (!string.IsNullOrEmpty(this.trxIdEndField.Text))
            Session[DieboldConstants.ADV_SEARCH_TRANSACTION_END] = this.trxIdEndField.Text;
        else
            ClearSessionItem(DieboldConstants.ADV_SEARCH_TRANSACTION_END);

        if (!this.onlyShowObservationsCheck.Checked)
            Session[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY] = this.onlyShowObservationsCheck.Checked;
        else
            ClearSessionItem(DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY);
    }

    protected void IncludePastSessions_Toggle(object sender, EventArgs e) {
        if (this.IncludePastSessions) {
            sessionList.Items.Clear();
            sessionList.DataSource = Utility.GetActiveSessionsList();
            sessionList.DataBind();

            this.IncludePastSessions = false;
            this.hidIncludeClosed.Value = "false";
            includePastSeriesLink.Visible = true;
            excludePastSeriesLink.Visible = false;
            this.sessionsLabel.Text = "Active Sessions";
            PastSessionToggleButton.Attributes.Add("class", "addButtonTop");
        }
        else {
            sessionList.Items.Clear();
            sessionList.DataSource = Utility.GetSessionsList();
            sessionList.DataBind();

            this.IncludePastSessions = true;
            this.hidIncludeClosed.Value = "true";
            includePastSeriesLink.Visible = false;
            excludePastSeriesLink.Visible = true;
            this.sessionsLabel.Text = "All Sessions";
            PastSessionToggleButton.Attributes.Add("class", "cancelButtonTop");
        }
    }

    protected void AdvancedSearch_Click(object sender, EventArgs e) {
        massEditButtonContainer.Visible = onlyShowObservationsCheck.Checked;

        SaveSearchParamsToSession();
        Response.Redirect("AdvancedSearch.aspx");
    }

    private void ClearSession() {
        Utility.ClearSearchSession(false);
        LoadAdvancedSearchBar();
    }

    protected void ClearSessionValue_Command(object sender, CommandEventArgs e) {
        if (e.CommandArgument != null && !string.IsNullOrEmpty(e.CommandArgument.ToString())) {
            ClearSessionItem(e.CommandArgument.ToString());
        }
        LoadAdvancedSearchBar();
    }

    private void ClearSessionItem(string sessionCode) {
        switch (sessionCode) {
            case DieboldConstants.ADV_SEARCH_TEXT:
                Session[DieboldConstants.ADV_SEARCH_TEXT] = null;
                this.searchField.Text = "";
                break;
            case DieboldConstants.ADV_SEARCH_SESSION:
                Session[DieboldConstants.ADV_SEARCH_SESSION] = null;
                Session[DieboldConstants.ADV_SEARCH_SESSION_NAME] = null;
                this.sessionList.SelectedIndex = -1;
                break;
            case DieboldConstants.ADV_SEARCH_START_DATE:
                Session[DieboldConstants.ADV_SEARCH_START_DATE] = null;
                this.startDateField.SelectedDate = null;
                break;
            case DieboldConstants.ADV_SEARCH_END_DATE:
                Session[DieboldConstants.ADV_SEARCH_END_DATE] = null;
                this.endDateField.SelectedDate = null;
                break;
            case DieboldConstants.ADV_SEARCH_TRANSACTION_START:
                Session[DieboldConstants.ADV_SEARCH_TRANSACTION_START] = null;
                this.trxIdStartField.Text = "";
                break;
            case DieboldConstants.ADV_SEARCH_TRANSACTION_END:
                Session[DieboldConstants.ADV_SEARCH_TRANSACTION_END] = null;
                this.trxIdEndField.Text = "";
                break;
            case DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY:
                Session[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY] = null;
                this.onlyShowObservationsCheck.Checked = true;
                break;
            case DieboldConstants.ADV_SEARCH_CELL:
                Session[DieboldConstants.ADV_SEARCH_CELL] = null;
                Session[DieboldConstants.ADV_SEARCH_CELL_NAME] = null;
                adv_CellDiv.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_DEVICE_TYPE:
                Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] = null;
                Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE_NAME] = null;
                adv_DeviceTypeDiv.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_DEVICE:
                Session[DieboldConstants.ADV_SEARCH_DEVICE] = null;
                Session[DieboldConstants.ADV_SEARCH_DEVICE_NAME] = null;
                adv_DeviceDiv.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_FAILURE_LOCATION:
                Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] = null;
                Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION_NAME] = null;
                adv_FailureLocationDiv.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_FAILURE_TYPE:
                Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] = null;
                Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE_NAME] = null;
                adv_FailureTypeDiv.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_MODULE_TYPE:
                Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] = null;
                Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE_NAME] = null;
                adv_ModuleTypeDiv.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA:
                Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] = null;
                Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA_NAME] = null;
                adv_InvestigationArea.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_TRIAGE_TYPE:
                Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] = null;
                Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE_NAME] = null;
                adv_TriageType.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_DISCIPLINE:
                Session[DieboldConstants.ADV_SEARCH_DISCIPLINE] = null;
                Session[DieboldConstants.ADV_SEARCH_DISCIPLINE_NAME] = null;
                adv_Discipline.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_OWNER:
                Session[DieboldConstants.ADV_SEARCH_OWNER] = null;
                Session[DieboldConstants.ADV_SEARCH_OWNER_NAME] = null;
                adv_Owner.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_OPERATOR:
                Session[DieboldConstants.ADV_SEARCH_OPERATOR] = null;
                Session[DieboldConstants.ADV_SEARCH_OPERATOR_NAME] = null;
                adv_Operator.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_EVENT:
                Session[DieboldConstants.ADV_SEARCH_EVENT] = null;
                Session[DieboldConstants.ADV_SEARCH_EVENT_NAME] = null;
                adv_Event.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_MEDIA_NUMBER:
                Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] = null;
                adv_MediaNumber.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_FILE_NUMBER:
                Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER] = null;
                adv_FileNumber.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_COMMAND_NUMBER:
                Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] = null;
                adv_CommandNumber.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_STATISTIC_NAME:
                Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] = null;
                Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] = null;
                Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME] = null;
                Session[DieboldConstants.ADV_SEARCH_STATISTIC_TREE] = null;
                adv_Statistic.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_SETTING:
                Session[DieboldConstants.ADV_SEARCH_SETTING] = null;
                Session[DieboldConstants.ADV_SEARCH_SETTING_NAME] = null;
                Session[DieboldConstants.ADV_SEARCH_SETTING_TREE] = null;
                adv_Setting.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_SETTING2:
                Session[DieboldConstants.ADV_SEARCH_SETTING2] = null;
                Session[DieboldConstants.ADV_SEARCH_SETTING_NAME2] = null;
                Session[DieboldConstants.ADV_SEARCH_SETTING_TREE2] = null;
                adv_Setting2.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_SETTING3:
                Session[DieboldConstants.ADV_SEARCH_SETTING3] = null;
                Session[DieboldConstants.ADV_SEARCH_SETTING_NAME3] = null;
                Session[DieboldConstants.ADV_SEARCH_SETTING_TREE3] = null;
                adv_Setting3.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE:
                Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] = null;
                adv_MinQuantizedValue.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE:
                Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] = null;
                adv_MaxQuantizedValue.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE:
                Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] = null;
                adv_projectedStartDate.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE:
                Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] = null;
                adv_projectedEndDate.Visible = false;
                break;
            case DieboldConstants.ADV_SEARCH_SOLUTION_STATE:
                Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] = null;
                Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE_NAME] = null;
                adv_SolutionStateDiv.Visible = false;
                break;
        }
    }

    private void LoadAdvancedSearchBar() {
        bool isBarVisible = false;

        if (Session[DieboldConstants.ADV_SEARCH_TEXT] != null)
            this.searchField.Text = Session[DieboldConstants.ADV_SEARCH_TEXT].ToString();

        if (Session[DieboldConstants.ADV_SEARCH_SESSION] != null)
            Utility.SetListBoxSelectedValuesFromString(this.sessionList, Session[DieboldConstants.ADV_SEARCH_SESSION].ToString());

        if (Session[DieboldConstants.ADV_SEARCH_START_DATE] != null) {
            this.startDateField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_START_DATE]);
            this.startTimeField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_START_DATE]);
        }

        if (Session[DieboldConstants.ADV_SEARCH_END_DATE] != null) {
            this.endDateField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_END_DATE]);
            this.endTimeField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_END_DATE]);
        }

        if (Session[DieboldConstants.ADV_SEARCH_TRANSACTION_START] != null)
            this.trxIdStartField.Text = Session[DieboldConstants.ADV_SEARCH_TRANSACTION_START].ToString();
        if (Session[DieboldConstants.ADV_SEARCH_TRANSACTION_END] != null)
            this.trxIdEndField.Text = Session[DieboldConstants.ADV_SEARCH_TRANSACTION_END].ToString();
        if (Session[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY] != null)
            this.onlyShowObservationsCheck.Checked = Convert.ToBoolean(Session[DieboldConstants.ADV_SEARCH_OBSERVATIONS_ONLY].ToString());

        if (Session[DieboldConstants.ADV_SEARCH_CELL_NAME] != null) {
            adv_CellDiv.Visible = true;
            adv_cellLabel.Text = Session[DieboldConstants.ADV_SEARCH_CELL_NAME].ToString();
            isBarVisible = true;
        }
        // DO NOT SHOW - DO NOT FILTER ON THIS VALUE TO ALLOW TRANSACTIONS TO BE INCLUDED IN SEARCH RESULTS
        //if (Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE_NAME] != null)
        //{
        //adv_DeviceTypeDiv.Visible = true;
        //adv_deviceTypeLabel.Text = Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE_NAME].ToString();
        //isBarVisible = true;
        //}
        if (Session[DieboldConstants.ADV_SEARCH_DEVICE_NAME] != null) {
            adv_DeviceDiv.Visible = true;
            adv_deviceLabel.Text = Session[DieboldConstants.ADV_SEARCH_DEVICE_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION_NAME] != null) {
            adv_FailureLocationDiv.Visible = true;
            adv_failureLocationLabel.Text = Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE_NAME] != null) {
            adv_FailureTypeDiv.Visible = true;
            adv_failureTypeLabel.Text = Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] != null) {
            adv_ModuleTypeDiv.Visible = true;
            adv_ModuleTypeLabel.Text = Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA_NAME] != null) {
            adv_InvestigationArea.Visible = true;
            adv_investigationAreaLabel.Text = Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE_NAME] != null) {
            adv_TriageType.Visible = true;
            adv_TriageTypeLabel.Text = Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_DISCIPLINE_NAME] != null) {
            adv_Discipline.Visible = true;
            adv_DisciplineLabel.Text = Session[DieboldConstants.ADV_SEARCH_DISCIPLINE_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_OWNER_NAME] != null) {
            adv_Owner.Visible = true;
            adv_ownerLabel.Text = Session[DieboldConstants.ADV_SEARCH_OWNER_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_OPERATOR_NAME] != null) {
            adv_Operator.Visible = true;
            adv_operatorLabel.Text = Session[DieboldConstants.ADV_SEARCH_OPERATOR_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] != null) {
            adv_projectedStartDate.Visible = true;
            adv_projectedStartDateLabel.Text = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE]).ToString("MM/dd/yyyy hh:mm tt");
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] != null) {
            adv_projectedEndDate.Visible = true;
            adv_projectedEndDateLabel.Text = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE]).ToString("MM/dd/yyyy hh:mm tt");
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_EVENT_NAME] != null) {
            adv_Event.Visible = true;
            adv_eventLabel.Text = Session[DieboldConstants.ADV_SEARCH_EVENT_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] != null) {
            adv_MediaNumber.Visible = true;
            adv_mediaNumberLabel.Text = Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER] != null) {
            adv_FileNumber.Visible = true;
            adv_fileNumberLabel.Text = Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] != null) {
            adv_CommandNumber.Visible = true;
            adv_commandNumberLabel.Text = Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME] != null) {
            adv_Statistic.Visible = true;
            adv_statisticLabel.Text = Session[DieboldConstants.ADV_SEARCH_STATISTIC_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_SETTING_NAME] != null) {
            adv_Setting.Visible = true;
            adv_settingLabel.Text = Session[DieboldConstants.ADV_SEARCH_SETTING_NAME].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_SETTING_NAME2] != null) {
            adv_Setting2.Visible = true;
            adv_settingLabel2.Text = Session[DieboldConstants.ADV_SEARCH_SETTING_NAME2].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_SETTING_NAME3] != null) {
            adv_Setting3.Visible = true;
            adv_settingLabel3.Text = Session[DieboldConstants.ADV_SEARCH_SETTING_NAME3].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] != null) {
            adv_MinQuantizedValue.Visible = true;
            adv_minQuantizedLabel.Text = Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] != null) {
            adv_MaxQuantizedValue.Visible = true;
            adv_maxQuantizedLabel.Text = Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE].ToString();
            isBarVisible = true;
        }
        if (Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] != null) {
            adv_SolutionStateDiv.Visible = true;
            adv_SolutionStateLabel.Text = Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE_NAME].ToString();
            isBarVisible = true;
        }
        AdvancedSearchBar.Visible = isBarVisible;
    }

    protected void EditGridButton_Click(object sender, EventArgs e) {
        Response.Redirect("MassEditObservations.aspx");
    }

    protected void JiraSyncButton_Click(object sender, EventArgs e) {
        //ensure search parameters are all in the session
        SaveSearchParamsToSession();

        List<Int64> obsIdList = new List<long>();
        DataSet ds = TransactionSource.GetTransactionsFromSessionParameters();
        if (ds.Tables != null && ds.Tables[0] != null) {
            foreach (DataRow row in ds.Tables[0].Rows) {
                Int64 obsId = DataFormatter.getInt64(row, "ObservationId");
                if (obsId > 0 && !obsIdList.Contains(obsId)) {
                    obsIdList.Add(obsId);
                }
            }
        }

        //call to Queue Service to start syncing process
        UploadClient uploadClient = new UploadClient();
        uploadClient.JiraSyncObservations(obsIdList);

        //alert that process has started in background
        Page.ClientScript.RegisterStartupScript(this.GetType(), "ShowJiraMessage", "ShowJiraSyncMessage()", true);
    }


    #region Data Export Functions

    protected void ExportButton_Click(object sender, EventArgs e) {
        DataSet ds = GetCurrentSearchDataSet(false, true);
        WriteExportData(ds);
    }

    private DataSet GetCurrentSearchDataSet(bool allowLiteSearch, bool includeSettings) {
        DataSet retVal = null;

        string sessionList = Utility.ConvertListBoxSelectionsToString(this.sessionList.Items, true);

        int trxStart = 0;
        if (!string.IsNullOrEmpty(this.trxIdStartField.Text))
            trxStart = Convert.ToInt32(this.trxIdStartField.Text);

        int trxEnd = 0;
        if (!string.IsNullOrEmpty(this.trxIdEndField.Text))
            trxEnd = Convert.ToInt32(this.trxIdEndField.Text);

        DateTime startDate = DateTime.MinValue;
        if (!string.IsNullOrEmpty(startDateField.SelectedDate.ToString()))
            startDate = (DateTime)startDateField.SelectedDate;

        DateTime startTime = DateTime.MinValue;
        if (!string.IsNullOrEmpty(startTimeField.SelectedDate.ToString()))
            startTime = (DateTime)startTimeField.SelectedDate;

        DateTime endDate = DateTime.MinValue;
        if (!string.IsNullOrEmpty(endDateField.SelectedDate.ToString()))
            endDate = (DateTime)endDateField.SelectedDate;

        DateTime endTime = DateTime.MinValue;
        if (!string.IsNullOrEmpty(endTimeField.SelectedDate.ToString()))
            endTime = (DateTime)endTimeField.SelectedDate;

        string cellList = null;
        if (Session[DieboldConstants.ADV_SEARCH_CELL] != null)
            cellList = Session[DieboldConstants.ADV_SEARCH_CELL].ToString();

        int deviceId = -1;
        if (Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] != null)
            deviceId = Convert.ToInt32(Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE]);

        string deviceList = null;
        if (Session[DieboldConstants.ADV_SEARCH_DEVICE] != null)
            deviceList = Session[DieboldConstants.ADV_SEARCH_DEVICE].ToString();

        string failureLocationList = null;
        if (Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] != null)
            failureLocationList = Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION].ToString();

        string failureTypeList = null;
        if (Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] != null)
            failureTypeList = Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE].ToString();

        string moduleTypeList = null;
        if (Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] != null)
            moduleTypeList = Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE].ToString();

        string investAreaList = null;
        if (Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] != null)
            investAreaList = Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA].ToString();

        string triageTypeList = null;
        if (Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] != null)
            triageTypeList = Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE].ToString();

        string disciplineList = null;
        if (Session[DieboldConstants.ADV_SEARCH_DISCIPLINE] != null)
            disciplineList = Session[DieboldConstants.ADV_SEARCH_DISCIPLINE].ToString();

        string ownerList = null;
        if (Session[DieboldConstants.ADV_SEARCH_OWNER] != null)
            ownerList = Session[DieboldConstants.ADV_SEARCH_OWNER].ToString();

        string operatorList = null;
        if (Session[DieboldConstants.ADV_SEARCH_OPERATOR] != null)
            operatorList = Session[DieboldConstants.ADV_SEARCH_OPERATOR].ToString();

        string eventList = null;
        if (Session[DieboldConstants.ADV_SEARCH_EVENT] != null)
            eventList = Session[DieboldConstants.ADV_SEARCH_EVENT].ToString();

        string mediaNumberList = null;
        if (Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] != null)
            mediaNumberList = Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER].ToString();

        string fileNumberList = null;
        if (Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER] != null)
            fileNumberList = Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER].ToString();

        string commandNumberList = null;
        if (Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] != null)
            commandNumberList = Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER].ToString();

        string statisticFieldsList = null;
        if (Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS] != null)
            statisticFieldsList = Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELDS].ToString();

        string statisticFieldOptionsList = null;
        if (Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS] != null)
            statisticFieldOptionsList = Session[DieboldConstants.ADV_SEARCH_STATISTIC_FIELD_OPTIONS].ToString();

        string settingList = null;
        if (Session[DieboldConstants.ADV_SEARCH_SETTING] != null)
            settingList = Session[DieboldConstants.ADV_SEARCH_SETTING].ToString();

        string settingList2 = null;
        if (Session[DieboldConstants.ADV_SEARCH_SETTING2] != null)
            settingList2 = Session[DieboldConstants.ADV_SEARCH_SETTING2].ToString();

        string settingList3 = null;
        if (Session[DieboldConstants.ADV_SEARCH_SETTING3] != null)
            settingList3 = Session[DieboldConstants.ADV_SEARCH_SETTING3].ToString();

        Decimal minQuantizedValue = Decimal.MinValue;
        if (Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] != null)
            minQuantizedValue = Convert.ToDecimal(Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE]);

        Decimal maxQuantizedValue = Decimal.MinValue;
        if (Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] != null)
            maxQuantizedValue = Convert.ToDecimal(Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE]);

        DateTime projectedStartDateObj = DateTime.MinValue;
        if (Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] != null)
            projectedStartDateObj = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE]);

        DateTime projectedEndDateObj = DateTime.MinValue;
        if (Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] != null)
            projectedEndDateObj = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE]);

        string solutionStateList = null;
        if (Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] != null)
            solutionStateList = Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE].ToString();

        retVal = TransactionSource.GetTransactions(allowLiteSearch, this.onlyShowObservationsCheck.Checked, searchField.Text.Trim(), startDate, endDate,
                        startTime, endTime, IncludePastSessions, trxStart, trxEnd, sessionList, cellList,
                        deviceId, deviceList, failureLocationList, failureTypeList, moduleTypeList, investAreaList, triageTypeList, disciplineList,
                        ownerList, operatorList, eventList, mediaNumberList, fileNumberList, commandNumberList, statisticFieldsList,
                        statisticFieldOptionsList, settingList, settingList2, settingList3, minQuantizedValue, maxQuantizedValue,
                        projectedStartDateObj, projectedEndDateObj, solutionStateList, includeSettings);

        return retVal;
    }

    private void WriteExportData(DataSet ds) {
        string[] columnNames = { "TranDate", "SessionName", "FailureTypeName", "Settings", "ObservationText", "Notes", "TestLocationName", "CellName", "RDToolTranId", "CumMediaCount",
            "DeviceTypeName", "ModuleDeviceTypeName", "SerialNumber", "OwnerName", "OperatorName", "InvestigationAreaName", "TriageTypeName", "DisciplineName", "FailureLocationName", "SeverityName",
            "SCRNumber", "Link", "PictureCount", "IsCensored" };
        Response.ClearContent();
        Response.ClearHeaders();
        Response.ContentType = "text/csv";
        Response.AppendHeader("content-disposition", "attachment; filename=Export_" + DateTime.Now.ToString("yyyyMMdd_hhmmss") + ".csv");

        bool isFirst = true;
        foreach (string col in columnNames) {
            if (!isFirst)
                Response.Write(",");
            Response.Write("\"" + col + "\"");
            isFirst = false;
        }

        Response.Write("\r\n");

        //Export the data for each row
        foreach (DataRow row in ds.Tables[0].Rows) {
            for (int k = 0; k < columnNames.Length; k++) {
                if (k > 0)
                    Response.Write(",");

                if (row[columnNames[k]] != null)
                    Response.Write("\"" + row[columnNames[k]].ToString().Replace("\r\n", " ").Replace("\"", "\"\"") + "\"");
                else
                    Response.Write("\"\"");
            }
            Response.Write("\r\n");
        }
        Response.End();
    }
    #endregion
}
