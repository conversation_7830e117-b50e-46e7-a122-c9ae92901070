using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web.Caching;
using System.Runtime.CompilerServices;

public sealed class SqlHelper
{
    private static bool dependencyStarted = false;
    private static object dependencyLockObj = new object();
    private static string TransactionalConnString = null;

    private static void AssignParameterValues(SqlParameter[] commandParameters, object[] parameterValues)
    {
        if (!((commandParameters == null) & (parameterValues == null)))
        {
            if (commandParameters.Length != parameterValues.Length)
            {
                throw new ArgumentException("Parameter count does not match Parameter Value count.");
            }
            for (int i = 0; i < commandParameters.Length; i++)
            {
                if (parameterValues[i] is string && string.IsNullOrEmpty((string)parameterValues[i]))
                    commandParameters[i].Value = DBNull.Value;
                else if (parameterValues[i] == null)
                    commandParameters[i].Value = DBNull.Value;
                else
                    commandParameters[i].Value = parameterValues[i];
            }
        }
    }

    private static void AttachParameters(SqlCommand command, SqlParameter[] commandParameters)
    {
        foreach (SqlParameter p in commandParameters)
        {
            if ((p.Direction == ParameterDirection.InputOutput) && (p.Value == null))
            {
                p.Value = null;
            }
            command.Parameters.Add(p);
        }
    }

    public static DataSet ExecuteDataset(string spName, OnChangeEventHandler callback, params object[] parameterValues)
    {
        string connString = GetConnectionString();

        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteDataset(connString, CommandType.StoredProcedure, spName, callback, commandParameters);
        }
        return ExecuteDataset(connString, CommandType.StoredProcedure, spName, callback, new SqlParameter[0]);
    }

    public static DataSet ExecuteDataset(string spName, params object[] parameterValues)
    {
        string connString = GetConnectionString();

        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteDataset(connString, CommandType.StoredProcedure, spName, null, commandParameters);
        }
        return ExecuteDataset(connString, CommandType.StoredProcedure, spName, null, new SqlParameter[0]);
    }

    private static DataSet ExecuteDataset(string connectionString, string spName, OnChangeEventHandler callback, params object[] parameterValues)
    {
        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteDataset(connectionString, CommandType.StoredProcedure, spName, callback, commandParameters);
        }
        return ExecuteDataset(connectionString, CommandType.StoredProcedure, spName, callback, new SqlParameter[0]);
    }

    private static DataSet ExecuteDataset(string connectionString, CommandType commandType, string commandText, OnChangeEventHandler callback, params SqlParameter[] commandParameters)
    {
        DataSet retVal;
        SqlConnection cn = new SqlConnection(connectionString);
        try
        {
            cn.Open();
            retVal = SqlHelper.ExecuteDataset(cn, commandType, commandText, callback, commandParameters);
        }
        finally
        {
            cn.Dispose();
        }
        return retVal;
    }

    private static DataSet ExecuteDataset(SqlConnection connection, CommandType commandType, string commandText, OnChangeEventHandler callback, params SqlParameter[] commandParameters)
    {
        SqlCommand cmd = new SqlCommand();
        PrepareCommand(cmd, connection, null, commandType, commandText, commandParameters);
        SqlDataAdapter adapter = new SqlDataAdapter(cmd);

        if (callback != null)
        {
            SqlDependency dependency = BuildSqlDependency(adapter);
            dependency.OnChange += callback;
        }

        DataSet ds = new DataSet();
        adapter.Fill(ds);
        cmd.Parameters.Clear();
        return ds;
    }

    public static int ExecuteNonQuery(string spName, params object[] parameterValues)
    {
        string connString = GetConnectionString();

        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteNonQuery(connString, CommandType.StoredProcedure, spName, commandParameters);
        }
        return ExecuteNonQuery(connString, CommandType.StoredProcedure, spName, new SqlParameter[0]);
    }

    public static int ExecuteNonQuery(SqlConnection conn, SqlTransaction tran, string spName, params object[] parameterValues)
    {
        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(conn.ConnectionString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteNonQuery(conn, tran, CommandType.StoredProcedure, spName, commandParameters);
        }
        return ExecuteNonQuery(conn, tran, CommandType.StoredProcedure, spName, new SqlParameter[0]);
    }

    private static int ExecuteNonQuery(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        int ExecuteNonQuery;
        SqlConnection cn = new SqlConnection(connectionString);
        try
        {
            cn.Open();
            ExecuteNonQuery = SqlHelper.ExecuteNonQuery(cn, null, commandType, commandText, commandParameters);
        }
        finally
        {
            cn.Dispose();
        }
        return ExecuteNonQuery;
    }

    private static int ExecuteNonQuery(SqlConnection connection, SqlTransaction tran, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        SqlCommand cmd = new SqlCommand();
        PrepareCommand(cmd, connection, tran, commandType, commandText, commandParameters);
        int retval = cmd.ExecuteNonQuery();
        cmd.Parameters.Clear();
        return retval;
    }

    public static object ExecuteScalar(string spName, params object[] parameterValues)
    {
        string connString = GetConnectionString();
        return ExecuteScalar(connString, spName, parameterValues);
    }

    public static object ExecuteScalar(SqlConnection conn, SqlTransaction tran, string spName, params object[] parameterValues)
    {
        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(conn.ConnectionString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteScalar(conn, tran, CommandType.StoredProcedure, spName, commandParameters);
        }
        return ExecuteScalar(conn, tran, CommandType.StoredProcedure, spName, new SqlParameter[0]);
    }

    private static object ExecuteScalar(string connectionString, string spName, params object[] parameterValues)
    {
        if ((parameterValues != null) & (parameterValues.Length > 0))
        {
            SqlParameter[] commandParameters = SqlHelperParameterCache.GetSpParameterSet(connectionString, spName);
            AssignParameterValues(commandParameters, parameterValues);
            return ExecuteScalar(connectionString, CommandType.StoredProcedure, spName, commandParameters);
        }
        return ExecuteScalar(connectionString, CommandType.StoredProcedure, spName, new SqlParameter[0]);
    }

    private static object ExecuteScalar(SqlConnection connection, SqlTransaction tran, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        SqlCommand cmd = new SqlCommand();
        PrepareCommand(cmd, connection, tran, commandType, commandText, commandParameters);
        object retval = RuntimeHelpers.GetObjectValue(cmd.ExecuteScalar());
        cmd.Parameters.Clear();
        return retval;
    }

    private static object ExecuteScalar(string connectionString, CommandType commandType, string commandText, params SqlParameter[] commandParameters)
    {
        object ExecuteScalar;
        SqlConnection cn = new SqlConnection(connectionString);
        try
        {
            cn.Open();
            ExecuteScalar = SqlHelper.ExecuteScalar(cn, null, commandType, commandText, commandParameters);
        }
        finally
        {
            cn.Dispose();
        }
        return ExecuteScalar;
    }

    private static void PrepareCommand(SqlCommand command, SqlConnection connection, SqlTransaction transaction, CommandType commandType, string commandText, SqlParameter[] commandParameters)
    {
        if (connection.State != ConnectionState.Open)
        {
            connection.Open();
        }
        command.Connection = connection;
        command.CommandText = commandText;
        command.CommandTimeout = 300;
        if (transaction != null)
        {
            command.Transaction = transaction;
        }
        command.CommandType = commandType;
        if (commandParameters != null)
        {
            AttachParameters(command, commandParameters);
        }
    }

    private static string GetConnectionString()
    {
        if (TransactionalConnString == null)
            TransactionalConnString = ConfigurationManager.ConnectionStrings["TransactionalDatabase"].ConnectionString;

        return TransactionalConnString;
    }

    private static SqlDependency BuildSqlDependency(SqlDataAdapter adapter)
    {
        if (!dependencyStarted)
        {
            lock (dependencyLockObj)
            {
                SqlDependency.Start(adapter.SelectCommand.Connection.ConnectionString);
                SqlCacheDependencyAdmin.EnableNotifications(adapter.SelectCommand.Connection.ConnectionString);
                dependencyStarted = true;
            }
        }

        return new SqlDependency(adapter.SelectCommand);
    }

    public static string getString(DataRow row, string columnName, string defaultValue)
    {
        String retVal = defaultValue;
        if (row.IsNull(columnName) == false)
        {
            retVal = (string)row[columnName];
        }

        return retVal;
    }

    public static int getInt32(DataRow row, string columnName, int defaultValue)
    {
        int retVal = defaultValue;

        if (row.IsNull(columnName) == false)
        {
            retVal = (int)row[columnName];
        }

        return retVal;
    }

    public static bool getBool(DataRow row, string columnName, bool defaultValue)
    {
        bool retVal = defaultValue;

        if (row.IsNull(columnName) == false)
        {
            retVal = Convert.ToBoolean(row[columnName]);
        }

        return retVal;
    }

    public static DateTime getDateTime(DataRow row, string columnName, DateTime defaultValue)
    {
        DateTime retVal = defaultValue;

        if (row.IsNull(columnName) == false)
        {
            retVal = Convert.ToDateTime(row[columnName]);
        }

        return retVal;
    }

    public static decimal getDecimal(DataRow row, string columnName, decimal defaultValue)
    {
        decimal retVal = defaultValue;

        if (row.IsNull(columnName) == false)
        {
            retVal = (decimal)row[columnName];
        }

        return retVal;
    }

    public static double getDouble(DataRow row, string columnName, double defaultValue)
    {
        double retVal = defaultValue;

        if (row.IsNull(columnName) == false)
        {
            retVal = (double)row[columnName];
        }

        return retVal;
    }

    public static object getObject(DataRow row, string columnName, object defaultValue)
    {
        object retVal = defaultValue;

        if (row.IsNull(columnName) == false)
        {
            retVal = (object)row[columnName];
        }

        return retVal;
    }
}


