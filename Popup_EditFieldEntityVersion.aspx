<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditFieldEntityVersion.aspx.cs" Inherits="Popup_EditFieldEntityVersion" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content2" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= DataGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
</script>

<asp:panel id="DefaultPanel" runat="server">

<%--<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
	<AjaxSettings>
		<telerik:AjaxSetting AjaxControlID="AddVersionButton">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="EditDiv" LoadingPanelID="LoadingPanel1" />
			</UpdatedControls>
		</telerik:AjaxSetting>
		<telerik:AjaxSetting AjaxControlID="DataGrid1">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="DataGrid1" LoadingPanelID="LoadingPanel1" />
				<telerik:AjaxUpdatedControl ControlID="EditDiv" />
			</UpdatedControls>
		</telerik:AjaxSetting>
		<telerik:AjaxSetting AjaxControlID="SaveButton_Click">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="DataGrid1" LoadingPanelID="LoadingPanel1" />
				<telerik:AjaxUpdatedControl ControlID="EditDiv" />
			</UpdatedControls>
		</telerik:AjaxSetting>
		<telerik:AjaxSetting AjaxControlID="CancelButton_Click">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="EditDiv" LoadingPanelID="LoadingPanel1" />
			</UpdatedControls>
		</telerik:AjaxSetting>
	</AjaxSettings>
</telerik:RadAjaxManager>--%>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Entity Version</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<div class="title"><asp:label id="FieldNameLabel" runat="server"></asp:label></div>
				<div style="padding:0px 0px 10px 14px;"><div class="goButton"><asp:linkbutton id="AddVersionButton" runat="server" causesvalidation="false" onclick="AddVersion_Click">Add Version</asp:linkbutton></div></div>
				
				<telerik:radgrid id="DataGrid1" allowmultirowselection="false" 
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="5" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="gridItem" />
					<alternatingitemstyle cssclass="gridItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings resizing-allowcolumnresize="true">
						<selecting allowrowselect="false" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="FieldId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="Entity Id" sortexpression="EntityId" uniquename="EntityId">
								<headerstyle width="20%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "EntityId", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Min Version" sortexpression="MinVersion" uniquename="MinVersion">
								<headerstyle width="30%" />
								<itemtemplate>
									<%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "MinVersion", "")) ? "" : VersionNumber.ConverToString(Convert.ToInt64(DataFormatter.Format(Container.DataItem, "MinVersion", "")))%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Max Version" sortexpression="MaxVersion" uniquename="MaxVersion">
								<headerstyle width="30%" />
								<itemtemplate>
									<%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "MaxVersion", "")) ? "" : VersionNumber.ConverToString(Convert.ToInt64(DataFormatter.Format(Container.DataItem, "MaxVersion", "")))%>
								</itemtemplate> 
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><asp:linkbutton id="EditButton" runat="server" causesvalidation="false" commandargument='<%# DataFormatter.Format(Container.DataItem, "VersionMappingId", "") %>' oncommand="EditButton_Command">Edit</asp:linkbutton></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><asp:linkbutton id="DeleteButton" causesvalidation="false" onclientclick="return confirm('Are you sure you wish to delete?');" runat="server" commandargument='<%# DataFormatter.Format(Container.DataItem, "VersionMappingId", "") %>' oncommand="DeleteButton_Command">Delete</asp:linkbutton></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>
				
				<div id="EditDiv" visible="false" runat="server" style="margin:10px;border:solid 1px #0077cc;">
					<div class="title" style="padding-bottom:0px;">Edit Form &nbsp;&nbsp;<asp:label id="ErrorLabel" runat="server" cssclass="error"></asp:label></div>
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:50%;">
								<table width="100%" border="0" cellpadding="0" cellspacing="10">
									<tr>
										<td style="width:120px;" class="rowHeading">Field Id</td>
										<td><asp:label id="FieldIdLabel" runat="server"></asp:label></td>
									</tr>
									<tr>
										<td style="width:120px;" class="rowHeading">Entity Id</td>
										<td>
											<asp:textbox id="EntityIdField" runat="server"></asp:textbox>
											<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="EntityIdField" cssclass="error" display="dynamic" errormessage="*"></asp:requiredfieldvalidator>
										</td>
									</tr>									
								</table>
							</td>
							<td style="width:50%;">
								<table width="100%" border="0" cellpadding="0" cellspacing="10">
									<tr>
										<td style="width:120px;" class="rowHeading">Min Version</td>
										<td>
											<asp:textbox id="MinVersionField" runat="server"></asp:textbox>
											<asp:requiredfieldvalidator id="Requiredfieldvalidator1" runat="server" controltovalidate="MinVersionField" cssclass="error" display="dynamic" errormessage="*"></asp:requiredfieldvalidator>	
										</td>
									</tr>
									<tr>
										<td style="width:120px;" class="rowHeading">Max Version</td>
										<td>
											<asp:textbox id="MaxVersionField" runat="server"></asp:textbox>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					
					<table border="0" cellpadding="0" cellspacing="0" style="padding-bottom:5px;">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" causesvalidation="true" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td>
								<div class="cancelButton"><asp:linkbutton runat="server" causesvalidation="false" onclick="CancelButton_Click" id="CancelButton">Cancel</asp:linkbutton></div>
							</td>
						</tr>
					</table>
				</div>
				<br />
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton causesvalidation="false" runat="server" onclick="BackButton_Click" id="Linkbutton1">Back</asp:linkbutton></div></td>
						<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Close</a></div></td>
					</tr>
				</table>
			</div>
		</td>
	</tr>
</table>

<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

</asp:panel>

</asp:Content>



