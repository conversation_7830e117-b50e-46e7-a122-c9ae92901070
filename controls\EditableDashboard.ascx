<%@ Control Language="C#" AutoEventWireup="true" CodeFile="EditableDashboard.ascx.cs" Inherits="controls_EditableDashboard" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
</telerik:RadAjaxManager>

<table style="padding-top:8px;background-color:#867f79;color:#ffffff;width:100%;" border="0" cellpadding="0" cellspacing="0">
	<tr>
		<td style="width:220px;padding-left:4px;">
			<div id="AdminModeDiv" visible="false" runat="server"><a id="A1" runat="server" href="~/Dashboard.aspx"><div style="cursor:pointer;" class="dashboardNavItemSelected">Shared Dashboard</div></a></div>
			<div id="NormalModeDiv" runat="server"><a id="A2" runat="server" href="~/Dashboard.aspx"><div class="dashboardNavItem">Shared Dashboard</div></a></div>
		</td>
		<td style="width:210px;">
			<div id="AdminModeDiv2" visible="false" runat="server"><a id="A3" runat="server" href="~/MyDashboard.aspx"><div class="dashboardNavItem">My Dashboard</div></a></div>
			<div id="NormalModeDiv2" runat="server"><div class="dashboardNavItemSelected" runat="server" id="MyDashboardImage">My Dashboard</div></div>
		</td>
		<td style="width:90px;">&nbsp;</td>
		<td style="width:150px;">
			<asp:dropdownlist runat="server" id="widgetList" width="150">
				<asp:listitem text="Add New Widget..." value="" selected="true"></asp:listitem>
				<asp:listitem text="Report" value="~/Controls/DashItem_Report.ascx"></asp:listitem>
				<asp:listitem text="Weekly Summary" value="~/Controls/DashItem_WeeklySummary.ascx"></asp:listitem>
				<asp:listitem text="Session Summary" value="~/Controls/DashItem_SessionSummary.ascx"></asp:listitem>
				<asp:listitem text="Message Board" value="~/Controls/DashItem_MessageBoard.ascx"></asp:listitem>
				<asp:listitem text="Custom Image/PDF" value="~/Controls/DashItem_Image.ascx"></asp:listitem>
			</asp:dropdownlist>
			<asp:requiredfieldvalidator id="val1" runat="server" display="dynamic" controltovalidate="widgetList" cssclass="error" errormessage="*"></asp:requiredfieldvalidator> 
		</td>
		<td style="width:95px;" class="leftPad"><div class="goButton" style="padding-left:14px;"><asp:linkbutton style="text-decoration:none;color:#dddddd;" runat="server" onclick="ButtonAddDock_Click" id="ButtonAddDock">Add Widget</asp:linkbutton></div></td>
        <td>&nbsp;</td>
	</tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="8" style="width:2050px;">
	<tr>
		<td>
			<telerik:raddocklayout runat="server" id="RadDockLayout1" enableembeddedskins="false" skin="Diebold" onsavedocklayout="RadDockLayout1_SaveDockLayout" onloaddocklayout="RadDockLayout1_LoadDockLayout">
				<telerik:raddockzone runat="server" id="RadDockZone1" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px;margin-right:15px;background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
				<telerik:raddockzone runat="server" id="RadDockZone2" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px;margin-right:15px;background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
                <telerik:raddockzone runat="server" id="RadDockZone3" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px;margin-right:15px;background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
                <telerik:raddockzone runat="server" id="RadDockZone4" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px;margin-right:15px;background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
                <telerik:raddockzone runat="server" id="RadDockZone5" enableembeddedskins="false" skin="Diebold" MinHeight="500" style="width:383px;margin-right:15px;background:#5b5551; border:solid 0px #5b5551;float:left;">
				</telerik:raddockzone>
				<div style="display:none">
					Hidden UpdatePanel, which is used to receive the new dock controls. 
					We will move them with script to the desired initial dock zone.
					<asp:updatepanel runat="server" id="UpdatePanel1">
						<contenttemplate>
						<triggers>
							<asp:asyncpostbacktrigger controlid="ButtonAddDock" eventname="Click" />
						</triggers>
						</contenttemplate>
					</asp:updatepanel>
				</div>
			</telerik:raddocklayout>
			<asp:linkbutton style="display:none;" runat="server" id="RefreshButton" causesvalidation="false">Hide me later</asp:linkbutton>
		</td>
	</tr>
</table>