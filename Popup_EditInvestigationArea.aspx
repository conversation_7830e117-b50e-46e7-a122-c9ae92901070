<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditInvestigationArea.aspx.cs" Inherits="Popup_EditInvestigationArea" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Investigation Area</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Add/Edit Investigation Areas</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="14" width="100%">
						<tr>
							<td class="rowHeading" style="width:150px;">Investigation Area</td>
							<td>
								<asp:textbox runat="server" width="230" id="investigationAreaNameField"></asp:textbox>
								<asp:requiredfieldvalidator id="val1" runat="server" errormessage="* Required" controltovalidate="investigationAreaNameField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Active</td>
							<td>
								<asp:CheckBox id="isActive" runat="server" />
							</td>
						</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />A investigation area already exists with the specified name.<br /></div>
				</div>
			</td>
		</tr>
	</table>

</asp:Content>



