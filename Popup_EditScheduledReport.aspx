<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditScheduledReport.aspx.cs" Inherits="Popup_EditScheduledReport" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
	<AjaxSettings>
		<telerik:AjaxSetting AjaxControlID="receiveEmailCheck">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="emailDiv" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
			</UpdatedControls>
		</telerik:AjaxSetting>
		<telerik:AjaxSetting AjaxControlID="printCheck">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="printDiv" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
			</UpdatedControls>
		</telerik:AjaxSetting>
	</AjaxSettings>
</telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>				
<asp:panel id="DefaultPanel" runat="server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Schedule Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Edit Scheduled Report</div>
					<div id="NewSnapshotArea" runat="server">
						<table border="0" cellpadding="0" cellspacing="13" width="100%">
							<tr id="rptNameRow" runat="server" visible="false">
								<td class="rowHeading" style="width:150px;">Report Name</td>
								<td>
									<asp:label id="reportName" runat="server"></asp:label>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Scheduled Time</td>
								<td>
									<telerik:radtimepicker id="timeField" runat="server" timepopupbutton-visible="true" width="120">
										<dateinput runat="server" dateformat="hh:mm tt"></dateinput>
										<TimeView runat="server" Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
									</telerik:radtimepicker>
									<asp:requiredfieldvalidator id="v13" runat="server" controltovalidate="timeField" errormessage="* Required"></asp:requiredfieldvalidator>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Schedule</td>
								<td>
									<asp:checkboxlist id="scheduleList" repeatlayout="table" repeatcolumns="4" repeatdirection="Horizontal" runat="server">
										<asp:listitem text="Sunday" value="sunday"></asp:listitem>
										<asp:listitem text="Monday" value="Monday"></asp:listitem>
										<asp:listitem text="Tuesday" value="Tuesday"></asp:listitem>
										<asp:listitem text="Wednesday" value="Wednesday"></asp:listitem>
										<asp:listitem text="Thursday" value="Thursday"></asp:listitem>
										<asp:listitem text="Friday" value="Friday"></asp:listitem>
										<asp:listitem text="Saturday" value="Saturday"></asp:listitem>
									</asp:checkboxlist>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Format</td>
								<td>
									<asp:dropdownlist id="formatList" runat="server">
										<asp:listitem text="Portrait" value="portrait" selected="true"></asp:listitem>
										<asp:listitem text="Landscape" value="landscape"></asp:listitem>
									</asp:dropdownlist>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Chart Scale</td>
								<td>
									<asp:dropdownlist id="chartScaleList" runat="server">
										<asp:listitem text="Hide Chart" value="hide"></asp:listitem>
										<asp:listitem text="Fit to Page Width" value="fit"></asp:listitem>
										<asp:listitem text="25%" value="25%"></asp:listitem>
										<asp:listitem text="50%" value="50%"></asp:listitem>
										<asp:listitem text="60%" value="60%"></asp:listitem>
										<asp:listitem text="75%" value="75%"></asp:listitem>
										<asp:listitem text="90%" value="90%"></asp:listitem>
										<asp:listitem text="100%" value="100%" selected="true"></asp:listitem>
										<asp:listitem text="150%" value="150%"></asp:listitem>
										<asp:listitem text="200%" value="200%"></asp:listitem>
										<asp:listitem text="300%" value="300%"></asp:listitem>
										<asp:listitem text="400%" value="400%"></asp:listitem>
										<asp:listitem text="500%" value="500%"></asp:listitem>
									</asp:dropdownlist>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Data Scale</td>
								<td>
									<asp:dropdownlist id="dataScaleList" runat="server">
										<asp:listitem text="Hide Data Grid" value="hide"></asp:listitem>
										<asp:listitem text="Fit to Page Width" value="fit"></asp:listitem>
										<asp:listitem text="25%" value="25%"></asp:listitem>
										<asp:listitem text="50%" value="50%"></asp:listitem>
										<asp:listitem text="60%" value="60%"></asp:listitem>
										<asp:listitem text="75%" value="75%"></asp:listitem>
										<asp:listitem text="90%" value="90%"></asp:listitem>
										<asp:listitem text="100%" value="100%" selected="true"></asp:listitem>
										<asp:listitem text="150%" value="150%"></asp:listitem>
										<asp:listitem text="200%" value="200%"></asp:listitem>
										<asp:listitem text="300%" value="300%"></asp:listitem>
										<asp:listitem text="400%" value="400%"></asp:listitem>
										<asp:listitem text="500%" value="500%"></asp:listitem>
									</asp:dropdownlist>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Separate Chart & Data</td>
								<td>
									<asp:checkbox id="dataSeparate" runat="server" text="Place data on a separate page" />
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">
									Email Report: 
								</td>
								<td>
									<asp:checkbox id="receiveEmailCheck" checked="false" causesvalidation="false" autopostback="true" oncheckedchanged="EmailCheck_Changed" runat="server" text=" Email the report when it is prepared" />
									<br /><br />
									
									<div id="emailDiv" runat="server" visible="false">
										<asp:textbox id="emailAddressBox" width="300" runat="server"></asp:textbox>
										<asp:requiredfieldvalidator id="v1" runat="server" controltovalidate="emailAddressBox" display="dynamic" cssclass="error" errormessage="* Required"></asp:requiredfieldvalidator>
										<div style="font-weight:normal;font-size:10px;color:#777;">Seperate multiple email addresses with semi-colons</div>
									</div>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Print Report</td>
								<td>
									<asp:checkbox id="printCheck" checked="false" causesvalidation="false" autopostback="true" oncheckedchanged="PrintCheck_Changed" runat="server" text=" Print the report when it is prepared" />
									<br /><br />
									
									<div id="printDiv" runat="server" visible="false">
										<asp:textbox id="printerPath" width="300" runat="server"></asp:textbox>
										<asp:requiredfieldvalidator id="v3" runat="server" controltovalidate="printerPath" display="dynamic" cssclass="error" errormessage="* Required"></asp:requiredfieldvalidator>
										<div style="font-weight:normal;font-size:10px;color:#777;">Input full path to printer. i.e. "\\Server_Name\Printer_Name"</div>
										<br />
										# Copies: <asp:textbox id="numCopies" maxlength="3" width="30" runat="server"></asp:textbox>
										<asp:comparevalidator id="v4" runat="server" display="dynamic" cssclass="error" controltovalidate="numCopies" operator="dataTypeCheck" type="integer" errormessage="* Invalid format"></asp:comparevalidator>

									</div>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Group Reports</td>
								<td>
									<asp:checkbox id="groupReportsCheck" runat="server" text="Merge into a single PDF with other reports scheduled at this same time" />
								</td>
							</tr>
						</table>
						
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
								<td style="width:80px;"><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
								<td><div class="cancelButton"><asp:linkbutton runat="server" causesvalidation="false" visible="false" onclick="DeleteButton_Click" id="deleteBtn">Delete</asp:linkbutton></div></div></td>
							</tr>
						</table>
						<br />
					</div>
					
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

