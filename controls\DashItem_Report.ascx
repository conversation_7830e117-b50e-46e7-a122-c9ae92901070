<%@ Control Language="C#" AutoEventWireup="true" CodeFile="DashItem_Report.ascx.cs" Inherits="controls_DashItem_Report" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ Import Namespace="Microsoft.AnalysisServices.AdomdClient" %>
<%@ register assembly="DundasWebUIControls" namespace="Dundas.Olap.WebUIControls" tagprefix="DOCWC" %>
<%@ register assembly="DundasWebOlapManager" namespace="Dundas.Olap.Manager" tagprefix="DOMC" %>
<%@ register assembly="DundasWebOlapDataProviderAdomdNet" namespace="Dundas.Olap.Data.AdomdNet" tagprefix="DODPN" %>
<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="dcwc" %>

<script type="text/javascript">
	function nodeClicking(sender, args)
    {
    	var node = args.get_node();

    	var comboBox = $find('<%= reportCombo.ClientID %>');
    	if (comboBox) {
    		
    		comboBox.set_text(node.get_text());
    		comboBox.hideDropDown();
    	}
    	else {
    		//certain versions of IE are not finding the combo box.
    		//manually find it and set the value, then hide the dropdown manually also
    		var comboBox = $('.radComboBox');
    		var comboBoxId = comboBox.attr('id');
    		
    		comboBoxInput = $('#' + comboBoxId + '_Input');
    		comboBoxDropDown = $('#' + comboBoxId + '_DropDown');

    		comboBoxInput.val(node.get_text());
    		comboBoxDropDown.parent().hide().css('overflow', 'hidden').css('top', '-1000px').css('left', '-1000px');
    	}
    }
    function StopPropagation(e) {
         if(!e)
            e = window.event;

         e.cancelBubble = true;

         if (e.stopPropagation) {
         	e.stopPropagation();
         }
    }
    function HandleKeyPress(comboBox, eventArgs)
    {
    	var text= comboBox.get_text();
        window.setTimeout(function() { comboBox.set_text(text); }, 100);
    }
</script> 

<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="610" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
	<windows><telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="~/promptsessions.aspx" Title="" Height="400" Width="600" ></telerik:radwindow></windows>
</telerik:radwindowmanager>

<telerik:RadAjaxManagerProxy ID="RadAjaxManagerProxy1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="SaveButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" loadingpanelid="LoadingPanel1" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="CancelButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" loadingpanelid="LoadingPanel1" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="HidEditButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:RadAjaxManagerProxy>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<telerik:radcodeblock id="panelCodeBlock" runat="server">
<asp:panel id="AjaxPanel" runat="server">
	<asp:panel id="DisplayPanel" runat="server">
		<table style="padding-top:5px;padding-bottom:5px;width:100%;" border="0" cellpadding="0" cellspacing="0" class="body">
			<tr>
				<td>
				    <div id="chartDiv" runat="server">
					    <dcwc:Chart ID="chartCntrl" runat="server" Width="360px" Palette="Dundas" Height="250px" BackColor="WhiteSmoke" BackGradientEndColor="White" 
                                    BackGradientType="DiagonalLeft" BorderLineColor="26, 59, 105" BorderLineStyle="Solid">
                            <ChartAreas><dcwc:ChartArea Name="Default" BackColor="White" BorderColor="26, 59, 105" BorderStyle="Solid" ShadowOffset="2">
                                <AxisY>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisY>
                                <AxisX>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisX>
                                <AxisX2>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisX2>
                                <AxisY2>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisY2>
                            </dcwc:ChartArea></ChartAreas>
                            <Titles>
                                <dcwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></dcwc:Title>
                            </Titles>
                            <Legends>
                                <dcwc:Legend BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
                                </dcwc:Legend>
                            </Legends>
                            <UI>
                                <Toolbar Enabled="False" Placement="OutsideChart" BorderColor="26, 59, 105"><BorderSkin SkinStyle="Emboss" PageColor="Transparent" /></Toolbar>
                                <ContextMenu Enabled="False"></ContextMenu>
                            </UI>
                            <BorderSkin FrameBackColor="CornflowerBlue" FrameBackGradientEndColor="CornflowerBlue" SkinStyle="Emboss" />
                        </dcwc:Chart>
                    </div>
                    <div id="olapDiv" runat="server">
    				    <dodpn:adomdnetdataprovider id="AdomdNetDataProvider1" runat="server"></dodpn:adomdnetdataprovider>
    				    <domc:OlapManager ID="OlapManager1" runat="server" AutoGenerateFirstQuery="false" DataProviderID="AdomdNetDataProvider1" onerrordetected="OlapManager1_ErrorDetected"></domc:OlapManager>
    				    <docwc:OlapChart ID="olapChartCntrl" runat="server" OlapManagerID="OlapManager1" Palette="Dundas" BackColor="WhiteSmoke" BackGradientEndColor="White" 
                                    BackGradientType="DiagonalLeft" BorderLineColor="26, 59, 105" BorderLineStyle="Solid">
                            <ChartAreas><docwc:ChartArea Name="Default" BackColor="White" BorderColor="26, 59, 105" BorderStyle="Solid" ShadowOffset="2">
                                <AxisY>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisY>
                                <AxisX>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisX>
                                <AxisX2>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisX2>
                                <AxisY2>
                                    <MajorGrid LineColor="Silver" />
                                    <MinorGrid LineColor="Silver" />
                                </AxisY2>
                            </docwc:ChartArea></ChartAreas>
                            <Titles>
                                <docwc:Title Name="Title1" Font="Arial, 11pt, style=Bold" Alignment="TopCenter"></docwc:Title>
                            </Titles>
                            <Legends>
                                <docwc:Legend BackColor="White" BorderColor="26, 59, 105" Name="Default" ShadowOffset="2">
                                </docwc:Legend>
                            </Legends>
                            <UI>
                                <Toolbar Enabled="False" Placement="OutsideChart" BorderColor="26, 59, 105"><BorderSkin SkinStyle="Emboss" PageColor="Transparent" /></Toolbar>
                                <ContextMenu Enabled="False"></ContextMenu>
                            </UI>
                            <BorderSkin FrameBackColor="CornflowerBlue" FrameBackGradientEndColor="CornflowerBlue" SkinStyle="Emboss" />
                          </docwc:OlapChart>
				    </div>
				</td>
			</tr>
		</table>
		<table border="0" cellpadding="0" cellspacing="0" class="widget">
			<tr>
				<td style="width:115px;" class="leftPad"><div class="goButton"><asp:linkbutton style="padding-left:14px;" runat="server" onclick="ViewReport_Click" id="viewReportButton" causesvalidation="false">View Report</asp:linkbutton></div></td>
			</tr>
		</table>
	</asp:panel>

	<asp:panel id="SettingsPanel" runat="server" visible="false" cssclass="body">
		<div class="title" style="padding-left:10px;padding-bottom:8px;padding-top:5px;">Edit Settings</div>
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td class="rowHeading">Select Report: <span style="font-weight:normal;"></span></td>
				</tr>
				<tr>
					<td style="padding:10px 0px 10px 14px;">
						<telerik:RadComboBox cssclass="radComboBox" ID="reportCombo" runat="server" Height="175px" Width="275px" ShowToggleImage="True" Skin="Default" 
						        style="padding-left:-3px; vertical-align:middle;" allowcustomtext="true" showdropdownontextboxclick="true" onclientkeypressing="HandleKeyPress">
							<Items>
								<telerik:RadComboBoxItem Text="" />
							</Items>
							<ItemTemplate>
								<div id='div1' onclick="StopPropagation();">
									<telerik:RadTreeView runat="Server" onclientnodeclicking="nodeClicking" multipleselect="false" ID="reportTree" EnableDragAndDrop="false" EnableDragAndDropBetweenNodes="false" Skin="WebBlue"></telerik:RadTreeView>
								</div>
							</ItemTemplate>
						</telerik:RadComboBox>
						<asp:hiddenfield id="selectedReport" runat="server" />
						<div>
							<asp:label id="errorLabel" runat="server" visible="false" cssclass="error">
								The item selected is not a Report.  Only 3rd level items in the tree are valid selections.	
							</asp:label>
						</div>
					</td>
				</tr>
				<tr>
					<td style="padding:10px 0px 10px 14px;">
						<asp:checkbox id="disableLegendCheck" runat="server" text=" Hide Chart Legend" />
					</td>
				</tr>
			</table>
			<br />
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton style="padding-left:14px;" runat="server" onclick="SaveSettings_Click" id="SaveButton" validationgroup="settingsPanel">Save</asp:linkbutton></div></td>
					<td><div class="cancelButton"><asp:linkbutton runat="server" style="padding-left:14px;" onclick="CancelSettings_Click" id="CancelButton" causesvalidation="false">Cancel</asp:linkbutton></div></td>
				</tr>
			</table>
			<br />
	</asp:panel>
	<asp:button runat="server" onclick="EditSettings_Click" id="HidEditButton" style="display:none;" usesubmitbehavior="false" causesvalidation="false"></asp:button>
</asp:panel>
</telerik:radcodeblock>
