﻿using System;
using System.Data;
using System.Web.UI;

public partial class EditJiraSyncSchedule : System.Web.UI.Page
{
	public int ScheduleId
    {
		get { if (this.ViewState["sr"] != null) return (int)this.ViewState["sr"]; else return 0; }
		set { this.ViewState["sr"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
        deleteBtn.Attributes.Add("onclick", "return confirm('Are you sure you wish to delete this sync?');");

		if (!Page.IsPostBack)
		{
            sessionList.DataSource = Utility.GetActiveSessionsList();
            sessionList.DataBind();

            RelativeStartList.DataSource = Utility.GetRelativeTimeList();
            RelativeStartList.DataBind();

            RelativeEndList.DataSource = Utility.GetRelativeTimeList();
            RelativeEndList.DataBind();

            if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.JIRA_SYNC_ID_KEY]))
                ScheduleId = Convert.ToInt32(Request.QueryString[DieboldConstants.JIRA_SYNC_ID_KEY]);

			if (ScheduleId > 0)
			{
				LoadSync();
				deleteBtn.Visible = true;
			}
		}
	}

	private void LoadSync()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadJiraSyncSchedule", this.ScheduleId);

		foreach (DataRow row in ds.Tables[0].Rows) //only loads one row.
		{
			this.ScheduleId = DataFormatter.getInt32(row, "ScheduleId");
            this.sessionList.SelectedValue = DataFormatter.getInt32(row, "SessionId").ToString();

			string nextScheduledDate = DataFormatter.getDateTime(row, "NextScheduledDate").ToShortDateString();
			if (string.Compare(nextScheduledDate, DateTime.MinValue.ToShortDateString()) != 0 && string.Compare(nextScheduledDate, DateTime.MaxValue.ToShortDateString()) != 0)
				timeField.SelectedDate = DataFormatter.getDateTime(row, "NextScheduledDate");

			scheduleList.Items[0].Selected = DataFormatter.getBool(row, "Sunday");
			scheduleList.Items[1].Selected = DataFormatter.getBool(row, "Monday");
			scheduleList.Items[2].Selected = DataFormatter.getBool(row, "Tuesday");
			scheduleList.Items[3].Selected = DataFormatter.getBool(row, "Wednesday");
			scheduleList.Items[4].Selected = DataFormatter.getBool(row, "Thursday");
			scheduleList.Items[5].Selected = DataFormatter.getBool(row, "Friday");
			scheduleList.Items[6].Selected = DataFormatter.getBool(row, "Saturday");

            ReportHelper.RelativeTimeEnum relStartTimeId = (ReportHelper.RelativeTimeEnum)DataFormatter.getInt32(row, "RelativeStartId");
            ReportHelper.RelativeTimeEnum relEndTimeId = (ReportHelper.RelativeTimeEnum)DataFormatter.getInt32(row, "RelativeEndId");

            DateTime fixedStartDate = DataFormatter.getDateTime(row, "FixedStartDate");
            DateTime fixedEndDate = DataFormatter.getDateTime(row, "FixedEndDate");

            if (relStartTimeId != ReportHelper.RelativeTimeEnum.UNSPECIFIED) {
                //Relative Time
                this.StartDateTypeList.SelectedIndex = 1;
                StartDateTypeList_SelectedIndexChanged(null, null);
                this.RelativeStartList.SelectedValue = Convert.ToString((int)relStartTimeId);

                if (fixedStartDate.TimeOfDay != TimeSpan.Zero)
                    startTimeField.SelectedDate = Convert.ToDateTime(fixedStartDate.ToString("hh:mm:ss tt"));
            }
            else {
                this.StartDateTypeList.SelectedIndex = 0;

                if (fixedStartDate != DateTime.MinValue) {
                    startDateField.SelectedDate = fixedStartDate.Date;
                    startTimeField.SelectedDate = Convert.ToDateTime(fixedStartDate.ToString("hh:mm:ss tt"));
                }
            }

            if (relEndTimeId != ReportHelper.RelativeTimeEnum.UNSPECIFIED) {
                //Relative Time
                this.EndDateTypeList.SelectedIndex = 1;
                EndDateTypeList_SelectedIndexChanged(null, null);
                this.RelativeEndList.SelectedValue = Convert.ToString((int)relEndTimeId);

                if (fixedEndDate.TimeOfDay != TimeSpan.Zero)
                    endTimeField.SelectedDate = Convert.ToDateTime(fixedEndDate.ToString("hh:mm:ss tt"));
            }
            else {
                this.EndDateTypeList.SelectedIndex = 0;

                if (fixedEndDate != DateTime.MinValue) {
                    endDateField.SelectedDate = fixedEndDate.Date;
                    endTimeField.SelectedDate = Convert.ToDateTime(fixedEndDate.ToString("hh:mm:ss tt"));
                }
            }
        }
	}
    
    protected void SaveButton_Click(object sender, EventArgs e)
	{
		SaveScheduledReport(true);
	}

	private void SaveScheduledReport(bool shouldRedirect)
	{
        TimeSpan startTime = TimeSpan.Zero;
        TimeSpan endTime = TimeSpan.Zero;

        if (startTimeField.SelectedDate != null && startTimeField.SelectedDate.HasValue) {
            startTime = new TimeSpan(((DateTime)startTimeField.SelectedDate).Hour, ((DateTime)startTimeField.SelectedDate).Minute, ((DateTime)startTimeField.SelectedDate).Second);
            if (startTime.Minutes % 30 != 0) {
                timeError.Visible = true;
                return;
            }
        }

        if (endTimeField.SelectedDate != null && endTimeField.SelectedDate.HasValue) {
            endTime = new TimeSpan(((DateTime)endTimeField.SelectedDate).Hour, ((DateTime)endTimeField.SelectedDate).Minute, ((DateTime)endTimeField.SelectedDate).Second);
            if (endTime.Minutes % 30 != 0) {
                timeError.Visible = true;
                return;
            }
        }

        DateTime fixedStartDate = DateTime.MinValue;
        DateTime fixedEndDate = DateTime.MinValue;
        ReportHelper.RelativeTimeEnum relativeStartTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;
        ReportHelper.RelativeTimeEnum relativeEndTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;
        if (StartDateTypeList.SelectedValue.Equals("fixed")) {
            relativeStartTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;

            if (startDateField.SelectedDate.HasValue)
                fixedStartDate = ((DateTime)startDateField.SelectedDate).Add(startTime);
        }
        else {
            //Relative Time
            if (!string.IsNullOrEmpty(RelativeStartList.SelectedValue)) {
                relativeStartTimeId = (ReportHelper.RelativeTimeEnum)Convert.ToInt32(RelativeStartList.SelectedValue);
                fixedStartDate = new DateTime(1900, 1, 1).Add(startTime);
            }
        }

        if (EndDateTypeList.SelectedValue.Equals("fixed")) {
            relativeEndTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;

            if (endDateField.SelectedDate.HasValue)
                fixedEndDate = ((DateTime)endDateField.SelectedDate).Add(endTime);
        }
        else {
            //Relative Time
            if (!string.IsNullOrEmpty(RelativeEndList.SelectedValue)) {
                relativeEndTimeId = (ReportHelper.RelativeTimeEnum)Convert.ToInt32(RelativeEndList.SelectedValue);
                fixedEndDate = new DateTime(1900, 1, 1).Add(endTime);
            }
        }
        
        object selectedTime = null;
        object startDate = null;
        object relativeStartId = null;
        object endDate = null;
        object relativeEndId = null;

        if (fixedStartDate != DateTime.MinValue)
            startDate = (DateTime)fixedStartDate;

        if (fixedEndDate != DateTime.MinValue)
            endDate = (DateTime)fixedEndDate;

        if (relativeStartTimeId != 0)
            relativeStartId = (int)relativeStartTimeId;

        if (relativeEndTimeId != 0)
            relativeEndId = (int)relativeEndTimeId;

        if (timeField.SelectedDate != null)
			selectedTime = Utility.CalculateNextScheduledReportDate((DateTime)timeField.SelectedDate, scheduleList);

        this.ScheduleId = Convert.ToInt32(SqlHelper.ExecuteScalar("RPT_UpdateJiraSyncSchedule",
            this.ScheduleId, this.sessionList.SelectedValue, selectedTime,
            startDate, endDate, relativeStartId, relativeEndId,
            scheduleList.Items[0].Selected, scheduleList.Items[1].Selected, scheduleList.Items[2].Selected, scheduleList.Items[3].Selected, 
            scheduleList.Items[4].Selected, scheduleList.Items[5].Selected, scheduleList.Items[6].Selected));

		if (shouldRedirect)
			Response.Redirect("JiraSyncSchedule.aspx");
	}

	protected void DeleteButton_Click(object sender, EventArgs e)
	{
		SqlHelper.ExecuteNonQuery("RPT_DeleteJiraSyncSchedule", this.ScheduleId);
		Response.Redirect("JiraSyncSchedule.aspx");
	}

    protected void StartDateTypeList_SelectedIndexChanged(object sender, EventArgs e) {
        if (StartDateTypeList.SelectedValue.Equals("fixed")) {
            this.startDateField.Style.Add(HtmlTextWriterStyle.Display, "");
            this.RelativeStartList.Style.Add(HtmlTextWriterStyle.Display, "none");
        }
        else {
            this.startDateField.Style.Add(HtmlTextWriterStyle.Display, "none");
            this.RelativeStartList.Style.Add(HtmlTextWriterStyle.Display, "");
        }
    }

    protected void EndDateTypeList_SelectedIndexChanged(object sender, EventArgs e) {
        if (EndDateTypeList.SelectedValue.Equals("fixed")) {
            this.endDateField.Style.Add(HtmlTextWriterStyle.Display, "");
            this.RelativeEndList.Style.Add(HtmlTextWriterStyle.Display, "none");
        }
        else {
            this.endDateField.Style.Add(HtmlTextWriterStyle.Display, "none");
            this.RelativeEndList.Style.Add(HtmlTextWriterStyle.Display, "");
        }
    }
}
