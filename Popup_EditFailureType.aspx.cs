using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditFailureType : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string FailureTypeId
	{
		get { return (string)this.ViewState["f"]; }
		set { this.ViewState["f"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FAILURE_TYPE_ID_KEY]))
			{
				this.FailureTypeId = Request.Params[DieboldConstants.FAILURE_TYPE_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetFailureType", this.FailureTypeId);
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					this.failureTypeNameField.Text = DataFormatter.getString(row, "FailureTypeName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
				}
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(FailureTypeId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertFailureType", this.failureTypeNameField.Text);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateFailureType", Convert.ToInt32(this.FailureTypeId), this.failureTypeNameField.Text, this.isActive.Checked);

		if (result == 0)
		{
			this.ErrorLabel.Visible = true;
		}
		else
		{
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditFailureType.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditFailureType.aspx';CloseRadWindow(); ", true);
		}
	}
}
