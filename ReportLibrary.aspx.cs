using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using AjaxControlToolkit;
using Telerik.Web.UI;

public partial class ReportLibrary : System.Web.UI.Page
{
	public bool IncludePastSessions
	{
		get { if (this.ViewState["i"] != null) return (bool)this.ViewState["i"]; else return false; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(Request.Params["sessionSelectedFolder"]) || !string.IsNullOrEmpty(Request.Params["mySelectedFolder"]) 
            || !string.IsNullOrEmpty(Request.Params["sharedSelectedFolder"]) || !string.IsNullOrEmpty(Request.Params["subReportExpandList"]))
		{
			if (!string.IsNullOrEmpty(Request.Params["sessionSelectedFolder"]))
				Session["sessionSelectedFolder"] = Request.Params["sessionSelectedFolder"];

			if (!string.IsNullOrEmpty(Request.Params["mySelectedFolder"]))
				Session["mySelectedFolder"] = Request.Params["mySelectedFolder"];

			if (!string.IsNullOrEmpty(Request.Params["sharedSelectedFolder"]))
				Session["sharedSelectedFolder"] = Request.Params["sharedSelectedFolder"];

            if (!string.IsNullOrEmpty(Request.Params["subReportExpandList"]))
                Session["subReportExpandList"] = Request.Params["subReportExpandList"];
            
            Response.End();
		}

		if (!Page.IsPostBack)
		{
			if(string.Compare(Request["v"],"1") == 0)
			{
				this.IncludePastSessions = true;
				ManageClosedDisplay();
			}
		}

        if (!string.IsNullOrEmpty(Request["action"]) && !string.IsNullOrEmpty(Request["reportId"]))
        {
            switch (Request["action"])
            {
                case "run":
                    RunReport(Request["reportId"], Request["subReportId"]);
                    break;
                case "delete":
                    DeleteReport(Request["reportId"]);
                    break;
                case "deleteMyFolder":
                case "deleteSharedFolder":
                    DeleteFolder(Request["reportId"]);
                    break;
            }
        }
	}

    protected string BuildSessionsJSON()
    {
        DataSet ds = LibrarySource.GetSessions(this.IncludePastSessions, Utility.GetUserName());

        StringBuilder str = new StringBuilder();
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            if (str.Length > 0)
                str.Append(',');

            str.Append(string.Format("{{" +
                "id:'{0}'," +
                "name:'{1}'," +
                "reports:[{2}]" +
                "}}", DataFormatter.getInt32(row, "SessionId"), Utility.FormatJsonString(row, "SessionName"), BuildChildren(row)));
        }

        return str.ToString();
    }

    protected string BuildMyReportsJSON()
    {
        DataSet ds = LibrarySource.GetMyLibrary(this.IncludePastSessions, Utility.GetUserName());

        StringBuilder str = new StringBuilder();
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            if (str.Length > 0)
                str.Append(',');

            str.Append(string.Format("{{" +
                "id:'{0}'," +
                "name:'{1}'," +
                "reports:[{2}]" +
                "}}", DataFormatter.getInt32(row, "FolderId"), Utility.FormatJsonString(row, "FolderName"), BuildChildren(row)));
        }

        return str.ToString();
    }

    protected string BuildSharedReportsJSON()
    {
        DataSet ds = LibrarySource.GetSharedLibrary(this.IncludePastSessions);

        StringBuilder str = new StringBuilder();
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            if (str.Length > 0)
                str.Append(',');

            str.Append(string.Format("{{" +
                "id:'{0}'," +
                "name:'{1}'," +
                "reports:[{2}]" +
                "}}", DataFormatter.getInt32(row, "FolderId"), Utility.FormatJsonString(row, "FolderName"), BuildChildren(row)));
        }

        return str.ToString();
    }
    
    private string BuildChildren(DataRow row)
    {
        StringBuilder childStr = new StringBuilder();
        foreach (DataRow child in row.GetChildRows("Children"))
        {
            if (!string.IsNullOrEmpty(DataFormatter.getString(child, "ReportName")))
            {
                if (childStr.Length > 0)
                    childStr.Append(',');

                childStr.Append(string.Format("{{" +
                    "id:'{0}'," +
                    "name:'{1}'," +
                    "type:'{2}'," +
                    "snap:'{3}'," +
                    "sub:[{4}]" +
                    "}}", DataFormatter.getInt32(child, "ReportId"), Utility.FormatJsonString(child, "ReportName"),
                            Utility.FormatJsonString(child, "ReportTypeName"), DataFormatter.getInt32(child, "SnapshotCount"),
                            BuildSubReports(child)));
            }
        }
        return childStr.ToString();
    }

    private string BuildSubReports(DataRow child)
    {
        StringBuilder subReportStr = new StringBuilder();
        foreach (DataRow subReport in child.GetChildRows("SubReports"))
        {
            if (!string.IsNullOrEmpty(DataFormatter.getString(subReport, "SubReportName")))
            {
                if (subReportStr.Length > 0)
                    subReportStr.Append(',');

                subReportStr.Append(string.Format("{{" +
                    "id:'{0}'," +
                    "name:'{1}'" +
                    "}}", DataFormatter.getInt32(subReport, "SubReportId"), Utility.FormatJsonString(subReport, "SubReportName")));
            }
        }
        return subReportStr.ToString();
    }
    
    protected void IncludePastSessions_Toggle(object sender, EventArgs e)
	{
		if (this.IncludePastSessions)
			Response.Redirect("ReportLibrary.aspx");
		else
			Response.Redirect("ReportLibrary.aspx?v=1");
	}

	private void ManageClosedDisplay()
	{
		if (this.IncludePastSessions)
		{
			this.hidIncludeClosed.Value = "true";
			this.pastSeriesLink.Text = "Exclude Closed Sessions";
			this.sessionsLabel.Text = "All Sessions:";
			PastSessionToggleButton.Attributes.Add("class", "cancelButtonTop");
		}
		else
		{
			this.hidIncludeClosed.Value = "false";
			this.pastSeriesLink.Text = "Include Closed Sessions";
			this.sessionsLabel.Text = "Active Sessions:";
			PastSessionToggleButton.Attributes.Add("class", "addButtonTop");
		}
	}

    protected void RunReport(string reportId, string subReportId)
    {
        ReportInfo rpt = Utility.LoadReportInfo(Convert.ToInt32(reportId));

        if (rpt.PromptSessions)
        {
            foreach (RadWindow win in RadWindowManager.Windows)
            {
                if (win.ID == "PromptSessionsWindow")
                {
                    win.NavigateUrl = "promptsessions.aspx?r=" + rpt.ReportId.ToString();
                    win.VisibleOnPageLoad = true;
                }
            }
        }
        else
        {
            if(!string.IsNullOrEmpty(subReportId))
                Response.Redirect(rpt.RunPageName + "?r=" + rpt.ReportId.ToString() + "&s=" + subReportId);
            else
                Response.Redirect(rpt.RunPageName + "?r=" + rpt.ReportId.ToString());
        }
    }

    protected void DeleteReport(string reportId)
    {
        SqlHelper.ExecuteNonQuery("RPT_DeleteReport", Convert.ToInt32(reportId));
        Response.Redirect("reportlibrary.aspx");
    }

    protected void DeleteFolder(string folderId)
	{
        SqlHelper.ExecuteNonQuery("RPT_DeleteFolder", Convert.ToInt32(folderId));
        Response.Redirect("reportlibrary.aspx");
	}
}
