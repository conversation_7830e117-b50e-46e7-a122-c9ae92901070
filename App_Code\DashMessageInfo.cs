using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class DashMessageInfo : ISerializable
{
	public int NumMessages = 5;

	// Default constructor.
	public DashMessageInfo() { }

	// Deserialization constructor.
	public DashMessageInfo(SerializationInfo info, StreamingContext context)
	{
		NumMessages = (int)info.GetValue("nm", typeof(int));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("nm", NumMessages);
	}
}

