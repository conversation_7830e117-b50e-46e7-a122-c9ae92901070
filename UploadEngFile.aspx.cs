using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.IO;

public partial class UploadEngFile : System.Web.UI.Page
{
	public const int MAX_FILE_SIZE = (10000 * 1024); // 10 MB

	public int CellId
	{
		get { if (this.ViewState["c"] != null) return (int)this.ViewState["c"]; else return 0; }
		set { this.ViewState["c"] = value; }
	}

	public int SessionId
	{
		get { if (this.ViewState["s"] != null) return (int)this.ViewState["s"]; else return 0; }
		set { this.ViewState["s"] = value; }
	}

	public int TransactionId
	{
		get { if (this.ViewState["t"] != null) return (int)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}

	public DateTime TransactionDate
	{
		get { if (this.ViewState["td"] != null) return (DateTime)this.ViewState["td"]; else return DateTime.MinValue; }
		set { this.ViewState["td"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!string.IsNullOrEmpty(Request.Params["c"]) && !string.IsNullOrEmpty(Request.Params["s"]) && !string.IsNullOrEmpty(Request.Params["t"]))
		{
			this.CellId = Convert.ToInt32(Request.Params["c"]);
			this.SessionId = Convert.ToInt32(Request.Params["s"]);
			this.TransactionId = Convert.ToInt32(Request.Params["t"]);
		}

		if (!Page.IsPostBack)
		{
			DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTransactionDevices", this.CellId, this.SessionId, this.TransactionId);
			if (ds.Tables[0] != null)
			{
				foreach (DataRow row in ds.Tables[0].Rows)
				{
					deviceList.Items.Add(new ListItem(DataFormatter.getString(row, "DeviceTypeName") + " - " + DataFormatter.getString(row, "SerialNumber"),
						DataFormatter.getInt32(row, "DeviceTypeCode").ToString() + "##" + DataFormatter.getString(row, "SerialNumber")));

					this.TransactionDate = DataFormatter.getDateTime(row, "TranDate");
				}
			}
			if (deviceList.Items.Count == 0)
			{
				UploadTable.Visible = false;
				NoDevicesLabel.Visible = true;
			}
		}
    }

	protected void UploadButton_Click(object sender, EventArgs e)
	{
		UploadFile(fileUpload1);

		if (!string.IsNullOrEmpty(fileUpload2.FileName))
			UploadFile(fileUpload2);

		if (!string.IsNullOrEmpty(fileUpload3.FileName))
			UploadFile(fileUpload3);

		if (!string.IsNullOrEmpty(fileUpload4.FileName))
			UploadFile(fileUpload4);

		//Close the window and refresh parent
        string closeScript = string.Format("<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.location.href='TransactionDetail.aspx?c={0}&s={1}&t={2}';CloseRadWindow(); \r\n</script>", this.CellId, this.SessionId, this.TransactionId);
		Page.ClientScript.RegisterStartupScript(typeof(UploadEngFile), "CloseScript", closeScript);
	}

	private void UploadFile(FileUpload currentFile)
	{
		if ((currentFile != null) && (currentFile.PostedFile != null) && (currentFile.PostedFile.ContentLength > 0))
		{
			string rootUploadDir = null;
			string fileName = Path.GetFileName(currentFile.PostedFile.FileName);

			GetFilePaths(ref rootUploadDir, ref fileName);

			string physDir = Server.MapPath(rootUploadDir);
			if (!string.IsNullOrEmpty(physDir) && !Directory.Exists(physDir))
				Directory.CreateDirectory(physDir);

			//if (currentFile.PostedFile.ContentLength > MAX_FILE_SIZE)
			//{
			//	popupError("Your file exceeds the " + MAX_FILE_SIZE / 1024 + " Mb size limit. Please reduce the file size and try again.");
			//	return;
			//}

			string targetPath = physDir + fileName;
			string collisionPath = targetPath;
			int collisionSuffix = 1;

			string[] nameParts = fileName.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);

			while (File.Exists(collisionPath))
			{
				collisionSuffix++;
				collisionPath = physDir + nameParts[0] + "(" + collisionSuffix.ToString() + ")." + nameParts[1];
			}

			if (collisionSuffix > 1)
			{
				targetPath = collisionPath;
				fileName = fileName + collisionSuffix;
			}

			//Save the file to the Server
			currentFile.PostedFile.SaveAs(targetPath);
		}
	}

	private void GetFilePaths(ref string rootUploadPath, ref string fileName)
	{
		string deviceTypeName = null;
		string deviceTypeCode = null;
		string serialNumber = null;
		DateTime transactionDate = DateTime.MinValue;
		
		//split values from dropdown
		if (!string.IsNullOrEmpty(deviceList.SelectedValue))
		{
			string[] selectedDeviceNameParts = deviceList.SelectedItem.Text.Split(new string[] { " - " }, StringSplitOptions.RemoveEmptyEntries);
			string[] selectedDeviceValueParts = deviceList.SelectedItem.Value.Split(new string[] { "##" }, StringSplitOptions.RemoveEmptyEntries); ;

			deviceTypeName = selectedDeviceNameParts[0];
			serialNumber = selectedDeviceNameParts[1];
			deviceTypeCode = selectedDeviceValueParts[0];
		}

		//rootUploadPath = this.ResolveUrl(Utility.GetRootUploadPath(deviceTypeName, serialNumber, this.TransactionDate));
		fileName = "CELL=" + this.CellId.ToString() + "_SESSION=" + this.SessionId.ToString() +
						"_TRX=" + this.TransactionId.ToString() + "_DC=" + deviceTypeCode + "_SN=" + serialNumber + "_" + fileName;
	}

	private void popupError(string errorMessage)
	{
		if (!string.IsNullOrEmpty(errorMessage))
		{
			string errorScript = "<script language='javascript'>\r\nalert('" + errorMessage + "');\r\n</script>";
			Page.ClientScript.RegisterStartupScript(GetType(), "ErrorScript", errorScript);
		}
	}
}
