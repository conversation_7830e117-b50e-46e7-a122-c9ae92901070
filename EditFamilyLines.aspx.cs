using System;
using System.Web.UI;

public partial class EditFamilyLines : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_FamilyLine", true);
		DataGrid1.DataBind();

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				DataGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}
	}
}
