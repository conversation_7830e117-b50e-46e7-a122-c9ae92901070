﻿using System;
using System.Data;
using System.Text;
using System.Web.UI;

public partial class QueryReports : System.Web.UI.Page {
    protected void Page_Load(object sender, EventArgs e) {
        DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_QueryReports");
        
        RadGrid1.DataSource = ds;
        RadGrid1.DataBind();

        if (!Page.IsPostBack) {
            if (!string.IsNullOrEmpty(Request.Params["page"]))
                RadGrid1.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
        }
    }
}
