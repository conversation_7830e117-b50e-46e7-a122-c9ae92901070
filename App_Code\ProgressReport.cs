using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Text;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class ProgressReport : BaseReport
{
    private bool includeAverage = false;
	private bool includeTimeOnRows = true;
    private bool useMaxIfManualVolume = false;

    public ProgressReport(ReportInfo repInfo) : base(repInfo, false) { }

	public ProgressReport(ReportInfo repInfo, bool includeAverage, bool includeTimeOnRows, bool useMaxIfManualVolume) : base(repInfo, false) 
    {
        this.includeAverage = includeAverage;
		this.includeTimeOnRows = includeTimeOnRows;
		this.useMaxIfManualVolume = useMaxIfManualVolume;
    }

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        StringBuilder mdxSelect = new StringBuilder();
        string mdxCategory = null;
        string mdxFromWhere = null;
        string timeHier = OLAPHelper.GetTimeHier(repInfo.ProgressInfo.GroupingId);
        bool splitByDevice = this.repInfo.ProgressInfo.SplitByCell;
		string aggregateFunction = "SUM";

        mdxSetup.Append(string.Format("WITH {0}\r\n", OLAPHelper.BuildMdxFilteredTime(repInfo.ProgressInfo.GroupingId, repInfo.StartDate, repInfo.EndDate, "FilteredTime")));

		//look at the session we are dealing with, it is is Manual Volume, we want to select a MAX from the transaction
		//range, rather than SUM because the values entered are already a sumation, not incremental transaction counts
		if (useMaxIfManualVolume && repInfo.AttachedSessions != null && repInfo.AttachedSessions.Count == 1)
		{
			bool isManualVolume = false;
			SessionInfo session = repInfo.AttachedSessions[0];
			DataSet ds = SqlHelper.ExecuteDataset("RPT_GetSession", session.SessionId);
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				isManualVolume = DataFormatter.getBool(row, "IsManualVolume");
			}
			
			if (isManualVolume)
				aggregateFunction = "MAX";
		}

        mdxSetup.Append("MEMBER [Transaction Cnt] AS ' ");
		mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, repInfo.StartDate, repInfo.EndDate, "FilteredTime", "[Measures].[Transaction Count]", aggregateFunction));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");
        mdxSetup.Append("MEMBER [Media Cnt] AS ' ");
		mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, repInfo.StartDate, repInfo.EndDate, "FilteredTime", "[Measures].[Media Count]", aggregateFunction));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");
        mdxSetup.Append("MEMBER [Stat Tran Cnt] AS ' ");
		mdxSetup.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, repInfo.StartDate, repInfo.EndDate, "FilteredTime", "[Measures].[Stat Tran Count]"));
        mdxSetup.Append("', FORMAT_STRING = \"#,#\" \r\n");
        if(includeAverage)
            mdxSetup.Append("MEMBER [Media Per Tran] AS ' [Media Cnt]/[Transaction Cnt] ', FORMAT_STRING = \"#,##0.0\" \r\n");

		mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.RateTypeId1, repInfo.ProgressInfo.DimensionName1,
				repInfo.ProgressInfo.DimensionMembers1, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel1, repInfo.ProgressInfo.SpecRate1, repInfo.ProgressInfo.WorkLoad1, repInfo.ProgressInfo.Format1));
        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.RateTypeId2, repInfo.ProgressInfo.DimensionName2,
				repInfo.ProgressInfo.DimensionMembers2, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel2, repInfo.ProgressInfo.SpecRate2, repInfo.ProgressInfo.WorkLoad2, repInfo.ProgressInfo.Format2));
        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.RateTypeId3, repInfo.ProgressInfo.DimensionName3,
				repInfo.ProgressInfo.DimensionMembers3, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel3, repInfo.ProgressInfo.SpecRate3, repInfo.ProgressInfo.WorkLoad3, repInfo.ProgressInfo.Format3));
        mdxSetup.Append(BuildProgressSeries(timeHier, repInfo.StartDate, repInfo.EndDate, repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.RateTypeId4, repInfo.ProgressInfo.DimensionName4,
				repInfo.ProgressInfo.DimensionMembers4, repInfo.ProgressInfo.Normalize, repInfo.ProgressInfo.SpecLabel4, repInfo.ProgressInfo.SpecRate4, repInfo.ProgressInfo.WorkLoad4, repInfo.ProgressInfo.Format4));

		BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.DimensionName1, repInfo.ProgressInfo.RateTypeId1, repInfo.ProgressInfo.SpecLabel1, repInfo.ProgressInfo.SpecRate1);
        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.DimensionName2, repInfo.ProgressInfo.RateTypeId2, repInfo.ProgressInfo.SpecLabel2, repInfo.ProgressInfo.SpecRate2);
        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.DimensionName3, repInfo.ProgressInfo.RateTypeId3, repInfo.ProgressInfo.SpecLabel3, repInfo.ProgressInfo.SpecRate3);
        BuildProgressSelect(mdxSelect, repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.DimensionName4, repInfo.ProgressInfo.RateTypeId4, repInfo.ProgressInfo.SpecLabel4, repInfo.ProgressInfo.SpecRate4);

        if (splitByDevice)
        {
            string deviceList = OLAPHelper.BuildMdxDeviceTupleFromFilters(repInfo.ReportFilters);
            if (string.IsNullOrEmpty(deviceList))
                deviceList = "[Device].[Type - Device].[Device]";
            string condition = null;

            //if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName1) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName1))
			//    condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName1, repInfo.ProgressInfo.SeriesName1);

            //if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName2) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName2))
			//    condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName2, repInfo.ProgressInfo.SeriesName2);

            //if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName3) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName3))
			//    condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName3, repInfo.ProgressInfo.SeriesName3);

            //if (!string.IsNullOrEmpty(repInfo.ProgressInfo.SeriesName4) && !string.IsNullOrEmpty(repInfo.ProgressInfo.DimensionName4))
			//    condition = (condition != null ? (condition + " OR ") : "") + string.Format(" [{0}] > 0 OR [Count: {1}] > 0 ", repInfo.ProgressInfo.SeriesName4, repInfo.ProgressInfo.SeriesName4);

            //if (condition == null)

            condition = " [Measures].[Transaction Cnt] > 0 OR [Measures].[Media Cnt] > 0 ";

            if (includeAverage)
                mdxSelect.Insert(0, string.Format("SELECT ({{[Measures].[Transaction Cnt], [Measures].[Media Cnt], [Measures].[Media Per Tran], "));
            else
                mdxSelect.Insert(0, string.Format("SELECT ({{[Measures].[Transaction Cnt], [Measures].[Media Cnt], "));
            mdxSelect.Append(string.Format("}}, Filter({0}, {1})) on columns", deviceList, condition));
        }
        else
        {
            if (includeAverage)
                mdxSelect.Insert(0, "SELECT {[Measures].[Transaction Cnt], [Measures].[Media Cnt], [Measures].[Media Per Tran], ");
            else
                mdxSelect.Insert(0, "SELECT {[Measures].[Transaction Cnt], [Measures].[Media Cnt], ");
            mdxSelect.Append("} on columns");
        }

		if (includeTimeOnRows)
			mdxCategory = ", \r\nNON EMPTY {FilteredTime} on rows \r\n";
		
        mdxFromWhere = string.Format(" FROM [Reporting] {0}", 
                OLAPHelper.BuildMdxWhereTuples(repInfo.AttachedSessions, repInfo.ReportFilters, splitByDevice, "WHERE ", true));

        string mdx = mdxSetup.ToString() + mdxSelect.ToString() + mdxCategory + mdxFromWhere;

        if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Request.Params["showmdx"])) {
            throw new ApplicationException(mdx);
        }
		ExecuteMdx(mdx);
		BuildGridDisplay();
    }

    private string BuildFilteredAggregate(string timeHier, string dimensionTuple, DateTime startDate, DateTime endDate)
    {
        StringBuilder retVal = new StringBuilder();
        string filtedTimeSetName = "FilteredTime";

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
				+ "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), {4}), "
				+ "[Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
		}

		if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
        }

        retVal.Append(string.Format("SUM(({0}, [Aggregate Type].[Global Level Function].&[Sum]), [Measures].[Stat Value Sum])", dimensionTuple));

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
			retVal.Append(")");

		retVal.Append(" + ");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "MAX(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
				+ "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), {4}), "
				+ "[Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

		}

        if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MAX(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MAX(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
        }

        retVal.Append(string.Format("MAX(({0}, [Aggregate Type].[Global Level Function].&[Max]), [Measures].[Stat Value Max])", dimensionTuple));

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
			retVal.Append(")");

        retVal.Append(" + ");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "MIN(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
				+ "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), {4}), "
				+ "[Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

		}

		if (!DateTime.MinValue.Equals(startDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "MIN(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
				+ "[Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

		}

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "MIN(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
        }

        retVal.Append(string.Format("MIN(({0}, [Aggregate Type].[Global Level Function].&[Min]), [Measures].[Stat Value Min])", dimensionTuple));

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
			retVal.Append(")");

        retVal.Append(" + (");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
				+ "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), {4}), "
				+ "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

		} 
		
		if (!DateTime.MinValue.Equals(startDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
				+ "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

		}

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
        }

        retVal.Append(string.Format("SUM(({0}, [Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Value Sum])", dimensionTuple));

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
			retVal.Append(")");

        retVal.Append(" / ");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
		{
			retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
				+ "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1, "
				+ "RANK([Time].[{1}].[DateTime].&[{3}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) - "
				+ "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime])) + 1), {4}), "
				+ "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count]),",
				filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

		}

		if (!DateTime.MinValue.Equals(startDate))
        {
            retVal.Append(string.Format("IIF([{0}_StartRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), "
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count]),",
                filtedTimeSetName, timeHier, startDate.AddMinutes(30).AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));

        }

        if (!DateTime.MinValue.Equals(endDate))
        {
            retVal.Append(string.Format("IIF([{0}_EndRank]>0 AND IsAncestor([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime].&[{2}]),"
                + "SUM(((SUBSET(DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]), 0,"
                + "RANK([Time].[{1}].[DateTime].&[{2}], DESCENDANTS([Time].[{1}].CurrentMember, [Time].[{1}].[DateTime]))-1), {3}), "
                + "[Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count]),",
                filtedTimeSetName, timeHier, endDate.AddSeconds(-1).ToString("yyyy-MM-ddTHH:mm:ss"), dimensionTuple));
        }

        retVal.Append(string.Format("SUM(({0}, [Aggregate Type].[Global Level Function].&[AverageOfChildren]), [Measures].[Stat Tran Count])", dimensionTuple));

        if (!DateTime.MinValue.Equals(endDate))
            retVal.Append(")");

        if (!DateTime.MinValue.Equals(startDate))
            retVal.Append(")");

		if (!DateTime.MinValue.Equals(startDate) && !DateTime.MinValue.Equals(endDate))
			retVal.Append(")");

        retVal.Append(")");
        return retVal.ToString();
    }

    private string BuildProgressSeries(string timeHier, DateTime startDate, DateTime endDate, string seriesName, ReportHelper.RateTypeEnum rateTypeId, string dimensionName,
            List<string> dimensionMembers, bool normalize, string specName, decimal specRate, decimal workloadAmount, string formatString)
    {
        StringBuilder retVal = new StringBuilder();

        if (!string.IsNullOrEmpty(seriesName) && !string.IsNullOrEmpty(dimensionName))
        {
            string normalizeString = null;
            string rateMeasure = null;
            string rawFormatString = null;
            string measureField = BuildMeasureName(dimensionName, rateTypeId);

			if (workloadAmount <= 0)
				workloadAmount = 0;

            rawFormatString = "FORMAT_STRING = \"#,#\"";
			
			if (!string.IsNullOrEmpty(formatString))
				formatString = string.Format("FORMAT_STRING = \"{0}\"", formatString);
		    
			if (rateTypeId == ReportHelper.RateTypeEnum.BY_STAT_VALUE)
            {
				if(string.IsNullOrEmpty(formatString))
					formatString = "FORMAT_STRING = \"#,#\"";

                // Add the member
                retVal.Append(string.Format("MEMBER [{0}] AS ' IIF([Measures].[Stat Tran Cnt]>0,{1},NULL) ', {2}\r\n", seriesName, BuildFilteredAggregate(timeHier, OLAPHelper.BuildMdxLevelTuple(false, dimensionName, dimensionMembers, false), startDate, endDate), formatString));

                // Add the spec member
                if (!string.IsNullOrEmpty(specName) && specRate > 0)
					retVal.Append(string.Format("MEMBER [Spec: {0}] AS ' IIF([Measures].[Stat Tran Cnt]>0,{2},NULL) ', {3}\r\n", specName, rateMeasure, specRate, formatString));
            }
            else
            {
                switch (rateTypeId)
                {
                    case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
					case ReportHelper.RateTypeEnum.PER_WORKLOAD_MEDIA: //Per Workload Media
						rateMeasure = "[Measures].[Media Cnt]";

						if (string.IsNullOrEmpty(formatString))
							formatString = "FORMAT_STRING = \"#,##0.0\"";
                        break;
                    case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
					case ReportHelper.RateTypeEnum.PER_WORKLOAD_TRANSACTIONS: //Per Workload Transactions
						rateMeasure = "[Measures].[Transaction Cnt]";

						if (string.IsNullOrEmpty(formatString))
							formatString = "FORMAT_STRING = \"#,##0.0\"";
                        break;
                    case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                        rateMeasure = "[Measures].[Media Cnt]";

						if (string.IsNullOrEmpty(formatString))
							formatString = "FORMAT_STRING = \"#,##0.000\\%\"";
						else
							formatString = formatString.Substring(0, formatString.Length - 1) + "\\%\""; 
						break;
                    case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                        rateMeasure = "[Measures].[Transaction Cnt]";

						if (string.IsNullOrEmpty(formatString))
							formatString = "FORMAT_STRING = \"#,##0.000\\%\"";
						else
							formatString = formatString.Substring(0, formatString.Length - 1) + "\\%\"";
                        break;
                    case ReportHelper.RateTypeEnum.MEDIA_PER_INCIDENT: // Media per Incident
                        rateMeasure = "[Measures].[Media Cnt]";

						if (string.IsNullOrEmpty(formatString))
							formatString = "FORMAT_STRING = \"#,##0.0\"";
                        break;
                    case ReportHelper.RateTypeEnum.TRANSACTIONS_PER_INCIDENT: // Transactions per Incident
                        rateMeasure = "[Measures].[Transaction Cnt]";

						if (string.IsNullOrEmpty(formatString))
							formatString = "FORMAT_STRING = \"#,##0.0\"";
                        break;
					default:
                        throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
                }

                if (normalize && specRate != 0)
                {
                    normalizeString = "/" + specRate.ToString();
                }

                retVal.Append(string.Format("SET [{0}Set] AS ' {1} '\r\n", seriesName, OLAPHelper.BuildMdxLevelTuple(false, dimensionName, dimensionMembers, false)));

                // Add the raw member
				retVal.Append(string.Format("MEMBER [Count: {0}] AS ' IIF({1}>0, SUM([{0}Set], ", seriesName, rateMeasure));
                retVal.Append(OLAPHelper.BuildMdxFilteredMeasure(timeHier, startDate, endDate, "FilteredTime", measureField));
                retVal.Append(string.Format("),NULL) ', {0}\r\n", rawFormatString));

                // Add the transformed member
				string adjustedRaw = string.Format("[Count: {0}]", seriesName);
                if (this.repInfo.ProgressInfo.AssumeOneFailure)
                {
					adjustedRaw = string.Format("IIF([Count: {0}] > 0, [Count: {0}], -1)", seriesName);
                }

                switch (rateTypeId)
                {
                    case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
                    case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0, ({4} * (1000000/{1})) {2} ,NULL)', {3}\r\n", seriesName, rateMeasure, normalizeString, formatString, adjustedRaw));
                        break;
                    case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                    case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0, ({4} / {1} * 100) {2} ,NULL)', {3}\r\n", seriesName, rateMeasure, normalizeString, formatString, adjustedRaw));
                        break;
                    case ReportHelper.RateTypeEnum.MEDIA_PER_INCIDENT: // Media per Incident
                    case ReportHelper.RateTypeEnum.TRANSACTIONS_PER_INCIDENT: // Transactions per Incident
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {4} <> 0, ({1} / {4}) {2} ,NULL)', {3}\r\n", seriesName, rateMeasure, normalizeString, formatString, adjustedRaw));
                        break;
					case ReportHelper.RateTypeEnum.PER_WORKLOAD_MEDIA: //Per Workload Media
					case ReportHelper.RateTypeEnum.PER_WORKLOAD_TRANSACTIONS: //Per Workload Transactions
                        retVal.Append(string.Format("MEMBER [{0}] AS ' IIF({1}>0 AND {2}>0, ({5} * ({1}/{2})) {3} ,NULL)', {4}\r\n", seriesName, workloadAmount, rateMeasure, normalizeString, formatString, adjustedRaw));
                        break;
                    default:
                        throw new ApplicationException("Unsupported rate type - " + rateTypeId.ToString());
                }

                // Add the spec member
                if (!string.IsNullOrEmpty(specName) && specRate > 0)
                {
                    retVal.Append(string.Format("MEMBER [Spec: {0}] AS ' IIF({1}>0,{2},NULL) ', FORMAT_STRING = \"#,#\"\r\n", specName, rateMeasure, specRate));
                }
            }
        }

        return retVal.ToString();
    }

    private string BuildMeasureName(string dimensionName, ReportHelper.RateTypeEnum rateTypeId)
    {
        string retVal = null;

        if (!string.IsNullOrEmpty(dimensionName))
        {
            if (dimensionName.StartsWith("[Statistic]."))
            {
                switch (rateTypeId)
                {
                    case ReportHelper.RateTypeEnum.PER_MILLION_MEDIA: // Per Million Media
					case ReportHelper.RateTypeEnum.PER_WORKLOAD_MEDIA: // Per Workload Media
                    case ReportHelper.RateTypeEnum.PERCENTAGE_OF_MEDIA: // Percentage of Media
                    case ReportHelper.RateTypeEnum.MEDIA_PER_INCIDENT: // Media per Incident
                        retVal = "[Measures].[Stat Media Count]";
                        break;
                    case ReportHelper.RateTypeEnum.PER_MILLION_TRANSACTIONS: // Per Million Transactions
					case ReportHelper.RateTypeEnum.PER_WORKLOAD_TRANSACTIONS: // Per Workload Transactions
                    case ReportHelper.RateTypeEnum.PERCENTAGE_OF_TRANSACTIONS: // Percentage of Transactions
                    case ReportHelper.RateTypeEnum.TRANSACTIONS_PER_INCIDENT: // Transactions per Incident
                    default:
                        retVal = "[Measures].[Stat Tran Count]";
                        break;
                } 
            }
            else if (dimensionName.StartsWith("[Event Type]."))
                retVal = "[Measures].[Event Count]";
            else if (dimensionName.StartsWith("[Setting]."))
                retVal = "[Measures].[Transaction Count]";
            else
                retVal = "[Measures].[Observation Count]";
        }
        return retVal;
    }

    private void BuildProgressSelect(StringBuilder mdxSelect, string seriesName, string dimensionName, ReportHelper.RateTypeEnum rateTypeId, string specName, decimal specRate)
    {
        if (!string.IsNullOrEmpty(seriesName) && !string.IsNullOrEmpty(dimensionName))
        {
            if (mdxSelect.Length > 0)
                mdxSelect.Append(",");

            if (rateTypeId != ReportHelper.RateTypeEnum.BY_STAT_VALUE)
            {
                // Add the raw member
                mdxSelect.Append(string.Format("[Count: {0}], ", seriesName));
            }

            // Add the transformed member
            mdxSelect.Append(string.Format("[{0}]", seriesName));

            // Add the spec
            if (!string.IsNullOrEmpty(specName) && specRate > 0)
            {
                mdxSelect.Append(string.Format(", [Spec: {0}]", specName));
            }
        }
    }

    public override void PopulateChart(Chart chart, bool includeToolTips)
    {
        TupleCollection colTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[1].Set.Hierarchies;

        Dundas.Charting.WebControl.Axis axisX = chart.ChartAreas[0].AxisX;
        Dundas.Charting.WebControl.Axis axisY = chart.ChartAreas[0].AxisY;

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
            priorSeries.Add(series.Name, series);

        chart.Series.Clear();
        double maxYVal = 0;

        // Build chart series data
        for (int col = 0; col < colTuples.Count; col++) // Skip Transaction Count and Media Count
        {
            Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];

            Series series = null;
            string seriesName = null;

            if (colTuple.Members.Count > 1)
                seriesName = string.Format("{0}: {1}", colTuple.Members[0].Caption, colTuple.Members[1].Caption);
            else
                seriesName = colTuple.Members[0].Caption;

			if (seriesName.StartsWith("Count: ") || seriesName.StartsWith("Transaction Cnt: ") || seriesName.StartsWith("Media Cnt: ")
                    || seriesName.Equals("Transaction Cnt") || seriesName.Equals("Media Cnt"))
                continue;

            if (priorSeries.ContainsKey(seriesName))
            {
                series = priorSeries[seriesName];
                priorSeries.Remove(series.Name);
                series.Points.Clear();
            }
            else
            {
                series = new Series(seriesName);
                series["DrawingStyle"] = "Cylinder";

                series.Type = SeriesChartType.Line;
            }
            series.XValueIndexed = true;

            for (int row = 0; row < rowTuples.Count; row++)
            {
                Cell cellA = cellSet.Cells[col, row];

                double YVal = 0;

                if (this.repInfo.ProgressInfo.Normalize && seriesName.StartsWith("Spec: "))
                    YVal = 1;
                else
                    YVal = (cellA.Value != null) ? double.Parse(cellA.Value.ToString()) : 0;

                maxYVal = (maxYVal < Math.Abs(YVal) ? Math.Abs(YVal) : maxYVal);

                DataPoint dp = new DataPoint(0, Math.Abs(YVal));
                if (this.repInfo.ProgressInfo.AssumeOneFailure && YVal <= 0)
                {
                    dp.MarkerColor = Color.Red;
                    dp.MarkerSize = 5;
                    dp.MarkerStyle = MarkerStyle.Circle;
                }

                dp.AxisLabel = rowTuples[row].Members[0].Caption;

				if (includeToolTips)
				{
					string formattedValue = cellA.FormattedValue;
					string tempName = seriesName;

					if (tempName.StartsWith("Spec: "))
						tempName = tempName.Substring("Spec: ".Length, tempName.Length - "Spec: ".Length);

					if ((string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName1) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format1) == 0)
							|| (string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName2) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format2) == 0)
							|| (string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName3) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format3) == 0)
							|| (string.Compare(tempName, this.repInfo.ProgressInfo.SeriesName4) == 0 && string.Compare("hex", this.repInfo.ProgressInfo.Format4) == 0)
						)
                        formattedValue = String.Format("{0:X}", Convert.ToInt32(Math.Abs(YVal)));

					dp.ToolTip = string.Format("{0}\r\nValue: {1}", series.Name, formattedValue);
				}

                series.Points.Add(dp);
            }

            chart.Series.Add(series);
        }

        chart.Titles["Title1"].Text = repInfo.ChartTitle;

        if (string.IsNullOrEmpty(axisY.Title))
        {
            axisY.Title = "Rate";
            axisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
        }

        if (!string.IsNullOrEmpty(repInfo.MinYScale))
            axisY.Minimum = double.Parse(repInfo.MinYScale, System.Globalization.NumberStyles.Any);
        else
            axisY.Minimum = 0;

        if (!string.IsNullOrEmpty(repInfo.MaxYScale))
        {
            axisY.Maximum = double.Parse(repInfo.MaxYScale, System.Globalization.NumberStyles.Any);
            ResetAxisMarks(axisY);
        }
        else
        {
            if (maxYVal > 0 && maxYVal < int.MaxValue)
            {
                axisY.Maximum = RoundAxis(maxYVal, 100);
                ResetAxisMarks(axisY);
            }
        }

        ResetAxisMarks(axisX);

        axisX.Minimum = 1;
        axisX.Maximum = rowTuples.Count;

        if (this.repInfo.ProgressInfo.AssumeOneFailure)
        {
            chart.Annotations.Clear();
            SixSigma.AddTextAnnotation("* Red indicates one failure assumed", axisX, axisY, axisX.Minimum + ((axisX.Maximum - axisX.Minimum) / 20),
                axisY.Minimum + ((axisY.Maximum - axisY.Minimum) / 8),
                Color.Red, new Font("Arial", 8, FontStyle.Bold), chart);
        }
        chart.ChartAreas[0].ReCalc();
    }
}
