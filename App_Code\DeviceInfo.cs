using System;
using System.Collections;
using System.Runtime.Serialization;

[Serializable()]
public class DeviceInfo : ISerializable
{
	public int DeviceId = 0;
	public int DeviceTypeId = 0; 
	public string SerialNumber = null;
	public int DeviceStatusId = 0;

	// Default constructor.
	public DeviceInfo() { }

	// Deserialization constructor.
	public DeviceInfo(SerializationInfo info, StreamingContext context)
	{
		DeviceId = (int)info.GetValue("DeviceId", typeof(int));
		DeviceTypeId = (int)info.GetValue("DeviceTypeId", typeof(int));
		SerialNumber = (string)info.GetValue("SerialNumber", typeof(string));
		DeviceStatusId = (int)info.GetValue("DeviceStatusId", typeof(int));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("DeviceId", DeviceId);
		info.AddValue("DeviceTypeId", DeviceTypeId);
		info.AddValue("SerialNumber", SerialNumber);
		info.AddValue("DeviceStatusId", DeviceStatusId);
	}
}
