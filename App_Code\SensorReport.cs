﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class SensorReport : BaseReport
{
    public SensorReport(ReportInfo repInfo, SubReportInfo subRepInfo) : base(repInfo, false) { this.subRepInfo = subRepInfo; }
    protected SubReportInfo subRepInfo = null;
    private List<int> cumMediaCnt = null;
	private List<int> cumTranCnt = null;
	
	public override void InitializeChart(Chart chart, string pageOrControlName)
	{
		base.InitializeChart(chart, pageOrControlName);
		chart.Customize += new CustomizeEventHandler(chart_Customize);
	}

	public override void LoadData()
	{
		StringBuilder mdxSetup = new StringBuilder();
		StringBuilder mdxSelect = new StringBuilder();
		string mdxCategory = null;
		string mdxFromWhere = null;

        List<string> dimMembers = repInfo.DimensionMembers;
        if (subRepInfo != null && subRepInfo.DimensionMembers.Count > 0)
            dimMembers = subRepInfo.DimensionMembers;

        List<SessionInfo> sessions = repInfo.AttachedSessions;
        if (subRepInfo != null && subRepInfo.AttachedSessions.Count > 0)
            sessions = subRepInfo.AttachedSessions;

        Dictionary<string, List<string>> reportFilters = repInfo.ReportFilters;
        if(subRepInfo != null && subRepInfo.DeviceFilters.Count > 0)
        {
            reportFilters = new Dictionary<string, List<string>>();
            foreach(string key in repInfo.ReportFilters.Keys)
            {
                if (string.Compare(key, DieboldConstants.DEVICE_FILTER_DIMENSION_NAME, true) == 0)
                    reportFilters.Add(key, subRepInfo.DeviceFilters);
                else
                    reportFilters.Add(key, repInfo.ReportFilters[key]);
            }
        }

        if (repInfo.DimensionName != null)
		{
			//Get Min, Max, and Average
			mdxSetup.Append(string.Format("WITH {0} \r\n" +
				"Member [QuantVal] AS 'IIF([Measures].[Frequency] > 0, [Measures].[Frequency]*[Distribution Values].[QuantizedValue].CurrentMember.MemberValue/[Measures].[Frequency], NULL)' \r\n" +
				"Member [FreqVal] AS 'IIF([Measures].[Frequency] > 0, [Measures].[Frequency] * [Distribution Values].[QuantizedValue].CurrentMember.MemberValue, NULL)' \r\n" +
				"Member [Min] AS 'MIN([Distribution Values].[QuantizedValue].[QuantizedValue].Members,[QuantVal])' \r\n" +
				"Member [Max] AS 'MAX([Distribution Values].[QuantizedValue].[QuantizedValue].Members,[QuantVal])' \r\n" +
				"Member [Avg] AS 'SUM([Distribution Values].[QuantizedValue].[QuantizedValue].Members,[FreqVal]) / [Measures].[Frequency]' \r\n", 
				OLAPHelper.BuildMdxFilteredTime(repInfo.ParetoInfo.GroupingId, this.repInfo.StartDate, this.repInfo.EndDate, "FilteredTime")));

				mdxSelect.Append(string.Format("SELECT ({0}, {{[Min],[Max],[Avg],[Media Count],[Transaction Count]}}) ON COLUMNS, \r\n",
                OLAPHelper.BuildMdxLevelTuple(false, repInfo.DimensionName, dimMembers, false)));
			
			mdxCategory = "NON EMPTY [FilteredTime] ON ROWS \r\n";
		}
		else
		{
			throw new ApplicationException("Unsupported distribution dimension - '" + repInfo.DimensionName + "'.");
		}

		mdxFromWhere = string.Format("FROM [Reporting] WHERE {0}",
            OLAPHelper.BuildMdxWhereTuples(sessions, reportFilters, false, null, true));

		string mdx = mdxSetup.ToString() + mdxSelect.ToString() + mdxCategory + mdxFromWhere;

        if (!string.IsNullOrEmpty(System.Web.HttpContext.Current.Request.Params["showmdx"])) {
            throw new ApplicationException(mdx);
        }
        ExecuteMdx(mdx);
		BuildAggregates();
		BuildGridDisplay();
	}

	private void BuildAggregates()
	{
		cumMediaCnt = new List<int>();
		cumTranCnt = new List<int>();

		TupleCollection colTuples = this.cellSet.Axes[0].Set.Tuples;
		HierarchyCollection colHierarchies = this.cellSet.Axes[0].Set.Hierarchies;
		TupleCollection rowTuples = this.cellSet.Axes[1].Set.Tuples;
		HierarchyCollection rowHierarchies = this.cellSet.Axes[1].Set.Hierarchies;
		
		for (int col = 0; col < colTuples.Count; col = col + 5)
		{
			for (int row = 0; row < rowTuples.Count; row++)
			{
				Cell cellA = this.cellSet.Cells[col + 3, row]; // Media Count
				Cell cellB = this.cellSet.Cells[col + 4, row]; // Transactions Count

				int mediaCnt = (cellA.Value == null ? 0 : int.Parse(cellA.Value.ToString()));
				int tranCnt = (cellB.Value == null ? 0 : int.Parse(cellB.Value.ToString()));

				// Calculate cumulative observations and transactions/media
				if (cumMediaCnt.Count > 0)
					mediaCnt += cumMediaCnt[cumMediaCnt.Count - 1];
				if (cumTranCnt.Count > 0)
					tranCnt += cumTranCnt[cumTranCnt.Count - 1];

				cumMediaCnt.Add(mediaCnt);
				cumTranCnt.Add(tranCnt);
			}
		}
	}

	public CellSet GetCellSet()
	{
		return cellSet;
	}

	public override void BuildGridDisplay()
	{
		base.BuildGridDisplay();	
	}

	public override void PopulateChart(Chart chart, bool includeToolTips)
	{
	}

	public void PopulateChart(Chart chart, bool includeToolTips, int colStartIndex, int colEndIndex)
	{
		TupleCollection colTuples = cellSet.Axes[0].Set.Tuples;
		HierarchyCollection colHierarchies = cellSet.Axes[0].Set.Hierarchies;
		TupleCollection rowTuples = cellSet.Axes[1].Set.Tuples;
		HierarchyCollection rowHierarchies = cellSet.Axes[1].Set.Hierarchies;
		double maxY = 0;
		double maxY2 = 0;

		Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
		foreach (Series series in chart.Series)
		{
			if (!priorSeries.ContainsKey(series.Name))
				priorSeries.Add(series.Name, series);
		}

		chart.Series.Clear();

		bool isFirst = true;
		string chartTitle = null;
		string legendText = null;
		Series throughputSeries = null;

		int lineStyleIndex = 0;
		Random rand = new Random();
		
		// Build chart series data
		for (int col = colStartIndex; col <= colEndIndex; col++) 
		{
			int width = rand.Next(2, 4);
			List<ChartDashStyle> lineStyles = new List<ChartDashStyle>();
			lineStyles.Add(ChartDashStyle.Solid);
			lineStyles.Add(ChartDashStyle.Dot); 
			lineStyles.Add(ChartDashStyle.Dash);
			lineStyles.Add(ChartDashStyle.DashDotDot); 
			lineStyles.Add(ChartDashStyle.DashDot);

            Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];

			Series series = null;
			string seriesName = colTuple.Members[0].Caption + " " + colTuple.Members[1].Caption;

			string[] seriesNameParts = seriesName.Split(new string[] { ":" }, StringSplitOptions.RemoveEmptyEntries);
			legendText = seriesNameParts[1];

			if (isFirst)
			{
				chartTitle = seriesNameParts[0];
				isFirst = false;
			}

			if (priorSeries.ContainsKey(legendText))
			{
				series = priorSeries[legendText];
				priorSeries.Remove(series.Name);
				series.Points.Clear();

				series.BorderWidth = width;
				series.BorderStyle = lineStyles[lineStyleIndex];
				
				lineStyleIndex++;
				if (lineStyleIndex > lineStyles.Count - 1)
					lineStyleIndex = 0;
			}
			else
			{
				series = new Series(legendText);
				series.Type = SeriesChartType.Line;
				
				series.BorderWidth = width;
				series.BorderStyle = lineStyles[lineStyleIndex];
				lineStyleIndex++;
				if (lineStyleIndex > lineStyles.Count - 1)
					lineStyleIndex = 0;
			}

			if ((col + 1) % 5 == 0) //Transaction Count
			{
				continue;
			}
			else if ((col + 1) % 5 == 4) // Media Count
			{
				if (throughputSeries != null)
					continue;
				
				series.Name = "Throughput";
				series.YAxisType = AxisType.Secondary;
				series.Color = Color.Red;
				throughputSeries = series;

				double cumMedia = 0;

				for (int row = 0; row < rowTuples.Count; row++)
				{
                    Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];

					Cell cell = cellSet.Cells[col, row];

					object valObj = cell.Value;
					DataPoint dp = null;
					if (valObj != null)
						cumMedia += double.Parse(cell.Value.ToString());

					dp = new DataPoint(0, cumMedia);

					maxY2 = (maxY2 > dp.GetValueY(0)) ? maxY2 : dp.GetValueY(0);
					dp.AxisLabel = string.Format("{0}", rowTuple.Members[0].Caption);

					if (includeToolTips)
						dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}", cumMedia.ToString("#,##0"), rowTuple.Members[0].Caption, "Throughput");

					series.Points.Add(dp);
				}
			}
			else //Min, Max, Average Columns
			{
				for (int row = 0; row < rowTuples.Count; row++)
				{
                    Microsoft.AnalysisServices.AdomdClient.Tuple rowTuple = rowTuples[row];

					Cell cell = cellSet.Cells[col, row];

					object valObj = cell.Value;
					DataPoint dp = null;
					if (valObj == null)
						dp = new DataPoint(0, 0);
					else
						dp = new DataPoint(0, double.Parse(cell.Value.ToString())); ;

					maxY = (maxY > dp.GetValueY(0)) ? maxY : dp.GetValueY(0);
					dp.AxisLabel = string.Format("{0}", rowTuple.Members[0].Caption);

					if (includeToolTips)
						dp.ToolTip = string.Format("Value: {0}\r\n{1}\r\n{2}", cell.FormattedValue, rowTuple.Members[0].Caption, legendText);

					series.Points.Add(dp);
				}
				chart.Series.Add(series);
			}
		}

		if (throughputSeries != null)
			chart.Series.Add(throughputSeries);

		chart.ChartAreas[0].AxisY.Minimum = 0;
		chart.ChartAreas[0].AxisY.Maximum = 260; //RoundAxis(maxY, 100);

		chart.ChartAreas[0].AxisY2.Minimum = 0;
		chart.ChartAreas[0].AxisY2.Maximum = RoundAxis(maxY2, 100);

		chart.ChartAreas[0].AxisX.Interval = 1;
		chart.ChartAreas[0].AxisX.Margin = true;

		ResetAxisMarks(chart.ChartAreas[0].AxisY);
		ResetAxisMarks(chart.ChartAreas[0].AxisY2);

		chart.Titles["Title1"].Text = chartTitle;

		if (string.IsNullOrEmpty(chart.ChartAreas[0].AxisY.Title))
		{
			chart.ChartAreas[0].AxisY.Title = "Voltage (decimal 8-bit)";
			chart.ChartAreas[0].AxisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
		}

		if (string.IsNullOrEmpty(chart.ChartAreas[0].AxisY2.Title))
		{
			chart.ChartAreas[0].AxisY2.Title = "Throughput";
			chart.ChartAreas[0].AxisY2.TitleFont = new Font("Arial", 10, FontStyle.Bold);
		}
	}

	private void chart_Customize(Chart chart)
	{
		Dictionary<string, Color> colorLookup = new Dictionary<string, Color>();
		chart.Legends["Default"].Docking = LegendDocking.Bottom;

		foreach (Series series in chart.Series)
		{
			if (series.ShowInLegend && series.Name.IndexOf("~") < 0)
			{
				colorLookup.Add(series.Name, series.Color);
			}
		}

		foreach (Series series in chart.Series)
		{
			if (!series.ShowInLegend && series.Name.IndexOf("~") >= 0)
			{
				string baseName = series.Name.Substring(0, series.Name.IndexOf("~"));
				if (colorLookup.ContainsKey(baseName))
					series.Color = colorLookup[baseName];
			}
		}
	}
}
