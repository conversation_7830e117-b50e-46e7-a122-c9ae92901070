using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class Dashboard : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		// writes to the output console in vs
		System.Diagnostics.Debug.WriteLine("Hello there1");
		// writes to the html directly. But it's visible
		//Response.Write("Hello there123<br/>");
		// sends user alert
		//ClientScript.RegisterStartupScript(this.GetType(), "alert", "alert('Hello there');", true);
		// writes to console in dev tools in browser
		ClientScript.RegisterStartupScript(this.GetType(), "log", "console.log('Hello there');", true);


        if (!Page.IsPostBack)
		{
			bool displayPersonalizedDashboard = false;
			SqlHelper.ExecuteNonQuery("RPT_UpdateUserDashboardActivity", Utility.GetUserName(), DateTime.Now, displayPersonalizedDashboard);
		}
	}
}

