<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Subscription.aspx.cs" Inherits="Subscription" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
	<AjaxSettings>
		<telerik:AjaxSetting AjaxControlID="receiveEmailCheck">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="emailAddressBox" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
				<telerik:AjaxUpdatedControl ControlID="EmailReqVal"></telerik:AjaxUpdatedControl>
				<telerik:AjaxUpdatedControl ControlID="EmailFormatVal"></telerik:AjaxUpdatedControl>
			</UpdatedControls>
		</telerik:AjaxSetting>
		<telerik:AjaxSetting AjaxControlID="allActiveCheck">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="sessionList" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
			</UpdatedControls>
		</telerik:AjaxSetting>
	</AjaxSettings>
</telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>				
<asp:panel id="DefaultPanel" runat="server">
<table width="100%" border="0" cellpadding="0" cellspacing="10">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Edit Subscriptions</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">			
				<div class="title" style="padding-bottom:2px;">My Subscription Settings</div>	
				<table border="0" cellpadding="0" cellspacing="10" style="width:100%;">
					<tr>	
						<td class="rowHeading" valign="top">Notification Settings: <span style="font-weight:normal;">(hold Ctrl to select multiple)</span></td>
					</tr>
					<tr>
						<td>
							<asp:checkbox id="allActiveCheck" cssclass="entryControl" runat="server" checked="true" text="All Active Sessions" autopostback="true" oncheckedchanged="AllSessionsCheck_Changed" />
							<asp:listbox id="sessionList" selectionmode="multiple" rows="8" enabled="false" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:listbox>
							<br />
							<asp:checkbox id="generalSessionsCheck" checked="true" cssclass="entryControl" runat="server" text="Receive general notifications not associated with any session" />
							<br />
							<asp:checkbox id="observCheck" checked="true" cssclass="entryControl" runat="server" text="Receive notifications for observations" />
						</td>
					</tr>
					<tr>	
						<td class="rowHeading">Message Type:</td>
					</tr>
					<tr>
						<td>
							<asp:radiobuttonlist id="messageTypeList" cssclass="entryControl" runat="server">
								<asp:listitem text="All Messages" value="0" selected="true"></asp:listitem>
								<asp:listitem text="Only Urgent Messages" value="1"></asp:listitem>
							</asp:radiobuttonlist>
						</td>
					</tr>
					<tr>	
						<td class="rowHeading">Receive Email Copy:</td>
					</tr>
					<tr>
						<td>
							<asp:checkbox id="receiveEmailCheck" cssclass="entryControl" causesvalidation="false" autopostback="true" oncheckedchanged="EmailCheck_Changed" runat="server" text=" When new notifications are created, also send me an email" />
						</td>
					</tr>
					<tr>
						<td style="padding-left:35px; width:100%;">
							<table border="0" cellpadding="0" cellspacing="0" style="width:100%;">
								<tr>	
									<td style="width:250px;"><asp:textbox id="emailAddressBox" runat="server" width="240" visible="false"></asp:textbox></td>
									<td style="vertical-align:middle;">
										<asp:requiredfieldvalidator id="EmailReqVal" runat="server" visible="false" controltovalidate="emailAddressBox" display="dynamic" cssclass="error" errormessage="* Required"></asp:requiredfieldvalidator>
										<asp:regularexpressionvalidator id="EmailFormatVal" visible="false" controltovalidate="EmailAddressBox" cssclass="error" runat="server" display="dynamic" validationexpression="\w+([-+.&amp;]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" errormessage="* Invalid format"></asp:regularexpressionvalidator>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding-top:5px;">
					<tr>	
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="submitButton" onclick="SaveButton_Click">Save</asp:linkbutton></div></td>
						<td><div class="cancelButton"><a href="javascript:CloseRadWindow();">Cancel</a></div></td>
					</tr>
				</table>
				<br />	
			</div>
		</td>
	</tr>
</table>
</asp:panel>

</asp:Content>

