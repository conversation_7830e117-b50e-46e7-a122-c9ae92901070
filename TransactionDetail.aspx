<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="TransactionDetail.aspx.cs" Inherits="TransactionDetail" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
                
	<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="600" width="800" modal="true" title="Extension" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="ExtensionWindow" openerelementid="ExtensionButton" VisibleOnPageLoad="false" OffsetElementID="offsetElement"
				Top="30" Left="30" NavigateUrl="TranExtensionList.aspx" Title="Extensions" Height="600" Width="800" >
			</telerik:radwindow>
			<telerik:radwindow runat="server" ID="UploadWindow" openerelementid="UploadButton" VisibleOnPageLoad="false" OffsetElementID="offsetElement"
				Top="30" Left="30" NavigateUrl="UploadEngFile.aspx" Title="Upload Engineering Files" Height="600" Width="800" >
			</telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>

	<table width="100%" border="0" cellpadding="0" cellspacing="20">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Transaction Detail</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;"></td>
					</tr>
				</table>				
				<div class="widget">
					<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:50%;" valign="top">
								<table border="0" cellpadding="0" cellspacing="10">
									<tr>
										<td style="width:150px;" class="rowHeading">Transaction Id:</td>
										<td><asp:label id="transIdLabel" runat="server"></asp:label></td>
									</tr>
									<tr>
										<td style="width:150px;" class="rowHeading">Date:</td>
										<td><asp:label id="dateLabel" runat="server"></asp:label></td>
									</tr>
									<tr>
										<td style="width:150px;" class="rowHeading">Session:</td>
										<td><asp:label id="sessionLabel" runat="server"></asp:label></td>
									</tr>
									<tr>
										<td style="width:150px;" class="rowHeading">Cell:</td>
										<td><asp:label id="cellLabel" runat="server"></asp:label></td>
									</tr>
									<%--<tr>
										<td style="width:150px;" class="rowHeading">Extension Data:</td>
										<td><div class="goButton"><a runat="server" id="ExtensionButton">Extensions</a></div></td>
									</tr>--%>
								</table>
							</td>
							
						</tr>
					</table>
					
					<table style="width:100%;" border="0" cellpadding="0" cellspacing="10">
						<tr>
							<td style="width:50%;" valign="top">
								<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td class="rowHeading">Observations:</td>
									</tr>
									<asp:repeater id="obsRepeater" runat="server">
										<itemtemplate>
											<tr><td class="repeaterSubItem"><a style="padding-left:0px;padding-bottom:0px;" href='<%# (Utility.IsUserAdmin()?("editobservation.aspx?o=" + DataFormatter.Format(Container.DataItem, "ObservationId")):"Search.aspx") %>'><%# DataFormatter.Format(Container.DataItem, "FailureTypeName") + " - " + DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "ObservationText", "", true, false), 25, 30, true)%></a></td></tr>
										</itemtemplate>
										<alternatingitemtemplate>
											<tr><td class="repeaterSubItemAlt"><a style="padding-left:0px;padding-bottom:0px;" href='<%# (Utility.IsUserAdmin()?("editobservation.aspx?o=" + DataFormatter.Format(Container.DataItem, "ObservationId")):"Search.aspx") %>'><%# DataFormatter.Format(Container.DataItem, "FailureTypeName") + " - " + DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "ObservationText", "", true, false), 25, 30, true)%></a></td></tr>
										</alternatingitemtemplate>
									</asp:repeater>
									<tr>
										<td style="padding-left:14px;padding-top:10px;">
											<asp:label id="NoObservationsLabel" runat="server">No observation records found.<br /><br /></asp:label>	
										</td>
									</tr>
								</table>
							</td>
							<td style="width:50%;" valign="top">
								<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
									<tr>
										<td class="rowHeading">XML Files: <span style="font-weight:normal;">(right click and choose save file as)</span></td>
									</tr>
									<asp:repeater id="xmlRepeater" runat="server">
										<itemtemplate>
											<tr><td class="repeaterSubItem"><a style="padding-left:0px;" href='<%# Container.DataItem %>' title='<%# Container.DataItem %>'><%# GetShortFileName((string)Container.DataItem, 6) %></a></td></tr>
										</itemtemplate>
										<alternatingitemtemplate>
											<tr><td class="repeaterSubItemAlt"><a style="padding-left:0px;" href='<%# Container.DataItem %>' title='<%# Container.DataItem %>'><%# GetShortFileName((string)Container.DataItem, 6) %></a></td></tr>
										</alternatingitemtemplate>
									</asp:repeater>
									<tr>
										<td style="padding-left:14px;padding-top:10px;">
											<asp:label id="NoXMLLabel" runat="server">No XML files found.<br /><br /></asp:label>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					<%--<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td colspan="9" class="rowHeading">Functions:</td>
						</tr>
						<tr>
							<td class="cellHeading">Command Name</td>
							<td class="cellHeading">Function Name</td>
							<td class="cellHeading">Result</td>
							<td class="cellHeading">Cmd Result</td>
							<td class="cellHeading">Date/Time</td>
							<td class="cellHeading">Info</td>
							<td class="cellHeading">Data</td>
							<td class="cellHeading">Metrics</td>
							<td class="cellHeading">Status</td>
						</tr>
						<tr>
							<asp:repeater id="functionsRepeater" runat="server">
								<itemtemplate>
									<tr>
										<td class="repeaterItem"><%# DataFormatter.Format(Container.DataItem, "CommandName", "Unspecified") + " (" + DataFormatter.Format(Container.DataItem, "CommandId") + ")"%></td>
										<td class="repeaterItem"><%# DataFormatter.Format(Container.DataItem, "FunctionName", "Unspecified") + " (" + DataFormatter.Format(Container.DataItem, "FunctionId") + ")"%></td>
										<td class="repeaterItem"><%# DataFormatter.Format(Container.DataItem, "FunctionResultName") %></td>
										<td class="repeaterItem"><%# DataFormatter.Format(Container.DataItem, "CommandResultName")%></td>
										<td class="repeaterItem"><%# DataFormatter.FormatDate(Container.DataItem, "FunctionDate", "M/dd/yyyy", "")%><br /><%# DataFormatter.FormatDate(Container.DataItem, "FunctionDate", "h:mm:ss tt", "") %></td>
										<td class="repeaterItem" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "InfoCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functioninfolist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "InfoCnt") + "</b></div></a>"%></td>
										<td class="repeaterItem" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "DataCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functiondatalist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "DataCnt") + "</b></div></a>"%></td>
										<td class="repeaterItem" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "MetricCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functionmetriclist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "MetricCnt") + "</b></div></a>"%></td>
										<td class="repeaterItem" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "StatusCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functionStatuslist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "StatusCnt") + "</b></div></a>"%></td>
									</tr>
								</itemtemplate>
								<alternatingitemtemplate>
									<tr>
										<td class="repeaterItemAlt"><%# DataFormatter.Format(Container.DataItem, "CommandName", "Unspecified") + " (" + DataFormatter.Format(Container.DataItem, "CommandId") + ")"%></td>
										<td class="repeaterItemAlt"><%# DataFormatter.Format(Container.DataItem, "FunctionName", "Unspecified") + " (" + DataFormatter.Format(Container.DataItem, "FunctionId") + ")"%></td>
										<td class="repeaterItemAlt"><%# DataFormatter.Format(Container.DataItem, "FunctionResultName")%></td>
										<td class="repeaterItemAlt"><%# DataFormatter.Format(Container.DataItem, "CommandResultName")%></td>
										<td class="repeaterItemAlt"><%# DataFormatter.FormatDate(Container.DataItem, "FunctionDate", "M/dd/yyyy", "")%><br /><%# DataFormatter.FormatDate(Container.DataItem, "FunctionDate", "h:mm:ss tt", "") %></td>
										<td class="repeaterItemAlt" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "InfoCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functioninfolist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "InfoCnt") + "</b></div></a>"%></td>
										<td class="repeaterItemAlt" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "DataCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functiondatalist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "DataCnt") + "</b></div></a>"%></td>
										<td class="repeaterItemAlt" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "MetricCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functionmetriclist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "MetricCnt") + "</b></div></a>"%></td>
										<td class="repeaterItemAlt" style="padding-left:0px;"><%# (DataFormatter.Format(Container.DataItem, "StatusCnt").Equals("0")) ? "<div style=\"color:#999999; padding: 10px 0px 10px 14px;\">0</div>" : "<div class=\"goButton\"><a onclick=\"window.radopen('functionStatuslist.aspx?c=" + DataFormatter.Format(Container.DataItem, "CellId") + "&s=" + DataFormatter.Format(Container.DataItem, "SessionId") + "&t=" + DataFormatter.Format(Container.DataItem, "RDToolTranId") + "&n=" + DataFormatter.Format(Container.DataItem, "RDToolNodeNumber") + "', null);return false;\"><b>" + DataFormatter.Format(Container.DataItem, "StatusCnt") + "</b></div></a>"%></td>
									</tr>
								</alternatingitemtemplate>
							</asp:repeater>
						</tr>
					</table>--%>
					<br /><br />
				</div>
			</td>
		</tr>
	</table>	
</asp:Content>
