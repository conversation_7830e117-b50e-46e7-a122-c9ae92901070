<?xml version="1.0" encoding="utf-8" ?>
<siteMap xmlns="http://schemas.microsoft.com/AspNet/SiteMap-File-1.0" >
	<siteMapNode url="~/default.aspx">
		<siteMapNode url="~/default.aspx#" title="Home" description="" >
			<siteMapNode url="~/Dashboard.aspx" title="Shared Dashboard" description=""/>
			<siteMapNode url="~/EditSharedDashboard.aspx" title="Shared Dashboard" description=""/>
			<siteMapNode url="~/MyDashboard.aspx" title="My Dashboard" description=""/>
			<siteMapNode url="~/Error.aspx" title="Error" description=""/>
		</siteMapNode>
		<siteMapNode url="~/ReportLibrary.aspx" title="Report Library" description="" >
			<siteMapNode url="~/RunReport.aspx" title="Run Report" description=""/>
			<siteMapNode url="~/RunClientReport.aspx" title="Run Client Report" description=""/>
			<siteMapNode url="~/RunSensorReport.aspx" title="Run Sensor Report" description=""/>
			<siteMapNode url="~/PrintReport.aspx" title="Print Report" description=""/>
			<siteMapNode url="~/RunShiftReport.aspx" title="Run Shift Report" description=""/>
			<siteMapNode url="~/PrintShiftReport.aspx" title="Print Shift Report" description=""/>
		</siteMapNode>
		<siteMapNode url="~/Search.aspx?cs=1" title="Observations" description="">
			<siteMapNode url="~/Search.aspx" title="Observations" description="" />
			<siteMapNode url="~/EditObservation.aspx" title="Edit Observation" description=""/>
			<siteMapNode url="~/EditTransaction.aspx" title="Edit Transaction" description=""/>
			<siteMapNode url="~/MassEditObservations.aspx" title="Mass Edit" description=""/>
			<siteMapNode url="~/TransactionDetail.aspx" title="Tranasaction Details" description=""/>
			<siteMapNode url="~/AdvancedSearch.aspx" title="Advanced Search" description=""/>
			<siteMapNode url="~/Settings.aspx" title="Settings" description=""/>
			<siteMapNode url="~/Statistics.aspx" title="Statistics" description=""/>
		</siteMapNode>
		<siteMapNode url="~/ScheduledReports.aspx" title="Scheduled Reports" description="">
			<siteMapNode url="~/EditScheduledReport.aspx" title="Scheduled Reports" description=""/>
			<siteMapNode url="~/EditScheduledReportItem.aspx" title="Scheduled Reports" description=""/>
		</siteMapNode>	
		<siteMapNode url="~/Messages.aspx" title="Message Center" description="">
			<siteMapNode url="~/Subscription.aspx" title="Edit Subscriptions" description=""/>
			<siteMapNode url="~/EditMessage.aspx" title="Edit Message" description=""/>
			<siteMapNode url="~/NotificationClient.aspx" title="Download Notification Client" description=""/>
		</siteMapNode>
		<siteMapNode url="~/ImportOverview.aspx" title="Import Queue" description="">
			<siteMapNode url="~/ImportErrors.aspx" title="Import Errors" description=""/>
		</siteMapNode>
		<siteMapNode url="~/JiraSyncSchedule.aspx" title="Schedule Jira Sync" description="secured">
			<siteMapNode url="~/EditJiraSyncSchedule.aspx" title="Edit Schedule" description="secured"/>
		</siteMapNode>
		<siteMapNode url="~/JiraSyncLog.aspx" title="Jira Sync Log" description="secured">
			<siteMapNode url="~/JiraSyncItemLog.aspx" title="Sync Items" description="secured"/>
		</siteMapNode>
		<siteMapNode url="~/TransactionVolume.aspx" title="Transaction Volume" description="secured">
			<siteMapNode url="~/EditTransactionVolume.aspx" title="Edit Transaction Volume" description="secured"/>
		</siteMapNode>
		<siteMapNode url="~/AdministrationNav.aspx" title="Administration" description="secured">
			<siteMapNode url="~/EditOperator.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EditFailureType.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EditFailureLocation.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EditInvestigationArea.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EditDevices.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EditCells.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EditSessions.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/DeviceType.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/Events.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/Entity.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/Extensions.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/FieldOptions.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/EngineeringFields.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/Reprocess.aspx" title="Edit Device Type" description="secured"/>
			<siteMapNode url="~/SessionArchive.aspx" title="Archive Session" description="secured"/>
			<siteMapNode url="~/SessionArchiveEdit.aspx" title="Archive Session" description="secured"/>
			<siteMapNode url="~/SourceControl.aspx" title="RDTool Source Control" description="secured"/>
			<siteMapNode url="~/ScriptLog.aspx" title="Cell Script Logs" description="secured"/>
			<siteMapNode url="~/EditTriageType.aspx" title="Edit Triage Type" description="secured"/>
			<siteMapNode url="~/EditDiscipline.aspx" title="Edit Discipline" description="secured"/>
			<siteMapNode url="~/EditSolutionState.aspx" title="Edit Solution State" description="secured"/>
			<siteMapNode url="~/EditFamilyLines.aspx" title="Edit Family Lines" description="secured"/>
			<siteMapNode url="~/EditProductLines.aspx" title="Edit Product Lines" description="secured"/>
			<siteMapNode url="~/EditModelTypes.aspx" title="Edit Model Types" description="secured"/>
			<siteMapNode url="~/EditTestType.aspx" title="Edit Test Types" description="secured"/>
      <siteMapNode url="~/UploaderSetup.aspx" title="Uploader Setup" description="secured"/>
      <siteMapNode url="~/UploadFieldDefinition.aspx" title="Upload XML Field Definition" description="secured"/>
		</siteMapNode>
		<siteMapNode url="~/UserGuide.doc#" title="User Guide" description="" >
			<siteMapNode url="~/UserGuide.doc" title="User Guide" description=""/>
		</siteMapNode>
	</siteMapNode>
</siteMap>
