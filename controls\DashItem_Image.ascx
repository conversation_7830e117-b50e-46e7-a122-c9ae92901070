<%@ Control Language="C#" AutoEventWireup="true" CodeFile="DashItem_Image.ascx.cs" Inherits="controls_DashItem_Image" %>
<%@ Register TagPrefix="telerik" Namespace="Telerik.Web.UI" Assembly="Telerik.Web.UI" %>
<link href="../style.css" rel="stylesheet" type="text/css" />

<telerik:RadAjaxManagerProxy ID="RadAjaxManagerProxy1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="SaveButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="CancelButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="HidEditButton">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DisplayPanel" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="SettingsPanel" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:RadAjaxManagerProxy>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<telerik:RadWindowManager id="RadWindowManager1" runat="server" height="500" width="400" ></telerik:RadWindowManager>

<telerik:radcodeblock id="panelCodeBlock" runat="server">
	<asp:panel id="DisplayPanel" runat="server">
		<div class="title" style="padding-left:10px;padding-bottom:2px;"><asp:label id="imgDescription" runat="server"></asp:label></div>
		<center><asp:label id="imgLabel" runat="server"></asp:label></center>
		<br />
	</asp:panel>

	<asp:panel id="SettingsPanel" runat="server" visible="false" cssclass="body">
		<div class="title" style="padding-left:10px;padding-bottom:5px;padding-top:0px;">Edit Settings</div>
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td class="rowHeading">Image Title: </td>
				</tr>
				<tr>
					<td style="padding:10px 0px 10px 10px;">
						<asp:textbox id="imgDescriptionBox" width="300" runat="server" maxlength="60"></asp:textbox>
					</td>
				</tr>
				<tr>
					<td class="rowHeading">Image or PDF File: </td>
				</tr>
				<tr>
					<td style="padding:0px 0px 10px 0px;">
						<iframe src ="UploadFrame.aspx" frameborder="0" width="320" height="90">
						  <p>Your browser does not support iframes.</p>
						</iframe>
						<asp:label id="MissingImgLbl" visible="false" style="font-size:10px;color:#c00;" runat="server"><br /><span style="padding-left:10px;">* Please upload an image or PDF before saving.</span></asp:label>
					</td>
				</tr>
			</table>
			<br />
			<table border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td style="width:90px;" class="leftPad"><div class="goButton"><asp:linkbutton style="padding-left:14px;" runat="server" onclick="SaveSettings_Click" id="SaveButton" validationgroup="settingsPanel">Save</asp:linkbutton></div></td>
					<td><div class="cancelButton"><asp:linkbutton runat="server" style="padding-left:14px;" onclick="CancelSettings_Click" id="CancelButton" causesvalidation="false">Cancel</asp:linkbutton></div></td>
				</tr>
			</table>
			<br />
	</asp:panel>
	<asp:button runat="server" onclick="EditSettings_Click" id="HidEditButton" style="display:none;" usesubmitbehavior="false" causesvalidation="false"></asp:button>
</telerik:radcodeblock>