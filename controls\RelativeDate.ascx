﻿<%@ Control Language="C#" AutoEventWireup="true" CodeFile="RelativeDate.ascx.cs" Inherits="controls_RelativeDate" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<script type="text/javascript">
	function ToggleStartDatePopup<%= this.ClientID %>() { showDateUp<%= this.ClientID %>($find("<%= startDateField.ClientID %>")); }  
	function showDateUp<%= this.ClientID %>(picker)
    {
        var textBox = picker.get_textBox();
        var popupElement = picker.get_popupContainer();
        var dimensions = picker.getElementDimensions(popupElement);
        var position = picker.getElementPosition(textBox);
        picker.showPopup(position.x, position.y - dimensions.height);
    }
    function showFixed<%= this.ClientID %>()
    {
		document.getElementById('<%= fixedDiv.ClientID %>').style.display='';
		document.getElementById('<%= RelativeStartList.ClientID %>').style.display='none';
    }
    function showRelative<%= this.ClientID %>()
    {
		
		document.getElementById('<%= fixedDiv.ClientID %>').style.display='none';
		document.getElementById('<%= RelativeStartList.ClientID %>').style.display='';
    }
</script>

<asp:panel id="p1" runat="server">
<table width="100%" cellpadding="0" cellspacing="0" border="0">
	<tr>
		<td>
			<asp:radiobutton id="StartTypeFixed" runat="server" text="Fixed" groupname="p1group" />
			<asp:radiobutton id="StartTypeRelative" runat="server" text="Relative" groupname="p1group" />
		</td>
	</tr>
	<tr>
		<td style="padding:5px 0px 0px 5px;">
			<div id="fixedDiv" runat="server" style="display:none;">
				<telerik:RadDatePicker id="startDateField" runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" Width="110px">        
					<calendar skin="Default2006" showrowheaders="false"></calendar>       
					<DatePopupButton Visible="False"></DatePopupButton>
				</telerik:RadDatePicker>
			</div>
			<asp:dropdownlist id="RelativeStartList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" runat="server">
				<asp:listitem text="Select..." value=""></asp:listitem>
			</asp:dropdownlist>
		</td>
	</tr>
</table>	

<telerik:RadAjaxLoadingPanel id="RadAjaxLoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image2" runat="server" ImageUrl="images/loadingSlim.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>
</asp:panel>