using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Microsoft.AnalysisServices.AdomdClient;

public partial class controls_SettingSelector : System.Web.UI.UserControl
{
	public int DeviceTypeId
	{
		get { if (this.ViewState["d"] != null) return (int)this.ViewState["d"]; else return 0; }
		set { this.ViewState["d"] = value; }
	}

	public List<SelectedField> SelectedFieldsList
	{
		get { if (this.ViewState["s"] != null) return (List<SelectedField>)this.ViewState["s"]; else return null; }
		set { this.ViewState["s"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		
	}

	public void PopulateTree(int deviceTypeId)
	{
		this.DeviceTypeId = deviceTypeId;

        if (Session[DieboldConstants.ADV_SEARCH_SETTING_TREE] != null)
			this.SelectedFieldsList = (List<SelectedField>)Session[DieboldConstants.ADV_SEARCH_SETTING_TREE];

		settingFieldsTree.Nodes.Clear();

		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_SettingFields", this.DeviceTypeId);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			//prepend "f" to the value to signify it is a Field being checked for Load on Demand functionality.
			RadTreeNode node = new RadTreeNode(DataFormatter.getString(row, "FieldName"), "f" + DataFormatter.getInt32(row, "FieldId").ToString());
			node.Checkable = true;
			
			//load any previously selected items
			if (this.SelectedFieldsList != null)
			{
				foreach (SelectedField field in this.SelectedFieldsList)
				{
					if (field.FieldId == DataFormatter.getInt32(row, "FieldId") && field.IsSelected)
						node.Checked = true;
				}
			}

			node.Expanded = node.Checked;

			settingFieldsTree.Nodes.Add(node);
			LoadFieldOptions(node);
		}
	}

	private void LoadFieldOptions(RadTreeNode parentNode)
	{
		string fieldId = parentNode.Value.Substring(1, parentNode.Value.Length - 1);
		
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetList_SettingFieldOptions", this.DeviceTypeId, fieldId);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			//prepend "p" to the value to signify it is a FieldOption being checked for Load on Demand functionality.
			RadTreeNode node = new RadTreeNode(DataFormatter.getString(row, "DisplayName"), "p" + DataFormatter.getInt32(row, "FieldOptionId").ToString());
			node.Checkable = true;
			
			//load any previously selected items
			if (this.SelectedFieldsList != null)
			{
				foreach (SelectedField field in this.SelectedFieldsList)
				{
					if (field.FieldId == Convert.ToInt32(fieldId))
					{
						foreach (SelectedFieldOption fieldOption in field.SelectedFieldOptions)
						{
							if (fieldOption.FieldOptionId == DataFormatter.getInt32(row, "FieldOptionId"))
							{
								node.Checked = true;
								parentNode.Expanded = true;								
							}
						}
					}
				}
			}

			parentNode.Nodes.Add(node);
		}
	}

	public void SaveSelectedValuesToSession()
	{
		List<SelectedField> tempList = new List<SelectedField>();

		foreach (RadTreeNode node in this.settingFieldsTree.CheckedNodes)
		{
			if (node.Value.StartsWith("f"))
			{
				SelectedField tempField = new SelectedField();
				tempField.FieldName = node.Text;
				tempField.FieldId = Convert.ToInt32(node.Value.Substring(1, node.Value.Length - 1));
				tempField.IsSelected = true;
			}
			else if (node.Value.StartsWith("p"))
			{
				//find the field options parent field and add the option to its collection
				SelectedField parentField = null;
				int parentFieldId = Convert.ToInt32(node.ParentNode.Value.Substring(1, node.ParentNode.Value.Length - 1));
				foreach(SelectedField field in tempList)
				{
					if (field.FieldId == parentFieldId)
						parentField = field;
				}

				//if parent is not already in the list add it first.
				if (parentField == null)
				{
					parentField = new SelectedField();
					parentField.FieldId = parentFieldId;
					parentField.FieldName = node.ParentNode.Text;
					parentField.IsSelected = false;
					tempList.Add(parentField);
				}

				//Add the option to the parent
				SelectedFieldOption tempOption = new SelectedFieldOption();
				tempOption.FieldOptionName = node.Text;
				tempOption.FieldOptionId =  Convert.ToInt32(node.Value.Substring(1, node.Value.Length - 1));
				parentField.SelectedFieldOptions.Add(tempOption);				
			}
		}

		if (tempList.Count != 0)
		{
            Session[DieboldConstants.ADV_SEARCH_SETTING] = Utility.FormatFieldIdList(tempList, false, true, null, null, ", "); ;
            Session[DieboldConstants.ADV_SEARCH_SETTING_NAME] = Utility.FormatFieldNameList(tempList, true, true, "; ", " - ", ", ");
			Session[DieboldConstants.ADV_SEARCH_SETTING_TREE] = tempList; //object list for rebuilding tree
		}
		else
		{
			Session[DieboldConstants.ADV_SEARCH_SETTING] = null;
			Session[DieboldConstants.ADV_SEARCH_SETTING_NAME] = null;
			Session[DieboldConstants.ADV_SEARCH_SETTING_TREE] = null;
		}
	}

	public void ClearTree()
	{
		settingFieldsTree.Nodes.Clear();
		this.SelectedFieldsList = null;
	}

	public void ClearSelections()
	{
		settingFieldsTree.ClearCheckedNodes();
		settingFieldsTree.ClearSelectedNodes();
		this.SelectedFieldsList = null;
	}
}

