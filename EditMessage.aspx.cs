using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class EditMessage : System.Web.UI.Page
{
	public int MessageId
	{
		get { if (this.ViewState["m"] != null) return (int)this.ViewState["m"]; else return 0; }
		set { this.ViewState["m"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!Utility.IsUserAdmin())
				Response.Redirect("Unauthorized.aspx");

			subjectField.Focus();

			sessionList.Items.Add(new RadComboBoxItem("General - No Session", ""));
			sessionList.DataSource = Utility.GetActiveSessionsList();
			sessionList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params["m"]))
			{
				this.MessageId = Convert.ToInt32(Request.Params["m"]);
				LoadMessage();
				this.submitButton.Text = "Resend";
			}
		}
	}

	private void LoadMessage()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadMessage", this.MessageId);

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				subjectField.Text = DataFormatter.getString(row, "MessageSubject");
				bodyField.Text = DataFormatter.getString(row, "MessageBody");
				isUrgentCheck.Checked = DataFormatter.getBool(row, "IsUrgent");
				
				if (sessionList.FindItemByValue(DataFormatter.getInt32(row, "SessionId").ToString()) != null)
					sessionList.SelectedValue = DataFormatter.getInt32(row, "SessionId").ToString();
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		Page.Validate();
		if (Page.IsValid)
		{
			object sessionId = null;
			if (!string.IsNullOrEmpty(sessionList.SelectedValue))
				sessionId = Convert.ToInt32(this.sessionList.SelectedValue);

			if (this.MessageId != 0)
			{
				//Update Message
				int success = SqlHelper.ExecuteNonQuery("RPT_UpdateMessage", this.MessageId, isUrgentCheck.Checked, sessionId, 
									Utility.GetUserName(), subjectField.Text.Trim(), bodyField.Text.Trim());
			}
			else
			{
				//Insert New Message
                this.MessageId = (int)SqlHelper.ExecuteScalar("RPT_InsertMessage", isUrgentCheck.Checked, sessionId,
                                    Utility.GetUserName(), subjectField.Text.Trim(), bodyField.Text.Trim());
			}

			//Send notifications //NOW HANDLED BY THE QUEUESERVICE - QueueManager
			//if (this.MessageId != 0)
			//	Utility.SendNotificationEmail(this.MessageId, this.Page);

			Response.Redirect("Messages.aspx");
		}
	}

	protected void CancelButton_Click(object sender, EventArgs e)
	{
		Response.Redirect("Messages.aspx");
	}

    protected void DeleteButton_Click(object sender, EventArgs e)
    {
        if(this.MessageId > 0)
            SqlHelper.ExecuteNonQuery("RPT_DeleteMessage", this.MessageId);

        Response.Redirect("Messages.aspx");
    }
}
