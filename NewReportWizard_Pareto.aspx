<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Pareto.aspx.cs" Inherits="NewReportWizard_Pareto" %>
<%@ register tagprefix="rpt" tagname="DimensionSelector" src="~/controls/DimensionSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
<telerik:radscriptblock runat="server">
<script language="javascript" type="text/javascript">
    function validateChecks()
    {
        var totalChk = document.getElementById('<%=totalCheck.ClientID %>');
        var splitChk = document.getElementById('<%=splitByCell.ClientID %>');
        if(totalChk.checked && splitChk.checked)
        {
            alert('The Include Total feature is not available in conjunction with Split Columns By Device.');
            totalChk.checked = false;
            splitChk.checked = true;
        }
    }
</script>
</telerik:radscriptblock>

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
</telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
					
					<table border="0" cellpadding="0" cellspacing="10" style="width:100%;">
						<tr>
							<td style="width:200px;" valign="top">
								<div class="rowHeading">Select Dimension:</div>
								<div class="leftPad" style="padding-top:15px;">
									The selected dimension will display as the X-axis item of the report's chart. 
									<br /><br />
									Any unselected dimension members will be filtered off from display on the final chart.
								</div>
							</td>
							<td valign="top">
							    <rpt:DimensionSelector isrequired="true" id="dimensionSel" runat="server"></rpt:DimensionSelector>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Grouping:</div></td>
							<td valign="top" style="padding-top:0px;paddding-bottom:0px;">
								<asp:radiobuttonlist runat="server" id="groupingList" repeatdirection="horizontal" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:radiobuttonlist>
								<asp:requiredfieldvalidator id="val1" runat="server" display="dynamic" controltovalidate="groupingList" cssclass="error"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td>
								<asp:checkbox id="totalCheck" runat="server" text="Include Total" onclick="validateChecks();" />
							</td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td>
						        <asp:checkbox id="splitByCell" runat="server" text="Split Columns By Device" onclick="validateChecks();" />
							</td>
						</tr>
						<tr>
						    <td>&nbsp;</td>
						    <td style="paddding-bottom:5px;">
						        <asp:checkbox id="statisticValue" runat="server" text="Display Statistic Value Totals" />
						    </td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Rate Type:</div></td>
							<td> 
								<asp:dropdownlist id="rateTypeList" runat="server" autopostback="true" onselectedindexchanged="rateTypeList_selectedIndexChanged" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:dropdownlist>
								<asp:requiredfieldvalidator id="val3" runat="server" controltovalidate="rateTypeList" cssclass="error" errormessage="*"></asp:requiredfieldvalidator>
								<asp:customvalidator display="dynamic" id="v" runat="server" onservervalidate="ValidateRate" cssclass="error" errormessage="* 'By Statistic Value' is only valid with the Statistic dimension."></asp:customvalidator>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Display Format:</div></td>
							<td> 
								<asp:dropdownlist id="formatTypeList" runat="server">
									<asp:listitem text="Standard" value=""></asp:listitem>
									<asp:listitem text="Numeric, no decimals" value="#,#"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 1 decimal" value="#,##0.0"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 2 decimals" value="#,##0.00"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 3 decimals" value="#,##0.000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 4 decimals" value="#,##0.0000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 5 decimals" value="#,##0.00000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 6 decimals" value="#,##0.000000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 7 decimals" value="#,##0.0000000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 8 decimals" value="#,##0.00000000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 9 decimals" value="#,##0.000000000"></asp:listitem>
									<asp:listitem text="Numeric, rounded to 10 decimals" value="#,##0.0000000000"></asp:listitem>
									<asp:listitem text="Hexidecimal" value="hex"></asp:listitem>
								</asp:dropdownlist>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Workload Amount:</div></td>
							<td> 
								<asp:textbox id="workLoadField" runat="server" enabled="false"></asp:textbox>
								<asp:customvalidator display="dynamic" id="workLoadVal" runat="server" onservervalidate="ValidateWorkLoad" cssclass="error" errormessage="* Must be > 0"></asp:customvalidator>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Max # columns to display:</div></td>
							<td valign="top">
								<asp:textbox id="maxColumnsField" width="60" runat="server"></asp:textbox>
								<asp:comparevalidator id="val2" runat="server" display="dynamic" controltovalidate="maxColumnsField" operator="dataTypeCheck" type="integer" errormessage="* Invalid format"></asp:comparevalidator>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Y-Axis Scale (optional):</div></td>
							<td valign="top" style="padding-top:0px;paddding-bottom:0px;">
								<asp:textbox id="minYScale" width="60" runat="server"></asp:textbox> Min 
								<asp:comparevalidator id="val6" runat="server" display="dynamic" controltovalidate="minYScale" operator="dataTypeCheck" type="double" errormessage="* Invalid format"></asp:comparevalidator>
                                <span style="display:inline-block;width:15px;">&nbsp;</span>
								<asp:textbox id="maxYScale" width="60" runat="server"></asp:textbox> Max
								<asp:comparevalidator id="val7" runat="server" display="dynamic" controltovalidate="maxYScale" operator="dataTypeCheck" type="double" errormessage="* Invalid format"></asp:comparevalidator>
							</td>
						</tr>
					</table>
					
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>
