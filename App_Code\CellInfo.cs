using System;
using System.Collections;
using System.Runtime.Serialization;

[Serializable()]
public class CellInfo : ISerializable
{
	public int CellId = 0;
	public string CellName = null;	
	public int CellStatusId = 0;

	// Default constructor.
	public CellInfo() { }

	// Deserialization constructor.
    public CellInfo(SerializationInfo info, StreamingContext context)
	{
        CellId = (int)info.GetValue("CellId", typeof(int));
        CellName = (string)info.GetValue("CellName", typeof(string));
        CellStatusId = (int)info.GetValue("CellStatusId", typeof(int));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
        info.AddValue("CellId", CellId);
        info.AddValue("CellName", CellName);
        info.AddValue("CellStatusId", CellStatusId);
	}
}
