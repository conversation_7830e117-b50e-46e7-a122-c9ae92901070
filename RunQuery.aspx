﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="RunQuery.aspx.cs" Inherits="RunQuery" %>
<%@ Import Namespace="Microsoft.AnalysisServices.AdomdClient" %>
<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="dcwc" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
	
<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">

	function distroValueClick(val) {
		if (val) {
			var hidCntrl = document.getElementById('<%= distroClickedVal.ClientID %>');
			var hidBtn = document.getElementById('<%= distroHidBtn.ClientID %>');
			hidCntrl.value = val;

			hidBtn.click();
		}
	}
	
</script>

<asp:panel id="InitialLoadingPanel" runat="server" visible="true">
	<div style="padding-top:50px;"><center><img src="images/loading.gif" alt="Loading Report..." border="0" /></center></div>
</asp:panel>

<asp:panel id="MainPanel" runat="server" visible="false">
	<telerik:radwindowmanager runat="server" ID="RadWindowManager1" height="680" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="SaveReportWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Height="600" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="EditWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" Height="680" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="promptsessions.aspx" Height="400" Width="600" ></telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>

	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"> 
					<tr>
						<td class="widgetTitle">Run Query</td>
						<td class="widgetTop" style="width:155px;">&nbsp;</td>
						<td class="widgetTop" style="width:260px;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="width:55px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="printButton" onclick="PrintButton_Click">Print</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>
									<td style="width:65px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="exportButton" onclick="ExportButton_Click">Export</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>
								</tr>
							</table>
						</td>
						<td class="widgetTop">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget" style="padding-bottom:20px;">
					<table width="700px" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="title"><asp:label id="reportNameLabel" runat="server"></asp:label></td>
							<td>&nbsp;</td>
							<td style="width:90px;text-align:right;padding-right:10px;"><div class="goButton" id="EditBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="EditButton_Click" id="editButton">Edit Report</asp:linkbutton></div></td>
							<td style="width:95px;text-align:right;padding-right:10px;"><div class="goButton" id="SaveBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save Report</asp:linkbutton></div></td>
						</tr>
					</table>
					<br />
				</div>
				
				<div class="widget" style="padding-bottom:20px; text-align:center;">
		            <asp:Panel ID="GridBindingPanel" runat="server">
						<table border="0" cellpadding="0" cellspacing="1">
	                        <asp:Repeater ID="ColumnHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders %>'>
	                            <ItemTemplate>
	                                <tr>
	                                    <asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
	                                        <ItemTemplate>
	                                            <td><%# "&nbsp;" %></td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                    <asp:Repeater ID="ColumnHeaderRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders[ColumnHeaderLevelRepeater.Items.Count] %>'>
	                                        <ItemTemplate>
	                                            <td class="cellHeading" style="padding:4px 2px 4px 2px;"><%# Container.DataItem %></td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                </tr>
	                            </ItemTemplate>
	                        </asp:Repeater>
	                        <asp:Repeater ID="GridRowsRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayData %>'>
	                            <ItemTemplate>
	                                <tr style="background-color: #f9f9f9;">
	                                    <asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
	                                        <ItemTemplate>
	                                            <td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                    <asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
	                                        <ItemTemplate>
	                                            <td><%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : (string)Container.DataItem)%></td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                </tr>
	                            </ItemTemplate>
	                            <alternatingitemtemplate>
								    <tr style="background-color: #e9e9e9;">
	                                    <asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
	                                        <ItemTemplate>
	                                            <td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                    <asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
	                                        <ItemTemplate>
	                                            <td><%# (string.IsNullOrEmpty((string)Container.DataItem)?"&nbsp;":(string)Container.DataItem) %></td>
	                                        </ItemTemplate>
	                                    </asp:Repeater>
	                                </tr>
	                            </alternatingitemtemplate>
	                            <footertemplate>
	                            </footertemplate>
	                        </asp:Repeater>
					    </table> 
		            </asp:Panel>
				</div>
			</td>
		</tr>
	</table>
	<input id="distroClickedVal" type="hidden" runat="server" />
	<asp:button id="distroHidBtn" runat="server" style="display:none;" onclick="GridItem_Click" causesvalidation="false" />

</asp:panel>

</asp:Content>

