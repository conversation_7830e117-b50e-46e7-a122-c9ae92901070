<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="RunClientReport.aspx.cs" Inherits="RunClientReport" %>
<%@ register assembly="DundasWebOlapManager" namespace="Dundas.Olap.Manager" tagprefix="DOMC" %>
<%@ register assembly="DundasWebOlapDataProviderAdomdNet" namespace="Dundas.Olap.Data.AdomdNet" tagprefix="DODPN" %>
<%@ register assembly="DundasWebUIControls" namespace="Dundas.Olap.WebUIControls" tagprefix="DOCWC" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<telerik:radwindowmanager runat="server" ID="RadWindowManager1" height="650" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="SaveReportWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Height="600" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="EditWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" Height="650" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="promptsessions.aspx" Height="400" Width="600" ></telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>
	
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"> 
					<tr>
						<td class="widgetTitle">Run Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget" style="padding-bottom:20px;">
					<table width="700px" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="title"><asp:label id="reportNameLabel" runat="server"></asp:label></td>
							<td>&nbsp;</td>
							<td style="width:90px;text-align:right;padding-right:10px;"><div class="goButton" id="EditBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="EditButton_Click" id="editButton">Edit Report</asp:linkbutton></div></td>
							<td style="width:95px;text-align:right;padding-right:10px;"><div class="goButton" id="SaveBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save Report</asp:linkbutton></div></td>
						</tr>
					</table>
					<docwc:olapclient id="OlapClient1" runat="server" height="540px" width="900px" ShowReportList="false" ShowCubeSelector="false" dataproviderid="AdomdNetDataProvider1">
					</docwc:olapclient>
					<br />
					<br />
					
				    <dodpn:adomdnetdataprovider id="AdomdNetDataProvider1" runat="server">
					</dodpn:adomdnetdataprovider>
				</div>
			</td>
		</tr>
	</table>

</asp:Content>
