﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditScheduledReportItem.aspx.cs" Inherits="EditScheduledReportItem" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="b" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">
    function ClientNodeChecked(sender, eventArgs) {
        var node = eventArgs.get_node();
        var checked = node.get_checked();
        if (node.get_value() && node.get_value().substr(0, 1) == 'R') {
            for (var i = 0; i < node.get_nodes().get_count(); i++) {
                var subNode = node.get_nodes().getNode(i);
                subNode.set_checked(checked);
            }
        } else if (node.get_value() && node.get_value().substr(0, 1) == 'S') {
            node.get_parent().uncheck();
        }
    }
    function ClientNodeClicked(sender, eventArgs) {
        var tree = $find("<%= reportTree.ClientID %>");
        tree.unselectAllNodes();
        var node = eventArgs.get_node();
        if (node.get_value() && node.get_value().substr(0, 1) == 'S') {
            node.set_checked(!node.get_checked());
            node.get_parent().uncheck();
        } else if (node.get_nodes().get_count() > 0) {
            node.set_expanded(!node.get_expanded());
        }
    }
</script> 

<table width="100%" border="0" cellpadding="0" cellspacing="10">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Scheduled Report Item(s)</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">
				<div class="title" style="padding-bottom:2px;">Scheduled Report Item(s)</div>
				<table border="0" cellpadding="0" cellspacing="13" width="700">
					<tr id="TreeRow" runat="server">
						<td class="rowHeading" style="width:150px;">Report</td>
						<td>
            				<div style="height: 300px; width:330px; border: solid 1px #999999; overflow:auto;" >
    							<telerik:RadTreeView runat="Server" multipleselect="false" id="reportTree" 
    							    enabledraganddrop="false" enabledraganddropbetweennodes="false" skin="WebBlue"
    							    onclientnodechecked="ClientNodeChecked" onclientnodeclicked="ClientNodeClicked"></telerik:RadTreeView>
							</div>
							<asp:customvalidator id="v1" runat="server" onservervalidate="ValidateTree" errormessage="* Required" />
						</td>
					</tr>
                    <tr>
                        <td class="rowHeading" style="width:150px;">Desired File Type</td>
						<td>
							<asp:radiobutton id="pdfRadio" runat="server" text="PDF" checked="true" groupname="fileType" />
                            <asp:radiobutton id="xlsRadio" runat="server" text="XLS" groupname="fileType" />
                            &nbsp;&nbsp;&nbsp;<b>Note</b>: Only "Query" type reports support the XLS format.
						</td>                
                    </tr>
					<tr>
						<td class="rowHeading" style="width:150px;">Format</td>
						<td>
							<asp:dropdownlist id="formatList" runat="server">
								<asp:listitem text="Portrait" value="portrait"></asp:listitem>
								<asp:listitem text="Landscape" value="landscape" selected="true"></asp:listitem>
							</asp:dropdownlist>
						</td>
					</tr>
					<tr>
						<td class="rowHeading" style="width:150px;">Chart Scale</td>
						<td>
							<asp:dropdownlist id="chartScaleList" runat="server">
								<asp:listitem text="Hide Chart" value="hide"></asp:listitem>
								<asp:listitem text="Fit to Page Width" value="fit" selected="true"></asp:listitem>
								<asp:listitem text="25%" value="25%"></asp:listitem>
								<asp:listitem text="50%" value="50%"></asp:listitem>
								<asp:listitem text="60%" value="60%"></asp:listitem>
								<asp:listitem text="75%" value="75%"></asp:listitem>
								<asp:listitem text="90%" value="90%"></asp:listitem>
								<asp:listitem text="100%" value="100%"></asp:listitem>
								<asp:listitem text="150%" value="150%"></asp:listitem>
								<asp:listitem text="200%" value="200%"></asp:listitem>
								<asp:listitem text="300%" value="300%"></asp:listitem>
								<asp:listitem text="400%" value="400%"></asp:listitem>
								<asp:listitem text="500%" value="500%"></asp:listitem>
							</asp:dropdownlist>
						</td>
					</tr>
					<tr>
						<td class="rowHeading" style="width:150px;">Data Scale</td>
						<td>
							<asp:dropdownlist id="dataScaleList" runat="server">
								<asp:listitem text="Hide Data Grid" value="hide" selected="true"></asp:listitem>
								<asp:listitem text="Fit to Page Width" value="fit"></asp:listitem>
								<asp:listitem text="25%" value="25%"></asp:listitem>
								<asp:listitem text="50%" value="50%"></asp:listitem>
								<asp:listitem text="60%" value="60%"></asp:listitem>
								<asp:listitem text="75%" value="75%"></asp:listitem>
								<asp:listitem text="90%" value="90%"></asp:listitem>
								<asp:listitem text="100%" value="100%"></asp:listitem>
								<asp:listitem text="150%" value="150%"></asp:listitem>
								<asp:listitem text="200%" value="200%"></asp:listitem>
								<asp:listitem text="300%" value="300%"></asp:listitem>
								<asp:listitem text="400%" value="400%"></asp:listitem>
								<asp:listitem text="500%" value="500%"></asp:listitem>
							</asp:dropdownlist>
						</td>
					</tr>
					<tr>
						<td class="rowHeading" style="width:150px;">Separate Chart & Data</td>
						<td>
							<asp:checkbox id="dataSeparate" runat="server" text="Place data on a separate page" />
						</td>
					</tr>
				</table>
				<br />
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
						<td style="width:80px;"><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to cancel without saving?');" href="<%= string.Format("EditScheduledReport.aspx?{0}={1}", DieboldConstants.SCHEDULED_REPORT_ID_KEY, this.ScheduledReportId) %>">Cancel</a></div></td>
					</tr>
				</table>
				<br /><br />
			</div>
		</td>
	</tr>
</table>

</asp:Content>



