﻿<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Bubble.aspx.cs" Inherits="NewReportWizard_Bubble" %>
<%@ register tagprefix="rpt" tagname="reportfilterselector" src="~/controls/ReportFilterSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    function ToggleStartDatePopup() { showDateUp($find("<%= startDateField.ClientID %>")); }
    function ToggleStartTimePopup() { showTimeUp($find("<%= startTimeField.ClientID %>")); }
    function ToggleEndDatePopup() { showDateUp($find("<%= endDateField.ClientID %>")); }
    function ToggleEndTimePopup() { showTimeUp($find("<%= endTimeField.ClientID %>")); }
    function showDateUp(picker) {
       picker.showPopup();
    }
    function showTimeUp(picker) {
        picker.showTimePopup();
    }
</script>

<telerik:radajaxmanager ID="RadAjaxManager1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="DeviceTypeList">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="DeviceList" loadingpanelid="LoadingPanel1" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="StartDateTypeList">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="startAjax" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="EndDateTypeList" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="EndDateTypeList">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="endAjax" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="StartDateTypeList" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:radajaxmanager>
	
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 3 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Filter Report</div>
					<div class="title" style="padding-top:0px; font-weight:normal;"><asp:label id="reportNameLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
					<br />
					
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td class="rowHeading" style="width:45%;">Session:</td>
							<td class="rowHeading" style="width:55%;"></td>
						</tr>
						<tr>
							<td style="padding:10px 0px 10px 0px;" valign="top">
								<asp:dropdownlist id="sessionList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" runat="server" cssclass="entryControl">
									<asp:listitem text="Select..." value=""></asp:listitem>
								</asp:dropdownlist>
								<asp:requiredfieldvalidator id="val2" runat="server" errormessage="* Required" cssclass="error" display="dynamic" controltovalidate="sessionList"></asp:requiredfieldvalidator>
							</td>
							<td style="padding:10px 0px 0px 0px;" valign="top">
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:45%;">Transaction Id Range Filter:</td>
							<td class="rowHeading" style="width:55%;"></td>
						</tr>
						<tr>
							<td style="padding:5px 0px 10px 15px;" valign="top">
							    Min Tran Id: <asp:textbox id="minTranId" runat="server"></asp:textbox>
							</td>
							<td style="padding:5px 0px 10px 15px;" valign="top">
							</td>
						</tr>
						<tr>
							<td style="padding:5px 0px 10px 15px;" valign="top">
							    Max Tran Id: <asp:textbox id="maxTranId" runat="server"></asp:textbox>
							</td>
							<td style="padding:5px 0px 10px 15px;" valign="top">
							</td>
						</tr>
						<tr id="acceptLevel3NotesHeading" runat="server">
							<td class="rowHeading" style="width:45%;">Accept Level 3 Notes:</td>
							<td class="rowHeading" style="width:55%;"></td>
						</tr>
						<tr id="acceptLevel3NotesRow" runat="server">
							<td style="padding:5px 0px 10px 15px;" valign="top">
							    <asp:checkbox id="acceptLevel3Check" runat="server" />
							</td>
							<td style="padding:5px 0px 10px 15px;" valign="top">
							</td>
						</tr>
                        <tr id="fieldTypeIdHeading" runat="server">
							<td class="rowHeading" style="width:45%;">Field Type Id:</td>
							<td class="rowHeading" style="width:55%;"></td>
						</tr>
						<tr id="fieldTypeIdRow" runat="server">
							<td style="padding:5px 0px 10px 15px;" valign="top">
							    <asp:dropdownlist id="fieldTypeIdList" runat="server"></asp:dropdownlist>
							</td>
							<td style="padding:5px 0px 10px 15px;" valign="top">
							</td>
						</tr>
				    </table>
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td class="rowHeading" style="width:45%;">Start Date:</td>
							<td class="rowHeading" style="width:55%;">End Date:</td>
						</tr>
						<tr>
							<td style="padding-left:20px;">
								<asp:radiobuttonlist id="StartDateTypeList" repeatdirection="horizontal" autopostback="true" onselectedindexchanged="StartDateTypeList_IndexChanged" runat="server">
									<asp:listitem text="Fixed" selected="true" value="fixed"></asp:listitem>
									<asp:listitem text="Relative" value="relative"></asp:listitem>
								</asp:radiobuttonlist>
							</td>
							<td style="padding-left:20px;">
								<asp:radiobuttonlist id="EndDateTypeList" repeatdirection="horizontal" autopostback="true" onselectedindexchanged="EndDateTypeList_IndexChanged" runat="server">
									<asp:listitem text="Fixed" selected="true" value="fixed"></asp:listitem>
									<asp:listitem text="Relative" value="relative"></asp:listitem>
								</asp:radiobuttonlist>
							</td>
						</tr>
						<tr>
							<td style="padding-left:30px;">
								<table width="100%" cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td style="vertical-align:bottom;">
											<asp:panel id="startAjax" runat="Server">
												<telerik:RadDatePicker id="startDateField" runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" style="padding-top:1px;" Width="110px">        
													<calendar skin="Default2006" showrowheaders="false"></calendar>       
													<DatePopupButton Visible="False"></DatePopupButton>
													<DateInput onclick="ToggleStartDatePopup()"></DateInput>                           	                                             
												</telerik:RadDatePicker>
												<asp:dropdownlist id="RelativeStartList" appenddatabounditems="true" style="display:none;" datatextfield="Name" datavaluefield="Code" runat="server">
													<asp:listitem text="Select..." value=""></asp:listitem>
												</asp:dropdownlist>
											</asp:panel>
										</td>
										<td style="padding-left:10px;vertical-align:bottom;">
											<telerik:radtimepicker id="startTimeField" runat="server" timepopupbutton-visible="false" width="80">
												<dateinput onclick="ToggleStartTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
												<TimeView Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
											</telerik:radtimepicker>
										</td>
										<td style="width:100%;">&nbsp;</td>
									</tr>
								</table>
							</td>
							<td style="padding-left:30px;">
								<table width="100%" cellpadding="0" cellspacing="0" border="0">
									<tr>
										<td style="vertical-align:bottom;">
											<asp:panel id="endAjax" runat="Server">
												<telerik:RadDatePicker id="endDateField" runat="server" style="padding-top:1px;" Width="110px">        
													<calendar skin="Default2006" showrowheaders="false"></calendar>       
													<DatePopupButton Visible="False"></DatePopupButton>
													<DateInput onclick="ToggleEndDatePopup()"></DateInput>                           	                                             
												</telerik:RadDatePicker>
												<asp:dropdownlist id="RelativeEndList" appenddatabounditems="true" style="display:none;" datatextfield="Name" datavaluefield="Code" runat="server">
													<asp:listitem text="Select..." value=""></asp:listitem>
												</asp:dropdownlist>
											</asp:panel>
										</td>
										<td style="padding-left:10px;vertical-align:bottom;">
											<telerik:radtimepicker id="endTimeField" runat="server" timepopupbutton-visible="false" width="80">
												<dateinput onclick="ToggleEndTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
												<TimeView Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
											</telerik:radtimepicker>
										</td>
										<td style="width:100%;">&nbsp;</td>
									</tr>
								</table>								
							</td>
						</tr>
						<tr>
							<td colspan="2" style="padding-left:30px;">
                                <asp:label cssclass="error" id="timeError" runat="server" visible="false">The time values must be on the hour or half hour.</asp:label>
                                <asp:label cssclass="error" style="color:orangered;" id="dataSizeWarning" runat="server" visible="false">
                                    * Warning: selecting a date range larger than 1 day may return extremely large data results, and require long execution times. 
                                    <br />Consider limiting the date range to 1 single day, or by filtering by Transaction Id Range.</asp:label>
							</td>
						</tr>
					</table>	
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="nextButton" onclick="NextButton_Click">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>





