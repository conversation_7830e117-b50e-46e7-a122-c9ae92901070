﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="PrintQueryReport.aspx.cs" Inherits="PrintQueryReport" %>
<%@ Import Namespace="Microsoft.AnalysisServices.AdomdClient" %>
<%@ Register Assembly="DundasWebChart" Namespace="Dundas.Charting.WebControl" TagPrefix="dcwc" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" >
<head id="Head1" runat="server">
    <title>Diebold Online Reporting</title>
    <link href="style.css" rel="stylesheet" type="text/css" />
</head>
<body style="margin: 0px 0px 0px 0px;">
    <form id="form1" runat="server">
     
		<script language="javascript" type="text/javascript">
			function printMyPage()
			{
				window.print();
			}
			
			var singlePageWidth = 612;
			function changeLayout()
			{
				var cntrl = document.getElementById('layoutList');
				var outerZoom = document.getElementById('OuterZoom');
				if (cntrl)
				{
					var layoutSelection = cntrl.options[cntrl.selectedIndex].value;
					
					//8.5x11 = 612 width x 792 height in points
					//8.5x11 = 816 width x 1056 height in pixels
					//1.5 inches of printer margin = 108 points
					if (layoutSelection == 'landscape')
					{
						singlePageWidth = 792;
						outerZoom.style.width = (792 - 108) + 'pt';
					}
					else
					{
						singlePageWidth = 612;
						outerZoom.style.width = (612 - 108) + 'pt';
					}
				}
				
				changeDataZoom();
			}
			
			function changeChartZoom()
			{
				var chartZoom = document.getElementById('ChartZoomDiv');
				var cntrl = document.getElementById('chartList');
				if (cntrl)
				{
					chartZoom.style.zoom = '1';
					var printArea = document.body.createTextRange(); 
					printArea.moveToElementText(chartZoom);
					
					var newSelectedZoom = cntrl.options[cntrl.selectedIndex].value;	
					if (newSelectedZoom == 'hide')
					{	
						chartZoom.style.display = "none";
					}
					else if (newSelectedZoom == 'fit')
					{	
						//add 3/4 inch margin to each side of the page (IE default margin) = 108 points
						chartZoom.style.display = "";
						chartZoom.style.zoom = (singlePageWidth - 108) / (printArea.boundingWidth * .75);
					}
					else
					{ 	
						chartZoom.style.display = "";
						chartZoom.style.zoom = newSelectedZoom;
					}
				}
			}
			
		</script>
		<asp:scriptmanager id="Scriptmanager1" runat="server"></asp:scriptmanager>
		<div class="body" style="vertical-align:top;">
			<asp:panel id="PrintControlsPanel" runat="server">
				<div style="padding-left:15px; padding-top:10px;" class="printControl">
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td style="width:80px;"><div class="goButton"><a style="padding-left:14px;" onclick="printMyPage();return false;">Print</a></div></td>
							<td style="width:80px;"><div class="goButton"><asp:linkbutton runat="server" style="padding-left:14px;" id="pdfButton" onclick="PDFButton_Click">PDF</asp:linkbutton></div></td>
							<%--<td style="width:170px;">
								Layout: <asp:dropdownlist id="layoutList" runat="server" onchange="changeLayout(this); return false;" cssclass="body">
									<asp:listitem text="Portrait" value="portrait"></asp:listitem>
									<asp:listitem text="Landscape" value="landscape"></asp:listitem>
								</asp:dropdownlist>
							</td>--%>
							<%--<td style="width:220px;">
								Report Scale: 
								<asp:dropdownlist id="chartList" runat="server" onchange="changeChartZoom(); return false;" cssclass="body">
									<asp:listitem text="Hide Chart" value="hide"></asp:listitem>
									<asp:listitem text="Fit to Page Width" value="fit"></asp:listitem>
									<asp:listitem text="25%" value="25%"></asp:listitem>
									<asp:listitem text="50%" value="50%"></asp:listitem>
									<asp:listitem text="60%" value="60%"></asp:listitem>
									<asp:listitem text="75%" value="75%"></asp:listitem>
									<asp:listitem text="90%" value="90%"></asp:listitem>
									<asp:listitem text="100%" value="100%" selected="true"></asp:listitem>
									<asp:listitem text="150%" value="150%"></asp:listitem>
									<asp:listitem text="200%" value="200%"></asp:listitem>
									<asp:listitem text="300%" value="300%"></asp:listitem>
									<asp:listitem text="400%" value="400%"></asp:listitem>
									<asp:listitem text="500%" value="500%"></asp:listitem>
								</asp:dropdownlist>
							</td>--%>
							<td>&nbsp;</td>
						</tr>
					</table>
					<hr />
				</div>
			</asp:panel>
			
			<div id="OuterZoom" runat="server" style="width:504pt; text-align:center;">
				<div id="DataZoomDiv" runat="server">
					<asp:Panel ID="GridBindingPanel" runat="server">
						<table border="0" cellpadding="0" cellspacing="1" style="text-align:center;">
							<asp:Repeater ID="ColumnHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders %>'>
								<ItemTemplate>
									<tr>
										<asp:Repeater ID="ColumnHeaderSpacingRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
											<ItemTemplate>
												<td nowrap="nowrap">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>
											</ItemTemplate>
										</asp:Repeater>
										<asp:Repeater ID="ColumnHeaderRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders[ColumnHeaderLevelRepeater.Items.Count] %>'>
											<ItemTemplate>
												<td class="cellHeading" style="padding:4px 2px 4px 2px;"><%# Container.DataItem %></td>
											</ItemTemplate>
										</asp:Repeater>
									</tr>
								</ItemTemplate>
							</asp:Repeater>
							<asp:Repeater ID="GridRowsRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayData %>'>
								<ItemTemplate>
									<tr style="background-color: #f9f9f9;">
										<asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
											<ItemTemplate>
												<td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
											</ItemTemplate>
										</asp:Repeater>
										<asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
											<ItemTemplate>
												<td><%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : (string)Container.DataItem)%></td>
											</ItemTemplate>
										</asp:Repeater>
									</tr>
								</ItemTemplate>
								<alternatingitemtemplate>
									<tr style="background-color: #e9e9e9;">
										<asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
											<ItemTemplate>
												<td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
											</ItemTemplate>
										</asp:Repeater>
										<asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
											<ItemTemplate>
												<td><%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : (string)Container.DataItem)%></td>
											</ItemTemplate>
										</asp:Repeater>
									</tr>
								</alternatingitemtemplate>
								<footertemplate>
								</footertemplate>
							</asp:Repeater>
						</table> 
					</asp:Panel>
				</div>
			</div>	
		</div>
    </form>
</body>
</html>