﻿using System;
using System.Data;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.UI;

public partial class EditScheduledReport : System.Web.UI.Page
{
	public int ScheduledReportId
	{
		get { if (this.ViewState["sr"] != null) return (int)this.ViewState["sr"]; else return 0; }
		set { this.ViewState["sr"] = value; }
	}

    private string reportsJSON = null;
    public string ReportsJSON
    {
        get
        {
            if (reportsJSON == null)
                reportsJSON = BuildReportsJSON(null);
            return reportsJSON;
        }
    }

	protected void Page_Load(object sender, EventArgs e)
	{
        if (!string.IsNullOrEmpty(Request.Params["subReportExpandList"]))
        {
            if (!string.IsNullOrEmpty(Request.Params["subReportExpandList"]))
                Session["scheduledSubReportExpandList"] = Request.Params["subReportExpandList"];

            Response.End();
        }

		deleteBtn.Attributes.Add("onclick", "return confirm('Are you sure you wish to delete this scheduled report?');");

		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SCHEDULED_REPORT_ID_KEY]))
				ScheduledReportId = Convert.ToInt32(Request.QueryString[DieboldConstants.SCHEDULED_REPORT_ID_KEY]);

			if (ScheduledReportId > 0)
			{
				LoadScheduledReport();
				deleteBtn.Visible = true;
			}
		}

        if (!string.IsNullOrEmpty(Request.Params["SelectAction"]))
        {
            switch (Request.Params["SelectAction"])
            {
                case "Add":
                    AddReports();
                    break;
                case "Format":
                    FormatReports();
                    break;
                case "Remove":
                    DeleteReports();
                    break;
                case "MoveTop":
                    MoveTop();
                    break;
                case "MoveUp":
                    MoveUp();
                    break;
                case "MoveDown":
                    MoveDown();
                    break;
                case "MoveBottom":
                    MoveBottom();
                    break;
            }
        }
	}

	private void LoadScheduledReport()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadScheduledReport", this.ScheduledReportId);

		foreach (DataRow row in ds.Tables[0].Rows) //only loads one row.
		{
			this.ScheduledReportId = DataFormatter.getInt32(row, "ScheduledReportId");
			scheduledReportName.Text = DataFormatter.getString(row, "ReportName");

			string nextScheduledDate = DataFormatter.getDateTime(row, "NextScheduledDate").ToShortDateString();
			if (string.Compare(nextScheduledDate, DateTime.MinValue.ToShortDateString()) != 0 && string.Compare(nextScheduledDate, DateTime.MaxValue.ToShortDateString()) != 0)
				timeField.SelectedDate = DataFormatter.getDateTime(row, "NextScheduledDate");

			scheduleList.Items[0].Selected = DataFormatter.getBool(row, "Sunday");
			scheduleList.Items[1].Selected = DataFormatter.getBool(row, "Monday");
			scheduleList.Items[2].Selected = DataFormatter.getBool(row, "Tuesday");
			scheduleList.Items[3].Selected = DataFormatter.getBool(row, "Wednesday");
			scheduleList.Items[4].Selected = DataFormatter.getBool(row, "Thursday");
			scheduleList.Items[5].Selected = DataFormatter.getBool(row, "Friday");
			scheduleList.Items[6].Selected = DataFormatter.getBool(row, "Saturday");

            if (!string.IsNullOrEmpty(DataFormatter.getString(row, "SaveToFilePath"))) {
                saveToFilePathBox.Text = DataFormatter.getString(row, "SaveToFilePath");
                saveToFileCheck.Checked = true;
            }

            if (!string.IsNullOrEmpty(DataFormatter.getString(row, "EmailList")))
			{
				emailAddressBox.Text = DataFormatter.getString(row, "EmailList");
				receiveEmailCheck.Checked = true;
			}

			if (!string.IsNullOrEmpty(DataFormatter.getString(row, "PrinterPath")))
			{
				printerPath.Text = DataFormatter.getString(row, "PrinterPath");
				printCheck.Checked = true;
				numCopies.Text = DataFormatter.getInt32(row, "NumCopies").ToString();
			}
		}

        this.reportsJSON = BuildReportsJSON(ds);
	}

    private string BuildReportsJSON(DataSet ds)
    {
        if (ds == null)
            ds = SqlHelper.ExecuteDataset("RPT_LoadScheduledReport", this.ScheduledReportId);

        ds.Relations.Add("SubReports", ds.Tables[1].Columns["ReportId"], ds.Tables[2].Columns["ReportId"]);

        StringBuilder str = new StringBuilder();
        foreach (DataRow row in ds.Tables[1].Rows)
        {
            if (str.Length > 0)
                str.Append(',');

            str.Append(string.Format("{{" +
                "id:'{0}'," +
                "name:'{1}'," +
                "orient:'{2}'," +
                "chartScale:'{3}'," +
                "dataScale:'{4}'," +
                "separate:'{5}'," +
                "sub:[{6}]" +
                "}}", DataFormatter.getInt32(row, "ReportId"), Utility.FormatJsonString(row, "ReportName"),
                    (DataFormatter.getBool(row, "Landscape") ? "Landscape" : "Portrait"), DataFormatter.getString(row, "ChartScale"), 
                    DataFormatter.getString(row, "DataScale"), (DataFormatter.getBool(row, "DataSeparate") ? "Yes" : "No"),
                    BuildSubReports(row)));
        }

        return str.ToString();
    }

    private string BuildSubReports(DataRow child)
    {
        StringBuilder subReportStr = new StringBuilder();
        foreach (DataRow subReport in child.GetChildRows("SubReports"))
        {
            if (!string.IsNullOrEmpty(DataFormatter.getString(subReport, "SubReportName")))
            {
                if (subReportStr.Length > 0)
                    subReportStr.Append(',');

                subReportStr.Append(string.Format("{{" +
                    "id:'{0}'," +
                    "name:'{1}'," +
                    "orient:'{2}'," +
                    "chartScale:'{3}'," +
                    "dataScale:'{4}'," +
                    "separate:'{5}'" +
                    "}}", DataFormatter.getInt32(subReport, "SubReportId"), Utility.FormatJsonString(subReport, "SubReportName"),
                    (DataFormatter.getBool(subReport, "Landscape") ? "Landscape" : "Portrait"), DataFormatter.getString(subReport, "ChartScale"),
                    DataFormatter.getString(subReport, "DataScale"), (DataFormatter.getBool(subReport, "DataSeparate") ? "Yes" : "No")));
            }
        }
        return subReportStr.ToString();
    }

	protected void AddReports()
	{
        Page.Validate();
		if (Page.IsValid)
		{
			//if never been saved yet, create a new report before adding a child record to it.
			if (this.ScheduledReportId == 0)
				SaveScheduledReport(false);

			Response.Redirect(string.Format("EditScheduledReportItem.aspx?{0}={1}", DieboldConstants.SCHEDULED_REPORT_ID_KEY, this.ScheduledReportId));
		}		
	}

    protected void FormatReports()
    {
        Page.Validate();
		if (Page.IsValid && (!string.IsNullOrEmpty(Request.Params["reportId"]) || !string.IsNullOrEmpty(Request.Params["subReportId"])))
		{
			//if never been saved yet, create a new report before adding a child record to it.
			if (this.ScheduledReportId == 0)
				SaveScheduledReport(false);

            Response.Redirect(string.Format("EditScheduledReportItem.aspx?{0}={1}&{2}={3}&{4}={5}", DieboldConstants.SCHEDULED_REPORT_ID_KEY, this.ScheduledReportId,
                DieboldConstants.REPORT_ID_KEY, Request.Params["reportId"], DieboldConstants.SUBREPORT_ID_KEY, Request.Params["subReportId"]));
		}
    }

    protected void DeleteReports()
    {
        if (!string.IsNullOrEmpty(Request.Params["subReportId"]))
        {
            foreach (string subReportStr in Request.Params["subReportId"].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                SqlHelper.ExecuteNonQuery("RPT_DeleteScheduledReportSubItem", this.ScheduledReportId, subReportStr);
            }
        }

        if (!string.IsNullOrEmpty(Request.Params["reportId"]))
        {
            foreach (string reportStr in Request.Params["reportId"].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                SqlHelper.ExecuteNonQuery("RPT_DeleteScheduledReportItem", this.ScheduledReportId, reportStr);
            }
        }
    }

    protected void MoveTop()
    {
        if (!string.IsNullOrEmpty(Request.Params["reportId"]))
        {
            int pos = 1;
            foreach (string reportStr in Request.Params["reportId"].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReportOrder", this.ScheduledReportId, reportStr, pos);
                pos = pos + 1;
            }
        }
        LoadScheduledReport();
    }

    protected void MoveUp()
    {
        if (!string.IsNullOrEmpty(Request.Params["reportId"]))
        {
            foreach (string reportStr in Request.Params["reportId"].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReportOrder", this.ScheduledReportId, reportStr, -2);
            }
        }
        LoadScheduledReport();
    }

    protected void MoveDown()
    {
        if (!string.IsNullOrEmpty(Request.Params["reportId"]))
        {
            foreach (string reportStr in Request.Params["reportId"].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReportOrder", this.ScheduledReportId, reportStr, -3);
            }
        }
        LoadScheduledReport();
    }

    protected void MoveBottom()
    {
        if (!string.IsNullOrEmpty(Request.Params["reportId"]))
        {
            foreach (string reportStr in Request.Params["reportId"].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReportOrder", this.ScheduledReportId, reportStr, -4);
            }
        }
        LoadScheduledReport();
    }
    
    protected void SaveButton_Click(object sender, EventArgs e)
	{
		SaveScheduledReport(true);
	}

	private void SaveScheduledReport(bool shouldRedirect)
	{
		object saveToFileValue = null;
        object emailAddresses = null;
        object printPath = null;
		object selectedTime = null;
		int copies = 0;

        if (saveToFileCheck.Checked && !string.IsNullOrEmpty(saveToFilePathBox.Text.Trim())) {
            invalidExtensionMsg.Visible = false;
            string tempFileName = saveToFilePathBox.Text.Trim();
            string directory = Path.GetDirectoryName(tempFileName);
            string fileName = Path.GetFileName(tempFileName);
            string extension = Path.GetExtension(tempFileName);

            //clean directory name
            string dirRegexSearch = new string(Path.GetInvalidPathChars());
            Regex dr = new Regex(string.Format("[{0}]", Regex.Escape(dirRegexSearch)));
            directory = dr.Replace(directory, "");

            //clean file name
            string fileRegexSearch = new string(Path.GetInvalidFileNameChars());
            Regex fr = new Regex(string.Format("[{0}]", Regex.Escape(fileRegexSearch)));
            fileName = fr.Replace(fileName, "");

            //update file name on page in case validation fails
            string cleanPath = directory + "\\" + fileName;
            saveToFilePathBox.Text = cleanPath;
            saveToFileValue = cleanPath;

            if (string.Compare(extension, ".pdf", true) != 0) {
                invalidExtensionMsg.Visible = true;
                return;
            }
        }

        if (receiveEmailCheck.Checked && !string.IsNullOrEmpty(emailAddressBox.Text.Trim()))
			emailAddresses = emailAddressBox.Text.Trim().Replace(" ", "");

        if (printCheck.Checked && !string.IsNullOrEmpty(printerPath.Text.Trim())) {
            printPath = printerPath.Text.Trim();

            if (!string.IsNullOrEmpty(numCopies.Text.Trim()))
                copies = Convert.ToInt32(numCopies.Text.Trim());
            else
                copies = 0;
        }

        if (timeField.SelectedDate != null)
			selectedTime = Utility.CalculateNextScheduledReportDate((DateTime)timeField.SelectedDate, scheduleList);

		this.ScheduledReportId = Convert.ToInt32(SqlHelper.ExecuteScalar("RPT_UpdateScheduledReport",
			this.ScheduledReportId, this.scheduledReportName.Text.Trim(), selectedTime, scheduleList.Items[0].Selected,
			scheduleList.Items[1].Selected, scheduleList.Items[2].Selected, scheduleList.Items[3].Selected, scheduleList.Items[4].Selected,
			scheduleList.Items[5].Selected, scheduleList.Items[6].Selected, saveToFileValue, emailAddresses, printPath, copies));

		if (shouldRedirect)
			Response.Redirect("ScheduledReports.aspx");
	}

	protected void DeleteButton_Click(object sender, EventArgs e)
	{
		SqlHelper.ExecuteNonQuery("RPT_DeleteScheduledReport", this.ScheduledReportId);
		Response.Redirect("ScheduledReports.aspx");
	}
}
