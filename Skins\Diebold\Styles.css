/* Docking Zone properties */
a {
	text-decoration:none !important;
}
.RadDockZone_Diebold
{
    border:1px solid #d3d3d3;
    padding:5px 0 0 5px;
    background:#fff;
}

*>.RadDockZone_Diebold.rdVertical /* hidden from IE6 */
{
    padding-right:5px;
}

/* Dock Object properties */

/* wrappers and borders */

.RadDockZone_Diebold .rdPlaceHolder
{
    border-color:#666;
}

.RadDock_Diebold
{
    margin:0 5px 5px 0;
    border-bottom:solid 20px #5b5551;
    background:#fafafa;
    color:#000;
    font-family:'ms sans serif',sans-serif;
    text-align:left;
}

.RadDock_Diebold .rdTopBorder,
.RadDock_Diebold .rdSideBorders,
.RadDock_Diebold .rdBottomBorder
{
    border-width:1px;
    border-color:#ebebeb;
}
.RadDock_Diebold .rdSideBorders
{
    border-style:none solid none;
    overflow:visible;
}
.RadDock_Diebold .rdTopBorder
{
    border-style:none none none;
}
.RadDock_Diebold .rdBottomBorder
{
    border-style:none none solid;
}


/* titlebar and buttons */

.RadDock_Diebold .rdTop
{
    top:0px; /*brd*/
    color:#fff;
}

.RadDock_Diebold .rdTop .rdLeft
{
    height:30px;
	width:9px !important;
    background:url(img/TitlebarLeft.gif) no-repeat !important;
    margin-left:-1px;
}
.RadDock_Diebold .rdTop .rdCenter
{
    height:30px;
    width:365px !important;
    background:url(img/TitlebarCenter.gif) no-repeat !important;
}

.RadDock_Diebold .rdTop .rdRight
{
	height:30px;
	background:url(img/TitlebarRight.gif) no-repeat !important;
}

.RadDock_Diebold .rdCommands
{
    right:-1px;
	width:120px !important;
}

.RadDock_Diebold .rdCommands a
{
	display:block;
	margin:0;
    height:32px;
	width:55px !important;
}
.RadDock_Diebold .rdCommands a span {
	display:block;
	width:100% !important;
	cursor:pointer !important;
}
.RadDock_Diebold .rdCollapse
{
    width:15px;
    background:url(img/Collapse.gif) repeat-x;
}
.RadDock_Diebold .rdExpand
{
    width:15px;
    background:url(img/Expand.gif) repeat-x;
}

.RadDock_Diebold .rdCustom
{
    width:32px !important;
    background:url(img/edit.gif) no-repeat;
    cursor:pointer;
	text-indent:0px !important;
	background-position:23px -7px !important;
}
.RadDock_Diebold .rdClose
{
    width:53px !important;
    background:url(img/Remove.gif) repeat-x;
    cursor:pointer;
	background-position:0px -7px !important;
}

.RadDock_Diebold .rdPin
{
    width:15px;
    background:url(img/Pin.gif) repeat-x;
}
.RadDock_Diebold .rdUnpin
{
    width:15px;
    background:url(img/Unpin.gif) repeat-x;
}

/* inner content */

.RadDock_Diebold .rdContent
{
    top:0px; /*brd*/
    padding-bottom:0px; /*brd*/
    font:11px/1.2 verdana,sans-serif;
}

.RadDock_Diebold .rdVTitlebar .rdContent
{
    margin-left:34px;
}

.RadDock .rdCenter .rdCommands {
	width:100px;
	font: normal normal 12px "Segoe UI",Arial,Sans-serif;
}

/* drag grips */

.RadDock_Diebold .rdGripTop,
.RadDock_Diebold .rdGripLeft
{
    background:#666;
}

.RadDock_Diebold .rdGripTop
{
    top:0px; /*brd*/
    height:3px;
}
.RadDock_Diebold .rdGripLeft
{
    left:1px; /*brd*/
    width:3px;
}

.RadDock_Diebold .rdWGripLeft .rdContent
{
    margin-left:3px;
}

.RadDock_Diebold .DockHightlight
{
	border:solid 1px #ff0000;
}

.RadComboBoxDropDown_Default {
	border-color: #828282;
	color: #333;
	background: white !important;
	font-family: "Segoe UI",Arial,Helvetica,sans-serif;
	font-size: 12px;
}

.RadComboBoxDropDown .rcbScroll {
	position: relative;
	overflow: auto !important;
}

.reportTreeViewScroller {
	width:100%;
	height:175px !important;
}