using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class FieldOptions : System.Web.UI.Page
{
	public string FieldId
	{
		get { return (string)this.ViewState["f"]; }
		set { this.ViewState["f"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FIELD_ID_KEY]))
				this.FieldId = Request.Params[DieboldConstants.FIELD_ID_KEY];

			if (!string.IsNullOrEmpty(Request.Params["page"]))
				FieldOptionsGrid.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}

		this.NewFieldOptionButton.Attributes.Add("onclick", string.Format("window.radopen('Popup_EditFieldOption.aspx?{0}={1}&page={2}', null); return false;", DieboldConstants.FIELD_ID_KEY, this.FieldId, FieldOptionsGrid.CurrentPageIndex));
		
		FieldOptionsGrid.DataSource = SqlHelper.ExecuteDataset("RPT_GetFieldOptions", this.FieldId);
		FieldOptionsGrid.DataBind();
	}

	public void RebindData(object sender, EventArgs e)
	{
		//do nothing, data rebound on every page load.
	}
}
