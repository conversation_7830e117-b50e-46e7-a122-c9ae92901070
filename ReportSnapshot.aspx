<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="ReportSnapshot.aspx.cs" Inherits="ReportSnapshot" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Report Snapshots</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<%--<div class="title" style="padding-bottom:2px;">Save Snapshot</div>--%>
					<br />
					<div id="NewSnapshotArea" runat="server">
						<div class="cellHeading">New Snapshot</div>
						<table border="0" cellpadding="0" cellspacing="14" width="100%">
							<tr>
								<td class="rowHeading" style="width:150px;">Snapshot Name</td>
								<td>
									<asp:textbox id="SnapshotNameField" runat="server" width="220"></asp:textbox>
									<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="SnapshotNameField" display="dynamic" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator>
								</td>
							</tr>
							<tr>
								<td class="rowHeading" style="width:150px;">Description</td>
								<td>
									<asp:textbox id="DescriptionField" runat="server" textmode="multiLine" rows="5" width="320"></asp:textbox>
								</td>
							</tr>
						</table>
						
						<table border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
								<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
							</tr>
						</table>
						<br />
					</div>
					
					<div class="title" style="padding-bottom:2px;">Saved Report Snapshots</div>
					<br />
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:200px;" class="cellHeading">Snapshot Name</td>
							<td style="width:85px;" class="cellHeading">Date</td>
							<td style="width:205px;" class="cellHeading">Description</td>
							<td class="cellHeading">&nbsp;</td>
						</tr>
					</table>
					<div style="padding-top:5px; padding-left:0px; overflow:auto; height:190px;">
						<table width="96%" border="0" cellpadding="0" cellspacing="0">
							<asp:repeater id="SnapshotRepeater" runat="server">
								<itemtemplate>
									<tr class="gridItem">
										<td style="width:200px;"><%# DataFormatter.Format(Container.DataItem, "SnapshotName") %></td>
										<td style="width:85px;"><%# DataFormatter.FormatDate(Container.DataItem, "SnapshotDate", "MM/dd/yyyy hh:mm tt", "")%></td>
										<td style="width:205px;"><asp:label id="descLabel" runat="server" tooltip='<%# DataFormatter.Format(Container.DataItem, "Description") %>'><%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "Description"), 30, 35)%></asp:label></td>
										<td><div class="goButton"><asp:linkbutton runat="server" causesvalidation="false" commandargument='<%# DataFormatter.Format(Container.DataItem, "SnapshotId") %>' oncommand="ViewSnapshot_Command" id="ViewButton">View</asp:linkbutton></div></td>
									</tr>
								</itemtemplate>
								<alternatingitemtemplate>
									<tr class="gridItemAlt">
										<td style="width:200px;"><%# DataFormatter.Format(Container.DataItem, "SnapshotName") %></td>
										<td style="width:85px;"><%# DataFormatter.FormatDate(Container.DataItem, "SnapshotDate", "MM/dd/yyyy hh:mm tt", "") %></td>
										<td style="width:205px;"><asp:label id="descLabel" runat="server" tooltip='<%# DataFormatter.Format(Container.DataItem, "Description") %>'><%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "Description"), 30, 35) %></asp:label></td>
										<td><div class="goButton"><asp:linkbutton runat="server" causesvalidation="false" commandargument='<%# DataFormatter.Format(Container.DataItem, "SnapshotId") %>' oncommand="ViewSnapshot_Command" id="ViewButton">View</asp:linkbutton></div></td>
									</tr>
								</alternatingitemtemplate>
							</asp:repeater>
							<asp:label id="NoDataLabel" runat="server" visible="false"><div style="padding-left:14px; padding-top:10px;">No saved snapshots were found for this report.</div></asp:label>
						</table>
					</div>
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

