using System;
using System.Data;
using System.Configuration;
using System.Collections.Generic;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using AjaxControlToolkit;

public partial class NewReportWizard_Progress : System.Web.UI.Page
{
    private string[] SupressedDimensions = new string[] { "Cell", "Session", "Measures", "Time", "Test Session", "Censoring", "Test Location", "Device",
        "Metric Distribution", "Metric Values", "Engineering Distribution", "Engineering Values", "Engineering Note Number", "Engineering Index Number", 
		"Distribution", "Distribution Values", "Setting", "Setting 2", "Setting 3", "Sensor", "File Number", "Media Number", "Command Number" };

	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	public int SeriesCount
	{
		get { if (this.ViewState["s"] != null) return (int)this.ViewState["s"]; else return 1; }
		set { this.ViewState["s"] = value; }
	}

	public int SelectedSeries
	{
		get { if (this.ViewState["ss"] != null) return (int)this.ViewState["ss"]; else return 1; }
		set { this.ViewState["ss"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			this.RepInfo = Utility.GetReportInfoFromTransfer();
			if (this.RepInfo == null)
				Response.Redirect("popuperror.aspx");

			DeleteSeriesButton.Attributes.Add("onclick", "return confirm('Are you sure you wish to delete the series?');");

			if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
			{
				this.dimensionSel.PopulateDimensions(true, SupressedDimensions);
				this.splitByCell.Text = "Split Series By Cell";
			}
			else
			{
				this.dimensionSel.PopulateDimensions(false, SupressedDimensions);
				this.splitByCell.Text = "Split Series By Device";
			}

			groupingList.DataSource = Utility.GetDimensionGroupByList();
			groupingList.DataBind();

			rateTypeList.DataSource = Utility.GetProgressRateTypeList();
			rateTypeList.DataBind();

			reportLabel.Text = this.RepInfo.ReportName;
			reportTypeLabel.Text = this.RepInfo.ReportTypeName; 
			normalizeCheck.Checked = this.RepInfo.ProgressInfo.Normalize;
			splitByCell.Checked = this.RepInfo.ProgressInfo.SplitByCell;
			assumeOneFailureCheck.Checked = this.RepInfo.ProgressInfo.AssumeOneFailure;
			
			if (this.RepInfo.ProgressInfo.GroupingId != 0)
				groupingList.SelectedValue = ((int)this.RepInfo.ProgressInfo.GroupingId).ToString();
			else
				groupingList.SelectedIndex = 1;

            this.minYScale.Text = this.RepInfo.MinYScale;
            this.maxYScale.Text = this.RepInfo.MaxYScale;
            
            SetupSeriesDisplay();
			LoadSeriesData(1);
			
			this.seriesName.Focus();
		}
	}

	protected void AddSeries_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			SaveSelectedSeries();
			this.SeriesCount++;
			this.SelectedSeries = this.SeriesCount;
			LoadSeriesData(0);
			UpdateSeriesDisplay();

			if (this.RepInfo.ReportTypeName.ToLower().Contains("legacy"))
				this.dimensionSel.PopulateDimensions(true, SupressedDimensions);
			else
				this.dimensionSel.PopulateDimensions(false, SupressedDimensions);
		}
	}

	protected void DeleteSeries_Click(object sender, EventArgs e)
	{
		LoadSeriesData(0); //load blank data and save to clear out series
		SaveSelectedSeries();

		//if not deleting last series added, move all later series down into the deleted postion
		while (this.SelectedSeries < this.SeriesCount)
		{
			CompactSeriesData();
		}

		this.SeriesCount--;
		this.SelectedSeries = this.SeriesCount;
		LoadSeriesData(this.SelectedSeries);
		UpdateSeriesDisplay();
	}

	private void CompactSeriesData()
	{
		//load next series data to current postion
		LoadSeriesData(this.SelectedSeries + 1);
		SaveSelectedSeries();
		
		this.SelectedSeries++;

		//if next position is new final series, clear out its old data
		if (this.SelectedSeries == this.SeriesCount)
		{
			LoadSeriesData(0);
			SaveSelectedSeries();
		}
	}

	protected void ChangeSelectedSeries_Command(object sender, CommandEventArgs e)
	{
		if (Page.IsValid)
		{
			if (e.CommandArgument != null)
			{
				SaveSelectedSeries();
				this.SelectedSeries = Convert.ToInt32(e.CommandArgument.ToString());
				LoadSeriesData(this.SelectedSeries);
				UpdateSeriesDisplay();
			}
		}
	}

	protected void NextButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			//Set Page Values
			this.RepInfo.ProgressInfo.Normalize = normalizeCheck.Checked;
			this.RepInfo.ProgressInfo.SplitByCell = splitByCell.Checked;
			this.RepInfo.ProgressInfo.AssumeOneFailure = assumeOneFailureCheck.Checked;

			if (!string.IsNullOrEmpty(groupingList.SelectedValue))
				this.RepInfo.ProgressInfo.GroupingId = (ReportHelper.DateGroupingEnum)Convert.ToInt32(groupingList.SelectedValue);

            this.RepInfo.MinYScale = this.minYScale.Text;
            this.RepInfo.MaxYScale = this.maxYScale.Text;
            
            SaveSelectedSeries();

			Utility.SetReportInfoForTransfer(this.RepInfo);
			Response.Redirect("NewReportWizard_Format.aspx");
		}
	}

	private void SaveSelectedSeries()
	{
		switch (this.SelectedSeries)
		{
			case 1:
				this.RepInfo.ProgressInfo.DimensionName1 = this.dimensionSel.DimensionName;
				this.RepInfo.ProgressInfo.DimensionMembers1 = this.dimensionSel.CheckedDimensionMembers;
				this.RepInfo.ProgressInfo.SeriesName1 = seriesName.Text.Trim();
				this.RepInfo.ProgressInfo.SpecLabel1 = specLabelField.Text.Trim();
				this.RepInfo.ProgressInfo.Format1 = formatTypeList.SelectedValue;
				
				if (!string.IsNullOrEmpty(rateTypeList.SelectedValue))
					this.RepInfo.ProgressInfo.RateTypeId1 = (ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue);

                if (!String.IsNullOrEmpty(specRateField.Text))
                    this.RepInfo.ProgressInfo.SpecRate1 = Convert.ToDecimal(specRateField.Text.Trim());
                else
                    this.RepInfo.ProgressInfo.SpecRate1 = decimal.MinValue;

				if (!String.IsNullOrEmpty(workLoadField.Text))
					this.RepInfo.ProgressInfo.WorkLoad1 = Convert.ToDecimal(workLoadField.Text.Trim());
				else
					this.RepInfo.ProgressInfo.WorkLoad1 = decimal.MinValue;
				break;

			case 2:
				this.RepInfo.ProgressInfo.DimensionName2 = this.dimensionSel.DimensionName;
				this.RepInfo.ProgressInfo.DimensionMembers2 = this.dimensionSel.CheckedDimensionMembers;
				this.RepInfo.ProgressInfo.SeriesName2 = seriesName.Text.Trim();
				this.RepInfo.ProgressInfo.SpecLabel2 = specLabelField.Text.Trim();
				this.RepInfo.ProgressInfo.Format2 = formatTypeList.SelectedValue;

				if (!string.IsNullOrEmpty(rateTypeList.SelectedValue))
					this.RepInfo.ProgressInfo.RateTypeId2 = (ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue);

				if (!String.IsNullOrEmpty(specRateField.Text))
					this.RepInfo.ProgressInfo.SpecRate2 = Convert.ToDecimal(specRateField.Text.Trim());
                else
                    this.RepInfo.ProgressInfo.SpecRate2 = decimal.MinValue;

				if (!String.IsNullOrEmpty(workLoadField.Text))
					this.RepInfo.ProgressInfo.WorkLoad2 = Convert.ToDecimal(workLoadField.Text.Trim());
				else
					this.RepInfo.ProgressInfo.WorkLoad2 = decimal.MinValue;
                break;

			case 3:
				this.RepInfo.ProgressInfo.DimensionName3 = this.dimensionSel.DimensionName;
				this.RepInfo.ProgressInfo.DimensionMembers3 = this.dimensionSel.CheckedDimensionMembers;
				this.RepInfo.ProgressInfo.SeriesName3 = seriesName.Text.Trim();
				this.RepInfo.ProgressInfo.SpecLabel3 = specLabelField.Text.Trim();
				this.RepInfo.ProgressInfo.Format3 = formatTypeList.SelectedValue;

				if (!string.IsNullOrEmpty(rateTypeList.SelectedValue))
					this.RepInfo.ProgressInfo.RateTypeId3 = (ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue);

				if (!String.IsNullOrEmpty(specRateField.Text))
					this.RepInfo.ProgressInfo.SpecRate3 = Convert.ToDecimal(specRateField.Text.Trim());
                else
                    this.RepInfo.ProgressInfo.SpecRate3 = decimal.MinValue;

				if (!String.IsNullOrEmpty(workLoadField.Text))
					this.RepInfo.ProgressInfo.WorkLoad3 = Convert.ToDecimal(workLoadField.Text.Trim());
				else
					this.RepInfo.ProgressInfo.WorkLoad3 = decimal.MinValue;
                break;

			case 4:
				this.RepInfo.ProgressInfo.DimensionName4 = this.dimensionSel.DimensionName;
				this.RepInfo.ProgressInfo.DimensionMembers4 = this.dimensionSel.CheckedDimensionMembers;
				this.RepInfo.ProgressInfo.SeriesName4 = seriesName.Text.Trim();
				this.RepInfo.ProgressInfo.SpecLabel4 = specLabelField.Text.Trim();
				this.RepInfo.ProgressInfo.Format4 = formatTypeList.SelectedValue;

				if (!string.IsNullOrEmpty(rateTypeList.SelectedValue))
					this.RepInfo.ProgressInfo.RateTypeId4 = (ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue);

				if (!String.IsNullOrEmpty(specRateField.Text))
					this.RepInfo.ProgressInfo.SpecRate4 = Convert.ToDecimal(specRateField.Text.Trim());
                else
                    this.RepInfo.ProgressInfo.SpecRate4 = decimal.MinValue;

				if (!String.IsNullOrEmpty(workLoadField.Text))
					this.RepInfo.ProgressInfo.WorkLoad4 = Convert.ToDecimal(workLoadField.Text.Trim());
				else
					this.RepInfo.ProgressInfo.WorkLoad4 = decimal.MinValue;
                break;
		}
	}

	private void UpdateSeriesDisplay()
	{
		this.SeriesNumLabel.Text = this.SelectedSeries.ToString();

		string selectedTabClassName = "tab_selected";
		string tabClassName = "tab";

		if (this.SelectedSeries == 1)
			this.Tab1.Attributes.Add("class", selectedTabClassName);
		else
			this.Tab1.Attributes.Add("class", tabClassName);

		if (this.SelectedSeries == 2)
			this.Tab2.Attributes.Add("class", selectedTabClassName);
		else
			this.Tab2.Attributes.Add("class", tabClassName);

		if (this.SelectedSeries == 3)
			this.Tab3.Attributes.Add("class", selectedTabClassName);
		else
			this.Tab3.Attributes.Add("class", tabClassName);

		if (this.SelectedSeries == 4)
			this.Tab4.Attributes.Add("class", selectedTabClassName);
		else
			this.Tab4.Attributes.Add("class", tabClassName);

		this.Series2Btn.Visible = (this.SeriesCount >= 2);
		this.Series3Btn.Visible = (this.SeriesCount >= 3);
		this.Series4Btn.Visible = (this.SeriesCount == 4);
		this.NewSeriesButton.Visible = (this.SeriesCount < 4);
		this.DeleteSeriesButton.Visible = (this.SeriesCount > 1);
	}

	private void SetupSeriesDisplay()
	{
		this.SelectedSeries = 1;
		this.SeriesNumLabel.Text = this.SelectedSeries.ToString();

		if (!string.IsNullOrEmpty(this.RepInfo.ProgressInfo.SeriesName1))
		{
			this.SeriesCount = 1;
		}
		if (!string.IsNullOrEmpty(this.RepInfo.ProgressInfo.SeriesName2))
		{
			this.SeriesCount = 2;
		}
		if (!string.IsNullOrEmpty(this.RepInfo.ProgressInfo.SeriesName3))
		{
			this.SeriesCount = 3;
		}
		if (!string.IsNullOrEmpty(this.RepInfo.ProgressInfo.SeriesName4))
		{
			this.SeriesCount = 4;
		}
		
		this.Series2Btn.Visible = (this.SeriesCount >= 2);
		this.Series3Btn.Visible = (this.SeriesCount >= 3);
		this.Series4Btn.Visible = (this.SeriesCount == 4);
		this.NewSeriesButton.Visible = (this.SeriesCount < 4);
		this.DeleteSeriesButton.Visible = (this.SeriesCount > 1);
	}

	private void LoadSeriesData(int seriesNum)
	{
		switch (seriesNum)
		{
			case 0:
                seriesName.Text = null;
				dimensionSel.DimensionName = "";
				dimensionSel.CheckedDimensionMembers.Clear(); 
				dimensionSel.CheckedDimensionMembers = null;
				dimensionSel.UpdateTreeDisplay();

				specLabelField.Text = null;
				specRateField.Text = null;
				formatTypeList.SelectedIndex = 0;
				rateTypeList.SelectedIndex = 0;
				workLoadField.Text = null;
				workLoadField.Enabled = false;
				break;
			case 1:
				seriesName.Text = this.RepInfo.ProgressInfo.SeriesName1;
				dimensionSel.DimensionName = this.RepInfo.ProgressInfo.DimensionName1;
				dimensionSel.CheckedDimensionMembers = this.RepInfo.ProgressInfo.DimensionMembers1;
				dimensionSel.UpdateTreeDisplay();

				specLabelField.Text = this.RepInfo.ProgressInfo.SpecLabel1;
				formatTypeList.SelectedValue = this.RepInfo.ProgressInfo.Format1;
				
				if (this.RepInfo.ProgressInfo.SpecRate1 != decimal.MinValue)
					specRateField.Text = this.RepInfo.ProgressInfo.SpecRate1.ToString("0.#");
                else
                    specRateField.Text = null;

				if (this.RepInfo.ProgressInfo.RateTypeId1 != 0)
					rateTypeList.SelectedValue = ((int)this.RepInfo.ProgressInfo.RateTypeId1).ToString();
				else
					rateTypeList.SelectedIndex = 1;

				//conditional workload field
				if (this.RepInfo.ProgressInfo.WorkLoad1 != decimal.MinValue)
					workLoadField.Text = this.RepInfo.ProgressInfo.WorkLoad1.ToString("0.#");
				else
					workLoadField.Text = null;

				if (rateTypeList.SelectedItem != null)
					workLoadField.Enabled = rateTypeList.SelectedItem.Text.ToLower().Contains("workload");

				break;
			case 2:
				seriesName.Text = this.RepInfo.ProgressInfo.SeriesName2;
				dimensionSel.DimensionName = this.RepInfo.ProgressInfo.DimensionName2;
				dimensionSel.CheckedDimensionMembers = this.RepInfo.ProgressInfo.DimensionMembers2;
				dimensionSel.UpdateTreeDisplay();

				specLabelField.Text = this.RepInfo.ProgressInfo.SpecLabel2;
				formatTypeList.SelectedValue = this.RepInfo.ProgressInfo.Format2;

				if (this.RepInfo.ProgressInfo.SpecRate2 != decimal.MinValue)
					specRateField.Text = this.RepInfo.ProgressInfo.SpecRate2.ToString("0.#");
                else
                    specRateField.Text = null;

				if (this.RepInfo.ProgressInfo.RateTypeId2 != 0)
					rateTypeList.SelectedValue = ((int)this.RepInfo.ProgressInfo.RateTypeId2).ToString();
				else
					rateTypeList.SelectedIndex = 1;

				//conditional workload field
				if (this.RepInfo.ProgressInfo.WorkLoad2 != decimal.MinValue)
					workLoadField.Text = this.RepInfo.ProgressInfo.WorkLoad2.ToString("0.#");
				else
					workLoadField.Text = null;

				if (rateTypeList.SelectedItem != null)
					workLoadField.Enabled = rateTypeList.SelectedItem.Text.ToLower().Contains("workload");

				break;
			case 3:
				seriesName.Text = this.RepInfo.ProgressInfo.SeriesName3;
				dimensionSel.DimensionName = this.RepInfo.ProgressInfo.DimensionName3;
				dimensionSel.CheckedDimensionMembers = this.RepInfo.ProgressInfo.DimensionMembers3;
				dimensionSel.UpdateTreeDisplay();

				specLabelField.Text = this.RepInfo.ProgressInfo.SpecLabel3;
				formatTypeList.SelectedValue = this.RepInfo.ProgressInfo.Format3;

                if (this.RepInfo.ProgressInfo.SpecRate3 != decimal.MinValue)
                    specRateField.Text = this.RepInfo.ProgressInfo.SpecRate3.ToString("0.#");
                else
                    specRateField.Text = null;

				if (this.RepInfo.ProgressInfo.RateTypeId3 != 0)
					rateTypeList.SelectedValue = ((int)this.RepInfo.ProgressInfo.RateTypeId3).ToString();
				else
					rateTypeList.SelectedIndex = 1;

				//conditional workload field
				if (this.RepInfo.ProgressInfo.WorkLoad3 != decimal.MinValue)
					workLoadField.Text = this.RepInfo.ProgressInfo.WorkLoad3.ToString("0.#");
				else
					workLoadField.Text = null;

				if (rateTypeList.SelectedItem != null)
					workLoadField.Enabled = rateTypeList.SelectedItem.Text.ToLower().Contains("workload");

				break;
			case 4:
				seriesName.Text = this.RepInfo.ProgressInfo.SeriesName4;
				dimensionSel.DimensionName = this.RepInfo.ProgressInfo.DimensionName4;
				dimensionSel.CheckedDimensionMembers = this.RepInfo.ProgressInfo.DimensionMembers4;
				dimensionSel.UpdateTreeDisplay();

				specLabelField.Text = this.RepInfo.ProgressInfo.SpecLabel4;
				formatTypeList.SelectedValue = this.RepInfo.ProgressInfo.Format4;

				if (this.RepInfo.ProgressInfo.SpecRate4 != decimal.MinValue)
					specRateField.Text = this.RepInfo.ProgressInfo.SpecRate4.ToString("0.#");
                else
                    specRateField.Text = null;

				if (this.RepInfo.ProgressInfo.RateTypeId4 != 0)
					rateTypeList.SelectedValue = ((int)this.RepInfo.ProgressInfo.RateTypeId4).ToString();
				else
					rateTypeList.SelectedIndex = 1;

				//conditional workload field
				if (this.RepInfo.ProgressInfo.WorkLoad4 != decimal.MinValue)
					workLoadField.Text = this.RepInfo.ProgressInfo.WorkLoad4.ToString("0.#");
				else
					workLoadField.Text = null;

				if (rateTypeList.SelectedItem != null)
					workLoadField.Enabled = rateTypeList.SelectedItem.Text.ToLower().Contains("workload");
				
				break;
		}
	}

    private List<string> buildUsedNameList()
    {
        List<string> retVal = new List<string>();
        ProgressInfo pi = this.RepInfo.ProgressInfo;

        if (this.SelectedSeries != 1)
        {
            if (!string.IsNullOrEmpty(pi.SeriesName1))
                retVal.Add(pi.SeriesName1.ToLower());
            if (!string.IsNullOrEmpty(pi.SpecLabel1))
                retVal.Add(pi.SpecLabel1.ToLower());
        }
        if (this.SelectedSeries != 2)
        {
            if (!string.IsNullOrEmpty(pi.SeriesName2))
                retVal.Add(pi.SeriesName2.ToLower());
            if (!string.IsNullOrEmpty(pi.SpecLabel2))
                retVal.Add(pi.SpecLabel2.ToLower());
        }
        if (this.SelectedSeries != 3)
        {
            if (!string.IsNullOrEmpty(pi.SeriesName3))
                retVal.Add(pi.SeriesName3.ToLower());
            if (!string.IsNullOrEmpty(pi.SpecLabel3))
                retVal.Add(pi.SpecLabel3.ToLower());
        }
        if (this.SelectedSeries != 4)
        {
            if (!string.IsNullOrEmpty(pi.SeriesName4))
                retVal.Add(pi.SeriesName4.ToLower());
            if (!string.IsNullOrEmpty(pi.SpecLabel4))
                retVal.Add(pi.SpecLabel4.ToLower());
        }

        return retVal;
    }

	protected void rateTypeList_selectedIndexChanged(object sender, EventArgs e)
	{
		workLoadField.Enabled = rateTypeList.SelectedItem.Text.ToLower().Contains("workload");

		if (!workLoadField.Enabled)
			workLoadField.Text = null;
	}
	
	protected void ValidateWorkLoad(object sender, ServerValidateEventArgs e)
	{
		e.IsValid = true;
		if (workLoadField.Enabled)
		{
			try
			{
				decimal val = Convert.ToDecimal(workLoadField.Text);
				if (val <= 0)
					e.IsValid = false;
			}
			catch
			{
				e.IsValid = false;
			}
		}
	}

	protected void ValidateSeriesNameCollision(object sender, ServerValidateEventArgs e)
	{
		if (e != null)
		{
            List<string> usedNames = buildUsedNameList();

            if (string.IsNullOrEmpty(this.seriesName.Text.Trim()) ||
                    (this.seriesName.Text.Trim() != this.specLabelField.Text.Trim() && usedNames.Contains(this.seriesName.Text.Trim().ToLower()) == false))
				e.IsValid = true;
            else
                e.IsValid = false;
		}	
	}

	protected void ValidateSpecNameCollision(object sender, ServerValidateEventArgs e)
	{
		if (e != null)
		{
            List<string> usedNames = buildUsedNameList();

            if (string.IsNullOrEmpty(this.specLabelField.Text.Trim()) ||
                    (this.seriesName.Text.Trim() != this.specLabelField.Text.Trim() && usedNames.Contains(this.specLabelField.Text.Trim().ToLower()) == false))
                e.IsValid = true;
            else
                e.IsValid = false;
		}
	}

	protected void ValidateRate(object sender, ServerValidateEventArgs e)
	{
        e.IsValid = true;
        if (e != null && !string.IsNullOrEmpty(rateTypeList.SelectedValue))
		{
            if (ReportHelper.RateTypeEnum.BY_STAT_VALUE == ((ReportHelper.RateTypeEnum)Convert.ToInt32(rateTypeList.SelectedValue)))
            {
                if (string.IsNullOrEmpty(this.dimensionSel.DimensionName) || !this.dimensionSel.DimensionName.StartsWith("[Statistic]."))
                    e.IsValid = false;
            }
		}
	}

    protected void ValidateSpecRate(object sender, ServerValidateEventArgs e)
	{
		if (e != null)
		{
			if (!String.IsNullOrEmpty(specRateField.Text.Trim()))
			{
				if (Convert.ToDecimal(specRateField.Text) > 0)
					e.IsValid = true;
				else
					e.IsValid = false;
			}
			else
			{
				e.IsValid = true;
			}
		}
	}
}
