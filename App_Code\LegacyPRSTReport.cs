using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;

public class LegacyPRSTReport : BaseReport
{
	public LegacyPRSTReport(ReportInfo repInfo) : base(repInfo, true) { }

    public override void LoadData()
    {
        StringBuilder mdxSetup = new StringBuilder();
        string mdxSelect = null;
        string mdxCategory = null;
        string mdxFromWhere = null;

        mdxSetup.Append(string.Format("WITH {0}\r\n",
            OLAPHelper.BuildMdxFilteredTime(ReportHelper.DateGroupingEnum.BY_DATETIME, repInfo.StartDate, repInfo.EndDate, "FilteredTime", "[Measures].[Transaction Count] > 0")));

        mdxSetup.Append(string.Format("{0}\r\n",
            OLAPHelper.BuildMdxFilteredTime(ReportHelper.DateGroupingEnum.BY_DATE, repInfo.StartDate, repInfo.EndDate, "FilteredDates", "[Measures].[Transaction Count] > 0")));

        mdxSetup.Append("SET Uncensored AS '{[Censoring].[Censoring]}'\r\n");
        mdxSetup.Append("SET Observations AS '{[Censoring].[Censoring].&[0]}'\r\n");
        mdxSetup.Append("MEMBER [Censoring].[Censoring].[Uncensored] AS 'Aggregate(Uncensored)'\r\n");
        mdxSetup.Append("MEMBER [Censoring].[Censoring].[Observations] AS 'Aggregate(Observations)'\r\n");

        if(!string.IsNullOrEmpty(repInfo.DimensionName) && repInfo.DimensionMembers != null && repInfo.DimensionMembers.Count > 0)
        {
            mdxSetup.Append("MEMBER [Observation Cnt] AS 'SUM(");
			mdxSetup.Append(OLAPHelper.BuildMdxLevelTuple(true, repInfo.DimensionName, repInfo.DimensionMembers, false));
            mdxSetup.Append(",[Measures].[Observation Count]) + 0', FORMAT_STRING = \"#,##0\"\r\n");
        } else {
            mdxSetup.Append("MEMBER [Observation Cnt] AS 'SUM([Measures].[Observation Count]) + 0', FORMAT_STRING = \"#,##0\"\r\n");
        }

        mdxSetup.Append("MEMBER [Cumulative Observations] AS 'SUM(HEAD(Axis(1), RANK([Time].[Year-Month-Date].CurrentMember, Axis(1))), [Measures].[Observation Cnt])', FORMAT_STRING = \"#,##0\"\r\n");
        mdxSetup.Append("MEMBER TempRank AS 'RANK([Time].[Year-Month-Date].CurrentMember.Parent, FilteredDates)'\r\n");

        switch (repInfo.PRSTInfo.XaxisTypeId)
        {
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_MEDIA:
                mdxSetup.Append("MEMBER [Cumulative Media] AS 'SUM(HEAD(Axis(1), RANK([Time].[Year-Month-Date].CurrentMember, Axis(1))), [Measures].[Media Count])', FORMAT_STRING = \"#,##0\"\r\n");

                mdxSetup.Append("MEMBER [Avg Media] AS '[Measures].[Transaction Count] * (AVG(TAIL(HEAD(FilteredDates, TempRank), 5),[Measures].[Media Count])) / [Measures].[Transaction Count]', FORMAT_STRING = \"#,##0.0\"\r\n");

                mdxSelect = "SELECT CROSSJOIN({[Censoring].[Censoring].[Observations], [Censoring].[Censoring].[Uncensored] }, {[Observation Cnt], [Cumulative Observations], [Measures].[Media Count], [Cumulative Media], [Avg Media]}) on columns, ";
                break;

            case ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS:
            default:
                mdxSetup.Append("MEMBER [Cumulative Transactions] AS 'SUM(HEAD(Axis(1), RANK([Time].[Year-Month-Date].CurrentMember, Axis(1))), [Measures].[Transaction Count])', FORMAT_STRING = \"#,##0\"\r\n");

                mdxSetup.Append("MEMBER [Avg Transactions] AS '[Measures].[Transaction Count] * (AVG(TAIL(HEAD(FilteredDates, TempRank), 5),[Measures].[Transaction Count])) / [Measures].[Transaction Count]', FORMAT_STRING = \"#,##0.0\"\r\n");

                mdxSelect = "SELECT CROSSJOIN({[Censoring].[Censoring].[Observations], [Censoring].[Censoring].[Uncensored] }, {[Observation Cnt], [Cumulative Observations], [Measures].[Transaction Count], [Cumulative Transactions], [Avg Transactions]}) on columns, ";
                break;
        }


        mdxCategory = "NON EMPTY {FilteredTime} on rows ";

        mdxFromWhere = string.Format("FROM [Transactions] WHERE ({0})", OLAPHelper.BuildMdxWhereTuples(true, repInfo.AttachedSessions, repInfo.CellFilters, null));

        string mdx = mdxSetup.ToString() + mdxSelect + mdxCategory + mdxFromWhere;

        ExecuteMdx(mdx);
        BuildGridDisplay();
    }

    public override void PopulateChart(Dundas.Charting.WebControl.Chart chart, bool includeToolTips)
    {
        TupleCollection colTuples = cellSet.Axes[0].Set.Tuples;
        HierarchyCollection colHierarchies = cellSet.Axes[0].Set.Hierarchies;
        TupleCollection rowTuples = cellSet.Axes[1].Set.Tuples;
        HierarchyCollection rowHierarchies = cellSet.Axes[1].Set.Hierarchies;

        string xAxisLabelPart = null;
        switch (repInfo.PRSTInfo.XaxisTypeId)
        {
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_MEDIA:
                xAxisLabelPart = "Media";
                break;
            case ReportHelper.XAxisTypeEnum.CUMULATIVE_TRANSACTIONS:
            default:
                xAxisLabelPart = "Transactions";
                break;
        }

        Dundas.Charting.WebControl.Axis axisX = chart.ChartAreas[0].AxisX;
        Dundas.Charting.WebControl.Axis axisY = chart.ChartAreas[0].AxisY;

        Dictionary<string, Series> priorSeries = new Dictionary<string, Series>();
        foreach (Series series in chart.Series)
            priorSeries.Add(series.Name, series);

        chart.Series.Clear();

        double maxTran = 0;
        double maxObs = 0;
        double maxObsOverall = 0;
        double prodTot = 0;
        double xsqrTot = 0;
        double ysqrTot = 0;
        double pointCnt = 0;
        double lastAvg = 0;

        int r = 15;
        double T = 0;
        double a = 1;
        double b = 1;
        double c = 1;
        bool testFailed = false;
        bool testSucceeded = false;
        double trendX = 0;
        double trendY = 0;

        // Calculate PRST accept/reject limits
        if (repInfo.PRSTInfo.MTBFSpec > 0 && (repInfo.PRSTInfo.CustomizeRatioRisk == false
            || (repInfo.PRSTInfo.DiscriminationRatio > 0 && repInfo.PRSTInfo.ProducersRisk > 0 && repInfo.PRSTInfo.ProducersRisk != 1 && repInfo.PRSTInfo.ConsumersRisk > 0)))
        {
            double thetaZero = (double)repInfo.PRSTInfo.MTBFSpec;
            double delta = (double)(repInfo.PRSTInfo.CustomizeRatioRisk ? repInfo.PRSTInfo.DiscriminationRatio : 2);
            double thetaOne = thetaZero / delta;
            double alpha = (repInfo.PRSTInfo.CustomizeRatioRisk ? (double)repInfo.PRSTInfo.ProducersRisk : 0.1);
            double beta = (repInfo.PRSTInfo.CustomizeRatioRisk ? (double)repInfo.PRSTInfo.ConsumersRisk : 0.1);
            T = thetaZero * 20.599235534668 / 2;
            double A = ((delta + 1) * (1 - beta)) / (2 * delta * alpha);
            double B = beta / (1 - alpha);
            a = Math.Log(B) / Math.Log(delta);
            b = ((1 / thetaOne) - (1 / thetaZero)) / Math.Log(thetaZero / thetaOne);
            c = Math.Log(A) / Math.Log(2);
        }

        // Build chart series data
        for (int col = 0; col < colTuples.Count; col = col + (colTuples.Count / colHierarchies.Count))
        {
            Microsoft.AnalysisServices.AdomdClient.Tuple colTuple = colTuples[col];

            Series series = null;
            string seriesName = colTuple.Members[0].Caption; // Observations or Uncensored

            if (priorSeries.ContainsKey(seriesName))
            {
                series = priorSeries[seriesName];
                priorSeries.Remove(series.Name);
                series.Points.Clear();
            }
            else
            {
                series = new Series(seriesName);
                series["DrawingStyle"] = "Cylinder";

                series.Type = SeriesChartType.StepLine;
            }

            for (int row = 0; row < rowTuples.Count; row++)
            {
                Cell cellA = cellSet.Cells[col + 1, row]; // Cumulative Observations
                Cell cellB = cellSet.Cells[col + 3, row]; // Cumulative Transactions/Media
                Cell cellC = cellSet.Cells[col + 4, row]; // Average Transactions/Media (over the last 5 days with activity)

                double YVal = (cellA.Value == null ? 0 : double.Parse(cellA.Value.ToString()));
                double XVal = (cellB.Value == null ? 0 : double.Parse(cellB.Value.ToString()));
                double AvgVal = (cellC.Value == null ? 0 : double.Parse(cellC.Value.ToString()));

                // Only total based on the observation series, not the uncensored series
                if (col == 0)
                {
                    pointCnt++;
                    maxTran = (maxTran < XVal ? XVal : maxTran);
                    maxObs = (maxObs < YVal ? YVal : maxObs);
                    prodTot += YVal * XVal;
                    xsqrTot += XVal * XVal;
                    ysqrTot += YVal * YVal;
                    lastAvg = (AvgVal > 0 ? AvgVal : lastAvg);

                    // Test for success or failure
                    if (!testFailed && !testSucceeded)
                    {
                        double testFailY = (maxTran * b) + c;
                        // Test for failure
                        if (maxObs > r || maxObs > testFailY)
                        {
                            testFailed = true;
                        }
                        else
                        {
                            // Test for success
                            double testAcceptX = (maxObs - a) / b;
                            if (maxTran > T || maxTran > testAcceptX)
                                testSucceeded = true;
                        }
                    }
                }

                maxObsOverall = (maxObsOverall < YVal ? YVal : maxObsOverall);

                DataPoint dp = new DataPoint(XVal, YVal); ;

                if(includeToolTips)
                    dp.ToolTip = string.Format("{0}\r\nObservations: {1}\r\n{2}: {3}", series.Name, cellA.FormattedValue, xAxisLabelPart, cellB.FormattedValue);
    
                series.Points.Add(dp);
            }

            if (string.Compare(series.Name, "Uncensored") == 0)
            {
                if (!repInfo.PRSTInfo.IncludeUncensoredSeries)
                    continue;
            }

            chart.Series.Add(series);
        }

        chart.Annotations.Clear();
        // Calculate trend line
        if (pointCnt > 0 && maxObs > 0 && maxTran > 0 && repInfo.PRSTInfo.IncludeTrendline)
        {
            double m = (prodTot - (maxObs * maxTran)) / (xsqrTot - (maxTran * maxTran));
            double trend_b = (maxObs - (m * maxTran)) / pointCnt;

            trendX = maxTran;
            trendY = m * trendX + trend_b;
        }

        // Ensure data is visible on chart
        axisX.Minimum = 0;
        axisY.Minimum = 0;

        axisX.Maximum = RoundAxis(Math.Max(Math.Max(maxTran, T), trendX), 100000);
        axisY.Maximum = RoundAxis(Math.Max(Math.Max((repInfo.PRSTInfo.IncludeUncensoredSeries ? maxObsOverall : maxObs), r), trendY), 10);

	if((repInfo.PRSTInfo.IncludeUncensoredSeries ? maxObsOverall : maxObs) <= 0)
	{
            Series acceptRejectSeries = new Series("Accept/Reject");
            acceptRejectSeries.ShowInLegend = false;
            acceptRejectSeries.Points.AddXY(T, r);
            acceptRejectSeries.Points[0].Color = Color.FromArgb(0, 0, 0, 0);
            chart.Series.Add(acceptRejectSeries);
	}

        ResetAxisMarks(axisX);
        ResetAxisMarks(axisY);

	axisX.LabelStyle.Interval = axisX.Maximum / 5;
	axisX.Interval = axisX.Maximum / 5;
	
        chart.ChartAreas[0].ReCalc();

        // Build reject and accept lines
        string daysToCompleteMessage = null;
        if (repInfo.PRSTInfo.MTBFSpec > 0 && (repInfo.PRSTInfo.CustomizeRatioRisk == false
            || (repInfo.PRSTInfo.DiscriminationRatio > 0 && repInfo.PRSTInfo.ProducersRisk > 0 && repInfo.PRSTInfo.ProducersRisk != 1 && repInfo.PRSTInfo.ConsumersRisk > 0)))
        {
            // Accept line
            double accX0 = (0 - a) / b;
            double accY1 = a + (b * T);
            SixSigma.AddLineAnnotation(axisX, axisY, accX0, 0, T - accX0, accY1, Color.Green, chart);
            SixSigma.AddLineAnnotation(axisX, axisY, T, accY1, 0, r - accY1, Color.Green, chart);

            // Reject line
            double rejX1 = (r - c) / b;
            SixSigma.AddLineAnnotation(axisX, axisY, 0, c, rejX1, r - c, Color.Red, chart);
            SixSigma.AddLineAnnotation(axisX, axisY, rejX1, r, T - rejX1, 0, Color.Red, chart);

            // Create days to complete message
            if (lastAvg > 0)
            {
                if (testSucceeded)
                    daysToCompleteMessage = "Test Complete - Accept";
                else if (testFailed)
                {
                    daysToCompleteMessage = "Test Complete - Reject";
                }
                else
                {
                    double daysCnt = 0;
                    double testX = (maxObs - a) / b;
                    while ((maxTran + (daysCnt * lastAvg)) < testX)
                        daysCnt++;

                    if (daysCnt <= 0)
                        daysToCompleteMessage = "Test Complete - Accept";
                    else
                        daysToCompleteMessage = "Days To Complete = " + daysCnt.ToString();
                }
            }
        }

        // Show trend line
        if (pointCnt > 0 && maxObs > 0 && maxTran > 0 && repInfo.PRSTInfo.IncludeTrendline)
        {
            SixSigma.AddLineAnnotation(axisX, axisY, 0, 0, trendX, trendY, Color.Black, chart);
        }

        chart.Titles["Title1"].Text = repInfo.ChartTitle;

        if (string.IsNullOrEmpty(axisY.Title))
        {
            axisY.Title = "Observations";
            axisY.TitleFont = new Font("Arial", 10, FontStyle.Bold);
        }

        axisX.Title = "Cumulative " + xAxisLabelPart;
        axisX.TitleFont = new Font("Arial", 10, FontStyle.Bold);

        // Add text annotations
        string dateRangeStr = null;
        if (DateTime.MinValue.Equals(repInfo.StartDate))
            if (DateTime.MinValue.Equals(repInfo.EndDate))
                dateRangeStr = "Data through " + DateTime.Today.ToString("MM/dd/yyyy");
            else
                dateRangeStr = "Data through " + repInfo.EndDate.ToString("MM/dd/yyyy");
        else
            if (DateTime.MinValue.Equals(repInfo.EndDate))
                dateRangeStr = "From " + repInfo.StartDate.ToString("MM/dd/yyyy") + " to " + DateTime.Today.ToString("MM/dd/yyyy");
            else
                dateRangeStr = "From " + repInfo.StartDate.ToString("MM/dd/yyyy") + " to " + repInfo.EndDate.ToString("MM/dd/yyyy");

        if (maxObsOverall > maxObs)
        {
            int diff = (int)(maxObsOverall - maxObs);
            SixSigma.AddTextAnnotation(string.Format("{0} observation{1} censored", diff, (diff > 1 ? "s were" : " was")), axisX, axisY,
                axisX.Maximum - (axisX.Maximum / 4),
                axisY.Minimum - (axisY.Maximum / 6),
                Color.Red, new Font("Arial", 8, FontStyle.Regular), chart);
        }

        StringBuilder annotationText = new StringBuilder();
        annotationText.AppendLine(daysToCompleteMessage);
        annotationText.AppendLine(dateRangeStr);
        annotationText.AppendLine("MTBF: " + repInfo.PRSTInfo.MTBFSpec.ToString("#,#"));
        annotationText.AppendLine(xAxisLabelPart + ": " + maxTran.ToString("#,#"));

        SixSigma.AddTextAnnotation(annotationText.ToString(), axisX, axisY,
            axisX.Minimum - (axisX.Maximum / 8),
            axisY.Minimum - (axisY.Maximum / 8),
            Color.Black, new Font("Arial", 8, FontStyle.Regular), chart);

        SetChartTitle(chart, "SpaceHolder", " ", Docking.Bottom, ContentAlignment.MiddleCenter, new Font("Arial", 8, FontStyle.Regular));
    }

}
