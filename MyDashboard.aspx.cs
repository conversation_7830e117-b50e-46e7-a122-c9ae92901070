using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Text;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class MyDashboard : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			bool displayPersonalizedDashboard = true;
			SqlHelper.ExecuteNonQuery("RPT_UpdateUserDashboardActivity", Utility.GetUserName(), DateTime.Now, displayPersonalizedDashboard);
		}
	}
}
