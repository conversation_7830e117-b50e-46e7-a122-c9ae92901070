<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="RunShiftReport.aspx.cs" Inherits="RunShiftReport" %>
<%@ Import Namespace="Microsoft.AnalysisServices.AdomdClient" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
	
<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="InitialLoadingPanel" runat="server" visible="true">
	<div style="padding-top:50px;"><center><img src="images/loading.gif" alt="Loading Report..." border="0" /></center></div>
</asp:panel>
<asp:panel id="MainPanel" runat="server" visible="false">
	<telerik:radwindowmanager runat="server" ID="RadWindowManager1" height="680" width="800" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="SaveReportWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" NavigateUrl="savereport.aspx" Height="600" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="EditWindow" VisibleOnPageLoad="false" OffsetElementID="offsetElement" Top="30" Left="30" Height="680" Width="800" ></telerik:radwindow>
			<telerik:radwindow runat="server" ID="PromptSessionsWindow" VisibleOnPageLoad="false" NavigateUrl="promptsessions.aspx" Height="400" Width="600" ></telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>
				
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0"> 
					<tr>
						<td class="widgetTitle">Run Report</td>
						<td class="widgetTop" style="width:155px;">&nbsp;</td>
						<td class="widgetTop" style="width:260px;">
							<table cellpadding="0" cellspacing="0" border="0">
								<tr>
									<td style="width:55px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="printButton" onclick="PrintButton_Click">Print</asp:linkbutton></div></td>
									<td style="width:10px;">&nbsp;</td>
									<td style="width:55px;"><div class="goButtonTop"><asp:linkbutton runat="server" id="viewTranButton" onclick="ViewTranButton_Click">Transactions</asp:linkbutton></div></td>
								</tr>
							</table>
						</td>
						<td class="widgetTop">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<table width="700px" border="0" cellpadding="0" cellspacing="10" runat="server" id="SnapshotTable" visible="false">
						<tr>
							<td class="rowHeading" style="width:140px;">Snapshot Name:</td>
							<td><asp:label id="SnapshotName" runat="server"></asp:label></td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:140px;">Snapshot Date:</td>
							<td><asp:label id="SnapshotDate" runat="server"></asp:label></td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:140px;">Description:</td>
							<td><asp:label id="SnapshotDesc" runat="server"></asp:label></td>
						</tr>
					</table>
					<table width="700px" style="padding-top:10px;" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="title"></td>
							<td>&nbsp;</td>
							<td style="width:90px;text-align:right;padding-right:10px;"><div class="goButton" id="EditBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="EditButton_Click" id="editButton">Edit Report</asp:linkbutton></div></td>
							<td style="width:95px;text-align:right;padding-right:10px;"><div class="goButton" id="SaveBtnDiv" runat="server"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save Report</asp:linkbutton></div></td>
						</tr>
					</table>
				</div>
				
				<div class="widget" style="padding-bottom:20px; text-align:center;">
		            <table width="100%" border="0" cellpadding="0" cellspacing="10">
						<tr>
							<td>
								<!-- Title Section -->
								<div style="text-align:center;">
									<asp:label id="reportNameLabel" style="font-size:22px; font-weight:bold;" runat="server"></asp:label>
									<br />
									<span style="font-size:14px; font-weight:bold;">Shift Summary&nbsp;</span><span style="font-size:8px; color:red; font-weight:bold;">(red indicates one failure assumed)</span>
									<br />
									<asp:label id="dateLbl" style="font-size:13px;" runat="server"></asp:label>
									<asp:label id="firmwareLbl" style="font-size:13px;" runat="server"></asp:label>
								</div>
								<!-- END Title Section -->
							</td>
						</tr>
					</table>
					
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td>
								<asp:Panel ID="GridBindingPanel" runat="server">
									<table border="1" cellpadding="0" cellspacing="1" width="100%">
										<asp:Repeater ID="ColumnHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders %>'>
											<ItemTemplate>
												<tr>
													<asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
														<ItemTemplate>
															<td><%# "&nbsp;" %></td>
														</ItemTemplate>
													</asp:Repeater>
													<asp:Repeater ID="ColumnHeaderRepeater" runat="server" DataSource='<%# this.ReportObj.ColumnDisplayHeaders[ColumnHeaderLevelRepeater.Items.Count] %>'>
														<ItemTemplate>
															<td style="padding:4px; font-weight:bold;"><%# Container.DataItem %></td>
														</ItemTemplate>
													</asp:Repeater>
												</tr>
											</ItemTemplate>
										</asp:Repeater>
										<asp:Repeater ID="GridRowsRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayData %>'>
											<ItemTemplate>
												<tr>
													<asp:Repeater ID="RowHeaderLevelRepeater" runat="server" DataSource='<%# this.ReportObj.RowDisplayHeaders %>'>
														<ItemTemplate>
															<td><%# ((System.Collections.Generic.List<string>)Container.DataItem)[GridRowsRepeater.Items.Count]%>&nbsp;</td>
														</ItemTemplate>
													</asp:Repeater>
													<asp:Repeater ID="ColumnDataRepeater" runat="server" DataSource='<%# ((System.Collections.Generic.List<string>)Container.DataItem) %>'>
														<ItemTemplate>
	                                                        <td <%# (((string)Container.DataItem) != null && ((string)Container.DataItem).StartsWith("~")) ? "style='color:red;'" : "" %>>
                                                                <%# (string.IsNullOrEmpty((string)Container.DataItem) ? "&nbsp;" : ((string)Container.DataItem).Replace("~", "")) %>
	                                                        </td>
														</ItemTemplate>
													</asp:Repeater>
												</tr>
											</ItemTemplate>
											<footertemplate>
											</footertemplate>
										</asp:Repeater>
									</table> 
								</asp:Panel>
							</td>
						</tr>
						<tr>
							<td>
								<asp:repeater id="lowerRep" runat="server">
									<headertemplate>
										<table width="100%" cellpadding="0" cellspacing="0" border="1" class="shiftReportItem">
									</headertemplate>
									<itemtemplate>
											<%# FormatReportSeperator(Container.DataItem)%>
											<tr>
												<td><%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "DeviceFullName")) ? "&nbsp;" : DataFormatter.Format(Container.DataItem, "DeviceFullName") %></td>
												<td><%# string.IsNullOrEmpty(DataFormatter.FormatDate(Container.DataItem, "TranDate", "MMM d, yyyy h:mmtt", "")) ? "&nbsp;" : DataFormatter.FormatDate(Container.DataItem, "TranDate", "MMM d, yyyy h:mmtt", "") %></td>
												<td><%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "RDToolTranId")) ? "&nbsp;" : DataFormatter.Format(Container.DataItem, "RDToolTranId")%></td>
												<td><%# string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "ObservationText")) ? "&nbsp;" : DataFormatter.Format(Container.DataItem, "ObservationText")%></td>
											</tr>
									</itemtemplate>
									<footertemplate>
										</table>
									</footertemplate>
								</asp:repeater>
							</td>
						</tr>
					</table>	
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

