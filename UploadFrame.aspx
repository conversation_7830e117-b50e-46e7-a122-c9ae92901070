﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="UploadFrame.aspx.cs" Inherits="UploadFrame" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Upload Image or PDF</title>
</head>
<body>
    <form id="form1" runat="server">
    <div style="font-size:11px;">
		<asp:fileupload id="fileUploadCntrl" width="220" runat="server" />
		<asp:button id="upload" text="Upload" height="20" runat="server" onclick="UploadBtn_Click" />
		<br />
		<asp:requiredfieldvalidator id="v" runat="server" controltovalidate="fileUploadCntrl" display="Dynamic" errormessage="*Please select a file to upload."></asp:requiredfieldvalidator>
		<asp:label id="successLbl" visible="false" runat="server">Your image has been uploaded successfully. Click the save button below to view.</asp:label>
    </div>
    </form>
</body>
</html>
