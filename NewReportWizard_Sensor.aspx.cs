using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class NewReportWizard_Sensor : System.Web.UI.Page
{
    private string[] SupressedDimensions = new string[] { "Agilis", "Event Type", "Failure Location", "Failure Type", "Investigation Area",
		"Operator", "Severity", "Statistic", "Cell", "Session", "Measures", "Time", "Test Session", "Censoring", "Test Location", "Device",
        "Metric", "Metric Distribution", "Metric Values", "Engineering Field", "Engineering Distribution", "Engineering Values", "Engineering Note Number", "Engineering Index Number", 
		"Distribution", "Distribution Values", "Setting", "Setting 2", "Setting 3", "File Number", "Media Number", "Command Number" };

    public ReportInfo RepInfo
    {
        get { return (ReportInfo)this.ViewState["r"]; }
        set { this.ViewState["r"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            this.RepInfo = Utility.GetReportInfoFromTransfer();
            if (this.RepInfo == null)
                Response.Redirect("popuperror.aspx");

            reportLabel.Text = this.RepInfo.ReportName;
            reportTypeLabel.Text = this.RepInfo.ReportTypeName;
            subReportDimCheck.Checked = this.RepInfo.SubReportsByDimension;

            groupingList.DataSource = Utility.GetDimensionGroupByList();
            groupingList.DataBind();

            if (this.RepInfo.ParetoInfo.GroupingId != 0)
                groupingList.SelectedValue = ((int)this.RepInfo.ParetoInfo.GroupingId).ToString();
            else
                groupingList.SelectedIndex = 1;

            this.dimensionSel.PopulateDimensions(false, SupressedDimensions);
            this.dimensionSel.DimensionName = this.RepInfo.DimensionName;
            this.dimensionSel.CheckedDimensionMembers = this.RepInfo.DimensionMembers;
            this.dimensionSel.UpdateTreeDisplay();

            dimensionSel.Focus();
        }
    }

    protected void NextButton_Click(object sender, EventArgs e)
    {
        if (Page.IsValid)
        {
			//Set Page Values
			this.RepInfo.DimensionName = dimensionSel.DimensionName;
			this.RepInfo.DimensionMembers = dimensionSel.CheckedDimensionMembers;
            this.RepInfo.SubReportsByDimension = subReportDimCheck.Checked;

			if (!string.IsNullOrEmpty(groupingList.SelectedValue))
				this.RepInfo.ParetoInfo.GroupingId = (ReportHelper.DateGroupingEnum) Convert.ToInt32(groupingList.SelectedValue);

            Utility.SetReportInfoForTransfer(this.RepInfo);
            Response.Redirect("NewReportWizard_Format.aspx");
        }
    }
}
