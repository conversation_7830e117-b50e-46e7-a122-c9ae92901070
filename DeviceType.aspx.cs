using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class DeviceType : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		bool includeInactive = true;
		DeviceTypeGrid.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_DeviceType", includeInactive);
		DeviceTypeGrid.DataBind();
	}
}
