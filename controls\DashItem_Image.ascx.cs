using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using System.IO;

public partial class controls_DashItem_Image : System.Web.UI.UserControl, IDashboardItem
{
    public bool UseMasterUser
    {
        get { if (this.ViewState["um"] != null) return (bool)this.ViewState["um"]; else return false; }
        set { this.ViewState["um"] = value; }
    }
    
    DashImageInfo dashInfo = null;
	public DashImageInfo DashInfo
	{
		get
		{
			if (dashInfo != null) { return dashInfo; }
			else
			{
				dashInfo = new DashImageInfo();
                string ctrlXML = null;

                if (this.UseMasterUser)
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME));
                else
                    ctrlXML = Convert.ToString(SqlHelper.ExecuteScalar("RPT_LoadDashboardItem", this.UniqueControlGuid, this.UserName));
                
				if (!string.IsNullOrEmpty(ctrlXML))
					dashInfo = (DashImageInfo)XmlSerializable.LoadFromXmlText(typeof(DashImageInfo), ctrlXML);

				return dashInfo;
			}
		}
		set { dashInfo = value; }
	}

	private string UserName = Utility.GetUserName();
	protected Guid UniqueControlGuid = Guid.Empty;
	private string ImageDir = ConfigurationManager.AppSettings["DashboardImagesPath"];
	
	protected void Page_Load(object sender, EventArgs e)
	{
	}

	public void Initialize(Guid dockUniqueName, bool useMasterUser)
	{
		this.UseMasterUser = useMasterUser;
		this.UniqueControlGuid = dockUniqueName;
	}
	
	public void DisplayContent()
	{
		LoadImage();
	}

	private void LoadImage()
    {
		if (!string.IsNullOrEmpty(this.DashInfo.FileName))
		{
			//link to actual PDF file if they uploaded one, rather than to the thumbnail image
			FileInfo file = new FileInfo(Server.MapPath(this.DashInfo.FileName));
			string pdfFileName = file.Name.Substring(0, file.Name.Length - file.Extension.Length) + ".pdf";
			FileInfo pdfFile = new FileInfo(Server.MapPath(this.ImageDir + pdfFileName));
			if (pdfFile.Exists)
				imgLabel.Text = string.Format("<a href=\"{0}{1}\" target=\"_blank\"><img src=\"{0}{2}\" width=\"360\" alt=\"{2}\" border=\"0\" /></a>", this.ImageDir, pdfFileName, this.DashInfo.FileName, this.DashInfo.FileDescription);
			else
				imgLabel.Text = string.Format("<a href=\"{0}{1}\" target=\"_blank\"><img src=\"{0}{1}\" width=\"360\" alt=\"{2}\" border=\"0\" /></a>", this.ImageDir, this.DashInfo.FileName, this.DashInfo.FileDescription);
		}
		else
		{
			EditSettings();
		}
		
		if (!string.IsNullOrEmpty(this.DashInfo.FileDescription))
			this.imgDescription.Text = this.DashInfo.FileDescription;
    }

	public void EditSettings()
	{
		if (this.DashInfo != null)
			this.imgDescriptionBox.Text = this.DashInfo.FileDescription;
		
		SettingsPanel.Visible = true;
		DisplayPanel.Visible = false;
	}

	public void SaveSettings()
	{
		if (Session["uploadedfile"] != null && !string.IsNullOrEmpty(Session["uploadedfile"].ToString()))
		{
			if (!string.IsNullOrEmpty(this.DashInfo.FileName) && !this.DashInfo.FileName.Equals(Session["uploadedfile"].ToString()))
				DeletePreviousFile(this.DashInfo.FileName);
			
			this.DashInfo.FileName = Session["uploadedfile"].ToString();
		}

		if (!string.IsNullOrEmpty(this.DashInfo.FileName))
		{
			SettingsPanel.Visible = false;
			DisplayPanel.Visible = true;
			MissingImgLbl.Visible = false;
		}
		else
		{
			MissingImgLbl.Visible = true;
		}

		this.DashInfo.FileDescription = imgDescriptionBox.Text.Trim();

		if (this.UseMasterUser)
			SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, DieboldConstants.DASHBOARD_MASTER_NAME, XmlSerializable.ConvertToXml(this.DashInfo));
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateDashboardItem", this.UniqueControlGuid, UserName, XmlSerializable.ConvertToXml(this.DashInfo));

		this.Session["ImageWidget:" + UniqueControlGuid.ToString("N")] = null;
		this.Session["uploadedfile"] = null;

		LoadImage();
	}

	public void DisplayEdit()
	{
		RadAjaxManager manager = RadAjaxManager.GetCurrent(this.Parent.Page);
		if (manager != null)
			manager.ResponseScripts.Add(string.Format("document.getElementById('{0}').click();", this.HidEditButton.ClientID));
	}

	protected void SaveSettings_Click(object sender, EventArgs e)
	{
		SaveSettings();
	}

	protected void EditSettings_Click(object sender, EventArgs e)
	{
		EditSettings();
	}

	protected void CancelSettings_Click(object sender, EventArgs e)
	{
		if (Session["uploadedfile"] != null && !string.IsNullOrEmpty(Session["uploadedfile"].ToString()))
		{
			DeletePreviousFile(Session["uploadedfile"].ToString());
			this.Session["uploadedfile"] = null;
		}

		SettingsPanel.Visible = false;
		DisplayPanel.Visible = true;
	}

	private void DeletePreviousFile(string fileName)
	{
		string fullName = Server.MapPath(this.ImageDir) + fileName;

		if (File.Exists(fullName))
			File.Delete(fullName);
	}
}
