using System;
using System.Data;
using System.Collections.Generic;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Drawing;
/// <summary>
/// Summary description for PearsonCurves
/// </summary>
public class PearsonCurves
{
    double[] SkewnessKey = { 0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0 };
    double[] <PERSON><PERSON><PERSON><PERSON> = { -1.4, -1.2, -1.0, -0.8, -0.6, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0, 3.2, 3.4, 3.6, 3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0, 5.2, 5.4, 5.6, 5.8, 6.0, 6.2, 6.4, 6.6, 6.8, 7.0, 7.2, 7.4, 7.6, 7.8, 8.0, 8.2, 8.4, 8.6, 8.8, 9.0, 9.2, 9.4, 9.6, 9.8, 10.0, 10.2, 10.4, 10.6, 10.8, 11.0, 11.2, 11.4, 11.6, 11.8, 12.0, 12.2 };

    List<double[]> Table1A = null;
    List<double[]> Table1B = null;
    List<double[]> Table2 = null;

    public double LookupLp(double kurtosis, double skewness)
    {
        if (skewness >= 0)
            return FindInterpolatedValue(Table1A, kurtosis, skewness);
        else
            return FindInterpolatedValue(Table1B, kurtosis, Math.Abs(skewness));
    }

    public double LookupUp(double kurtosis, double skewness)
    {
        if (skewness >= 0)
            return FindInterpolatedValue(Table1B, kurtosis, skewness);
        else
            return FindInterpolatedValue(Table1A, kurtosis, Math.Abs(skewness));
    }

    public double LookupM(double kurtosis, double skewness)
    {
        if (skewness >= 0)
            return (0-FindInterpolatedValue(Table2, kurtosis, skewness));
        else
            return FindInterpolatedValue(Table2, kurtosis, Math.Abs(skewness));
    }

    private double FindInterpolatedValue(List<double[]> lookupTable, double kurtosis, double skewness)
    {
        int skewLowIdx = FindLowKeyIndex(SkewnessKey, skewness);
        int kurtLowIdx = FindLowKeyIndex(KurtosisKey, kurtosis);
        int skewHighIdx = FindHighKeyIndex(SkewnessKey, skewness);
        int kurtHighIdx = FindHighKeyIndex(KurtosisKey, kurtosis);

        if (skewLowIdx == -1 || kurtLowIdx == -1 || skewHighIdx == -1 || kurtHighIdx == -1)
            return double.MinValue;

        double lowSkew = SkewnessKey[skewLowIdx];
        double lowKurt = KurtosisKey[kurtLowIdx];

        double highSkew = SkewnessKey[skewHighIdx];
        double highKurt = KurtosisKey[kurtHighIdx];

        double lowlowValue = lookupTable[kurtLowIdx][skewLowIdx];
        double lowhighValue = lookupTable[kurtLowIdx][skewHighIdx];
        double highlowValue = lookupTable[kurtHighIdx][skewLowIdx];
        double highhighValue = lookupTable[kurtHighIdx][skewHighIdx];

        double firstInterpolation = Interpolate(lowSkew, highSkew, skewness, lowlowValue, lowhighValue);
        double secondInterpolation = Interpolate(lowSkew, highSkew, skewness, highlowValue, highhighValue);

        double thirdInterpolation = Interpolate(lowKurt, highKurt, kurtosis, firstInterpolation, secondInterpolation);

        return thirdInterpolation;
    }

    private double Interpolate(double lowKey, double highKey, double targetKey, double lowResult, double highResult)
    {
		if (lowResult == highResult)
			return lowResult;
		else
			return Math.Round(((highKey - targetKey) / (highKey - lowKey) * lowResult) + ((targetKey - lowKey) / (highKey - lowKey) * highResult), 3);
    }

    private int FindLowKeyIndex(double[] lookupKey, double targetValue)
    {
        int retVal = -1;
        for (int x = 0; x < lookupKey.Length; x++)
        {
            if (lookupKey[x] <= targetValue)
                retVal = x;
            else
                break;
        }
        return retVal;
    }

    private int FindHighKeyIndex(double[] lookupKey, double targetValue)
    {
        int retVal = -1;
        for (int x = (lookupKey.Length - 1); x >= 0; x--)
        {
            if (lookupKey[x] >= targetValue)
                retVal = x;
            else
                break;
        }
        return retVal;
    }

    public PearsonCurves()
    {
        Table1A = new List<double[]>();
        Table1A.Add(new double[] { 1.512, 1.421, 1.317, 1.206, 1.092, 0.979, 0.868, 0.762, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 1.727, 1.619, 1.496, 1.364, 1.230, 1.100, 0.975, 0.858, 0.747, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 1.966, 1.840, 1.696, 1.541, 1.384, 1.232, 1.089, 0.957, 0.836, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 2.210, 2.072, 1.912, 1.736, 1.555, 1.377, 1.212, 1.062, 0.927, 0.804, 0.692, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 2.442, 2.298, 2.129, 1.941, 1.740, 1.539, 1.348, 1.175, 1.023, 0.887, 0.766, 0.658, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 2.653, 2.508, 2.335, 2.141, 1.930, 1.711, 1.496, 1.299, 1.125, 0.974, 0.841, 0.723, 0.616, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 2.839, 2.692, 2.522, 2.329, 2.116, 1.887, 1.655, 1.434, 1.235, 1.065, 0.919, 0.791, 0.677, 0.574, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.000, 2.856, 2.689, 2.500, 2.289, 2.059, 1.817, 1.578, 1.356, 1.163, 1.000, 0.861, 0.739, 0.630, 0.531, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.140, 2.986, 2.834, 2.653, 2.447, 2.220, 1.978, 1.726, 1.485, 1.269, 1.086, 0.933, 0.801, 0.686, 0.583, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.261, 3.088, 2.952, 2.785, 2.589, 2.368, 2.127, 1.873, 1.619, 1.382, 1.178, 1.008, 0.865, 0.742, 0.634, 0.536, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.366, 3.164, 3.045, 2.896, 2.714, 2.502, 2.267, 2.015, 1.754, 1.502, 1.277, 1.087, 0.931, 0.799, 0.685, 0.583, 0.489, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.458, 3.222, 3.118, 2.986, 2.821, 2.622, 2.396, 2.148, 1.887, 1.625, 1.381, 1.172, 1.000, 0.857, 0.736, 0.629, 0.533, 0.0, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.539, 3.266, 3.174, 3.058, 2.910, 2.727, 2.512, 2.271, 2.013, 1.748, 1.491, 1.262, 1.072, 0.917, 0.787, 0.675, 0.575, 0.484, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.611, 3.300, 3.218, 3.115, 2.983, 2.817, 2.616, 2.385, 2.132, 1.876, 1.602, 1.357, 1.149, 0.979, 0.840, 0.721, 0.617, 0.524, 0.0, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.674, 3.327, 3.254, 3.161, 3.043, 2.893, 2.708, 2.488, 2.243, 1.981, 1.713, 1.456, 1.230, 1.045, 0.894, 0.768, 0.659, 0.562, 0.475, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.731, 3.349, 3.282, 3.199, 3.092, 2.957, 2.787, 2.581, 2.345, 2.089, 1.821, 1.556, 1.316, 1.113, 0.950, 0.815, 0.701, 0.600, 0.510, 0.0, 0.0 });
        Table1A.Add(new double[] { 3.782, 3.367, 3.306, 3.229, 3.133, 3.011, 2.856, 2.664, 2.438, 2.189, 1.925, 1.664, 1.404, 1.185, 1.008, 0.863, 0.743, 0.638, 0.546, 0.461, 0.0 });
        Table1A.Add(new double[] { 3.828, 3.382, 3.325, 3.255, 3.167, 3.055, 2.914, 2.736, 2.524, 2.283, 2.023, 1.755, 1.494, 1.261, 1.068, 0.913, 0.785, 0.676, 0.580, 0.494, 0.0 });
        Table1A.Add(new double[] { 3.870, 3.395, 3.342, 3.277, 3.196, 3.093, 2.964, 2.800, 2.600, 2.369, 2.116, 1.850, 1.584, 1.339, 1.132, 0.964, 0.828, 0.714, 0.615, 0.526, 0.445 });
        Table1A.Add(new double[] { 3.908, 3.405, 3.356, 3.295, 3.220, 3.126, 3.006, 2.865, 2.669, 2.448, 2.202, 1.940, 1.673, 1.420, 1.198, 1.018, 0.873, 0.752, 0.649, 0.557, 0.475 });
        Table1A.Add(new double[] { 3.943, 3.415, 3.367, 3.311, 3.241, 3.153, 3.043, 2.904, 2.730, 2.521, 2.283, 2.026, 1.760, 1.501, 1.267, 1.073, 0.918, 0.791, 0.683, 0.589, 0.504 });
        Table1A.Add(new double[] { 3.975, 3.423, 3.378, 3.324, 3.259, 3.177, 3.075, 2.946, 2.784, 2.588, 2.358, 2.107, 1.844, 1.581, 1.338, 1.131, 0.965, 0.830, 0.717, 0.620, 0.533 });
        Table1A.Add(new double[] { 4.004, 3.430, 3.387, 3.326, 3.274, 3.198, 3.103, 2.983, 2.831, 2.646, 2.427, 2.183, 1.924, 1.661, 1.410, 1.191, 1.013, 0.870, 0.752, 0.651, 0.562 });
        Table1A.Add(new double[] { 4.031, 3.436, 3.395, 3.346, 3.288, 3.216, 3.127, 3.015, 2.874, 2.699, 2.491, 2.254, 2.000, 1.738, 1.483, 1.253, 1.063, 0.911, 0.787, 0.681, 0.590 });
        Table1A.Add(new double[] { 4.056, 3.441, 3.402, 3.356, 3.300, 3.233, 3.149, 3.043, 2.911, 2.747, 2.649, 2.321, 2.072, 1.813, 1.555, 1.317, 1.115, 0.953, 0.822, 0.712, 0.618 });
        Table1A.Add(new double[] { 4.079, 3.446, 3.408, 3.364, 3.311, 3.247, 3.168, 3.069, 2.945, 2.790, 2.602, 2.383, 2.140, 1.884, 1.626, 1.381, 1.169, 0.998, 0.858, 0.744, 0.646 });
        Table1A.Add(new double[] { 4.101, 3.450, 3.414, 3.371, 3.321, 3.259, 3.184, 3.091, 2.974, 2.829, 2.651, 2.440, 2.205, 1.953, 1.695, 1.446, 1.224, 1.041, 0.895, 0.775, 0.674 });
        Table1A.Add(new double[] { 4.121, 3.454, 3.419, 3.378, 3.329, 3.271, 3.200, 3.111, 3.001, 2.864, 2.695, 2.494, 2.265, 2.018, 1.762, 1.510, 1.281, 1.088, 0.932, 0.807, 0.702 });
        Table1A.Add(new double[] { 4.140, 3.458, 3.423, 3.384, 3.337, 3.281, 3.213, 3.129, 3.025, 2.895, 2.735, 2.543, 2.321, 2.080, 1.827, 1.574, 1.338, 1.135, 0.971, 0.839, 0.730 });
        Table1A.Add(new double[] { 4.157, 3.461, 3.428, 3.389, 3.344, 3.290, 3.225, 3.145, 3.047, 2.923, 2.771, 2.588, 2.374, 2.138, 1.889, 1.636, 1.396, 1.184, 1.011, 0.872, 0.758 });
        Table1A.Add(new double[] { 4.174, 3.464, 3.431, 3.394, 3.350, 3.299, 3.236, 3.460, 3.066, 2.949, 2.805, 2.629, 2.424, 2.194, 1.948, 1.697, 1.453, 1.234, 1.052, 0.905, 0.786 });
        Table1A.Add(new double[] { 4.189, 3.466, 3.435, 3.399, 3.358, 3.306, 3.246, 3.173, 3.084, 2.972, 2.835, 2.668, 2.470, 2.246, 2.005, 1.756, 1.510, 1.285, 1.094, 0.939, 0.815 });
        Table1A.Add(new double[] { 4.204, 3.469, 3.438, 3.403, 3.362, 3.313, 3.256, 3.186, 3.100, 2.994, 2.863, 2.703, 2.513, 2.296, 2.059, 1.813, 1.566, 1.336, 1.137, 0.975, 0.844 });
        Table1A.Add(new double[] { 4.218, 3.471, 3.441, 3.406, 3.367, 3.320, 3.264, 3.197, 3.114, 3.013, 2.888, 2.735, 2.562, 2.342, 2.111, 1.867, 1.621, 1.387, 1.181, 1.010, 0.874 });
        Table1A.Add(new double[] { 4.231, 3.473, 3.444, 3.410, 3.371, 3.326, 3.272, 3.207, 3.128, 3.031, 2.911, 2.765, 2.589, 2.386, 2.160, 1.920, 1.675, 1.438, 1.225, 1.047, 0.904 });
        Table1A.Add(new double[] { 4.243, 3.475, 3.446, 3.413, 3.375, 3.331, 3.279, 3.216, 3.140, 3.047, 2.933, 2.793, 2.624, 2.427, 2.206, 1.970, 1.727, 1.489, 1.270, 1.085, 0.935 });
        Table1A.Add(new double[] { 4.255, 3.477, 3.448, 3.416, 3.379, 3.336, 3.288, 3.225, 3.152, 3.062, 2.952, 2.818, 2.656, 2.465, 2.260, 2.019, 1.778, 1.539, 1.316, 1.123, 0.966 });
        Table1A.Add(new double[] { 4.266, 3.478, 3.451, 3.419, 3.383, 3.341, 3.292, 3.233, 3.162, 3.076, 2.970, 2.841, 2.685, 2.501, 20292, 2.065, 1.827, 1.588, 1.361, 1.162, 0.999 });
        Table1A.Add(new double[] { 4.276, 3.480, 3.453, 3.422, 3.386, 3.345, 3.297, 3.240, 3.172, 3.089, 2.987, 2.863, 2.713, 2.535, 2.332, 2.109, 1.874, 1.635, 1.407, 1.202, 1.031 });
        Table1A.Add(new double[] { 4.286, 3.481, 3.454, 3.424, 3.389, 3.349, 3.303, 3.247, 3.181, 3.100, 3.003, 2.883, 2.739, 2.567, 2.369, 2.151, 1.919, 1.682, 1.452, 1.242, 1.065 });
        Table1A.Add(new double[] { 4.296, 3.483, 3.456, 3.426, 3.392, 3.353, 3.308, 3.254, 3.189, 3.111, 3.017, 2.902, 2.763, 2.597, 2.405, 2.191, 1.962, 1.727, 1.496, 1.282, 1.099 });
        Table1A.Add(new double[] { 4.305, 3.484, 3.458, 3.429, 3.395, 3.357, 3.312, 3.260, 3.197, 3.122, 3.030, 2.919, 2.785, 2.624, 2.438, 2.229, 2.004, 1.771, 1.540, 1.323, 1.134 });
        Table1A.Add(new double[] { 4.313, 3.485, 3.459, 3.431, 3.398, 3.360, 3.316, 3.265, 3.204, 3.131, 3.043, 2.936, 2.806, 2.651, 2.469, 2.265, 2.044, 1.814, 1.583, 1.363, 1.169 });
        Table1A.Add(new double[] { 4.322, 3.486, 3.461, 3.432, 3.400, 3.363, 3.321, 3.270, 3.211, 3.140, 3.054, 2.951, 2.825, 2.675, 2.499, 2.300, 2.083, 1.855, 1.625, 1.403, 1.204 });
        Table1A.Add(new double[] { 4.330, 3.487, 3.462, 3.434, 3.403, 3.366, 3.324, 3.275, 3.218, 3.148, 3.065, 2.965, 2.843, 2.698, 2.527, 2.333, 2.120, 1.895, 1.666, 1.443, 1.240 });
        Table1A.Add(new double[] { 4.337, 3.488, 3.464, 3.436, 3.405, 3.369, 3.328, 3.280, 3.224, 3.156, 3.075, 2.978, 2.860, 2.720, 2.554, 2.364, 2.155, 1.933, 1.706, 1.482, 1.276 });
        Table1A.Add(new double[] { 4.344, 3.489, 3.465, 3.437, 3.407, 3.372, 3.331, 3.084, 3.229, 3.164, 3.085, 2.990, 2.876, 2.740, 2.579, 2.394, 2.189, 1.970, 1.744, 1.521, 1.311 });
        Table1A.Add(new double[] { 4.351, 3.490, 3.466, 3.439, 3.409, 3.374, 3.335, 3.289, 3.235, 3.171, 3.094, 3.002, 2.891, 2.759, 2.603, 2.422, 2.221, 2.005, 1.782, 1.559, 1.347 });
        Table1A.Add(new double[] { 4.358, 3.491, 3.467, 3.440, 3.411, 3.377, 3.338, 3.292, 3.240, 3.177, 3.103, 3.013, 2.906, 2.777, 2.625, 2.449, 2.252, 2.040, 1.818, 1.596, 1.382 });
        Table1A.Add(new double[] { 4.365, 3.492, 3.468, 3.442, 3.412, 3.379, 3.340, 3.296, 3.244, 3.183, 3.111, 3.023, 2.919, 2.794, 2.646, 2.475, 2.282, 2.073, 1.854, 1.632, 1.418 });
        Table1A.Add(new double[] { 4.371, 3.492, 3.469, 3.443, 3.414, 3.381, 3.343, 3.300, 3.249, 3.189, 3.118, 3.033, 2.932, 2.810, 2.666, 2.499, 2.310, 2.104, 1.888, 1.667, 1.452 });
        Table1A.Add(new double[] { 4.377, 3.493, 3.470, 3.444, 3.416, 3.383, 3.346, 3.303, 3.253, 3.195, 3.125, 3.042, 2.943, 2.825, 2.685, 2.522, 2.337, 2.135, 1.921, 1.702, 1.486 });
        Table1A.Add(new double[] { 4.382, 3.494, 3.471, 3.445, 3.417, 3.385, 3.348, 3.306, 3.257, 3.200, 3.132, 3.051, 2.955, 2.839, 2.703, 2.544, 2.363, 2.164, 1.953, 1.736, 1.520 });
        Table1A.Add(new double[] { 4.388, 3.495, 3.472, 3.447, 3.418, 3.387, 3.351, 3.309, 3.261, 3.205, 3.138, 3.059, 2.985, 2.853, 2.720, 2.565, 2.388, 2.192, 1.984, 1.768, 1.553 });
        Table1A.Add(new double[] { 4.393, 3.495, 3.473, 3.448, 3.420, 3.388, 3.353, 3.312, 3.265, 3.209, 3.144, 3.067, 2.975, 2.866, 23736, 2.585, 2.411, 2.219, 2.014, 1.800, 1.586 });
        Table1A.Add(new double[] { 4.398, 3.496, 3.473, 3.449, 3.421, 3.390, 3.355, 3.315, 3.268, 3.214, 3.150, 3.075, 2.985, 2.878, 2.752, 2.604, 2.434, 2.245, 2.042, 1.831, 1.617 });
        Table1A.Add(new double[] { 4.403, 3.496, 3.474, 3.450, 3.422, 3.392, 3.357, 3.317, 3.272, 3.218, 3.156, 3.082, 2.994, 2.890, 2.766, 2.622, 2.456, 2.271, 2.070, 1.861, 1.648 });
        Table1A.Add(new double[] { 4.408, 3.497, 3.475, 3.451, 3.424, 3.393, 3.359, 3.320, 3.275, 3.222, 3.161, 3.088, 3.003, 2.901, 2.780, 2.639, 2.476, 2.295, 2.097, 1.890, 1.679 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 3.425, 3.395, 3.361, 3.322, 3.278, 3.226, 3.166, 3.095, 3.011, 2.911, 2.793, 2.655, 2.496, 2.318, 2.123, 1.918, 1.708 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 3.396, 3.363, 3.325, 3.281, 3.230, 3.171, 3.101, 3.019, 2.921, 2.806, 2.671, 2.515, 2.340, 2.148, 1.945, 1.737 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.364, 3.327, 3.283, 3.233, 3.175, 3.107, 3.026, 2.930, 2.818, 2.686, 2.533, 2.361, 2.172, 1.972, 1.765 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.329, 3.286, 3.237, 3.179, 3.112, 3.033, 2.940, 2.829, 2.700, 2.551, 2.382, 2.196, 1.998, 1.793 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.289, 3.240, 3.184, 3.118, 3.040, 2.948, 2.840, 2.714, 2.567, 2.401, 2.218, 2.023, 1.819 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.243, 3.188, 3.123, 3.046, 2.956, 2.851, 2.727, 2.583, 2.420, 2.240, 2.047, 1.845 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.191, 3.128, 3.053, 2.964, 2.861, 2.739, 2.598, 2.438, 2.261, 2.070, 1.870 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.195, 3.132, 3.058, 2.972, 2.870, 2.751, 2.613, 2.456, 2.281, 2.093, 1.895 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.137, 3.064, 2.979, 2.879, 2.762, 2.627, 2.473, 2.301, 2.115, 1.919 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.141, 3.070, 2.986, 2.888, 2.773, 2.641, 2.489, 2.320, 2.136, 1.942 });
        Table1A.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 3.075, 2.993, 2.896, 2.784, 2.653, 2.505, 2.338, 2.157, 1.965 });

        Table1B = new List<double[]>();
        Table1B.Add(new double[] { 1.512, 1.584, 1.632, 1.655, 1.653, 1.626, 1.579, 1.516, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 1.727, 1.813, 1.871, 1.899, 1.895, 1.861, 1.803, 1.726, 1.636, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 1.966, 2.065, 2.134, 2.170, 2.169, 2.131, 2.061, 1.966, 1.856, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 2.210, 2.320, 2.400, 2.446, 2.454, 2.422, 2.349, 2.241, 2.108, 1.965, 1.822, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 2.442, 2.560, 2.648, 2.704, 2.726, 2.708, 2.646, 2.540, 2.395, 2.225, 2.052, 1.885, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 2.653, 2.774, 2.869, 2.934, 2.969, 2.968, 2.926, 2.837, 2.699, 2.518, 2.314, 2.114, 1.928, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 2.839, 2.961, 3.060, 3.133, 3.179, 3.194, 3.173, 3.109, 2.993, 2.824, 2.608, 2.373, 2.152, 1.952, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.000, 3.123, 3.224, 3.303, 3.358, 3.387, 3.385, 3.345, 3.259, 3.116, 2.914, 2.665, 2.405, 2.169, 1.960, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.140, 3.261, 3.364, 3.447, 3.510, 3.550, 3.564, 3.546, 3.488, 3.378, 3.206, 2.970, 2.690, 2.412, 2.167, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.261, 3.381, 3.484, 3.570, 3.639, 3.688, 3.715, 3.715, 3.681, 3.603, 3.468, 3.264, 2.993, 2.687, 2.398, 2.149, 0.0, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.366, 3.485, 3.588, 3.676, 3.749, 3.805, 3.843, 3.858, 3.844, 3.793, 3.693, 3.529, 3.290, 2.984, 2.658, 2.366, 2.119, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.458, 3.575, 3.678, 3.768, 3.844, 3.905, 3.951, 3.978, 3.981, 3.953, 3.883, 3.758, 3.561, 3.283, 2.945, 2.609, 2.322, 0.0, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.539, 3.654, 3.757, 3.847, 3.926, 3.991, 4.044, 4.080, 4.096, 4.087, 4.043, 3.952, 3.797, 3.561, 2.243, 2.881, 2.547, 2.269, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.611, 3.724, 3.826, 3.917, 3.997, 4.066, 4.124, 4.167, 4.194, 4.208, 4.177, 4.115, 3.998, 3.808, 3.529, 3.172, 2.798, 2.476, 0.0, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.674, 3.786, 3.887, 3.978, 4.060, 4.131, 4.193, 4.243, 4.278, 4.296, 4.290, 4.252, 4.168, 4.020, 3.789, 3.463, 3.075, 2.705, 2.399, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.731, 3.842, 3.942, 4.033, 4.115, 4.189, 4.253, 4.308, 4.351, 4.378, 4.386, 4.367, 4.311, 4.200, 4.015, 3.736, 3.364, 2.961, 2.609, 0.0, 0.0 });
        Table1B.Add(new double[] { 3.782, 3.891, 3.990, 4.081, 4.164, 4.239, 4.307, 4.365, 4.414, 4.449, 4.468, 4.472, 4.431, 4.352, 4.209, 3.979, 3.646, 3.238, 2.840, 2.511, 0.0 });
        Table1B.Add(new double[] { 3.828, 3.936, 4.034, 4.125, 4.208, 4.285, 4.354, 4.416, 4.468, 4.511, 4.539, 4.549, 4.532, 4.479, 4.372, 4.189, 3.907, 3.522, 3.095, 2.719, 0.0 });
        Table1B.Add(new double[] { 3.870, 3.976, 4.073, 4.164, 4.248, 4.325, 4.396, 4.460, 4.517, 4.564, 4.600, 4.620, 4.619, 4.587, 4.510, 4.369, 4.137, 3.796, 3.370, 2.949, 2.603 });
        Table1B.Add(new double[] { 3.908, 4.013, 4.109, 4.199, 4.283, 4.361, 4.433, 4.500, 4.559, 4.611, 4.653, 4.682, 4.693, 4.678, 4.627, 4.521, 4.336, 4.047, 3.648, 3.201, 2.808 });
        Table1B.Add(new double[] { 3.943, 4.046, 4.142, 4.231, 4.315, 4.394, 4.467, 4.535, 4.597, 4.653, 4.700, 4.736, 4.757, 4.756, 4.725, 4.649, 4.506, 4.269, 3.916, 3.471, 3.033 });
        Table1B.Add(new double[] { 3.975, 4.077, 4.172, 4.261, 4.344, 4.423, 4.498, 4.567, 4.631, 4.690, 4.741, 4.783, 4.812, 4.824, 4.809, 4.758, 4.650, 4.460, 4.160, 3.745, 3.280 });
        Table1B.Add(new double[] { 4.004, 4.105, 4.199, 4.287, 4.371, 4.450, 4.525, 4.596, 4.662, 4.723, 4.777, 4.824, 4.860, 4.882, 4.881, 4.850, 4.771, 4.623, 4.376, 4.007, 3.544 });
        Table1B.Add(new double[] { 4.031, 4.131, 4.224, 4.312, 4.396, 4.475, 4.550, 4.622, 4.689, 4.752, 4.810, 4.861, 4.903, 4.932, 4.944, 4.929, 4.875, 4.762, 4.563, 4.247, 3.813 });
        Table1B.Add(new double[] { 4.056, 4.155, 4.247, 4.335, 4.418, 4.498, 4.573, 4.645, 4.714, 4.779, 4.839, 4.893, 4.940, 4.976, 4.997, 4.996, 4.963, 4.880, 4.723, 74.461, 4.072 });
        Table1B.Add(new double[] { 4.079, 4.177, 4.269, 4.356, 4.439, 4.518, 4.594, 4.667, 4.737, 4.803, 4.865, 4.922, 4.973, 5.015, 5.044, 5.055, 5.038, 4.980, 4.859, 4.647, 4.311 });
        Table1B.Add(new double[] { 4.101, 4.197, 4.288, 4.375, 4.458, 4.537, 4.614, 4.687, 4.757, 4.825, 4.888, 4.948, 5.002, 5.049, 5.085, 5.106, 5.103, 5.066, 4.976, 4.806, 4.524 });
        Table1B.Add(new double[] { 4.121, 4.217, 4.307, 4.393, 4.476, 4.555, 4.631, 4.705, 4.776, 4.845, 4.910, 4.972, 5.029, 5.080, 5.122, 5.150, 5.159, 5.139, 5.075, 4.943, 4.712 });
        Table1B.Add(new double[] { 4.140, 4.234, 4.324, 4.410, 4.492, 4.571, 4.648, 4.722, 4.794, 4.863, 4.929, 4.993, 5.052, 5.107, 5.153, 5.189, 5.208, 5.202, 5.159, 5.059, 4.873 });
        Table1B.Add(new double[] { 4.157, 4.251, 4.340, 4.425, 4.508, 4.587, 4.663, 4.737, 4.809, 4.879, 4.947, 5.012, 5.074, 5.131, 5.181, 5.223, 5.250, 5.257, 5.232, 5.159, 5.012 });
        Table1B.Add(new double[] { 4.174, 4.267, 4.355, 4.440, 4.522, 4.601, 4.677, 4.752, 4.824, 4.895, 4.963, 5.029, 5.093, 5.152, 5.207, 5.253, 5.288, 5.305, 5.295, 5.244, 5.131 });
        Table1B.Add(new double[] { 4.189, 4.281, 4.369, 4.454, 4.535, 4.614, 4.691, 4.765, 4.838, 4.909, 4.978, 5.045, 5.110, 5.172, 5.229, 5.280, 5.321, 5.346, 5.349, 5.318, 5.233 });
        Table1B.Add(new double[] { 4.204, 4.295, 4.383, 4.467, 4.548, 4.627, 4.703, 4.778, 4.851, 4.922, 4.992, 5.060, 5.126, 5.190, 5.249, 5.303, 5.350, 5.383, 5.396, 5.381, 5.320 });
        Table1B.Add(new double[] { 4.218, 4.308, 4.395, 4.479, 4.560, 4.638, 4.715, 4.789, 4.862, 4.934, 5.004, 5.073, 5.141, 5.206, 5.267, 5.325, 5.376, 5.415, 5.437, 5.436, 5.395 });
        Table1B.Add(new double[] { 4.231, 4.321, 4.407, 4.409, 4.571, 4.649, 4.725, 4.800, 4.873, 4.945, 5.016, 5.086, 5.154, 5.220, 5.284, 5.344, 5.399, 5.443, 5.474, 5.483, 5.460 });
        Table1B.Add(new double[] { 4.243, 4.332, 4.418, 4.501, 4.581, 4.659, 4.736, 4.810, 4.884, 4.956, 5.027, 5.097, 5.166, 5.233, 5.299, 5.361, 5.418, 5.468, 5.505, 5.525, 5.516 });
        Table1B.Add(new double[] { 4.255, 4.343, 4.429, 4.511, 4.591, 4.669, 4.745, 4.820, 4.893, 4.966, 5.037, 5.108, 5.177, 5.246, 5.312, 5.376, 5.436, 5.491, 5.533, 5.561, 5.565 });
        Table1B.Add(new double[] { 4.266, 4.354, 4.439, 4.521, 4.600, 4.678, 4.754, 4.829, 4.902, 4.975, 5.048, 5.117, 5.188, 5.257, 5.325, 5.390, 5.452, 5.511, 5.558, 5.593, 5.608 });
        Table1B.Add(new double[] { 4.276, 4.364, 4.448, 4.530, 4.609, 4.695, 4.763, 4.837, 4.911, 4.983, 5.055, 5.126, 5.197, 5.267, 5.336, 5.403, 5.467, 5.529, 5.581, 5.621, 5.645 });
        Table1B.Add(new double[] { 4.286, 4.373, 4.457, 4.538, 4.618, 4.703, 4.771, 4.845, 4.919, 4.991, 5.063, 5.135, 5.206, 5.076, 5.346, 5.414, 5.480, 5.542, 5.600, 5.646, 5.678 });
        Table1B.Add(new double[] { 4.296, 4.382, 4.466, 4.547, 4.626, 4.710, 4.778, 4.853, 4.926, 4.999, 5.071, 5.143, 5.214, 5.285, 5.356, 5.425, 5.492, 5.557, 5.618, 5.669, 5.706 });
        Table1B.Add(new double[] { 4.305, 4.391, 4.474, 4.554, 4.633, 4.717, 4.785, 4.860, 4.933, 5.006, 5.078, 5.150, 5.222, 5.293, 5.364, 5.434, 5.503, 5.569, 5.634, 5.688, 5.732 });
        Table1B.Add(new double[] { 4.313, 4.399, 4.481, 4.562, 4.640, 4.724, 4.792, 4.867, 4.940, 5.013, 5.085, 5.157, 5.229, 5.301, 5.372, 5.443, 5.513, 5.581, 5.648, 5.706, 5.754 });
        Table1B.Add(new double[] { 4.322, 4.406, 4.489, 4.569, 4.647, 4.730, 4.799, 4.873, 4.946, 5.019, 5.091, 5.164, 5.236, 5.308, 5.380, 5.451, 5.522, 5.591, 5.658, 5.722, 5.775 });
        Table1B.Add(new double[] { 4.330, 4.414, 4.496, 4.576, 4.654, 4.736, 4.805, 4.879, 4.952, 5.025, 5.097, 5.170, 5.242, 5.314, 5.387, 5.459, 5.530, 5.601, 5.669, 5.736, 5.792 });
        Table1B.Add(new double[] { 4.337, 4.421, 4.503, 4.682, 4.660, 4.742, 4.811, 4.885, 4.958, 5.031, 5.103, 5.175, 5.248, 5.320, 5.393, 5.466, 5.538, 5.609, 5.679, 5.749, 5.808 });
        Table1B.Add(new double[] { 4.344, 4.428, 4.509, 4.588, 4.666, 4.747, 4.817, 4.890, 4.963, 5.036, 5.109, 5.181, 5.253, 5.326, 5.399, 5.472, 5.545, 5.617, 5.688, 5.760, 5.823 });
        Table1B.Add(new double[] { 4.351, 4.434, 4.515, 4.594, 4.672, 4.763, 4.822, 4.896, 4.969, 5.041, 5.114, 5.186, 5.259, 5.331, 5.404, 5.478, 5.551, 5.624, 5.696, 5.771, 5.836 });
        Table1B.Add(new double[] { 4.358, 4.441, 4.521, 4.600, 4.677, 4.758, 4.827, 4.901, 4.974, 5.046, 5.118, 5.191, 5.263, 5.336, 5.410, 5.483, 5.557, 5.631, 5.704, 5.775, 5.847 });
        Table1B.Add(new double[] { 4.365, 4.447, 4.527, 4.604, 4.682, 4.762, 4.832, 4.905, 4.978, 5.051, 5.123, 5.195, 5.268, 5.341, 5.414, 5.488, 5.562, 5.637, 5.710, 5.783, 5.858 });
        Table1B.Add(new double[] { 4.371, 4.452, 4.532, 4.611, 4.687, 4.767, 4.837, 4.910, 4.983, 5.055, 5.127, 5.200, 5.272, 5.345, 5.419, 5.493, 5.567, 5.642, 5.717, 5.790, 5.867 });
        Table1B.Add(new double[] { 4.377, 4.468, 4.538, 4.616, 4.692, 4.772, 4.841, 4.914, 4.987, 5.059, 5.132, 5.204, 5.276, 5.349, 5.423, 5.497, 5.572, 5.647, 5.722, 5.797, 5.875 });
        Table1B.Add(new double[] { 4.382, 4.463, 4.543, 4.621, 4.697, 4.776, 4.845, 4.918, 4.991, 5.063, 5.135, 5.208, 5.280, 5.353, 5.427, 5.501, 5.576, 5.652, 5.727, 5.803, 5.883 });
        Table1B.Add(new double[] { 4.388, 4.468, 4.548, 4.625, 4.701, 4.780, 4.850, 4.923, 4.995, 5.067, 5.139, 5.211, 5.284, 5.357, 5.431, 5.505, 5.580, 5.656, 5.732, 5.808, 5.883 });
        Table1B.Add(new double[] { 4.393, 4.473, 4.552, 4.630, 4.705, 4.784, 4.854, 4.926, 4.999, 5.071, 5.143, 5.215, 5.287, 5.361, 5.434, 5.509, 5.584, 5.660, 5.736, 5.813, 5.889 });
        Table1B.Add(new double[] { 4.398, 4.478, 4.557, 4.634, 4.710, 4.788, 4.857, 4.930, 5.002, 5.074, 5.146, 5.218, 5.291, 5.364, 5.437, 5.512, 5.587, 5.663, 5.740, 5.817, 5.894 });
        Table1B.Add(new double[] { 4.403, 4.483, 4.561, 4.638, 4.714, 4.791, 4.861, 4.934, 5.006, 5.078, 5.149, 5.222, 5.294, 5.367, 5.440, 5.515, 5.590, 5.667, 5.744, 5.821, 5.898 });
        Table1B.Add(new double[] { 4.408, 4.487, 4.565, 4.642, 4.717, 4.795, 4.865, 4.937, 5.009, 5.081, 5.153, 5.225, 5.297, 5.370, 5.443, 5.518, 5.593, 5.670, 5.747, 5.825, 5.903 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 4.721, 4.798, 4.868, 4.940, 5.012, 5.084, 5.156, 5.228, 5.300, 5.373, 5.446, 5.521, 5.596, 5.673, 5.750, 5.828, 5.906 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.871, 4.943, 5.015, 5.087, 5.158, 5.230, 5.303, 5.375, 5.449, 5.523, 5.599, 5.675, 5.753, 5.831, 5.910 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.874, 4.947, 5.018, 5.090, 5.161, 5.233, 5.305, 5.378, 5.451, 5.526, 5.601, 5.678, 5.755, 5.834, 53913 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 4.949, 5.021, 5.092, 5.164, 5.236, 5.308, 5.380, 5.454, 5.528, 5.603, 5.680, 5.757, 5.836, 5.915 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.024, 5.095, 5.166, 5.238, 5.310, 5.383, 5.456, 5.530, 5.605, 5.682, 5.760, 5.838, 5.918 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.098, 5.169, 5.240, 5.312, 5.385, 5.458, 5.532, 5.607, 5.684, 5.762, 5.840, 5.920 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.171, 5.243, 5.314, 5.387, 5.460, 5.534, 5.609, 5.686, 5.763, 5.842, 5.922 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.173, 5.245, 5.316, 5.389, 5.462, 5.536, 5.611, 5.687, 5.765, 5.844, 5.924 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.247, 5.318, 5.391, 5.464, 5.538, 5.613, 5.689, 5.767, 5.845, 5.925 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.249, 5.320, 5.393, 5.465, 5.539, 5.614, 5.690, 5.768, 5.847, 5.927 });
        Table1B.Add(new double[] { 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 5.322, 5.394, 5.467, 5.541, 5.616, 5.692, 5.769, 5.848, 5.928 });

        Table2 = new List<double[]>();
        Table2.Add(new double[] { 0.000,0.053,0.111,0.184,0.282,0.424,0.627,0.754,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.039,0.082,0.132,0.196,0.284,0.412,0.591,0.727,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.031,0.065,0.103,0.151,0.212,0.297,0.419,0.586,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.026,0.054,0.085,0.123,0.169,0.231,0.317,0.439,0.598,0.681,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.023,0.047,0.073,0.104,0.142,0.190,0.254,0.343,0.468,0.616,0.653,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.020,0.041,0.064,0.091,0.122,0.161,0.212,0.280,0.375,0.504,0.633,0.616,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.018,0.037,0.058,0.081,0.108,0.141,0.183,0.237,0.311,0.413,0.542,0.638,0.574,0.0,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.017,0.034,0.053,0.073,0.097,0.126,0.161,0.206,0.266,0.347,0.456,0.579,0.621,0.531,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.015,0.032,0.049,0.068,0.089,0.114,0.145,0.183,0.233,0.299,0.388,0.501,0.605,0.582,0.0,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.014,0.029,0.045,0.063,0.082,0.105,0.132,0.165,0.208,0.263,0.336,0.433,0.545,0.607,0.536,0.0,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.013,0.028,0.043,0.059,0.077,0.097,0.122,0.151,0.188,0.235,0.297,0.379,0.481,0.579,0.579,0.489,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.013,0.026,0.040,0.055,0.072,0.091,0.113,0.140,0.172,0.213,0.266,0.336,0.425,0.527,0.590,0.533,0.0,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.012,0.026,0.038,0.053,0.068,0.086,0.108,0.130,0.159,0.196,0.242,0.301,0.379,0.474,0.563,0.569,0.484,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.011,0.024,0.036,0.050,0.065,0.082,0.100,0.122,0.148,0.181,0.222,0.274,0.341,0.426,0.520,0.576,0.524,0.0,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.011,0.023,0.035,0.048,0.062,0.078,0.095,0.116,0.140,0.169,0.206,0.252,0.310,0.385,0.474,0.554,0.555,0.475,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.010,0.022,0.034,0.046,0.060,0.074,0.091,0.110,0.132,0.159,0.192,0.233,0.285,0.351,0.432,0.518,0.654,0.510,0.0,0.0});
        Table2.Add(new double[] { 0.000,0.010,0.021,0.032,0.044,0.057,0.072,0.087,0.105,0.126,0.151,0.180,0.217,0.264,0.323,0.396,0.480,0.549,0.540,0.461,0.0});
        Table2.Add(new double[] { 0.000,0.009,0.020,0.031,0.043,0.055,0.069,0.084,0.101,0.120,0.143,0.171,0.204,0.246,0.299,0.365,0.443,0.521,0.552,0.494,0.0});
        Table2.Add(new double[] { 0.000,0.009,0.020,0.030,0.042,0.054,0.067,0.081,0.097,0.115,0.137,0.162,0.193,0.231,0.279,0.338,0.410,0.488,0.544,0.522,0.445});
        Table2.Add(new double[] { 0.000,0.009,0.019,0.029,0.040,0.052,0.065,0.078,0.094,0.111,0.131,0.155,0.183,0.218,0.261,0.315,0.381,0.456,0.524,0.538,0.475});
        Table2.Add(new double[] { 0.000,0.008,0.018,0.029,0.039,0.051,0.063,0.076,0.091,0.107,0.126,0.148,0.175,0.207,0.246,0.295,0.355,0.426,0.498,0.539,0.503});
        Table2.Add(new double[] { 0.000,0.008,0.018,0.028,0.038,0.049,0.061,0.074,0.088,0.104,0.122,0.143,0.167,0.197,0.233,0.278,0.333,0.398,0.470,0.526,0.522});
        Table2.Add(new double[] { 0.000,0.008,0.017,0.027,0.037,0.048,0.059,0.072,0.085,0.101,0.118,0.138,0.161,0.189,0.222,0.263,0.313,0.374,0.443,0.506,0.530});
        Table2.Add(new double[] { 0.000,0.008,0.017,0.027,0.037,0.047,0.058,0.070,0.083,0.098,0.114,0.133,0.155,0.181,0.212,0.250,0.296,0.352,0.417,0.483,0.525});
        Table2.Add(new double[] { 0.000,0.008,0.017,0.026,0.036,0.046,0.057,0.068,0.081,0.095,0.111,0.129,0.150,0.174,0.203,0.239,0.281,0.333,0.394,0.460,0.513});
        Table2.Add(new double[] { 0.000,0.007,0.016,0.025,0.035,0.045,0.056,0.067,0.079,0.093,0.108,0.125,0.145,0.168,0.196,0.228,0.268,0.316,0.373,0.437,0.495});
        Table2.Add(new double[] { 0.000,0.007,0.016,0.025,0.034,0.044,0.054,0.066,0.078,0.091,0.105,0.122,0.141,0.163,0.188,0.219,0.256,0.301,0.354,0.415,0.475});
        Table2.Add(new double[] { 0.000,0.007,0.015,0.025,0.034,0.043,0.053,0.064,0.076,0.089,0.103,0.119,0.137,0.158,0.182,0.211,0.246,0.288,0.337,0.395,0.455});
        Table2.Add(new double[] { 0.000,0.007,0.015,0.024,0.033,0.043,0.053,0.063,0.075,0.087,0.101,0.116,0.133,0.153,0.176,0.204,0.236,0.276,0.322,0.376,0.435});
        Table2.Add(new double[] { 0.000,0.007,0.015,0.024,0.033,0.042,0.052,0.062,0.073,0.085,0.099,0.113,0.130,0.149,0.171,0.197,0.228,0.265,0.308,0.359,0.416});
        Table2.Add(new double[] { 0.000,0.007,0.015,0.023,0.032,0.041,0.051,0.061,0.072,0.084,0.097,0.111,0.127,0.145,0.167,0.191,0.220,0.255,0.296,0.344,0.399});
        Table2.Add(new double[] { 0.000,0.006,0.015,0.023,0.032,0.041,0.050,0.060,0.071,0.082,0.095,0.109,0.124,0.142,0.162,0.186,0.213,0.246,0.285,0.330,0.382});
        Table2.Add(new double[] { 0.000,0.006,0.014,0.023,0.031,0.040,0.049,0.059,0.070,0.081,0.093,0.107,0.122,0.139,0.158,0.181,0.207,0.238,0.274,0.317,0.367});
        Table2.Add(new double[] { 0.000,0.006,0.014,0.022,0.031,0.040,0.049,0.058,0.069,0.080,0.092,0.105,0.119,0.136,0.155,0.176,0.201,0.231,0.265,0.308,0.353});
        Table2.Add(new double[] { 0.000,0.006,0.014,0.022,0.030,0.039,0.048,0.057,0.068,0.078,0.090,0.103,0.117,0.133,0.151,0.172,0.196,0.224,0.257,0.295,0.340});
        Table2.Add(new double[] { 0.000,0.006,0.014,0.022,0.030,0.039,0.047,0.057,0.067,0.077,0.089,0.101,0.115,0.131,0.148,0.168,0.191,0.218,0.249,0.285,0.328});
        Table2.Add(new double[] { 0.000,0.006,0.014,0.022,0.030,0.038,0.047,0.056,0.066,0.076,0.087,0.100,0.113,0.128,0.145,0.164,0.186,0.212,0.242,0.277,0.317});
        Table2.Add(new double[] { 0.000,0.006,0.014,0.021,0.029,0.038,0.046,0.055,0.065,0.075,0.086,0.098,0.111,0.126,0.142,0.161,0.182,0.207,0.235,0.268,0.307});
        Table2.Add(new double[] { 0.000,0.006,0.013,0.021,0.029,0.037,0.046,0.055,0.064,0.074,0.085,0.097,0.110,0.124,0.140,0.158,0.178,0.202,0.229,0.261,0.298});
        Table2.Add(new double[] { 0.000,0.006,0.013,0.021,0.029,0.037,0.045,0.054,0.063,0.073,0.084,0.096,0.108,0.122,0.137,0.155,0.175,0.197,0.223,0.254,0.289});
        Table2.Add(new double[] { 0.000,0.006,0.013,0.021,0.028,0.037,0.045,0.054,0.063,0.073,0.083,0.094,0.107,0.120,0.135,0.152,0.171,0.193,0.218,0.247,0.281});
        Table2.Add(new double[] { 0.000,0.006,0.013,0.021,0.028,0.036,0.044,0.053,0.062,0.072,0.082,0.093,0.105,0.118,0.133,0.150,0.168,0.189,0.213,0.241,0.273});
        Table2.Add(new double[] { 0.000,0.005,0.013,0.020,0.028,0.036,0.044,0.053,0.061,0.071,0.081,0.092,0.104,0.117,0.131,0.147,0.165,0.185,0.209,0.209,0.267});
        Table2.Add(new double[] { 0.000,0.005,0.013,0.020,0.028,0.036,0.044,0.052,0.061,0.070,0.080,0.091,0.103,0.115,0.129,0.145,0.162,0.182,0.205,0.230,0.260});
        Table2.Add(new double[] { 0.000,0.005,0.013,0.020,0.027,0.035,0.043,0.052,0.060,0.070,0.079,0.090,0.101,0.114,0.128,0.143,0.160,0.179,0.201,0.226,0.254});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.020,0.027,0.035,0.043,0.051,0.060,0.069,0.079,0.089,0.100,0.113,0.126,0.141,0.157,0.176,0.197,0.221,0.249});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.020,0.027,0.035,0.043,0.051,0.059,0.068,0.078,0.088,0.099,0.111,0.124,0.139,0.155,0.173,0.193,0.217,0.243});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.027,0.034,0.042,0.050,0.059,0.068,0.077,0.087,0.098,0.110,0.123,0.137,0.153,0.170,0.190,0.213,0.238});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.027,0.034,0.042,0.050,0.058,0.067,0.076,0.086,0.097,0.109,0.121,0.135,0.151,0.168,0.187,0.209,0.234});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.026,0.034,0.042,0.050,0.058,0.067,0.076,0.086,0.096,0.108,0.120,0.134,0.149,0.165,0.184,0.205,0.229});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.026,0.034,0.041,0.049,0.057,0.066,0.075,0.085,0.095,0.107,0.119,0.132,0.147,0.163,0.181,0.202,0.225});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.026,0.033,0.041,0.049,0.057,0.066,0.075,0.084,0.094,0.106,0.118,0.131,0.145,0.161,0.179,0.199,0.221});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.026,0.033,0.041,0.049,0.057,0.065,0.074,0.084,0.094,0.105,0.116,0.129,0.143,0.159,0.176,0.196,0.218});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.026,0.033,0.040,0.048,0.056,0.065,0.073,0.083,0.093,0.104,0.155,0.128,0.142,0.157,0.174,0.193,0.214});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.026,0.033,0.040,0.048,0.056,0.064,0.073,0.082,0.092,0.103,0.114,0.127,0.140,0.155,0.172,0.190,0.211});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.019,0.025,0.033,0.040,0.048,0.055,0.064,0.072,0.082,0.091,0.102,0.113,0.125,0.139,0.153,0.170,0.188,0.208});
        Table2.Add(new double[] { 0.000,0.005,0.012,0.018,0.025,0.032,0.040,0.047,0.055,0.063,0.072,0.081,0.091,0.101,0.112,0.124,0.137,0.152,0.168,0.185,0.205});
        Table2.Add(new double[] { 0.000,0.005,0.011,0.018,0.025,0.032,0.040,0.047,0.055,0.063,0.071,0.080,0.090,0.100,0.111,0.123,0.136,0.150,0.166,0.183,0.202});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.032,0.039,0.047,0.054,0.063,0.071,0.080,0.089,0.099,0.110,0.122,0.135,0.149,0.164,0.181,0.200});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.032,0.039,0.047,0.054,0.062,0.071,0.079,0.089,0.099,0.109,0.121,0.133,0.147,0.162,0.179,0.197});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.039,0.046,0.054,0.062,0.070,0.079,0.088,0.098,0.109,0.120,0.132,0.146,0.160,0.177,0.195});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.046,0.054,0.061,0.070,0.078,0.088,0.097,0.108,0.119,0.131,0.144,0.159,0.175,0.192});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.053,0.061,0.069,0.078,0.087,0.097,0.107,0.118,0.130,0.143,0.157,0.173,0.190});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.061,0.069,0.078,0.087,0.096,0.106,0.117,0.129,0.142,0.156,0.171,0.188});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.069,0.077,0.086,0.095,0.105,0.116,0.128,0.141,0.154,0.169,0.186});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.068,0.077,0.086,0.095,0.104,0.116,0.127,0.139,0.153,0.168,0.184});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.076,0.085,0.094,0.104,0.115,0.126,0.138,0.152,0.166,0.182});
        Table2.Add(new double[] { 0.000,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.076,0.085,0.094,0.104,0.114,0.125,0.137,0.150,0.165,0.181});
        Table2.Add(new double[] { 0.000, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.084, 0.093, 0.103, 0.113, 0.124, 0.136, 0.149, 0.163, 0.179 });

        // Validate table sizes
        if (Table1A.Count != KurtosisKey.Length)
            throw new ApplicationException("Invalid row count for Table 1A.");
        if (Table1B.Count != KurtosisKey.Length)
            throw new ApplicationException("Invalid row count for Table 1B.");
        if (Table2.Count != KurtosisKey.Length)
            throw new ApplicationException("Invalid row count for Table 2.");

        foreach (double[] arr in Table1A)
            if (arr.Length != SkewnessKey.Length)
                throw new ApplicationException("Invalid row size for Table 1A row index " + Table1A.IndexOf(arr).ToString() + ".");

        foreach (double[] arr in Table1B)
            if (arr.Length != SkewnessKey.Length)
                throw new ApplicationException("Invalid row size for Table 1B row index " + Table1B.IndexOf(arr).ToString() + ".");

        foreach (double[] arr in Table2)
            if (arr.Length != SkewnessKey.Length)
                throw new ApplicationException("Invalid row size for Table 2 row index " + Table2.IndexOf(arr).ToString() + ".");
    
    }
}
