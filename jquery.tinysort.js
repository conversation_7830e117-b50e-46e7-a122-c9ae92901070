﻿/*
* jQ<PERSON>y <PERSON>ort 1.0.2
* Copyright (c) 2008 <PERSON>
* Dual licensed under the MIT and GPL licenses:
*   http://www.opensource.org/licenses/mit-license.php
*   http://www.gnu.org/licenses/gpl.html
*/
(function(B) { B.tinysort = { id: "TinySort", version: "1.0.2", defaults: { order: "asc", attr: "", place: "start", returns: false} }; B.fn.extend({ tinysort: function(H, I) { if (H && typeof (H) != "string") { I = H; H = null } var E = B.extend({}, B.tinysort.defaults, I); var O = {}; this.each(function(S) { var U = (!H || H == "") ? B(this) : B(this).find(H); var T = E.order == "rand" ? "" + Math.random() : (E.attr == "" ? U.text() : U.attr(E.attr)); var R = B(this).parent(); if (!O[R]) { O[R] = { s: [], n: []} } if (U.length > 0) { O[R].s.push({ s: T, e: B(this), n: S }) } else { O[R].n.push({ e: B(this), n: S }) } }); for (var G in O) { var D = O[G]; D.s.sort(function J(T, S) { var R = T.s.toLowerCase ? T.s.toLowerCase() : T.s; var U = S.s.toLowerCase ? S.s.toLowerCase() : S.s; if (C(T.s) && C(S.s)) { R = parseFloat(T.s); U = parseFloat(S.s) } return (E.order == "asc" ? 1 : -1) * (R < U ? -1 : (R > U ? 1 : 0)) }) } var L = []; for (var G in O) { var D = O[G]; var M = []; var F = B(this).length; switch (E.place) { case "first": B.each(D.s, function(R, S) { F = Math.min(F, S.n) }); break; case "org": B.each(D.s, function(R, S) { M.push(S.n) }); break; case "end": F = D.n.length; break; default: F = 0 } var P = [0, 0]; for (var K = 0; K < B(this).length; K++) { var N = K >= F && K < F + D.s.length; if (A(M, K)) { N = true } var Q = (N ? D.s : D.n)[P[N ? 0 : 1]].e; Q.parent().append(Q); if (N || !E.returns) { L.push(Q.get(0)) } P[N ? 0 : 1]++ } } return this.setArray(L) } }); function C(D) { return /^[\+-]?\d*\.?\d*$/.exec(D) } function A(E, F) { var D = false; B.each(E, function(H, G) { if (!D) { D = G == F } }); return D } B.fn.TinySort = B.fn.Tinysort = B.fn.tsort = B.fn.tinysort })(jQuery);