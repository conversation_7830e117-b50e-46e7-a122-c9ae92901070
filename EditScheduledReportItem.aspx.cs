﻿using System;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class EditScheduledReportItem : System.Web.UI.Page
{
	public int ScheduledReportId
	{
		get { if (this.ViewState["sr"] != null) return (int)this.ViewState["sr"]; else return 0; }
		set { this.ViewState["sr"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
        if (!Page.IsPostBack)
        {
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.SCHEDULED_REPORT_ID_KEY]))
                this.ScheduledReportId = Convert.ToInt32(Request.QueryString[DieboldConstants.SCHEDULED_REPORT_ID_KEY]);

            // Populate Tree
            this.reportTree.CheckBoxes = true;
            this.reportTree.Nodes.Clear();

            RadTreeNode sessionNode = new RadTreeNode("Sessions");
            PopulateSessions(sessionNode);
            sessionNode.Checkable = false;
            this.reportTree.Nodes.Add(sessionNode);

            RadTreeNode myLibraryNode = new RadTreeNode("My Library");
            PopulateMyLibrary(myLibraryNode);
            myLibraryNode.Checkable = false;
            this.reportTree.Nodes.Add(myLibraryNode);

            RadTreeNode sharedLibraryNode = new RadTreeNode("Shared Library");
            PopulateSharedLibrary(sharedLibraryNode);
            sharedLibraryNode.Checkable = false;
            this.reportTree.Nodes.Add(sharedLibraryNode);


            if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.REPORT_ID_KEY]))
            {
                foreach (string reportStr in Request.Params[DieboldConstants.REPORT_ID_KEY].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    RadTreeNode node = this.reportTree.FindNodeByValue("R" + reportStr);
                    if (node == null)
                    {
                        node = new RadTreeNode("R" + reportStr);
                        node.Value = "R" + reportStr;
                        node.Checkable = true;
                        this.reportTree.Nodes.Add(node);
                    }
                    node.Checked = true;
                }
            }

            if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SUBREPORT_ID_KEY]))
            {
                foreach (string subReportStr in Request.Params[DieboldConstants.SUBREPORT_ID_KEY].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    RadTreeNode node = this.reportTree.FindNodeByValue("S" + subReportStr);
                    if (node == null)
                    {
                        node = new RadTreeNode("R" + subReportStr);
                        node.Value = "R" + subReportStr;
                        node.Checkable = true;
                        this.reportTree.Nodes.Add(node);
                    }
                    node.Checked = true;
                }
            }

            IList<RadTreeNode> checkedNodes = this.reportTree.CheckedNodes;
            if (checkedNodes.Count == 1)
            {
                RadTreeNode node = checkedNodes[0];
                if (node.Value != null && !string.IsNullOrEmpty(node.Value))
                {
                    if(string.Compare("R", node.Value.Substring(0, 1), true) == 0)
                        LoadScheduledReportItem(Int32.Parse(node.Value.Substring(1)));
                    else if(string.Compare("S", node.Value.Substring(0, 1), true) == 0)
                        LoadScheduledSubReportItem(Int32.Parse(node.Value.Substring(1)));
                }
            }

            TreeRow.Visible = (checkedNodes.Count == 0);
        }
    }

    protected void LoadScheduledReportItem(int reportId)
	{
        DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadScheduledReportItem", this.ScheduledReportId, reportId);
		
		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows) //only loads one row.
			{
                if (DataFormatter.getBool(row, "Landscape"))
                    formatList.SelectedValue = "landscape";
                else
                    formatList.SelectedValue = "portrait";

				if (DataFormatter.getBool(row, "UsePdfFormat"))
					pdfRadio.Checked = true;
				else
					xlsRadio.Checked = true;

				chartScaleList.SelectedValue = DataFormatter.getString(row, "ChartScale");
				dataScaleList.SelectedValue = DataFormatter.getString(row, "DataScale");
				dataSeparate.Checked = DataFormatter.getBool(row, "DataSeparate");
			}
		}
	}

	protected void LoadScheduledSubReportItem(int subReportId)
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadScheduledReportSubItem", this.ScheduledReportId, subReportId);

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows) //only loads one row.
			{
				if (DataFormatter.getBool(row, "Landscape"))
					formatList.SelectedValue = "landscape";
				else
					formatList.SelectedValue = "portrait";

				if (DataFormatter.getBool(row, "UsePdfFormat"))
					pdfRadio.Checked = true;
				else
					xlsRadio.Checked = true;

				chartScaleList.SelectedValue = DataFormatter.getString(row, "ChartScale");
				dataScaleList.SelectedValue = DataFormatter.getString(row, "DataScale");
				dataSeparate.Checked = DataFormatter.getBool(row, "DataSeparate");
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
        if (Page.IsValid)
        {
            bool isLandscape = false;
            if (string.Compare(formatList.SelectedValue, "landscape", StringComparison.OrdinalIgnoreCase) == 0)
                isLandscape = true;

			bool usePdfFormat = true;
			if (xlsRadio.Checked)
				usePdfFormat = false;

            IList<RadTreeNode> nodeList = this.reportTree.CheckedNodes;
            foreach (RadTreeNode node in nodeList)
            {
                if (!string.IsNullOrEmpty(node.Value))
                {
                    if (node.Value[0] == 'S' && node.ParentNode != null && !string.IsNullOrEmpty(node.ParentNode.Value))
                    {
						SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReportSubItem", this.ScheduledReportId, node.ParentNode.Value.Substring(1), node.Value.Substring(1), usePdfFormat, isLandscape,
                                        this.chartScaleList.SelectedValue, this.dataScaleList.SelectedValue, this.dataSeparate.Checked);
                    }
                    else if (node.Value[0] == 'R')
                    {
                        object runAll = null;
                        if (this.reportTree.Visible)
                            runAll = true;
						SqlHelper.ExecuteNonQuery("RPT_UpdateScheduledReportItem", this.ScheduledReportId, node.Value.Substring(1), usePdfFormat, isLandscape,
                                        this.chartScaleList.SelectedValue, this.dataScaleList.SelectedValue, this.dataSeparate.Checked, runAll);
                    }
                }
            }

            Response.Redirect(string.Format("EditScheduledReport.aspx?{0}={1}", DieboldConstants.SCHEDULED_REPORT_ID_KEY, this.ScheduledReportId));
        }
	}

    protected void ValidateTree(object sender, ServerValidateEventArgs e)
    {
        e.IsValid = (this.reportTree.CheckedNodes.Count > 0);
    }

    private void PopulateSessions(RadTreeNode sessionNode)
    {
        DataSet ds = LibrarySource.GetSessions(false, Utility.GetUserName());
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            RadTreeNode newNode = new RadTreeNode(DataFormatter.getString(row, "SessionName"));
            foreach (DataRow childRow in row.GetChildRows("Children"))
            {
                if (!DataFormatter.getBool(childRow, "PromptSessions"))
                {
					//ReportHelper.ReportTypeEnum reportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(childRow, "ReportTypeId");
					//if (reportTypeId != ReportHelper.ReportTypeEnum.SHIFT && reportTypeId != ReportHelper.ReportTypeEnum.SHIFT_MANUAL_VOLUME)
                    //{
                        RadTreeNode newChildNode = new RadTreeNode(DataFormatter.getString(childRow, "ReportName"));
                        newChildNode.Value = "R" + DataFormatter.getInt32(childRow, "ReportId").ToString();
                        newChildNode.Checkable = true;
                        newNode.Nodes.Add(newChildNode);

                        foreach (DataRow subChildRow in childRow.GetChildRows("SubReports"))
                        {
                            RadTreeNode newSubChildNode = new RadTreeNode(DataFormatter.getString(subChildRow, "SubReportName"));
                            newSubChildNode.Value = "S" + DataFormatter.getInt32(subChildRow, "SubReportId").ToString();
                            newSubChildNode.Checkable = true;
                            newChildNode.Nodes.Add(newSubChildNode);
                        }
                    //}
                }
            }
            newNode.Checkable = false;
            sessionNode.Nodes.Add(newNode);
        }
    }

    private void PopulateMyLibrary(RadTreeNode myLibraryNode)
    {
        DataSet ds = LibrarySource.GetMyLibrary(false, Utility.GetUserName());
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            RadTreeNode newNode = new RadTreeNode(DataFormatter.getString(row, "FolderName"));
            foreach (DataRow childRow in row.GetChildRows("Children"))
            {
                if (!DataFormatter.getBool(childRow, "PromptSessions"))
                {
					ReportHelper.ReportTypeEnum reportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(childRow, "ReportTypeId");
					if (reportTypeId != ReportHelper.ReportTypeEnum.SHIFT)
                    {
                        RadTreeNode newChildNode = new RadTreeNode(DataFormatter.getString(childRow, "ReportName"));
                        newChildNode.Value = "R" + DataFormatter.getInt32(childRow, "ReportId").ToString();
                        newChildNode.Checkable = true;
                        newNode.Nodes.Add(newChildNode);

                        foreach (DataRow subChildRow in childRow.GetChildRows("SubReports"))
                        {
                            RadTreeNode newSubChildNode = new RadTreeNode(DataFormatter.getString(subChildRow, "SubReportName"));
                            newSubChildNode.Value = "S" + DataFormatter.getInt32(subChildRow, "SubReportId").ToString();
                            newSubChildNode.Checkable = true;
                            newChildNode.Nodes.Add(newSubChildNode);
                        }
                    }
                }
            }
            newNode.Checkable = false;
            myLibraryNode.Nodes.Add(newNode);
        }
    }

    private void PopulateSharedLibrary(RadTreeNode sharedLibraryNode)
    {
        DataSet ds = LibrarySource.GetSharedLibrary(false);
        foreach (DataRow row in ds.Tables[0].Rows)
        {
            RadTreeNode newNode = new RadTreeNode(DataFormatter.getString(row, "FolderName"));
            foreach (DataRow childRow in row.GetChildRows("Children"))
            {
                if (!DataFormatter.getBool(childRow, "PromptSessions"))
                {
					ReportHelper.ReportTypeEnum reportTypeId = (ReportHelper.ReportTypeEnum)DataFormatter.getInt32(childRow, "ReportTypeId");
					if (reportTypeId != ReportHelper.ReportTypeEnum.SHIFT)
                    {
                        RadTreeNode newChildNode = new RadTreeNode(DataFormatter.getString(childRow, "ReportName"));
                        newChildNode.Value = "R" + DataFormatter.getInt32(childRow, "ReportId").ToString();
                        newChildNode.Checkable = true;
                        newNode.Nodes.Add(newChildNode);

                        foreach (DataRow subChildRow in childRow.GetChildRows("SubReports"))
                        {
                            RadTreeNode newSubChildNode = new RadTreeNode(DataFormatter.getString(subChildRow, "SubReportName"));
                            newSubChildNode.Value = "S" + DataFormatter.getInt32(subChildRow, "SubReportId").ToString();
                            newSubChildNode.Checkable = true;
                            newChildNode.Nodes.Add(newSubChildNode);
                        }
                    }
                }
            }
            newNode.Checkable = false;
            sharedLibraryNode.Nodes.Add(newNode);
        }
    }
}		