using System;
using System.Configuration;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class MasterPage : System.Web.UI.MasterPage
{
	private const string ROOT_NODE = "~/Default.aspx";

	protected void Page_Load(object sender, EventArgs e)
	{
        if (string.Compare("true", ConfigurationManager.AppSettings["EnableSSLRedirect"], true) == 0 && HttpContext.Current.Request.IsSecureConnection == false) {
            HttpContext.Current.Response.Redirect(HttpContext.Current.Request.Url.AbsoluteUri.Replace("http://", "https://"), true);
        }

        SiteMapSource.StartingNodeUrl = ROOT_NODE;
		SiteMapNav.Text = CreateSiteNav(SiteMapSource);
	}

	protected string CreateSiteNav(SiteMapDataSource data)
	{
        bool isAdmin = Utility.IsUserAdmin();

		if (data is IHierarchicalDataSource)
		{
			System.Text.StringBuilder navString = new System.Text.StringBuilder();

			IHierarchicalEnumerable enumerable = ((HierarchicalDataSourceView)((IHierarchicalDataSource)data).GetHierarchicalView("")).Select();
			navString.AppendLine("<table cellspacing=\"0\" cellpadding=\"0\" border=\"0\" class=\"leftNav\"><tr><td style=\"padding-top:5px;\">&nbsp;</td></tr><tr><tr><td class=\"leftNavTop\">&nbsp;</td></tr>");

			foreach (object item in enumerable)
			{
				IHierarchyData itemData = enumerable.GetHierarchyData(item);

				if (itemData != null)
				{
					SiteMapNode newNode = (SiteMapNode)itemData.Item;

                    if (string.Compare("secured", newNode.Description, true) != 0 || isAdmin)
                    {
                        navString.Append("<tr><td><a href=\"");
                        navString.Append(BuildNavigateUrl(newNode.Url.ToString()));
                        navString.Append("\" border=\"0\"><div class=\"");
                        navString.Append(GetStyleName(IsSelectedPage(this.Request.FilePath, newNode)));
                        navString.Append("\" >");
                        navString.Append(newNode.Title);
                        navString.AppendLine("</div></a></td></tr>");
                    }
				}
			}
			navString.AppendLine("<tr><td class=\"leftNavBottom\">&nbsp;</td></tr><tr><td style=\"padding-bottom:200px;\">&nbsp;</td></tr></tr></table>");
			return navString.ToString();
		}
		else
			throw new ApplicationException("Data Source not a descendant of IHierarchicalDataSource.");
	}

	protected string BuildNavigateUrl(string siteMapUrl)
	{
		string retVal = "";

		if ((!String.IsNullOrEmpty(siteMapUrl)))
		{
			if (siteMapUrl.EndsWith("#"))
				siteMapUrl = siteMapUrl.Substring(0, siteMapUrl.Length - 1);

			if (siteMapUrl.EndsWith("?"))
				siteMapUrl = siteMapUrl.Substring(0, siteMapUrl.Length - 1);

			//if (this.StudentId > 0)
			//    retVal = String.Format("{0}?{1}={2}", tmpContext.FilePath, WestConstants.STUDENT_ID_KEY, this.StudentId);
			//else
			retVal = siteMapUrl;
		}
		return retVal;
	}

	//Returns true if this or any of this.Descendants are this.CurrentPage
	protected bool IsSelectedPage(string curPage, SiteMapNode mapNode)
	{
		bool retVal = false;
		if (curPage.StartsWith(mapNode.Url, StringComparison.OrdinalIgnoreCase))
		{
			retVal = true;
		}
		else
		{
			if (mapNode.HasChildNodes)
			{
				foreach (SiteMapNode childMapNode in mapNode.ChildNodes)
				{
					if (IsSelectedPage(curPage, childMapNode))
						retVal = true;
				}
			}
		}
		return retVal;
	}

	private string GetStyleName(bool isSelected)
	{
		if (isSelected)
			return "leftNavItem_selected";
		else
			return "leftNavItem";
	}
}
