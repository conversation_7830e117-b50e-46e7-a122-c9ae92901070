using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using QueueServiceClient;

public partial class DownloadFile : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FILE_NAME_KEY]))
		{
			ErrorResult result = QueueErrors.DownloadErrorFile(Server.UrlDecode(Request.Params[DieboldConstants.FILE_NAME_KEY]));

			if (result != null)
			{
				Response.ClearContent();
				Response.ClearHeaders();
				Response.ContentType = result.MimeType;
				Response.AppendHeader("content-disposition", "attachment; filename=" + result.FileName + "; size=" + result.FileSize.ToString() + ";");
				Response.BinaryWrite(result.FileData);
				Response.End();
			}
		}
	}
}
