using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditDeviceType : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_NAME_KEY]))
				this.deviceTypeNameField.Text = Request.Params[DieboldConstants.DEVICE_TYPE_NAME_KEY];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.IS_TERMINAL_TYPE]))
				this.isTerminal.Checked = Convert.ToBoolean(Request.Params[DieboldConstants.IS_TERMINAL_TYPE]);

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.IS_ACTIVE_KEY]))
				this.isActive.Checked = Convert.ToBoolean(Request.Params[DieboldConstants.IS_ACTIVE_KEY]);

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]))
			{
				this.deviceTypeIdField.Enabled = false;
				this.deviceTypeIdField.Text = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];
				this.deviceTypeNameField.Focus();
			}
			else
			{
				this.deviceTypeIdField.Focus();
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (deviceTypeIdField.Enabled)
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertDeviceType", this.deviceTypeIdField.Text, this.deviceTypeNameField.Text, this.isTerminal.Checked);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateDeviceType", this.deviceTypeIdField.Text, this.deviceTypeNameField.Text, this.isTerminal.Checked, this.isActive.Checked);

		if (result == 0)
		{
			this.ErrorLabel.Visible = true;
		}
		else
		{
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='DeviceType.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='DeviceType.aspx';CloseRadWindow(); ", true);
		}
	}
}
