<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="StyleGuide.aspx.cs" Inherits="StyleGuide" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<div class="body">body</div>
				<br />
				<div class="navCallout">navCallout</div>
				<br />
				<div class="leftNavItem">leftNavItem</div>
				<br />
				<div class="leftNavItem_selected">leftNavItem_selected</div>
				<br />
				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">widgetTitle</td>
						<td class="widgetTop" style="width:50%;">widgetTop</td>
						<td class="widgetTop" style="text-align:right;"><table cellpadding="0" cellspacing="0" border="0"><tr><td><div class="addButtonTop"><asp:linkbutton runat="server" id="pastSeriesLink">addButtonTop</asp:linkbutton></div></td></tr></table></td>
					</tr>
				</table>
				<div class="widget">
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td>widget<br /><br /></td>
						</tr>
						<tr>
							<td class="error">error</td>
						</tr>
						<tr>
							<td class="link"><a href="#">link</a></td>
						</tr>
						<tr>
							<td class="title">title</td>
						</tr>
						<tr>
							<td class="goButton"><asp:linkbutton runat="server" id="test">goButton</asp:linkbutton></td>
						</tr>
						<tr>
							<td class="addButton"><asp:linkbutton runat="server" id="Linkbutton1">addButton</asp:linkbutton></td>
						</tr>
						<tr>
							<td class="cancelButton"><asp:linkbutton runat="server" id="Linkbutton2">cancelButton</asp:linkbutton></td>
						</tr>
						<tr>
							<td class="addButton"><asp:linkbutton runat="server" id="Linkbutton4">addButton</asp:linkbutton></td>
						</tr>
						<tr>
							<td class="noChildrenButton"><asp:linkbutton runat="server" id="Linkbutton5">noChildrenButton</asp:linkbutton></td>
						</tr>
					</table>
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="rowHeading">rowHeading</td>
							<td class="rowHeading" style="width:50%;">&nbsp;</td>
							<td class="rowHeading" style="text-align:right;"><table cellpadding="0" cellspacing="0" border="0"><tr><td><div id="Div5" runat="server" class="addButton"><a href="#">addButton</a></div></td></tr></table></td>
						</tr>
					</table>
					<br /><br />
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="cellHeading">cellHeading</td>
							<td class="cellHeading">cellHeading</td>
							<td class="cellHeading">cellHeading</td>
						</tr>
						<tr>
							<td class="repeaterItem"><a href="#">repeaterItem</a></td>
							<td class="repeaterItem">repeaterItem</td>
							<td class="repeaterItem">repeaterItem</td>
						</tr>
						<tr>
							<td class="repeaterItemAlt"><a href="#">repeaterItemAlt</a></td>
							<td class="repeaterItemAlt">repeaterItemAlt</td>
							<td class="repeaterItemAlt">repeaterItemAlt</td>
						</tr>
						<tr>
							<td class="repeaterItemSelected"><a href="#">repeaterItemSelected</a></td>
							<td class="repeaterItemSelected">repeaterItemSelected</td>
							<td class="repeaterItemSelected">repeaterItemSelected</td>
						</tr>
						<tr>
							<td class="repeaterTreeItemSelected"><a href="#">repeaterTreeItemSelected</a></td>
							<td class="repeaterTreeItemSelected">repeaterTreeItemSelected</td>
							<td class="repeaterTreeItemSelected">repeaterTreeItemSelected</td>
						</tr>
					</table>
					<br /><br />
				</div>			
			</td>
		</tr>
	</table>
</asp:Content>

