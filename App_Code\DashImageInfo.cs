using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class DashImageInfo : ISerializable
{
	public string FileName = null;
	public string FileDescription = null;

	// Default constructor.
	public DashImageInfo() { }

	// Deserialization constructor.
	public DashImageInfo(SerializationInfo info, StreamingContext context)
	{
		FileName = (string)info.GetValue("fn", typeof(string));
		FileDescription = (string)info.GetValue("fd", typeof(string));
	}

	// Serialization function.
	public void GetObjectData(SerializationInfo info, StreamingContext context)
	{
		info.AddValue("fn", FileName);
		info.AddValue("fd", FileDescription);
	}
}

