using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

[Serializable()]
public class ReportInfo : ISerializable {
    public int ReportId = 0;
    public string ReportName = null;
    public string ChartTitle = null;
    public ReportHelper.ReportTypeEnum ReportTypeId = ReportHelper.ReportTypeEnum.UNSPECIFIED;
    public string ReportTypeName = null;
    public string WizardPageName = null;
    public string RunPageName = null;
    public string PrintPageName = null;
    public string StoredProcName = null;
    public bool AllowFilterLevel3Notes = false;
    public bool AllowFilterFieldType = false;
    public int FolderId = 0;
    public string FolderName = null;
    public bool PromptSessions = false;
    public DateTime FixedStartDate = DateTime.MinValue;
    public DateTime StartDate { get { return CalculateRelativeDate(this.RelativeStartTimeId, FixedStartDate); } }
    public ReportHelper.RelativeTimeEnum RelativeStartTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;
    public DateTime FixedEndDate = DateTime.MinValue;
    public DateTime EndDate { get { return CalculateRelativeDate(this.RelativeEndTimeId, FixedEndDate); } }
    public ReportHelper.RelativeTimeEnum RelativeEndTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;
    public string ReportData = null;
    public string CellSetData = null;
    public DateTime LastSaveDate = DateTime.MinValue;
    public string DimensionName = null;
    public List<string> DimensionMembers = new List<string>();
    public Dictionary<string, List<string>> ReportFilters = new Dictionary<string, List<string>>();
    public List<SessionInfo> AttachedSessions = new List<SessionInfo>();
    public List<CellInfo> CellFilters = new List<CellInfo>();
    public List<DeviceInfo> DeviceFilters = new List<DeviceInfo>();
    public ParetoInfo ParetoInfo = new ParetoInfo();
    public PRSTInfo PRSTInfo = new PRSTInfo();
    public ProgressInfo ProgressInfo = new ProgressInfo();
    public DistributionInfo DistributionInfo = new DistributionInfo();
    public QueryInfo QueryInfo = new QueryInfo();
    public ReportHelper.PrebuiltReportsEnum PrebuildType = ReportHelper.PrebuiltReportsEnum.UNSPECIFIED;
    public bool SubReportsByDimension = false;
    public bool SubReportsBySession = false;
    public bool SubReportsByDevice = false;
    public string MinXScale = null;
    public string MaxXScale = null;
    public string MinYScale = null;
    public string MaxYScale = null;

    // Default constructor.
    public ReportInfo() { }

    // Deserialization constructor.
    public ReportInfo(SerializationInfo info, StreamingContext context) {
        ReportId = (int)info.GetValue("ReportId", typeof(int));
        ReportName = (string)info.GetValue("ReportName", typeof(string));
        ChartTitle = (string)info.GetValue("ChartTitle", typeof(string));
        ReportTypeId = (ReportHelper.ReportTypeEnum)info.GetValue("ReportTypeId", typeof(int));
        ReportTypeName = (string)info.GetValue("ReportTypeName", typeof(string));
        WizardPageName = (string)info.GetValue("WizardPageName", typeof(string));
        RunPageName = (string)info.GetValue("RunPageName", typeof(string));
        PrintPageName = (string)info.GetValue("PrintPageName", typeof(string));
        StoredProcName = (string)info.GetValue("StoredProcName", typeof(string));
        AllowFilterLevel3Notes = (bool)info.GetValue("AllowFilterLevel3Notes", typeof(bool));
        AllowFilterFieldType = (bool)info.GetValue("AllowFilterFieldType", typeof(bool));
        FolderId = (int)info.GetValue("FolderId", typeof(int));
        FolderName = (string)info.GetValue("FolderName", typeof(string));
        PromptSessions = (bool)info.GetValue("PromptSessions", typeof(bool));
        FixedStartDate = (DateTime)info.GetValue("StartDate", typeof(DateTime));
        RelativeStartTimeId = (ReportHelper.RelativeTimeEnum)info.GetValue("RelativeStartId", typeof(int));
        FixedEndDate = (DateTime)info.GetValue("EndDate", typeof(DateTime));
        RelativeEndTimeId = (ReportHelper.RelativeTimeEnum)info.GetValue("RelativeEndId", typeof(int));
        ReportData = (string)info.GetValue("ReportData", typeof(string));
        CellSetData = (string)info.GetValue("CellSetData", typeof(string));
        LastSaveDate = (DateTime)info.GetValue("LastSaveDate", typeof(DateTime));
        DimensionName = (string)info.GetValue("DimensionName", typeof(string));
        DimensionMembers = (List<string>)info.GetValue("DimensionMembers", typeof(List<string>));
        ReportFilters = (Dictionary<string, List<string>>)info.GetValue("ReportFilters", typeof(Dictionary<string, List<string>>));
        AttachedSessions = (List<SessionInfo>)info.GetValue("AttachedSessions", typeof(List<SessionInfo>));
        CellFilters = (List<CellInfo>)info.GetValue("CellFilters", typeof(List<CellInfo>));
        DeviceFilters = (List<DeviceInfo>)info.GetValue("DeviceFilters", typeof(List<DeviceInfo>));
        ParetoInfo = (ParetoInfo)info.GetValue("Pareto", typeof(ParetoInfo));
        PRSTInfo = (PRSTInfo)info.GetValue("PRST", typeof(PRSTInfo));
        ProgressInfo = (ProgressInfo)info.GetValue("Progress", typeof(ProgressInfo));
        DistributionInfo = (DistributionInfo)info.GetValue("Distrib", typeof(DistributionInfo));
        QueryInfo = (QueryInfo)info.GetValue("Query", typeof(QueryInfo));
        SubReportsByDimension = (bool)info.GetValue("SubDim", typeof(bool));
        SubReportsBySession = (bool)info.GetValue("SubSess", typeof(bool));
        SubReportsByDevice = (bool)info.GetValue("SubDev", typeof(bool));
        MinXScale = (string)info.GetValue("MinXScale", typeof(string));
        MaxXScale = (string)info.GetValue("MaxXScale", typeof(string));
        MinYScale = (string)info.GetValue("MinYScale", typeof(string));
        MaxYScale = (string)info.GetValue("MaxYScale", typeof(string));
    }

    // Serialization function.
    public void GetObjectData(SerializationInfo info, StreamingContext context) {
        info.AddValue("ReportId", ReportId);
        info.AddValue("ReportName", ReportName);
        info.AddValue("ChartTitle", ChartTitle);
        info.AddValue("ReportTypeId", (int)ReportTypeId);
        info.AddValue("ReportTypeName", ReportTypeName);
        info.AddValue("WizardPageName", WizardPageName);
        info.AddValue("RunPageName", RunPageName);
        info.AddValue("PrintPageName", PrintPageName);
        info.AddValue("StoredProcName", StoredProcName);
        info.AddValue("AllowFilterLevel3Notes", AllowFilterLevel3Notes);
        info.AddValue("AllowFilterFieldType", AllowFilterFieldType);
        info.AddValue("FolderId", FolderId);
        info.AddValue("FolderName", FolderName);
        info.AddValue("PromptSessions", PromptSessions);
        info.AddValue("StartDate", FixedStartDate);
        info.AddValue("RelativeStartId", RelativeStartTimeId);
        info.AddValue("EndDate", FixedEndDate);
        info.AddValue("RelativeEndId", RelativeEndTimeId);
        info.AddValue("ReportData", ReportData);
        info.AddValue("CellSetData", CellSetData);
        info.AddValue("LastSaveDate", LastSaveDate);
        info.AddValue("DimensionName", DimensionName);
        info.AddValue("DimensionMembers", DimensionMembers);
        info.AddValue("ReportFilters", ReportFilters);
        info.AddValue("AttachedSessions", AttachedSessions);
        info.AddValue("CellFilters", CellFilters);
        info.AddValue("DeviceFilters", DeviceFilters);
        info.AddValue("Pareto", ParetoInfo);
        info.AddValue("PRST", PRSTInfo);
        info.AddValue("Progress", ProgressInfo);
        info.AddValue("Distrib", DistributionInfo);
        info.AddValue("Query", QueryInfo);
        info.AddValue("SubDim", SubReportsByDimension);
        info.AddValue("SubSess", SubReportsBySession);
        info.AddValue("SubDev", SubReportsByDevice);
        info.AddValue("MinXScale", MinXScale);
        info.AddValue("MaxXScale", MaxXScale);
        info.AddValue("MinYScale", MinYScale);
        info.AddValue("MaxYScale", MaxYScale);
    }

    private DateTime CalculateRelativeDate(ReportHelper.RelativeTimeEnum relativeTimeId, DateTime curDate) {
        DateTime retVal = DateTime.MinValue;

        switch (relativeTimeId) {
            case ReportHelper.RelativeTimeEnum.UNSPECIFIED:
                return curDate;
            case ReportHelper.RelativeTimeEnum.TODAY:
                retVal = DateTime.Today;
                break;
            case ReportHelper.RelativeTimeEnum.YESTERDAY:
                retVal = DateTime.Today.AddDays(-1);
                break;
            case ReportHelper.RelativeTimeEnum.PREVIOUS_WORK_DAY:
                if (DateTime.Today.DayOfWeek == DayOfWeek.Sunday)
                    retVal = DateTime.Today.AddDays(-2);
                else if (DateTime.Today.DayOfWeek == DayOfWeek.Monday)
                    retVal = DateTime.Today.AddDays(-3);
                else
                    retVal = DateTime.Today.AddDays(-1);
                break;
            case ReportHelper.RelativeTimeEnum.TWO_DAYS_AGO:
                retVal = DateTime.Today.AddDays(-2);
                break;
            case ReportHelper.RelativeTimeEnum.TWO_WORK_DAYS_AGO:
                if (DateTime.Today.DayOfWeek == DayOfWeek.Monday)
                    retVal = DateTime.Today.AddDays(-3);
                else if (DateTime.Today.DayOfWeek == DayOfWeek.Tuesday)
                    retVal = DateTime.Today.AddDays(-4);
                else
                    retVal = DateTime.Today.AddDays(-2);
                break;
            case ReportHelper.RelativeTimeEnum.TEN_DAYS_AGO:
                retVal = DateTime.Today.AddDays(-10);
                break;
            case ReportHelper.RelativeTimeEnum.TEN_WORK_DAYS_AGO:
                if (DateTime.Today.DayOfWeek == DayOfWeek.Saturday)
                    retVal = DateTime.Today.AddDays(-12);
                else if (DateTime.Today.DayOfWeek == DayOfWeek.Sunday)
                    retVal = DateTime.Today.AddDays(-13);
                else
                    retVal = DateTime.Today.AddDays(-14);
                break;
            case ReportHelper.RelativeTimeEnum.START_CURRENT_WEEK:
                retVal = DateTime.Today.AddDays(-((int)DateTime.Today.DayOfWeek));
                break;
            case ReportHelper.RelativeTimeEnum.START_CURRENT_WORK_WEEK:
                //Sunday is special case, go back 1 full week since there have been no work days in the current week.
                if (DateTime.Today.DayOfWeek == DayOfWeek.Sunday)
                    retVal = DateTime.Today.AddDays(-6);
                else
                    retVal = DateTime.Today.AddDays(-(((int)DateTime.Today.DayOfWeek) - 1));
                break;
            case ReportHelper.RelativeTimeEnum.START_LAST_WEEK:
                retVal = DateTime.Today.AddDays(-(((int)DateTime.Today.DayOfWeek) + 7));
                break;
            case ReportHelper.RelativeTimeEnum.START_LAST_WORK_WEEK:
                if (DateTime.Today.DayOfWeek == DayOfWeek.Sunday)
                    retVal = DateTime.Today.AddDays(-13);
                else
                    retVal = DateTime.Today.AddDays(-((((int)DateTime.Today.DayOfWeek) - 1) + 7));
                break;
            case ReportHelper.RelativeTimeEnum.END_LAST_WEEK:
                retVal = DateTime.Today.AddDays(-(((int)DateTime.Today.DayOfWeek) + 1));
                break;
            case ReportHelper.RelativeTimeEnum.END_LAST_WORK_WEEK:
                retVal = DateTime.Today.AddDays(-(((int)DateTime.Today.DayOfWeek) + 2));
                break;
            case ReportHelper.RelativeTimeEnum.START_CURRENT_MONTH:
                retVal = DateTime.Today.AddDays(-(DateTime.Today.Day - 1));
                break;
            case ReportHelper.RelativeTimeEnum.START_LAST_MONTH:
                DateTime tempDate = DateTime.Today.AddDays(-(DateTime.Today.Day));
                retVal = DateTime.Today.AddDays(-((DateTime.Today.Day) + tempDate.Day - 1));
                break;
            case ReportHelper.RelativeTimeEnum.END_LAST_MONTH:
                retVal = DateTime.Today.AddDays(-(DateTime.Today.Day));
                break;
        }

        retVal = retVal.Add(curDate.TimeOfDay);

        return retVal;
    }
}