<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_Sensor.aspx.cs" Inherits="NewReportWizard_Sensor" %>
<%@ register tagprefix="rpt" tagname="DimensionSelector" src="~/controls/DimensionSelector.ascx" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
</telerik:RadAjaxManager>
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
					
					<table border="0" cellpadding="0" cellspacing="10" style="width:100%;">
						<tr>
							<td style="width:200px;" valign="top">
								<div class="rowHeading">Select Dimension:</div>
								<div class="leftPad" style="padding-top:15px;">
									The selected dimension(s) will be included as series on the report's chart. 
									<br /><br />
									Any unselected dimension members will be filtered off from display on the final chart.
								</div>
							</td>
							<td valign="top">
							    <rpt:DimensionSelector isrequired="true" id="dimensionSel" runat="server"></rpt:DimensionSelector>
							    <div style="padding: 10px 0px 10px 0px;">
								    <asp:checkbox id="subReportDimCheck" runat="server" text="Generate sub-reports for selected dimensions" />
							    </div>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Grouping:</div></td>
							<td valign="top">
								<asp:radiobuttonlist runat="server" id="groupingList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:radiobuttonlist>
								<asp:requiredfieldvalidator id="val1" runat="server" display="dynamic" controltovalidate="groupingList" cssclass="error"></asp:requiredfieldvalidator>
							</td>
						</tr>
					</table>
					
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>
