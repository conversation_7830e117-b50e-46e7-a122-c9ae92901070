using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using System.Reflection;

public class XmlSerializable
{
    public static object LoadFromXmlText(System.Type type, string documentXml)
    {
        byte[] buffer = System.Text.Encoding.UTF8.GetBytes(documentXml);
        MemoryStream memstream = new MemoryStream(buffer);
        XmlTextReader xmlReader = new XmlTextReader(memstream);
        XmlSerializer serializer = new XmlSerializer(type);

        return serializer.Deserialize(xmlReader);
    }

    public static string ConvertToXml(object obj)
    {
        XmlSerializer serializer = new XmlSerializer(obj.GetType());
        MemoryStream memstream = new MemoryStream();
        XmlTextWriter xmlWriter = new XmlTextWriter(memstream, System.Text.Encoding.UTF8);

        serializer.Serialize(xmlWriter, obj);
        xmlWriter.Close();

        return System.Text.Encoding.UTF8.GetString(memstream.ToArray());
    }
}
