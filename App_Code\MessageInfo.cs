using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization;

[Serializable()]
public class MessageInfo : ISerializable
{
	public int MessageId = 0;
	public bool IsUrgent = false;
	public DateTime LastModified = DateTime.MinValue;
	public int SessionId = 0;
	public string SessionName = null;
	public string CreatedBy = null;
	public string MessageSubject = null;
	public string MessageBody = null;
	
	// Default constructor.
	public MessageInfo() { }

	// Deserialization constructor.
	public MessageInfo(SerializationInfo info, StreamingContext context)
    {
		MessageId = (int)info.GetValue("MessageId", typeof(int));
		IsUrgent = (bool)info.GetValue("IsUrgent", typeof(bool));
		LastModified = (DateTime)info.GetValue("LastModified", typeof(DateTime));
		SessionId = (int)info.GetValue("SessionId", typeof(int));
		SessionName = (string)info.GetValue("SessionName", typeof(string));
		CreatedBy = (string)info.GetValue("CreatedBy", typeof(string));
		MessageSubject = (string)info.GetValue("MessageSubject", typeof(string));
		MessageBody = (string)info.GetValue("MessageBody", typeof(string));
    }

    // Serialization function.
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
		info.AddValue("MessageId", MessageId);
		info.AddValue("IsUrgent", IsUrgent);
		info.AddValue("LastModified", LastModified);
		info.AddValue("SessionId", SessionId);
		info.AddValue("SessionName", SessionName);
		info.AddValue("CreatedBy", CreatedBy);
		info.AddValue("MessageSubject", MessageSubject);
		info.AddValue("MessageBody", MessageBody);
    }
}