using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System.Configuration;
using Atlassian.Jira;
using System.Threading.Tasks;

public class JiraRestApi {
    public JiraRestApi() {
    }

    public static string ValidateConfiguration() {
        var issues = new List<string>();

        string url = ConfigurationManager.AppSettings["JiraRestApi_URL"];
        string username = ConfigurationManager.AppSettings["JiraRestApi_Username"];
        string password = ConfigurationManager.AppSettings["JiraRestApi_Password"];

        if (string.IsNullOrEmpty(url)) {
            issues.Add("JiraRestApi_URL is not configured");
        } else if (!Uri.IsWellFormedUriString(url, UriKind.Absolute)) {
            issues.Add($"JiraRestApi_URL is not a valid URL: {url}");
        }

        if (string.IsNullOrEmpty(username)) {
            issues.Add("JiraRestApi_Username is not configured");
        }

        if (string.IsNullOrEmpty(password)) {
            issues.Add("JiraRestApi_Password is not configured");
        }

        if (issues.Count == 0) {
            return "Configuration is valid";
        } else {
            return "Configuration issues found: " + string.Join(", ", issues);
        }
    }

    public static Jira GetRestClient() {
        try {
            string url = ConfigurationManager.AppSettings["JiraRestApi_URL"];
            string username = ConfigurationManager.AppSettings["JiraRestApi_Username"];
            string password = ConfigurationManager.AppSettings["JiraRestApi_Password"];

            System.Diagnostics.Debug.WriteLine($"Creating Jira REST client for URL: {url}, Username: {username}");

            if (string.IsNullOrEmpty(url) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password)) {
                throw new InvalidOperationException("Jira configuration is incomplete. Please check JiraRestApi_URL, JiraRestApi_Username, and JiraRestApi_Password in web.config");
            }

            //ensure connection is done over SSL/TLS
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            //ignore untrusted SSL certiciates because Diebold certs are not signed by a valid certificate authority
            ServicePointManager.ServerCertificateValidationCallback = (s, cert, chain, ssl) => true;

            Jira api = Jira.CreateRestClient(url, username, password);
            System.Diagnostics.Debug.WriteLine("Jira REST client created successfully");

            return api;
        }
        catch (Exception ex) {
            System.Diagnostics.Debug.WriteLine($"Error creating Jira REST client: {ex.Message}");
            throw new Exception($"Failed to create Jira REST client: {ex.Message}", ex);
        }
    }

    public static async Task<JiraUser> GetUser(string username) {
        try {
            if (string.IsNullOrEmpty(username)) {
                System.Diagnostics.Debug.WriteLine("Username is null or empty, returning null");
                return null;
            }

            System.Diagnostics.Debug.WriteLine($"Getting user: {username}");
            Jira api = GetRestClient();
            var user = await api.Users.GetUserAsync(username);

            System.Diagnostics.Debug.WriteLine($"Successfully retrieved user: {user?.DisplayName ?? "null"}");
            return user;
        }
        catch (Exception ex) {
            System.Diagnostics.Debug.WriteLine($"Error getting user {username}: {ex.Message}");
            throw new Exception($"Failed to retrieve user '{username}': {ex.Message}", ex);
        }
    }

    public static async Task<Issue> GetIssueById(string issueId) {
        try {
            System.Diagnostics.Debug.WriteLine($"Getting issue by ID: {issueId}");

            if (string.IsNullOrEmpty(issueId)) {
                throw new ArgumentException("Issue ID cannot be null or empty");
            }

            Jira api = GetRestClient();
            var issue = await api.Issues.GetIssueAsync(issueId);

            System.Diagnostics.Debug.WriteLine($"Successfully retrieved issue: {issue?.Key?.Value ?? "null"}");
            return issue;
        }
        catch (Exception ex) {
            System.Diagnostics.Debug.WriteLine($"Error getting issue {issueId}: {ex.Message}");
            throw new Exception($"Failed to retrieve issue '{issueId}': {ex.Message}", ex);
        }
    }

    public static async Task<Issue> GetIssueByUrl(string url) {
        System.Diagnostics.Debug.WriteLine("getting issue url");
        //FORMAT: https ://jerry.wincor-nixdorf.com/browse/TRAYTESTSW-868?jql=
        string[] urlParts = url.Split(new string[] { "/" }, StringSplitOptions.RemoveEmptyEntries);
        string issueId = null;
        if (urlParts.Length > 0) {
            issueId = urlParts[urlParts.Length - 1];

            //trim off parameters
            if (issueId.IndexOf("?") > 0) {
                issueId = issueId.Substring(0, issueId.IndexOf("?"));
            }
        }

        if (!string.IsNullOrEmpty(issueId)) {
            Jira api = GetRestClient();
            return await api.Issues.GetIssueAsync(issueId);
        }
        else {
            return null;
        }
    }

    public static string GetJsonForUrl(string url) {
        string retVal = "";

        if (url.ToLower().StartsWith("https")) {
            //ensure connection is done over SSL/TLS
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            //ignore untrusted SSL certiciates because Diebold certs are not signed by a valid certificate authority
            ServicePointManager.ServerCertificateValidationCallback = (s, cert, chain, ssl) => true;
        }

        Uri uri = new Uri(url);
        WebRequest request = WebRequest.Create(uri);
        request.Method = "GET";

        //REST basic authentication
        var credentialBuffer = new UTF8Encoding().GetBytes(ConfigurationManager.AppSettings["JiraRestApi_Username"] + ":" + ConfigurationManager.AppSettings["JiraRestApi_Password"]);
        request.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(credentialBuffer));

        try {
            WebResponse response = request.GetResponse();
            using (Stream responseStream = response.GetResponseStream()) {
                StreamReader reader = new StreamReader(responseStream, Encoding.UTF8);
                retVal = reader.ReadToEnd();
            }
        }
        catch (WebException ex) {
            WebResponse errorResponse = ex.Response;
            if (errorResponse != null) {
                using (Stream responseStream = errorResponse.GetResponseStream()) {
                    StreamReader reader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
                    string errorText = reader.ReadToEnd();
                    retVal = "Error1 - " + errorText + " : " + ex.Message;
                }
            }
            else {
                retVal = "Error2 - " + ex.Message;
            }
        }
        return retVal;
    }
}