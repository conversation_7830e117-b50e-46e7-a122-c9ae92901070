﻿using System;
using System.IO;
using System.Net;
using System.Text;
using System.Configuration;
using Atlassian.Jira;
using System.Threading.Tasks;

public class JiraRestApi {
    public JiraRestApi() {
    }

    public static Jira GetRestClient() {
        Jira api = Jira.CreateRestClient(ConfigurationManager.AppSettings["JiraRestApi_URL"], ConfigurationManager.AppSettings["JiraRestApi_Username"], ConfigurationManager.AppSettings["JiraRestApi_Password"]);
        System.Diagnostics.Debug.WriteLine("Hello there");
        //ensure connection is done over SSL/TLS
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

        //ignore untrusted SSL certiciates because Diebold certs are not signed by a valid certificate authority
        ServicePointManager.ServerCertificateValidationCallback = (s, cert, chain, ssl) => true;

        return api;
    }

    public static async Task<JiraUser> GetUser(string username) {
        if (!string.IsNullOrEmpty(username)) {
            Jira api = GetRestClient();
            return await api.Users.GetUserAsync(username);
        }
        else {
            return null;
        }
    }

    public static async Task<Issue> GetIssueById(string issueId) {
        System.Diagnostics.Debug.WriteLine("gettingIssueID");
        Jira api = GetRestClient();
        return await api.Issues.GetIssueAsync(issueId);
    }

    public static async Task<Issue> GetIssueByUrl(string url) {
        System.Diagnostics.Debug.WriteLine("getting issue url");
        //FORMAT: https ://jerry.wincor-nixdorf.com/browse/TRAYTESTSW-868?jql=
        string[] urlParts = url.Split(new string[] { "/" }, StringSplitOptions.RemoveEmptyEntries);
        string issueId = null;
        if (urlParts.Length > 0) {
            issueId = urlParts[urlParts.Length - 1];

            //trim off parameters
            if (issueId.IndexOf("?") > 0) {
                issueId = issueId.Substring(0, issueId.IndexOf("?"));
            }
        }

        if (!string.IsNullOrEmpty(issueId)) {
            Jira api = GetRestClient();
            return await api.Issues.GetIssueAsync(issueId);
        }
        else {
            return null;
        }
    }

    public static string GetJsonForUrl(string url) {
        string retVal = "";

        if (url.ToLower().StartsWith("https")) {
            //ensure connection is done over SSL/TLS
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            //ignore untrusted SSL certiciates because Diebold certs are not signed by a valid certificate authority
            ServicePointManager.ServerCertificateValidationCallback = (s, cert, chain, ssl) => true;
        }

        Uri uri = new Uri(url);
        WebRequest request = WebRequest.Create(uri);
        request.Method = "GET";

        //REST basic authentication
        var credentialBuffer = new UTF8Encoding().GetBytes(ConfigurationManager.AppSettings["JiraRestApi_Username"] + ":" + ConfigurationManager.AppSettings["JiraRestApi_Password"]);
        request.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(credentialBuffer));

        try {
            WebResponse response = request.GetResponse();
            using (Stream responseStream = response.GetResponseStream()) {
                StreamReader reader = new StreamReader(responseStream, Encoding.UTF8);
                retVal = reader.ReadToEnd();
            }
        }
        catch (WebException ex) {
            WebResponse errorResponse = ex.Response;
            if (errorResponse != null) {
                using (Stream responseStream = errorResponse.GetResponseStream()) {
                    StreamReader reader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
                    string errorText = reader.ReadToEnd();
                    retVal = "Error1 - " + errorText + " : " + ex.Message;
                }
            }
            else {
                retVal = "Error2 - " + ex.Message;
            }
        }
        return retVal;
    }
}