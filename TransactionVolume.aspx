<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="TransactionVolume.aspx.cs" Inherits="TransactionVolume" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">     
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= RadGrid1.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
</script>

<telerik:radwindowmanager runat="server" id="RadWindowManager" height="400" width="600" modal="true" title="Diebold Reporting" 
	behaviors="Close,Reload" reloadonshow="true" visiblestatusbar="false" destroyonclose="true"></telerik:radwindowmanager>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Transaction Volume</td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">
				<table border="0" cellpadding="0" cellspacing="0">
					<tr>	
						<td style="width:100px;padding-top:12px;padding-left:10px;"><div class="goButton" id="AddButtonDiv" runat="server"><asp:linkbutton id="addbtn" runat="server" onclick="AddButton_Click">Add Record</asp:linkbutton></div></td>
						<td>&nbsp;</td>
					</tr>
				</table>
				<br />
				<table border="0" cellpadding="0" cellspacing="0" style="width:100%;">
					<tr>
						<td style="width:250px;" class="rowHeading">Session:</td>
						<td class="rowHeading">Cell:</td>
					</tr>
					<tr>
						<td style="width:250px;padding-top:10px;">
							<asp:dropdownlist id="sessionList" autopostback="true" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:dropdownlist>
						</td>
						<td style="padding-top:10px;">
							<asp:dropdownlist id="cellList" autopostback="true" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:dropdownlist>
						</td>
					</tr>
				</table>
				<br />
				
				<telerik:radgrid id="RadGrid1" allowmultirowselection="false" allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" autogenerateeditcolumn="false" allowsorting="true">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="repeaterItem" />
					<alternatingitemstyle cssclass="repeaterItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings enablerowhoverstyle="true" resizing-allowcolumnresize="true">
						<selecting allowrowselect="true" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="TranDate" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="Transaction Date" sortexpression="TranDate" uniquename="TranDate">
								<itemtemplate>
									<%# DataFormatter.FormatDate(Container.DataItem, "TranDate", "M/dd/yyyy \r\n h:mm:ss tt", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Cell" sortexpression="CellName" uniquename="CellName">
								<headerstyle />
								<itemtemplate>
									<asp:label id="cellLabel" runat="server" tooltip='<%# DataFormatter.Format(Container.DataItem, "CellName", "", false, false)%>'><%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "CellName", "", true, false), 26, 35, true)%></asp:label>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridboundcolumn datafield="CumTranCount" headertext="Cumulative Tran Count" sortexpression="CumTranCount" uniquename="CumTranCount">
							</telerik:gridboundcolumn>
							<telerik:gridboundcolumn datafield="CumMediaCount" headertext="Cumulative Media Count" sortexpression="CumMediaCount" uniquename="CumMediaCount">
							</telerik:gridboundcolumn>
							<telerik:gridtemplatecolumn headertext="">
								<itemtemplate>
									<div class="goButton" runat="server" id="EditBtnDiv" visible='<%# this.IsUserAdmin %>'><asp:linkbutton id="editBtn" runat="server" commandargument='<%# DataFormatter.FormatDate(Container.DataItem, "TranDate", "MM/dd/yyyy hh:mm:ss tt", "")%>' commandname='<%# DataFormatter.Format(Container.DataItem, "CellId") %>' oncommand="EditButton_Click">Edit</asp:linkbutton></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="">
								<itemtemplate>
									<div class="goButton" runat="server" id="DeleteBtnDiv" visible='<%# this.IsUserAdmin %>'><asp:linkbutton onclientclick="return confirm('Are you sure you wish to delete this?');" id="delBtn" runat="server" commandargument='<%# DataFormatter.FormatDate(Container.DataItem, "TranDate", "MM/dd/yyyy hh:mm:ss tt", "")%>' commandname='<%# DataFormatter.Format(Container.DataItem, "CellId") %>' oncommand="DeleteButton_Click">Delete</asp:linkbutton></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>
					</mastertableview>
				</telerik:radgrid>
				
				<br />
				<br />
			</div>
		</td>
	</tr>
</table>

</asp:Content>

