<%@ page language="C#" masterpagefile="~/MasterPage.master" autoeventwireup="true" codefile="Search.aspx.cs" inherits="Search" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:content id="Content1" contentplaceholderid="BodyContent" runat="Server">
	<script type="text/javascript">
		function ToggleStartDatePopup() { $find("<%= startDateField.ClientID %>").showPopup(); }  
		function ToggleEndDatePopup() { $find("<%= endDateField.ClientID %>").showPopup(); }
		function ToggleStartTimePopup()
		{
			var picker = $find("<%= startTimeField.ClientID %>");
			picker.showTimePopup();
		}   
		function ToggleEndTimePopup()
		{
			var picker = $find("<%= endTimeField.ClientID %>");
			picker.showTimePopup();
		}   

		var tableView = null;
		var focusInPagingControl = false;
		function pageLoad(sender, args)
		{
			tableView = $find("<%= RadGrid1.ClientID %>").get_masterTableView();
		}
		function changePage(argument)
		{
			tableView.page(argument);
		}
		function RadNumericTextBox1_ValueChanged(sender, args) {
		    window.scrollTo(0, 0);
            tableView.page(sender.get_value());
        }
        function RadNumericTextBox1_Focus(sender, args) {
            focusInPagingControl = true;
        }
        function RadNumericTextBox1_Blur(sender, args) {
            focusInPagingControl = false;
        }
        function SearchButton_ClientClick(sender, args) {
            if (focusInPagingControl) {
                document.getElementById('<%= searchField.ClientID %>').focus();
                return false;
            } else {
                return true;
            }
        }
	    function ShowJiraSyncMessage() {
	        $('#jiraMessageDiv').slideDown();
	        setTimeout(function () { $('#jiraMessageDiv').slideUp(); }, 10000);
	    }
	</script>

	<asp:panel id="DefaultPanel" runat="server" defaultbutton="searchButton">
        <div id="jiraMessageDiv" style="display:none; background-color:#369347; color:#fff; padding:10px;">An update has been started to sync the below grid items with Jira. This process will run in the background and may take some time to complete.</div>

		<table width="100%" border="0" cellpadding="0" cellspacing="15">
			<tr>
				<td>
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="widgetTitle">Transactions</td>
							<td class="widgetTop" style="width: 20%;">&nbsp;</td>
							<td class="widgetTop" style="text-align: right;">
								<table cellpadding="0" cellspacing="0" border="0">
                                    <tr>
                                        <td style="width: 77px;" id="Td1" runat="server">
											<div class="goButtonTop">
												<asp:linkbutton runat="server" id="JiraSyncButton" onclick="JiraSyncButton_Click" tooltip="Sync Grid Observations with Jira">Jira Sync</asp:linkbutton></div>
										</td>
                                        <td style="width: 10px;">&nbsp;</td>
                                        <td style="width: 75px;" id="massEditButtonContainer" runat="server">
											<div class="goButtonTop">
												<asp:linkbutton runat="server" id="EditGridButton" onclick="EditGridButton_Click" tooltip="Edit Grid Observations">Edit Grid</asp:linkbutton></div>
										</td>
										<td style="width: 10px;">&nbsp;</td>
                                        <td style="width: 90px;">
											<div class="goButtonTop">
												<asp:linkbutton runat="server" id="exportButton" onclick="ExportButton_Click" tooltip="Export Grid Observations">Export Grid</asp:linkbutton></div>
										</td>
										<td style="width: 10px;">&nbsp;</td>
										<td style="width: 90px;">
											<div class="goButtonTop">
												<asp:linkbutton runat="server" id="DownloadEngBtn" onclick="DownloadEngBtn_Click" tooltip="Export Engineering Files">Export Eng.</asp:linkbutton></div>
										</td>
										<td style="width: 10px;">&nbsp;</td>
										<td style="width: 90px;">
											<div class="goButtonTop">
												<asp:linkbutton runat="server" id="DownloadXMLBtn" onclick="DownloadXMLBtn_Click" tooltip="Export XML">Export XML</asp:linkbutton></div>
										</td>
										<td style="width: 10px;">&nbsp;</td>
										<td style="width: 110px;" nowrap="nowrap">
											<div id="PastSessionToggleButton" runat="server" class="addButtonTop">
												<asp:linkbutton runat="server" id="includePastSeriesLink" onclick="IncludePastSessions_Toggle" tooltip="Include Closed Sessions" onclientclick="return confirm('Warning: Including closed sessions can create extremely long search times. Do you wish to proceed?')">Include Closed</asp:linkbutton>
                                                <asp:linkbutton runat="server" id="excludePastSeriesLink" onclick="IncludePastSessions_Toggle" tooltip="Exclude Closed Sessions" visible="false">Exclude Closed</asp:linkbutton>
											</div>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					<div class="widget">
						<div class="title">Search <asp:label id="sessionsLabel" runat="server">Active Sessions</asp:label></div>
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td valign="top">
									<table border="0" cellpadding="0" cellspacing="10" style="width:100%;">
										<tr>
											<td style="width:200px;" valign="top"><div class="rowHeading">Text Search:</div></td>
											<td valign="top" style="padding-left:14px;">
												<asp:textbox id="searchField" runat="server" width="250"></asp:textbox>
											</td>
										</tr>
										<tr>
											<td style="width:200px;" valign="top">
												<div class="rowHeading">Session:</div>
											</td>
											<td valign="top">
												<asp:listbox id="sessionList" selectionmode="multiple" runat="server" width="400px" rows="6" cssclass="entryControl" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code"></asp:listbox>
											</td>
										</tr>
										<tr>
											<td style="width:200px;" valign="top">
												<div class="rowHeading">Date/Time Range:</div>
											</td>
											<td valign="top">
												<telerik:raddatepicker id="startDateField" runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" cssclass="entryControl" style="padding-top: 1px;" width="80px">
													<calendar skin="Default2006" showrowheaders="false"></calendar>
													<datepopupbutton visible="False"></datepopupbutton>
													<dateinput onclick="ToggleStartDatePopup()"></dateinput>
												</telerik:raddatepicker>
												<telerik:radtimepicker id="startTimeField" runat="server" timepopupbutton-visible="false" width="80">
													<dateinput onclick="ToggleStartTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
													<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
												</telerik:radtimepicker>
												&nbsp;
												<telerik:raddatepicker id="endDateField" runat="server" cssclass="entryControl" style="padding-top: 1px;" width="80px">
													<calendar skin="Default2006" showrowheaders="false"></calendar>
													<datepopupbutton visible="False"></datepopupbutton>
													<dateinput onclick="ToggleEndDatePopup()"></dateinput>
												</telerik:raddatepicker>
												<telerik:radtimepicker id="endTimeField" runat="server" timepopupbutton-visible="false" width="80">
													<dateinput onclick="ToggleEndTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
													<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
												</telerik:radtimepicker>
											</td>
										</tr>
										<tr>
											<td style="width:200px;" valign="top"><div class="rowHeading">Transaction Id Range:</div></td>
											<td valign="top" style="padding-left:14px;">
												<asp:textbox id="trxIdStartField" runat="server" width="50"></asp:textbox>
												<asp:comparevalidator id="val4" runat="server" display="dynamic" controltovalidate="trxIdStartField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="*"></asp:comparevalidator>&nbsp;
												<asp:textbox id="trxIdEndField" runat="server" width="50"></asp:textbox>
												<asp:comparevalidator id="val5" runat="server" display="dynamic" controltovalidate="trxIdEndField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="* Invalid format, value must be an integer"></asp:comparevalidator>
											</td>
										</tr>
										<tr>
											<td style="width:200px;" valign="top"><div class="rowHeading">Transactions:</div></td>
											<td valign="top" style="padding-left:14px;">
												<asp:checkbox id="onlyShowObservationsCheck" checked="true" runat="server" text="Only show Observations"></asp:checkbox>
											</td>
										</tr>
									</table>
									
									<table border="0" cellpadding="0" cellspacing="0" class="leftPad" style="padding-top:10px;">
										<tr>
											<td style="width: 100px;">
												<div class="goButton">
													<asp:linkbutton runat="server" id="searchButton" onclientclick="return SearchButton_ClientClick();" onclick="SearchButton_Click">Search</asp:linkbutton></div>
											</td>
											<td style="width: 140px;">
												<div class="goButton">
													<asp:linkbutton runat="server" id="AdvancedSearchButton" onclick="AdvancedSearch_Click" >Advanced Search</asp:linkbutton></div>
											</td>
											<td>
												<div class="goButton" runat="server" id="newObserDiv">
													<asp:linkbutton runat="server" postbackurl="~/editobservation.aspx" id="Linkbutton1" causesvalidation="false">New Observation</asp:linkbutton></div>
											</td>
										</tr>
									</table>
								</td>
								<td>&nbsp;</td>
							</tr>
						</table>
						<br /><br />
						<table border="0" cellpadding="10" cellspacing="0" width="100%" id="AdvancedSearchBar" visible="false" runat="server">
							<tr>
								<td class="rowHeading" colspan="3">Advanced Search Filters</td>
							</tr>
							<tr>
								<td style="vertical-align:top; padding-left:14px;">
									<div id="adv_SessionNameDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Session Name: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_sessionNameLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="btn1" commandargument='<%# DieboldConstants.ADV_SEARCH_SESSION %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_TrxIdStartDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Start Trx Id Range: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_trxIdStartLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton2" commandargument='<%# DieboldConstants.ADV_SEARCH_TRANSACTION_START %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_TrxIdEndDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>End Trx Id Range: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_trxIdEndLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton10" commandargument='<%# DieboldConstants.ADV_SEARCH_TRANSACTION_END %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>									
									<div id="adv_CellDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Cell: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_cellLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton11" commandargument='<%# DieboldConstants.ADV_SEARCH_CELL %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_DeviceTypeDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Device Type: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_deviceTypeLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton12" commandargument='<%# DieboldConstants.ADV_SEARCH_DEVICE_TYPE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_DeviceDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Device: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_deviceLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton13" commandargument='<%# DieboldConstants.ADV_SEARCH_DEVICE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_FailureLocationDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Failure Location: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_failureLocationLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton14" commandargument='<%# DieboldConstants.ADV_SEARCH_FAILURE_LOCATION %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_FailureTypeDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Failure Type: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_failureTypeLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton15" commandargument='<%# DieboldConstants.ADV_SEARCH_FAILURE_TYPE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
                                    <div id="adv_ModuleTypeDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Module Type: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_ModuleTypeLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton26" commandargument='<%# DieboldConstants.ADV_SEARCH_MODULE_TYPE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_InvestigationArea" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Investigation Area: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_investigationAreaLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton16" commandargument='<%# DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Discipline" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Discipline: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_DisciplineLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton21" commandargument='<%# DieboldConstants.DISCIPLINE_ID_KEY %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_TriageType" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Triage Type: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_TriageTypeLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton22" commandargument='<%# DieboldConstants.ADV_SEARCH_TRIAGE_TYPE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Owner" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Owner: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_ownerLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton20" commandargument='<%# DieboldConstants.ADV_SEARCH_OWNER %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Operator" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Operator: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_operatorLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton25" commandargument='<%# DieboldConstants.ADV_SEARCH_OPERATOR %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
                                    <div id="adv_projectedStartDate" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Implementaion Projected Start: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_projectedStartDateLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton23" commandargument='<%# DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
                                    <div id="adv_projectedEndDate" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Implementaion Projected End: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_projectedEndDateLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton24" commandargument='<%# DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_MediaNumber" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Media Number: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_mediaNumberLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton17" commandargument='<%# DieboldConstants.ADV_SEARCH_MEDIA_NUMBER %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_FileNumber" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>File Number: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_fileNumberLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton18" commandargument='<%# DieboldConstants.ADV_SEARCH_FILE_NUMBER %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>									
									<div id="adv_CommandNumber" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Command Number: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_commandNumberLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton19" commandargument='<%# DieboldConstants.ADV_SEARCH_COMMAND_NUMBER %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>									
									<div id="adv_Event" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Event: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_eventLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton3" commandargument='<%# DieboldConstants.ADV_SEARCH_EVENT %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Statistic" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Statistics: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_statisticLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton4" commandargument='<%# DieboldConstants.ADV_SEARCH_STATISTIC_NAME %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Setting" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Setting: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_settingLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton5" commandargument='<%# DieboldConstants.ADV_SEARCH_SETTING %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Setting2" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Setting 2: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_settingLabel2"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton6" commandargument='<%# DieboldConstants.ADV_SEARCH_SETTING2 %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_Setting3" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Setting 3: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_settingLabel3"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton7" commandargument='<%# DieboldConstants.ADV_SEARCH_SETTING3 %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_MinQuantizedValue" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Minimum Value: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_minQuantizedLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton8" commandargument='<%# DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
									<div id="adv_MaxQuantizedValue" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Maximum Value: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_maxQuantizedLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton9" commandargument='<%# DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
                                    <div id="adv_SolutionStateDiv" runat="server" visible="false">
										<table cellpadding="0" cellspacing="0" border="0" width="100%" class="gridItem">
											<tr>
												<td style="width:130px;"><b>Solution States: </b></td>
												<td style="width:400px;"><asp:label runat="server" id="adv_SolutionStateLabel"></asp:label></td>
												<td><div class="cancelButton"><asp:linkbutton id="Linkbutton27" commandargument='<%# DieboldConstants.ADV_SEARCH_SOLUTION_STATE %>' oncommand="ClearSessionValue_Command" runat="server">Remove</asp:linkbutton></div></td>
											</tr>
										</table>
									</div>
								</td>
							</tr>
						</table>
						<telerik:radgrid id="RadGrid1" allowmultirowselection="false" allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
							autogeneratecolumns="False" datasourceid="SearchDataSource" showstatusbar="false" autogenerateeditcolumn="false" allowsorting="true">
							
							<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
							<headerstyle cssclass="gridHeading" />
							<itemstyle cssclass="repeaterItem" />
							<alternatingitemstyle cssclass="repeaterItemAlt" />
							<edititemstyle cssclass="gridItemSelected" />
							<clientsettings enablerowhoverstyle="true" resizing-allowcolumnresize="true">
								<selecting allowrowselect="false" />
							</clientsettings>

							<mastertableview commanditemdisplay="None" datakeynames="TranDate" editmode="EditForms" autogeneratecolumns="False" width="100%">
								<columns>
									<telerik:gridtemplatecolumn headertext="Transaction Date" sortexpression="TranDate" uniquename="TranDate">
										<headerstyle width="12%" />
										<itemtemplate>
											<%# DataFormatter.FormatDate(Container.DataItem, "TranDate", "MM/dd/yy hh:mm tt", "")%>
										</itemtemplate>
									</telerik:gridtemplatecolumn>
									<telerik:gridboundcolumn datafield="DeviceFullName" headertext="Device" sortexpression="DeviceFullName" uniquename="DeviceFullName">
										<headerstyle width="15%" />
									</telerik:gridboundcolumn>
									<telerik:gridtemplatecolumn headertext="Session Name" sortexpression="SessionName" uniquename="SessionName">
										<itemtemplate>
											<asp:label id="SessionName" runat="server" tooltip=' <%# HttpContext.Current.Server.HtmlDecode(DataFormatter.Format(Container.DataItem, "SessionName")) %>'>
												<%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "SessionName", "", true, false), 26, 35, true)%>&nbsp;</asp:label>
										</itemtemplate>
									</telerik:gridtemplatecolumn>
									<telerik:gridboundcolumn datafield="RDToolTranId" headertext="Trx ID" sortexpression="RDToolTranId" uniquename="RDToolTranId">
										<headerstyle width="7%" />
									</telerik:gridboundcolumn>
									<telerik:gridboundcolumn datafield="FailureTypeName" headertext="Failure Type" sortexpression="FailureTypeName" uniquename="FailureTypeName">
										<headerstyle width="12%" />
									</telerik:gridboundcolumn>
									<telerik:gridtemplatecolumn headertext="Observation" columneditorid="5" sortexpression="ObservationText" uniquename="ObservationText">
										<headerstyle width="16%" />
										<itemtemplate>
											<asp:label runat="server" id="lblUnitPrice" text='<%# DataFormatter.TruncateString(DataFormatter.Format(Container.DataItem, "ObservationText", "", true, false), 18, 28, true) %>'></asp:label>&nbsp;
										</itemtemplate>
									</telerik:gridtemplatecolumn>
									<telerik:gridtemplatecolumn headertext="Link">
										<headerstyle width="5%" />
										<itemtemplate>
											<%# !string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "Link")) ? "<a style=\"padding-left:0px;\" href=\"" + DataFormatter.Format(Container.DataItem, "Link") + "\" target=\"_blank\">Link</a>" : "&nbsp;"%>&nbsp;
										</itemtemplate>
									</telerik:gridtemplatecolumn>
									<telerik:gridtemplatecolumn headertext="Image">
										<headerstyle width="5%" />
										<itemtemplate>
											<center><%# (!string.IsNullOrEmpty(DataFormatter.Format(Container.DataItem, "PictureCount")) && !DataFormatter.Format(Container.DataItem, "PictureCount").Equals("0")) ? "<img src=\"images/imageIcon.gif\" alt=\"Images\" />" : "&nbsp;"%>&nbsp;</center>
										</itemtemplate>
									</telerik:gridtemplatecolumn>
									<telerik:gridtemplatecolumn headertext="">
										<headerstyle width="7%" />
										<itemtemplate>
											<div class="goButton" runat="server" id="editDiv">
                                                <a href='<%# !DataFormatter.Format(Container.DataItem, "ObservationCount").Equals("0") ? "EditObservation.aspx?o=" + DataFormatter.Format(Container.DataItem, "ObservationId") : "EditTransaction.aspx?t=" + DataFormatter.Format(Container.DataItem, "TranId") %>'>Details</a>
											</div>
										</itemtemplate>
									</telerik:gridtemplatecolumn>
								</columns>
								
								<PagerTemplate>
									<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
										<span style="float: right; padding-top:4px;">
											Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
											of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
											items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
											to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
											of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
										<p style="margin: 0px; padding: 0px;">
											<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
											&nbsp;&nbsp;
											<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
											&nbsp;&nbsp;
												<span style="vertical-align: middle;">Page:</span>
												<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
													Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
													runat="server">
													<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" OnFocus="RadNumericTextBox1_Focus" OnBlur="RadNumericTextBox1_Blur" />
													<NumberFormat DecimalDigits="0" />
												</telerik:RadNumericTextBox>
												<span style="vertical-align: middle;">of
													<%# DataBinder.Eval(Container, "Paging.PageCount")%>
												</span>
											&nbsp;&nbsp;
											<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
											&nbsp;&nbsp;
											<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
										</p>
									</asp:Panel>
								</PagerTemplate>
							</mastertableview>
						</telerik:radgrid>
						<asp:hiddenfield id="hidIncludeClosed" runat="server" value="false" />
						
						<asp:objectdatasource id="SearchDataSource" runat="server" typename="TransactionSource" selectmethod="GetTransactions">
							<selectparameters>
								<asp:parameter defaultvalue="true" name="allowLiteSearch" type="boolean" />
                                <asp:controlparameter controlid="onlyShowObservationsCheck" propertyname="Checked" name="onlyShowObservations" type="boolean" />
								<asp:controlparameter controlid="searchField" propertyname="Text" name="observationText" type="string" />
								<asp:controlparameter controlid="startDateField" propertyname="SelectedDate" name="startDate" type="datetime" />
								<asp:controlparameter controlid="endDateField" propertyname="SelectedDate" name="endDate" type="datetime" />
								<asp:controlparameter controlid="startTimeField" propertyname="SelectedDate" name="startTime" type="datetime" />
								<asp:controlparameter controlid="endTimeField" propertyname="SelectedDate" name="endTime" type="datetime" />
								<asp:controlparameter controlid="trxIdStartField" propertyname="Text" name="transactionIdStart" type="Int32" />
								<asp:controlparameter controlid="trxIdEndField" propertyname="Text" name="transactionIdEnd" type="Int32" />
								<asp:controlparameter controlid="hidIncludeClosed" propertyname="Value" name="includePastSessions" type="boolean" />
								<asp:sessionparameter sessionfield="SES" name="sessionList" type="string" />
                                <asp:sessionparameter sessionfield="CEL" name="cellList" type="string" />
								<asp:sessionparameter sessionfield="DET" name="deviceTypeId" type="Int32" />
								<asp:sessionparameter sessionfield="DEV" name="deviceList" type="string" />
								<asp:sessionparameter sessionfield="FAL" name="failureLocationList" type="string" />
								<asp:sessionparameter sessionfield="FAI" name="failureTypeList" type="string" />
                                <asp:sessionparameter sessionfield="MOD" name="moduleTypeList" type="string" />
								<asp:sessionparameter sessionfield="INV" name="investigationAreaList" type="string" />
								<asp:sessionparameter sessionfield="TRT" name="triageTypeList" type="string" />
								<asp:sessionparameter sessionfield="DIS" name="disciplineList" type="string" />
								<asp:sessionparameter sessionfield="OWR" name="ownerList" type="string" />
								<asp:sessionparameter sessionfield="OPP" name="operatorList" type="string" />
								<asp:sessionparameter sessionfield="EVT" name="eventList" type="string" />
								<asp:sessionparameter sessionfield="MNM" name="mediaNumberList" type="string" />
								<asp:sessionparameter sessionfield="FNM" name="fileNumberList" type="string" />
								<asp:sessionparameter sessionfield="CNM" name="commandNumberList" type="string" />
								<asp:sessionparameter sessionfield="STF" name="statisticFieldsList" type="string" />
								<asp:sessionparameter sessionfield="STO" name="statisticFieldOptionsList" type="string" />
								<asp:sessionparameter sessionfield="SET" name="settingList" type="string" />
								<asp:sessionparameter sessionfield="SET2" name="settingList2" type="string" />
								<asp:sessionparameter sessionfield="SET3" name="settingList3" type="string" />
								<asp:sessionparameter sessionfield="MIQ" name="minQuantizedValue" type="Decimal" defaultvalue="-79228162514264337593543950335" />
								<asp:sessionparameter sessionfield="MAQ" name="maxQuantizedValue" type="Decimal" defaultvalue="-79228162514264337593543950335" />
                                <asp:sessionparameter sessionfield="PSD" name="projectedImplementationStartDate" type="datetime" />
								<asp:sessionparameter sessionfield="PED" name="projectedImplementationEndDate" type="datetime" />
                                <asp:sessionparameter sessionfield="SOS" name="solutionStateList" type="string" />
                                <asp:parameter defaultvalue="false" name="includeSettings" type="Boolean" />
							</selectparameters>
						</asp:objectdatasource>
						
						<telerik:radajaxmanager id="RadAjaxManager1" runat="server">
							<ajaxsettings>
								<telerik:ajaxsetting ajaxcontrolid="RadGrid1">
									<updatedcontrols>
										<telerik:ajaxupdatedcontrol controlid="RadGrid1" loadingpanelid="RadAjaxLoadingPanel1" />
									</updatedcontrols>
								</telerik:ajaxsetting>
								<telerik:ajaxsetting ajaxcontrolid="RadioButtonList1">
									<updatedcontrols>
										<telerik:ajaxupdatedcontrol controlid="RadGrid1" loadingpanelid="RadAjaxLoadingPanel1" />
										<telerik:ajaxupdatedcontrol controlid="RadioButtonList1" />
									</updatedcontrols>
								</telerik:ajaxsetting>
							</ajaxsettings>
						</telerik:radajaxmanager>
						<telerik:radajaxloadingpanel id="RadAjaxLoadingPanel1" runat="server" height="75px"
							width="75px" transparency="50">
							<img alt="Loading..." src='<%= RadAjaxLoadingPanel.GetWebResourceUrl(Page, "Telerik.Web.UI.Skins.Default.Ajax.loading.gif") %>' style="border: 0;" />
						</telerik:radajaxloadingpanel>
						<br />
						<br />
					</div>
				</td>
			</tr>
		</table>

	</asp:panel>
	
</asp:content>