using System;
using System.Data;
using System.Collections.Generic;
using System.Web.UI;
using System.Web.UI.WebControls;
using QueueServiceClient;

public partial class Pictures : System.Web.UI.Page
{
	public Int64 TranId
	{
		get { if (this.ViewState["t"] != null) return (Int64)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}
	
	public Int64 ObservationId
	{
		get { if (this.ViewState["o"] != null) return (Int64)this.ViewState["o"]; else return 0; }
		set { this.ViewState["o"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["t"]) && Convert.ToInt64(Request.Params["t"]) != 0)
				this.TranId = Convert.ToInt64(Request.Params["t"]); 
			
			if (!string.IsNullOrEmpty(Request.Params["o"]) && Convert.ToInt64(Request.Params["o"]) != 0)
				this.ObservationId = Convert.ToInt64(Request.Params["o"]);

			if (this.ObservationId != 0 && this.TranId == 0)
			{
				DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservation", this.ObservationId);
				if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
					this.TranId = DataFormatter.getInt64(ds.Tables[0].Rows[0], "TranId");
			}

            //load the observation id to keep pictures in sync if there is an observation for this transaction
            if (this.ObservationId == 0 && this.TranId != 0) {
                DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservationInfoForTransaction", this.TranId);
                if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0) {
                    this.ObservationId = DataFormatter.getInt64(ds.Tables[0].Rows[0], "ObservationId");
                }
            }

			if (this.TranId == 0)
				ShowPanel("error");
			else
				LoadPictures();			
		}
    }

	private void LoadPictures()
	{
		PictureList.Items.Clear();

		int pictureIndex = 1;
		List<DataFile> fileList = new List<DataFile>();
        //try {
            fileList = TxFiles.LoadUploadedFiles(fileList, true, this.TranId, this.ObservationId);
        //}
        //catch {
            //ignore file loading issues
        //}

		foreach (DataFile df in fileList) {
			PictureList.Items.Add(new ListItem(pictureIndex.ToString(), string.Format(@"Txfile.aspx?tx=1&{0}=1&{1}={2}&{3}={4}&{5}={6}&{7}={8}&{9}={10}&{11}={12}", 
					DieboldConstants.FILE_IS_IMAGE_KEY, DieboldConstants.FILE_NAME_KEY, df.FileName, DieboldConstants.DEVICE_TYPE_NAME_KEY, 
					df.DeviceTypeName, DieboldConstants.SERIAL_NUMBER_KEY, df.SerialNumber, DieboldConstants.TRAN_DATE_KEY, df.TranDate.ToString("MM/dd/yyyy"), 
					DieboldConstants.TRAN_ID_KEY, this.TranId, DieboldConstants.OBSERVATION_ID_KEY, this.ObservationId)));
			pictureIndex++;
		}

        if (fileList.Count == 0) {
            ShowPanel("nopictures");
        }
	}

	private void popupError(string errorMessage)
	{
		if (!string.IsNullOrEmpty(errorMessage))
		{
			string errorScript = "<script language='javascript'>\r\nalert('" + errorMessage + "');\r\n</script>";
			Page.ClientScript.RegisterStartupScript(typeof(Pictures), "ErrorScript", errorScript);
		}
	}

    protected void ShowUploadButton_Click(object sender, EventArgs e)
	{
		ShowPanel("upload");
	}

	protected void BackButton_Click(object sender, EventArgs e)
	{
		CloseWindow();
	}

	private void CloseWindow()
	{
		string closeScript = "<script language='javascript'>\r\nCloseRadWindow();\r\n</script>";
		Page.ClientScript.RegisterStartupScript(typeof(Pictures), "CloseScript", closeScript);
	}

	private void ShowPanel(string panelName)
	{
		switch (panelName)
		{
			case "nopictures":
				ViewPicturesDiv.Attributes.Add("style", "display:none");
				ErrorDiv.Attributes.Add("style", "display:none");
				NoPicturesDiv.Attributes.Add("style", "display:block");
				break;
			case "picture":
				ViewPicturesDiv.Attributes.Add("style", "display:block");
				ErrorDiv.Attributes.Add("style", "display:none");
				NoPicturesDiv.Attributes.Add("style", "display:none");
				break;
			case "error":
				ViewPicturesDiv.Attributes.Add("style", "display:none");
				ErrorDiv.Attributes.Add("style", "display:block");
				NoPicturesDiv.Attributes.Add("style", "display:none");
				break;
		}
	}
}
