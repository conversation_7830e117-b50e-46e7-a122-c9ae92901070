using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

/// <summary>
/// Summary description for IDashboardItem
/// </summary>
public interface IDashboardItem
{
	void Initialize(Guid itemGuid, bool useMasterUser);
	void DisplayEdit();
    void DisplayContent();
}
