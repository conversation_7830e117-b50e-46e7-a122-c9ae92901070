<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Entity.aspx.cs" Inherits="Entity" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ register tagprefix="inv" tagname="entityitem" src="~/controls/EntityItem.ascx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    var tableView = null;
	function pageLoad(sender, args)
	{
		tableView = $find("<%= EntityGrid.ClientID %>").get_masterTableView();
	}
	function changePage(argument)
	{
		tableView.page(argument);
	}
	function RadNumericTextBox1_ValueChanged(sender, args)
	{
		tableView.page(sender.get_value());
	}
	function ReloadOnClientClose(sender, eventArgs)
	{
		var btn = document.getElementById("<%= hidRebindDataButton.ClientID %>");
		if (btn)
			btn.click();
	}
</script>

<telerik:radwindowmanager runat="server" id="RadWindowManager" height="600" width="800" modal="true" title="Diebold Reporting" 
	behaviors="Close,Reload" reloadonshow="true" OnClientClose="ReloadOnClientClose" visiblestatusbar="false" destroyonclose="true"></telerik:radwindowmanager>

<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle"><asp:label id="EntityNameLabel" runat="server"></asp:label></td>
					<td class="widgetTop" style="width:30%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget" style="padding-bottom:20px;">
				<%--<div style="padding:14px 14px 0px 14px;"><div class="goButton"><a runat="server" id="NewFieldButton">Add Field</a></div></div>--%>
				
				<inv:entityitem id="EntityNavBar" runat="server" showentitylist="true"></inv:entityitem>								
				<br />
				
				<telerik:radgrid id="EntityGrid" allowmultirowselection="false"  onitemdatabound="EntityGrid_OnItemDataBound"
					allowmultirowedit="false" skin="" runat="server" width="100%" pagesize="50" allowpaging="True" 
					autogeneratecolumns="False" showstatusbar="false" allowsorting="true" autogenerateeditcolumn="false">
					
					<pagerstyle mode="NextPrevAndNumeric" alwaysvisible="true" width="100%" position="topandbottom" cssclass="gridPager" />
					<headerstyle cssclass="gridHeading" />
					<itemstyle cssclass="gridItem" />
					<alternatingitemstyle cssclass="gridItemAlt" />
					<edititemstyle cssclass="gridItemSelected" />
					<clientsettings resizing-allowcolumnresize="true">
						<selecting allowrowselect="false" />
					</clientsettings>

					<mastertableview commanditemdisplay="None" datakeynames="FieldId" editmode="EditForms" autogeneratecolumns="False" width="100%">
						<columns>
							<telerik:gridtemplatecolumn headertext="Entity Id" sortexpression="FirstEntity" uniquename="EntityList">
								<headerstyle width="15%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "EntityList", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Field Name" sortexpression="FieldName" uniquename="FieldName">
								<headerstyle width="25%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "FieldName", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Field Type Name" sortexpression="FieldTypeName" uniquename="FieldTypeName">
								<headerstyle width="20%" />
								<itemtemplate>
									<%# DataFormatter.Format(Container.DataItem, "FieldTypeName", "")%>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn headertext="Tracking">
								<headerstyle width="10%" />
								<itemtemplate>
									<asp:label id="TrackingStatusLabel" runat="server"></asp:label>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="20%" />
								<itemtemplate>
									<div class="goButton" style="padding-bottom: 5px;"><a href="<%# string.Format("FieldOptions.aspx?{0}={1}", DieboldConstants.FIELD_ID_KEY, DataFormatter.Format(Container.DataItem, "FieldId", ""))%>">Field Options</a></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
							<telerik:gridtemplatecolumn>
								<headerstyle width="10%" />
								<itemtemplate>
									<div class="goButton"><a onclick="<%# string.Format("window.radopen('Popup_EditField.aspx?{0}={1}&page={2}', null); return false;", DieboldConstants.FIELD_ID_KEY, DataFormatter.Format(Container.DataItem, "FieldId", ""), EntityGrid.CurrentPageIndex) %>">Edit</a></div>
								</itemtemplate>
							</telerik:gridtemplatecolumn>
						</columns>
						
						<PagerTemplate>
							<asp:Panel ID="PagerPanel" Style="padding: 5px 5px 0px 5px;" runat="server">
								<span style="float: right; padding-top:4px;">
									Displaying page <%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.PageCount")%>, 
									items from <%# (int)DataBinder.Eval(Container, "Paging.FirstIndexInPage") + 1 %>
									to <%# (int)DataBinder.Eval(Container, "Paging.LastIndexInPage") + 1 %>
									of <%# DataBinder.Eval(Container, "Paging.DataSourceCount")%>.</span>
								<p style="margin: 0px; padding: 0px;">
									<asp:imagebutton ID="Button1" runat="server" OnClientClick="changePage('first'); return false;" CommandName="Page" CommandArgument="First" imageurl="~/images/PagingFirst.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button2" runat="server" OnClientClick="changePage('prev'); return false;" CommandName="Page" CommandArgument="Prev" imageurl="~/images/PagingPrev.gif" />
									&nbsp;&nbsp;
										<span style="vertical-align: middle;">Page:</span>
										<telerik:RadNumericTextBox ID="RadNumericTextBox1" Skin="Office2007" Width="25px"
											Value='<%# (int)DataBinder.Eval(Container, "Paging.CurrentPageIndex") + 1 %>'
											runat="server">
											<ClientEvents OnValueChanged="RadNumericTextBox1_ValueChanged" />
											<NumberFormat DecimalDigits="0" />
										</telerik:RadNumericTextBox>
										<span style="vertical-align: middle;">of
											<%# DataBinder.Eval(Container, "Paging.PageCount")%>
										</span>
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button3" runat="server" OnClientClick="changePage('next'); return false;" CommandName="Page" CommandArgument="Next" imageurl="~/images/PagingNext.gif" />
									&nbsp;&nbsp;
									<asp:imagebutton ID="Button4" runat="server" OnClientClick="changePage('last'); return false;" CommandName="Page" CommandArgument="Last" imageurl="~/images/PagingLast.gif" />
								</p>
							</asp:Panel>
						</PagerTemplate>

						
					</mastertableview>
				</telerik:radgrid>			
				
				<asp:button runat="server" id="hidRebindDataButton" style="display:none;" onclick="RebindData" />
			</div>
		</td>
	</tr>
</table>

</asp:Content>

