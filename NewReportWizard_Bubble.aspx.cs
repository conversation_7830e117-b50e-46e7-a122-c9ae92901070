﻿using System;
using System.Data;
using System.Configuration;
using System.Collections.Generic;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class NewReportWizard_Bubble : System.Web.UI.Page
{
    public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			this.RepInfo = Utility.GetReportInfoFromTransfer();
			if (this.RepInfo == null)
				Response.Redirect("popuperror.aspx");

            PopulateSessionList(this.RepInfo.AttachedSessions);

			RelativeStartList.DataSource = Utility.GetRelativeTimeList();
			RelativeStartList.DataBind();

			RelativeEndList.DataSource = Utility.GetRelativeTimeList();
			RelativeEndList.DataBind();

		    reportNameLabel.Text = this.RepInfo.ReportName;
			reportTypeLabel.Text = this.RepInfo.ReportTypeName;

			if (this.RepInfo.RelativeStartTimeId != ReportHelper.RelativeTimeEnum.UNSPECIFIED)
			{
				//Relative Time
				this.StartDateTypeList.SelectedIndex = 1;
				StartDateTypeList_IndexChanged(null, null);
				this.RelativeStartList.SelectedValue = ((int)this.RepInfo.RelativeStartTimeId).ToString();

				if (this.RepInfo.FixedStartDate.TimeOfDay != TimeSpan.Zero)
					startTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedStartDate.ToString("hh:mm:ss tt"));
			}
			else
			{
				this.StartDateTypeList.SelectedIndex = 0;

				if (this.RepInfo.FixedStartDate != DateTime.MinValue)
					startDateField.SelectedDate = this.RepInfo.FixedStartDate.Date;

				if (this.RepInfo.FixedStartDate.TimeOfDay != TimeSpan.Zero)
					startTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedStartDate.ToString("hh:mm:ss tt"));
			}

			if (this.RepInfo.RelativeEndTimeId != ReportHelper.RelativeTimeEnum.UNSPECIFIED)
			{
				//Relative Time
				this.EndDateTypeList.SelectedIndex = 1;
				EndDateTypeList_IndexChanged(null, null);
				this.RelativeEndList.SelectedValue = ((int)this.RepInfo.RelativeEndTimeId).ToString();

				if (this.RepInfo.FixedEndDate.TimeOfDay != TimeSpan.Zero)
					endTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedEndDate.ToString("hh:mm:ss tt"));
			}
			else
			{
				this.EndDateTypeList.SelectedIndex = 0;

				if (this.RepInfo.FixedEndDate != DateTime.MinValue)
					endDateField.SelectedDate = this.RepInfo.FixedEndDate.Date;

				if (this.RepInfo.FixedEndDate.TimeOfDay != TimeSpan.Zero)
					endTimeField.SelectedDate = Convert.ToDateTime(this.RepInfo.FixedEndDate.ToString("hh:mm:ss tt"));
			}

			if (this.RepInfo.QueryInfo.MinTranId != 0)
				this.minTranId.Text = this.RepInfo.QueryInfo.MinTranId.ToString();

			if (this.RepInfo.QueryInfo.MaxTranId != 0)
				this.maxTranId.Text = this.RepInfo.QueryInfo.MaxTranId.ToString();

			if (this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.ENA)
			{
				this.acceptLevel3NotesHeading.Visible = true;
				this.acceptLevel3NotesRow.Visible = true;
			}
			else
			{
				this.acceptLevel3NotesHeading.Visible = false;
				this.acceptLevel3NotesRow.Visible = false;
			}
			this.acceptLevel3Check.Checked = this.RepInfo.QueryInfo.AcceptLevel3Notes;

			if (this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.STATISTICS)
			{
				fieldTypeIdList.Items.Add(new ListItem("All", "0"));
				fieldTypeIdList.Items.Add(new ListItem("Engineering Data", DieboldConstants.ENGINEERING_DATA_KEY));
				fieldTypeIdList.Items.Add(new ListItem("Info Entity", DieboldConstants.INFO_ENTITY_KEY));
				fieldTypeIdList.Items.Add(new ListItem("Metric Entity", DieboldConstants.METRIC_ENTITY_KEY));
				fieldTypeIdList.Items.Add(new ListItem("Result Data", DieboldConstants.RESULT_DATA_KEY));
				fieldTypeIdList.Items.Add(new ListItem("Status Entity", DieboldConstants.STATUS_ENTITY_KEY));

				this.fieldTypeIdHeading.Visible = true;
				this.fieldTypeIdRow.Visible = true;

				this.fieldTypeIdList.SelectedValue = this.RepInfo.QueryInfo.FieldTypeId.ToString();
			}
			else
			{
				this.fieldTypeIdHeading.Visible = false;
				this.fieldTypeIdRow.Visible = false;
			}
		}
	}

    private void PopulateSessionList(List<SessionInfo> sessionFilters)
    {
        sessionList.DataSource = Utility.GetActiveSessionsList();
		sessionList.DataBind();

        if (sessionFilters != null)
        {
            foreach (SessionInfo curSession in sessionFilters)
            {
				if (sessionList.Items.FindByValue(curSession.SessionId.ToString()) == null)
					sessionList.Items.Insert(0, new ListItem(curSession.SessionName, curSession.SessionId.ToString()));

				((ListItem)sessionList.Items.FindByValue(curSession.SessionId.ToString())).Selected = true;
				break;
            }
        }
    }

	protected void NextButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			TimeSpan startTime = TimeSpan.Zero;
			TimeSpan endTime = TimeSpan.Zero;

			if (startTimeField.SelectedDate.HasValue)
			{
				startTime = new TimeSpan(((DateTime)startTimeField.SelectedDate).Hour, ((DateTime)startTimeField.SelectedDate).Minute, ((DateTime)startTimeField.SelectedDate).Second);
				if (startTime.Minutes % 30 != 0)
				{
					timeError.Visible = true;
					return;
				}
			}

			if (endTimeField.SelectedDate.HasValue)
			{
				endTime = new TimeSpan(((DateTime)endTimeField.SelectedDate).Hour, ((DateTime)endTimeField.SelectedDate).Minute, ((DateTime)endTimeField.SelectedDate).Second);
				if (endTime.Minutes % 30 != 0)
				{
					timeError.Visible = true;
					return;
				}
			}

			if (StartDateTypeList.SelectedValue.Equals("fixed"))
			{
				this.RepInfo.RelativeStartTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;

				if (startDateField.SelectedDate.HasValue)
					this.RepInfo.FixedStartDate = ((DateTime)startDateField.SelectedDate).Add(startTime);
				else
					this.RepInfo.FixedStartDate = DateTime.MinValue;
			}
			else
			{
				//Relative Time
				if (!string.IsNullOrEmpty(RelativeStartList.SelectedValue))
				{
					this.RepInfo.RelativeStartTimeId = (ReportHelper.RelativeTimeEnum)Convert.ToInt32(RelativeStartList.SelectedValue);
					this.RepInfo.FixedStartDate = Convert.ToDateTime("01/01/1900 " + ((DateTime)startTimeField.SelectedDate).ToString("hh:mm:ss tt"));
					//this.RepInfo.FixedStartDate = new DateTime(1900, 1, 1).Add(startTime);
				}
				else
				{
					this.RepInfo.FixedStartDate = DateTime.MinValue;
				}
			}

			if (EndDateTypeList.SelectedValue.Equals("fixed"))
			{
				this.RepInfo.RelativeEndTimeId = ReportHelper.RelativeTimeEnum.UNSPECIFIED;

				if (endDateField.SelectedDate.HasValue)
					this.RepInfo.FixedEndDate = ((DateTime)endDateField.SelectedDate).Add(endTime);
				else
					this.RepInfo.FixedEndDate = DateTime.MinValue;
			}
			else
			{
				//Relative Time
				if (!string.IsNullOrEmpty(RelativeEndList.SelectedValue))
				{
					this.RepInfo.RelativeEndTimeId = (ReportHelper.RelativeTimeEnum)Convert.ToInt32(RelativeEndList.SelectedValue);
					this.RepInfo.FixedEndDate = Convert.ToDateTime("01/01/1900 " + ((DateTime)endTimeField.SelectedDate).ToString("hh:mm:ss tt")); 
					//this.RepInfo.FixedEndDate = new DateTime(1900, 1, 1).Add(endTime);
				}
				else
				{
					this.RepInfo.FixedEndDate = DateTime.MinValue;
				}
			}

			//set min/max tran ids
			bool hasTranMinFilter = false;
			bool hasTranMaxFilter = false;

			if (!string.IsNullOrEmpty(this.minTranId.Text))
			{
				this.RepInfo.QueryInfo.MinTranId = Convert.ToInt64(this.minTranId.Text);
				hasTranMinFilter = true;
			}
			else
			{
				this.RepInfo.QueryInfo.MinTranId = 0;
			}

			if (!string.IsNullOrEmpty(this.maxTranId.Text))
			{
				this.RepInfo.QueryInfo.MaxTranId = Convert.ToInt64(this.maxTranId.Text);
				hasTranMaxFilter = true;
			}
			else
			{
				this.RepInfo.QueryInfo.MaxTranId = 0;
			}

			//check date range for stat reports to ensure result data is not too large
			//only warn them once though, and allow them to proceed if they still want to submit, or if they are already filtering on TranId
			if (!dataSizeWarning.Visible && this.RepInfo.ReportTypeId == ReportHelper.ReportTypeEnum.STATISTICS && (!hasTranMinFilter && !hasTranMaxFilter))
			{
				int dateInterval = (this.RepInfo.FixedEndDate - this.RepInfo.FixedStartDate).Days;
				if (dateInterval > 1 || dateInterval == 0)
				{
					//don't show warning if selected date is current date
					if ((DateTime.Today - this.RepInfo.FixedStartDate).Days > 1)
					{
						dataSizeWarning.Visible = true;
						return;
					}
				}
			}
            
			this.RepInfo.AttachedSessions = new List<SessionInfo>();
			SessionInfo curSession = new SessionInfo();
			curSession.SessionId = Convert.ToInt32(this.sessionList.SelectedItem.Value);
			curSession.SessionName = this.sessionList.SelectedItem.Text;
			this.RepInfo.AttachedSessions.Add(curSession);

			this.RepInfo.QueryInfo.AcceptLevel3Notes = this.acceptLevel3Check.Checked;

			if (!string.IsNullOrEmpty(this.fieldTypeIdList.SelectedValue))
				this.RepInfo.QueryInfo.FieldTypeId = Convert.ToInt32(this.fieldTypeIdList.SelectedValue);

			this.RepInfo.SubReportsBySession = false;
            this.RepInfo.SubReportsByDevice = false;
            
			Utility.SetReportInfoForTransfer(this.RepInfo);
			
			//Close the window and redirect parent to the Run Report screen.
            string closeScript = "<script language='javascript'>\r\nGetRadWindow().BrowserWindow.document.location.href='" + this.RepInfo.RunPageName + "';\r\nCloseRadWindow(); </script>";
			Page.ClientScript.RegisterStartupScript(typeof(NewReportWizard_Bubble), "CloseScript", closeScript);
		}
	}

	protected void StartDateTypeList_IndexChanged(object sender, EventArgs e)
	{
		if (StartDateTypeList.SelectedValue.Equals("fixed"))
		{
			this.startDateField.Style.Add(HtmlTextWriterStyle.Display, "");
			this.RelativeStartList.Style.Add(HtmlTextWriterStyle.Display, "none");
		}
		else
		{
			this.startDateField.Style.Add(HtmlTextWriterStyle.Display, "none");
			this.RelativeStartList.Style.Add(HtmlTextWriterStyle.Display, "");
		}
	}

	protected void EndDateTypeList_IndexChanged(object sender, EventArgs e)
	{
		if (EndDateTypeList.SelectedValue.Equals("fixed"))
		{
			this.endDateField.Style.Add(HtmlTextWriterStyle.Display, "");
			this.RelativeEndList.Style.Add(HtmlTextWriterStyle.Display, "none");
		}
		else
		{
			this.endDateField.Style.Add(HtmlTextWriterStyle.Display, "none");
			this.RelativeEndList.Style.Add(HtmlTextWriterStyle.Display, "");
		}
	}
}
