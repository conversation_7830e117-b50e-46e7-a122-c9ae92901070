using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditField : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string FieldId
	{
		get { return (string)this.ViewState["f"]; }
		set { this.ViewState["f"] = value; }
	}

	public bool IsTracking
	{
		get { if (this.ViewState["i"] != null) return (bool)this.ViewState["i"]; else return false; }
		set { this.ViewState["i"] = value; }
	}

	public bool IsExtension
	{
		get { if (this.ViewState["ie"] != null) return (bool)this.ViewState["ie"]; else return false; }
		set { this.ViewState["ie"] = value; }
	}

	public string DeviceTypeId
	{
		get { return (string)this.ViewState["d"]; }
		set { this.ViewState["d"] = value; }
	}

	public string EventTypeId
	{
		get { return (string)this.ViewState["ec"]; }
		set { this.ViewState["ec"] = value; }
	}

	public bool IsDisableTrackingRequested
	{
		get { if (this.ViewState["id"] != null) return (bool)this.ViewState["id"]; else return false; }
		set { this.ViewState["id"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			optionsParsingTypeList.DataSource = Utility.GetParsingTypeList();
			optionsParsingTypeList.DataBind();

			dataParsingTypeList.DataSource = Utility.GetParsingTypeList();
			dataParsingTypeList.DataBind();

			fieldStatusList.DataSource = Utility.GetFieldStatusList();
			fieldStatusList.DataBind();

			aggregateTypeList.DataSource = Utility.GetAggregateTypeList();
			aggregateTypeList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.EXTENSION_KEY]) && Request.Params[DieboldConstants.EXTENSION_KEY].Equals("1"))
			{
				this.IsExtension = true;
				EditEntityId_Button.Visible = false;
			}

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FIELD_ID_KEY]))
			{
				this.FieldId = Request.Params[DieboldConstants.FIELD_ID_KEY];
				LoadFieldData();
			}

			if (string.IsNullOrEmpty(this.FieldId))
			{
				if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]))
				{
					this.DeviceTypeId = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];
				}
				else
				{
					ContentRow.Visible = false;
					ErrorRow.Visible = true;
				}
			}
		}
	}

	protected void EditEntityButton_Click(object sender, EventArgs e)
	{
		Response.Redirect(string.Format("Popup_EditFieldEntityVersion.aspx?{0}={1}", DieboldConstants.FIELD_ID_KEY, this.FieldId));
	}

	private void LoadFieldData()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetField", this.FieldId);
		foreach (DataRow row in ds.Tables[0].Rows)
		{
			if ((DataFormatter.getInt32(row, "FieldTypeId") == 1) || (DataFormatter.getInt32(row, "FieldTypeId") == 5) || (DataFormatter.getInt32(row, "FieldTypeId") == 6))
				EditEntityId_Button.Visible = false;

			this.DeviceTypeId = DataFormatter.getInt32(row, "DeviceTypeId").ToString();
			this.fieldNameBox.Text = DataFormatter.getString(row, "FieldName");
			this.fieldTypeNameLabel.Text = DataFormatter.getString(row, "FieldTypeName");
			this.fieldStatusList.SelectedValue = DataFormatter.getInt32(row, "FieldStatusId").ToString();
			this.deviceTypeLabel.Text = DataFormatter.getInt32(row, "DeviceTypeId").ToString() + " - " + DataFormatter.getString(row, "DeviceTypeName");
            this.entityIdLabel.Text = DataFormatter.getString(row, "EntityList");
			this.libraryNameLabel.Text = DataFormatter.getString(row, "LibraryName");
			this.rdToolNameLabel.Text = DataFormatter.getString(row, "RDToolName");

			this.isSensorBox.Checked = DataFormatter.getBool(row, "IsSensor");
            this.reportCommandNumber.Checked = DataFormatter.getBool(row, "ReportCommandNumber");			

			if (DataFormatter.getInt32(row, "BitMask") != 0)
                this.bitMaskLabel.Text = String.Format("0x{0:X8}", DataFormatter.getInt32(row, "BitMask"));

			if (DataFormatter.getInt32(row, "EngineeringFieldId") != 0)
				this.engineeringFieldIdLabel.Text = DataFormatter.getInt32(row, "EngineeringFieldId").ToString();

			if (optionsParsingTypeList.Items.FindByValue(DataFormatter.getString(row, "OptionParsingTypeCode")) != null)
				optionsParsingTypeList.SelectedValue = DataFormatter.getString(row, "OptionParsingTypeCode");

			if (dataParsingTypeList.Items.FindByValue(DataFormatter.getString(row, "DataParsingTypeCode")) != null)
				dataParsingTypeList.SelectedValue = DataFormatter.getString(row, "DataParsingTypeCode");

			if (aggregateTypeList.Items.FindByValue(DataFormatter.getString(row, "AggregateTypeCode")) != null)
				aggregateTypeList.SelectedValue = DataFormatter.getString(row, "AggregateTypeCode");

			if (DataFormatter.getBool(row, "TrackAsSetting"))
			{
				trackAsList.SelectedValue = "setting";
				IsTracking = true;
			}
			else if (DataFormatter.getBool(row, "TrackAsStatistic"))
			{
				trackAsList.SelectedValue = "stat";
				IsTracking = true;
			}
			else if (DataFormatter.getBool(row, "TrackAsMediaCount"))
			{
				trackAsList.SelectedValue = "media";
				IsTracking = true;
			}

			if (DataFormatter.getBool(row, "DisableTrackingRequested"))
				this.IsDisableTrackingRequested = true;
			
			if (this.IsExtension)
                trackAsList.SelectedValue = "setting";

			ToggleTrackingFields(true);
		}

		if (!string.IsNullOrEmpty(this.DeviceTypeId) && this.DeviceTypeId != "0")
		{
			eventFilterList.DataSource = Utility.GetEventTypeList(Convert.ToInt32(this.DeviceTypeId));
			eventFilterList.DataBind();

			foreach (DataRow row2 in ds.Tables[1].Rows)
			{
				if (eventFilterList.Items.FindByValue(DataFormatter.getInt32(row2, "EventTypeId").ToString()) != null)
					eventFilterList.Items.FindByValue(DataFormatter.getInt32(row2, "EventTypeId").ToString()).Selected = true;

				selectedEventFiltersList.Text += DataFormatter.getString(row2, "EventTypeName") + ", ";
			}

			if (selectedEventFiltersList.Text.EndsWith(", "))
				selectedEventFiltersList.Text = selectedEventFiltersList.Text.Substring(0, selectedEventFiltersList.Text.Length - 2);
			else
				selectedEventFiltersList.Text = "None";
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		bool trackAsSetting = false;
		bool trackAsStatistic = false;
		bool trackAsMediaCount = false;

		if (IsTracking)
		{
			trackAsSetting = trackAsList.SelectedValue.Equals("setting");
			trackAsStatistic = trackAsList.SelectedValue.Equals("stat");
			trackAsMediaCount = trackAsList.SelectedValue.Equals("media");
		}
		
		SqlHelper.ExecuteNonQuery("RPT_UpdateField", this.FieldId, fieldNameBox.Text.Trim(), fieldStatusList.SelectedValue, trackAsSetting,
					trackAsStatistic, trackAsMediaCount, optionsParsingTypeList.SelectedValue, dataParsingTypeList.SelectedValue, 
					aggregateTypeList.SelectedValue, this.IsDisableTrackingRequested, this.isSensorBox.Checked, this.reportCommandNumber.Checked);

		
		//Delete all FieldEventTypes and re-add the selected list
		SqlHelper.ExecuteNonQuery("RPT_DeleteFieldEventTypes", this.FieldId);

		foreach (ListItem item in eventFilterList.Items)
		{
			if (item.Selected)
				SqlHelper.ExecuteNonQuery("RPT_InsertFieldEventType", this.FieldId, this.DeviceTypeId, item.Value);
		}

		ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "CloseRadWindow();", true);
	}

	protected void TrackDataButton_Click(object sender, EventArgs e)
	{
		this.IsTracking = true;
		ToggleTrackingFields(false);
	}

	protected void DisableTrackingButton_Click(object sender, EventArgs e)
	{
		SqlHelper.ExecuteNonQuery("RPT_DisableFieldTracking", this.FieldId);
		
		this.IsDisableTrackingRequested = true;
		ToggleTrackingFields(false);
	}

	private void ToggleTrackingFields(bool isInitialLoad)
	{
		if (this.IsDisableTrackingRequested)
		{
			this.PendingDisableDiv.Visible = true;
			this.TrackDataDiv.Visible = false;
			this.DisableTrackingDiv.Visible = false;

			this.trackAsList.Enabled = false;
			this.optionsParsingTypeList.Enabled = false;
			this.dataParsingTypeList.Enabled = false;
			this.aggregateTypeList.Enabled = false;
			this.eventFilterList.Enabled = false;
		}
		else
		{
			if (this.IsTracking)
			{
				this.TrackDataDiv.Visible = false;
				this.DisableTrackingDiv.Visible = true;
				this.TrackingPropertiesRow.Visible = true;

				if (!isInitialLoad && !this.IsExtension)
				{
					this.trackAsList.Enabled = true;
					this.optionsParsingTypeList.Enabled = true;
					this.dataParsingTypeList.Enabled = true;
					this.aggregateTypeList.Enabled = true;

					this.eventFilterList.Visible = true;
					this.selectedEventFiltersList.Visible = false;
				}

				if (!isInitialLoad && this.IsExtension)
					this.optionsParsingTypeList.Enabled = true;
			}
			else
			{
				this.TrackDataDiv.Visible = true;
				this.DisableTrackingDiv.Visible = false;
				this.TrackingPropertiesRow.Visible = false;

				this.eventFilterList.Visible = false;
				this.selectedEventFiltersList.Visible = true;
			}
		}
	}
}
