<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditTransaction.aspx.cs" Inherits="EditTransaction" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ import namespace="QueueServiceClient" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="600" width="800" modal="true" title="File Upload" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true"></telerik:radwindowmanager>

<script type="text/javascript">
    function ToggleDatePopup()
    {
        $find("<%= dateField.ClientID %>").showPopup();
    }    
    function ToggleTimePopup()
    {
        var picker = $find("<%= timeField.ClientID %>");
        picker.showTimePopup();
    }       
</script>

<asp:panel id="DefaultPanel" runat="server">
<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Transaction Details</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr style="padding-top:10px;">
						<td style="width:50%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>	
									<td style="width:130px;" class="rowHeading">Date:</td>
									<td style="width:300px;">
										<telerik:RadDatePicker id="dateField" runat="server" style="padding-top:1px;" width="80">        
											<calendar skin="Default2006" showrowheaders="false"></calendar>       
											<DatePopupButton Visible="False"></DatePopupButton>
											<DateInput onclick="ToggleDatePopup()"></DateInput>                           	                                             
										</telerik:RadDatePicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator2" runat="server" enableclientscript="false" controltovalidate="dateField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<telerik:radtimepicker id="timeField" runat="server" timepopupbutton-visible="false" width="80">
											<dateinput onclick="ToggleTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
											<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
										</telerik:radtimepicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator3" runat="server" enableclientscript="false" controltovalidate="timeField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Cell:</td>
									<td>
                                        <asp:label id="cellLabel" runat="server"></asp:label>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Session:</td>
									<td>
                                         <asp:label id="sessionLabel" runat="server"></asp:label>
									</td>
								</tr>
								<tr id="TransactionNoRow" runat="server">	
									<td style="width:130px;" class="rowHeading">Transaction No:</td>
									<td>
										 <asp:label id="transactionNumberLabel" runat="server"></asp:label>
									</td>
								</tr>
								<tr id="CumTranCountRow" runat="server" visible="false">
									<td style="width:130px;" class="rowHeading">Cumulative Transactions Count:</td>
									<td>
										 <asp:label id="cumTranCountLabel" runat="server"></asp:label>
									</td>
								</tr>
								<tr id="CumMediaCountRow" runat="server" visible="false">
									<td style="width:130px;" class="rowHeading">Cumulative Media Count:</td>
									<td>
										 <asp:label id="cumMediaCountLabel" runat="server"></asp:label>
									</td>
								</tr>
								<tr>	
									<td style="width:130px;" class="rowHeading">Device(s):</td>
									<td>
										<asp:label id="deviceListLabel" runat="server"></asp:label>
									</td>
								</tr>
                                <tr id="issueNoRow" runat="server" visible="false">	
									<td style="width:110px;" class="rowHeading">Issue No:</td>
									<td>
                                        <asp:textbox id="issueNumberField" runat="server"></asp:textbox> 
									</td>
								</tr>
								<tr id="linkRow" runat="server" visible="false">	
									<td style="width:110px;" class="rowHeading">Link:</td>
									<td>
										<asp:textbox id="linkField" runat="server"></asp:textbox> 
                                        <a runat="server" target="_blank" id="linkFieldActiveLink" visible="false">Go to Url</a>
									</td>
								</tr>
							</table>
						</td>
						<td style="width:50%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr id="addObservationRow" runat="server">	
									<td id="NewObservationTitle" runat="server" style="width:130px;" class="rowHeading">Observation:</td>
									<td id="NewObservationRow" runat="server">
										<div class="goButton"><asp:linkbutton id="addObservationBtn" runat="server" causesvalidation="false" onclick="NewObservation_Click">Add Observation</asp:linkbutton></div>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Settings:</td>
									<td>
										<div class="goButton"><a runat="server" id="SettingsLink">View Settings</a></div>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Statistics:</td>
									<td>
										<div class="goButton"><a runat="server" id="StatsLink">View Statistics</a></div>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">View Pictures:</td>
									<td>
										<div class="goButton"><a runat="server" id="PicturesButton">View Pictures</a></div>
									</td>
								</tr>
								<tr>	
									<td style="width:110px;" class="rowHeading">Upload Files:</td>
									<td>
										<div class="goButton"><asp:linkbutton runat="server" id="UploadFileBtn">Upload Files</asp:linkbutton></div>
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr>
						<td valign="top">
							<table style="width:100%; padding-left:10px; padding-top:10px;" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td class="rowHeading">XML Files: <span style="font-weight:normal;">(right click & choose Save Target As)</span></td>
								</tr>
								<asp:repeater id="xmlRepeater" runat="server">
									<itemtemplate>
										<tr><td class="repeaterSubItem"><a style="padding-left:0px;" href='TxFile.aspx?<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.FILE_IS_DATA_KEY %>=1&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46) %></a></td></tr>
									</itemtemplate>
									<alternatingitemtemplate>
										<tr><td class="repeaterSubItemAlt"><a style="padding-left:0px;" href='TxFile.aspx?<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.FILE_IS_DATA_KEY %>=1&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46)%></a></td></tr>
									</alternatingitemtemplate>
								</asp:repeater>
								<tr>
									<td style="padding-left:14px;padding-top:10px;">
										<asp:label id="NoXMLLabel" runat="server">No XML files found.<br /><br /></asp:label>
									</td>
								</tr>
							</table>
						</td>
						<td valign="top">
							<table style="width:100%;" border="0" cellpadding="0" cellspacing="0">
								<tr>
									<td class="rowHeading">Engineering & Other Files: <span style="font-weight:normal;">(right click & choose Save Target As)</span></td>
								</tr>
								<tr id="downloadAllRow" runat="server">
									<td style="padding:10px 0 0 15px;">
										<div class="goButton"><asp:linkbutton id="DownloadAllBtn" runat="server" causesvalidation="false" onclick="DownloadAllBtn_Click">Download All</asp:linkbutton></div>
									</td>
								</tr>
								<asp:repeater id="engRepeater" runat="server">
									<itemtemplate>
										<tr><td class="repeaterSubItem"><a style="padding-left:0px;" href='TxFile.aspx?<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46)%></a></td></tr>
									</itemtemplate>
									<alternatingitemtemplate>
										<tr><td class="repeaterSubItemAlt"><a style="padding-left:0px;" href='TxFile.aspx?<%# DieboldConstants.TRAN_ID_KEY %>=<%# ((DataFile)Container.DataItem).TranId %>&<%# DieboldConstants.FILE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).FileName %>&<%# DieboldConstants.DEVICE_TYPE_NAME_KEY %>=<%# ((DataFile)Container.DataItem).DeviceTypeName %>&<%# DieboldConstants.SERIAL_NUMBER_KEY %>=<%# ((DataFile)Container.DataItem).SerialNumber %>&<%# DieboldConstants.TRAN_DATE_KEY %>=<%# Server.UrlEncode(Convert.ToString(((DataFile)Container.DataItem).TranDate)) %>' title='<%# ((DataFile)Container.DataItem).FileName%>'><%# DataFormatter.TruncateString(((DataFile)Container.DataItem).FileName, 30, 46)%></a></td></tr>
									</alternatingitemtemplate>
								</asp:repeater>
								<tr>
									<td style="padding-left:14px;padding-top:10px;">
										<asp:label id="NoFilesLabel" runat="server">No engineering files found.<br /><br /></asp:label>
										<%--<div class="goButton"><a runat="server" id="UploadButton">Upload a file</a></div>--%>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				<br />
				<table width="100%" border="0" cellpadding="0" cellspacing="0" style="padding-top:20px;">
					<tr>
						<td style="width:100%;" class="rowHeading">Notes:</td>
					</tr>						
					<tr>
						<td colspan="3" style="padding:10px 0px 10px 0px;"><asp:textbox id="notesField" runat="server" width="90%" cssclass="entryControl" rows="6" textmode="multiLine"></asp:textbox></td>
					</tr>
				</table>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>	
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="submitButton" onclick="SaveButton_Click">Save</asp:linkbutton></div></td>
						<td style="width:120px;"><div class="cancelButton"><asp:linkbutton runat="server" id="cancelButton" onclick="CancelButton_Click" causesvalidation="false">Cancel</asp:linkbutton></div></td>
						<td>&nbsp;</td>
					</tr>
				</table>
				<br />
				<br />
			</div>
		</td>
	</tr>
</table>
<asp:hiddenfield id="hidCellId" runat="server" />
<asp:hiddenfield id="hidSessionId" runat="server" />
</asp:panel>

</asp:Content>

