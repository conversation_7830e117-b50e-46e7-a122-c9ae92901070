using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class EngineeringFields : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			DeviceTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
			DeviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
			DeviceTypeList.DataBind();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]) && this.DeviceTypeList.Items.FindItemByValue(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]) != null)
			{
				this.DeviceTypeList.SelectedValue = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];
				DeviceTypeList_SelectedIndexChanged(null, null);
			}

			if (!string.IsNullOrEmpty(Request.Params["page"]))
				EngineeringGrid.CurrentPageIndex = Convert.ToInt32(Request.Params["page"]);
		}

		//bind every page load to handle javascript paging.
		BindGridData();
	}

	protected void DeviceTypeList_SelectedIndexChanged(object sender, EventArgs e)
	{
		EngineeringGrid.CurrentPageIndex = 0;
		//do nothing, data rebound on every page load.

		//BindGridData();
	}

	public void RebindData(object sender, EventArgs e)
	{
		EngineeringGrid.CurrentPageIndex = 0;
		//data rebound on every page load.
		//BindGridData();
	}

	protected void EngineeringGrid_OnItemDataBound(object sender, Telerik.Web.UI.GridItemEventArgs e)
	{
		if (e.Item != null && e.Item.DataItem != null)
		{
			Label trackingLbl = null;

			if (e.Item.FindControl("TrackingStatusLabel") != null)
				trackingLbl = (Label)e.Item.FindControl("TrackingStatusLabel");

			if (trackingLbl != null)
			{
				string trackAsSetting = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsSetting", "true", "false", "");
				string trackAsStatistic = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsStatistic", "true", "false", "");
				string trackAsMediaCount = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsMediaCount", "true", "false", "");

				if (Convert.ToBoolean(trackAsSetting) || Convert.ToBoolean(trackAsStatistic) || Convert.ToBoolean(trackAsMediaCount))
					trackingLbl.Text = "<span style=\"color:#666666;font-weight:bold;\">Enabled</span>";
				else
					trackingLbl.Text = "<span style=\"color:#aaaaaa;\">Disabled</span>";
			}
		}
	}

	private void BindGridData()
	{
		EngineeringGrid.DataSource = SqlHelper.ExecuteDataset("RPT_LoadEngineeringFields", this.DeviceTypeList.SelectedValue);
		EngineeringGrid.DataBind();
	}
}
