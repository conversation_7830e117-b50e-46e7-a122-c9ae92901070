using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class ReportSnapshot : System.Web.UI.Page
{
	public ReportInfo RepInfo
	{
		get { return (ReportInfo)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
            if (!string.IsNullOrEmpty(Request.QueryString[DieboldConstants.REPORT_ID_KEY]))
			{
				this.RepInfo = Utility.LoadReportInfo(Convert.ToInt32(Request.QueryString[DieboldConstants.REPORT_ID_KEY]));
				NewSnapshotArea.Visible = false;
			}
			else
			{
				this.RepInfo = Utility.GetReportInfoFromTransfer();
			}
		}

		if (SnapshotRepeater.DataSource == null)
		{
			SnapshotRepeater.DataSource = SqlHelper.ExecuteDataset("RPT_GetReportSnapshots", this.RepInfo.ReportId);
			SnapshotRepeater.DataBind();

			if (SnapshotRepeater.Items.Count == 0)
				NoDataLabel.Visible = true;
		}
	}

	protected void ViewSnapshot_Command(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null && !string.IsNullOrEmpty(e.CommandArgument.ToString()))
		{
			Utility.SetReportInfoForTransfer(this.RepInfo);

			//Close the window and redirect parent to the Run Report screen.
            string closeScript = string.Format("\r\n GetRadWindow().BrowserWindow.document.location.href='runreport.aspx?{0}={1}';CloseRadWindow(); ", DieboldConstants.SNAPSHOT_ID_KEY, e.CommandArgument);
			ScriptManager.RegisterStartupScript(this, this.GetType(), "CloseScript", closeScript, true);
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		object paretoMaxColumns = null;
		object prstXaxisTypeId = null;
		object prstMTBFSpec = null;
		object prstDiscrim = null;
		object prstProducer = null;
		object prstConsumer = null;
		object distribLowerSpec = null;
		object distribUpperSpec = null;
		object startDate = null;
		object endDate = null;

		if (this.RepInfo.FixedStartDate != DateTime.MinValue)
			startDate = (DateTime)this.RepInfo.FixedStartDate;

		if (this.RepInfo.FixedEndDate != DateTime.MinValue)
			endDate = (DateTime)this.RepInfo.FixedEndDate;

		//Pareto specific
		if (this.RepInfo.ParetoInfo.MaxColumns != 0)
			paretoMaxColumns = (int)this.RepInfo.ParetoInfo.MaxColumns;

		//PRST specific
		if (this.RepInfo.PRSTInfo.XaxisTypeId != 0)
			prstXaxisTypeId = (int)this.RepInfo.PRSTInfo.XaxisTypeId;
		if (this.RepInfo.PRSTInfo.MTBFSpec != Decimal.MinValue)
			prstMTBFSpec = (decimal)this.RepInfo.PRSTInfo.MTBFSpec;
		if (this.RepInfo.PRSTInfo.DiscriminationRatio != Decimal.MinValue)
			prstDiscrim = (decimal)this.RepInfo.PRSTInfo.DiscriminationRatio;
		if (this.RepInfo.PRSTInfo.ProducersRisk != Decimal.MinValue)
			prstProducer = (decimal)this.RepInfo.PRSTInfo.ProducersRisk;
		if (this.RepInfo.PRSTInfo.ConsumersRisk != Decimal.MinValue)
			prstConsumer = (decimal)this.RepInfo.PRSTInfo.ConsumersRisk;

		//Distribution specific
		if (this.RepInfo.DistributionInfo.LowerSpecLimit != Decimal.MinValue)
			distribLowerSpec = (decimal)this.RepInfo.DistributionInfo.LowerSpecLimit;
		if (this.RepInfo.DistributionInfo.UpperSpecLimit != Decimal.MinValue)
			distribUpperSpec = (decimal)this.RepInfo.DistributionInfo.UpperSpecLimit;

		SqlHelper.ExecuteNonQuery("RPT_InsertReportSnapshot", 
			this.RepInfo.ReportId, this.SnapshotNameField.Text, this.RepInfo.ReportData, this.RepInfo.CellSetData, this.DescriptionField.Text,
			this.RepInfo.ChartTitle, startDate, endDate,
			this.RepInfo.ParetoInfo.SplitByCell, this.RepInfo.ParetoInfo.IncludeTotal, this.RepInfo.ParetoInfo.ByStatisticValue, paretoMaxColumns,
			prstXaxisTypeId, this.RepInfo.PRSTInfo.IncludeTrendline, this.RepInfo.PRSTInfo.IncludeUncensoredSeries, this.RepInfo.PRSTInfo.CustomizeRatioRisk,
			prstMTBFSpec, prstProducer, prstConsumer, prstDiscrim,
			this.RepInfo.ProgressInfo.Normalize,
			distribLowerSpec, distribUpperSpec, this.RepInfo.DistributionInfo.ShowNormalDistribution,
			this.RepInfo.DistributionInfo.ShowHistogram);
		
		ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "CloseRadWindow();", true);
	}
}
