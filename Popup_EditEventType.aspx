<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="Popup_EditEventType.aspx.cs" Inherits="Popup_EditEventType" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr id="ContentRow" runat="server">
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Event Type</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Add/Edit Event Type</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="14" width="100%">
						<tr>
							<td class="rowHeading" style="width:150px;">Device</td>
							<td>
								<asp:textbox runat="server" width="150" id="deviceTypeIdField" enabled="false"></asp:textbox>
								<asp:requiredfieldvalidator id="val" runat="server" errormessage="* Required" controltovalidate="deviceTypeIdField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Event Type Id</td>
							<td>
								<asp:textbox runat="server" width="150" id="eventIdField"></asp:textbox>
								<asp:requiredfieldvalidator id="val1" runat="server" errormessage="* Required" controltovalidate="eventIdField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Event Type Name</td>
							<td>
								<asp:textbox runat="server" width="230" id="eventNameField"></asp:textbox>
								<asp:requiredfieldvalidator id="val2" runat="server" errormessage="* Required" controltovalidate="eventNameField" display="dynamic" style="color:#ff0000;"></asp:requiredfieldvalidator>
							</td>
						</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to close the window without saving?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
					<div style="padding-left:14px;padding-bottom:14px;color:#ee0000;" id="ErrorLabel" visible="false" runat="server"><br />An event already exists with the specified Id.<br /></div>
				</div>
			</td>
		</tr>
		<tr id="ErrorRow" runat="server" visible="false">
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Error</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<br />
					<div class="title">Unable to identify a Device Type Id.</div>
					<div style="padding-left:14px;">
						Please select a Device Type from the list before adding Event Types.
						<br /><br />
						<div class="cancelButton"><a href="javascript:CloseRadWindow();">Close</a></div>
					</div>
					<br />
				</div>
			</td>
		</tr>
	</table>

</asp:Content>

