<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="FunctionMetricList.aspx.cs" Inherits="FunctionMetricList" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">
	<table border="0" cellpadding="0" cellspacing="0">
		<tr>
			<td class="cellHeading">Parameter Name:</td>
			<td class="cellHeading">Parameter Value:</td>
			<td class="cellHeading">Parameter Text:</td>
			<td class="cellHeading">Metric Id:</td>
		</tr>
	    <asp:repeater id="ListRepeater" runat="server">
	        <itemtemplate>
                <tr>
                    <td class='<%# ((Container.ItemIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>'><%# DataFormatter.Format(Container.DataItem, "ParameterName", "Unknown") %></td>
                    <td class='<%# ((Container.ItemIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>'><%# DataFormatter.Format(Container.DataItem, "ParameterValue", "-")%></td>
                    <td class='<%# ((Container.ItemIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>'><%# DataFormatter.Format(Container.DataItem, "ParameterValueName", "-")%></td>
                    <td class='<%# ((Container.ItemIndex % 2)==0?"repeaterItem":"repeaterItemAlt") %>'><%# DataFormatter.Format(Container.DataItem, "MetricId", "-")%></td>
                </tr>
	        </itemtemplate>
	    </asp:repeater>
    </table>
</asp:Content>

