using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.IO;

public partial class TransactionDetail : System.Web.UI.Page
{
	public int CellId
	{
		get { if (this.ViewState["c"] != null) return (int)this.ViewState["c"]; else return 0; }
		set { this.ViewState["c"] = value; }
	}

	public int SessionId
	{
		get { if (this.ViewState["s"] != null) return (int)this.ViewState["s"]; else return 0; }
		set { this.ViewState["s"] = value; }
	}

	public int TransactionId
	{
		get { if (this.ViewState["t"] != null) return (int)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}

    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["c"]) && !string.IsNullOrEmpty(Request.Params["s"]) && !string.IsNullOrEmpty(Request.Params["t"]))
			{
				this.CellId = Convert.ToInt32(Request.Params["c"]);
				this.SessionId = Convert.ToInt32(Request.Params["s"]);
				this.TransactionId = Convert.ToInt32(Request.Params["t"]);

				LoadTransaction();
			}
		}
		//ExtensionButton.Attributes.Add("onclick", "window.radopen('" + String.Format("TranExtensionList.aspx?c={0}&s={1}&t={2}'", this.CellId, this.SessionId, this.TransactionId) + ", null);return false;");
		//UploadButton.Attributes.Add("onclick", "window.radopen('" + String.Format("UploadEngFile.aspx?c={0}&s={1}&t={2}'", this.CellId, this.SessionId, this.TransactionId) + ", null);return false;");
    }

	private void LoadTransaction()
	{
		DataSet ds = SqlHelper.ExecuteDataset("RPT_GetTransactionData", this.CellId, this.SessionId, this.TransactionId);

		if (ds.Tables[0] != null)
		{
			foreach (DataRow row in ds.Tables[0].Rows)
			{
				if (DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "") != null)
					this.dateLabel.Text = DataFormatter.FormatDate(row, "TranDate", "MM/dd/yyyy", "").ToString();

				transIdLabel.Text = DataFormatter.getInt32(row, "RDToolTranId").ToString();
				sessionLabel.Text = DataFormatter.getString(row, "SessionName");
				cellLabel.Text = DataFormatter.getString(row, "CellName");

				//ExtensionButton.InnerText = "Extensions (" + DataFormatter.getInt32(row, "ExtensionCnt").ToString() + ")";
			}
		}

		ds = SqlHelper.ExecuteDataset("RPT_GetTransactionDevices", this.CellId, this.SessionId, this.TransactionId);
		if (ds.Tables[0] != null)
		{
			List<string> engineeringFiles = new List<string>();
			List<string> xmlFiles = new List<string>();

			for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
			{
				DataRow row = ds.Tables[0].Rows[i];

				//string virtDir = this.ResolveUrl(Utility.GetRootUploadPath(DataFormatter.getString(row, "DeviceTypeName"), DataFormatter.getString(row, "SerialNumber"), DataFormatter.getDateTime(row, "TranDate")));
				//string filePattern = "CELL=" + this.CellId + "_SESSION=" + this.SessionId + "_TRX=" + this.TransactionId; 
										//+ "_DC=" + DataFormatter.getInt32(row, "DeviceTypeCode").ToString() + "_SN=" + DataFormatter.getString(row, "SerialNumber");

				//XML File Pattern:  data\CELL=234_SESSION=108_TRX=1697_DC=38000_SN=NCENG0001_DH=200807280038_DV=ENA_FT=xml.zip

				//engineeringFiles = GetFileList(virtDir, filePattern);
				//xmlFiles = GetFileList(virtDir + "data/", filePattern);	
			}

			//daqRepeater.DataSource = engineeringFiles;
			//daqRepeater.DataBind();

			//xmlRepeater.DataSource = xmlFiles;
			//xmlRepeater.DataBind();

			//if (daqRepeater.Items.Count > 0)
			//	NoFilesLabel.Visible = false;
			//else
			//	NoFilesLabel.Visible = true;

			if (xmlRepeater.Items.Count > 0)
				NoXMLLabel.Visible = false;
			else
				NoXMLLabel.Visible = true;
		}

		ds = SqlHelper.ExecuteDataset("RPT_GetTransactionObservations", this.CellId, this.SessionId, this.TransactionId);
		if (ds.Tables[0] != null)
		{
			obsRepeater.DataSource = ds.Tables[0];
			obsRepeater.DataBind();

			if (obsRepeater.Items.Count > 0)
				NoObservationsLabel.Visible = false;
			else
				NoObservationsLabel.Visible = true;
		}

		//ds = SqlHelper.ExecuteDataset("RPT_GetTransactionFunctions", this.CellId, this.SessionId, this.TransactionId);
		//if (ds.Tables[0] != null)
		//{
		//    functionsRepeater.DataSource = ds.Tables[0];
		//    functionsRepeater.DataBind();
		//}
	}

	private List<string> GetFileList(string virtDir, string filePattern)
	{
		List<string> retVal = new List<string>();

		if (Directory.Exists(Server.MapPath(virtDir)))
		{
			DirectoryInfo dirInfo = new DirectoryInfo(Server.MapPath(virtDir));
			foreach (FileInfo curFile in dirInfo.GetFiles(filePattern + "*.*", SearchOption.TopDirectoryOnly))
			{
				if (!string.IsNullOrEmpty(virtDir + curFile.Name))
					retVal.Add(virtDir + curFile.Name);
			}
		}

		return retVal;
	}

	protected string GetShortFileName(string fileName, int folderCount)
	{
		string retVal = null;
		int index = 0;
		string[] nameParts = fileName.Split(new string[] { "/" }, StringSplitOptions.RemoveEmptyEntries);

		foreach (string part in nameParts)
		{
			if (index >= folderCount)
				retVal += part;

			index++;
		}

		return DataFormatter.TruncateString(retVal, 22, 40);
	}
}
