<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="PopupError.aspx.cs" Inherits="PopupError" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Report Error</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Error loading the report object.</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="0" width="100%">
						<tr>
							<td class="leftPad">&nbsp;</td>
							<td>The report object has been lost due to a timeout, browser session reset, or some other circumstance. To continue creating a report you can click the continue button below.</td>
						</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" postbackurl="~/newreportwizard_setup.aspx" id="nextButton">Continue</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

