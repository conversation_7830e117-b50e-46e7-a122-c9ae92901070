using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;

public partial class Settings : System.Web.UI.Page
{
	public Int64 TranId
	{
		get { if (this.ViewState["t"] != null) return (Int64)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["t"]))
				this.TranId = Convert.ToInt64(Request.Params["t"]);
		}

		//bind every page load to handle javascript paging.
		BindGridData();
	}

	protected void DataGrid1_OnItemDataBound(object sender, Telerik.Web.UI.GridItemEventArgs e)
	{
		if (e.Item != null && e.Item.DataItem != null)
		{
			Label trackingLbl = null;

			if (e.Item.FindControl("TrackingStatusLabel") != null)
				trackingLbl = (Label)e.Item.FindControl("TrackingStatusLabel");

			if (trackingLbl != null)
			{
				string trackAsSetting = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsSetting", "true", "false", "");
				string trackAsStatistic = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsStatistic", "true", "false", "");
				string trackAsMediaCount = DataFormatter.FormatBool(e.Item.DataItem, "TrackAsMediaCount", "true", "false", "");

				if (Convert.ToBoolean(trackAsSetting) || Convert.ToBoolean(trackAsStatistic) || Convert.ToBoolean(trackAsMediaCount))
					trackingLbl.Text = "<span style=\"color:#666666;font-weight:bold;\">Enabled</span>";
				else
					trackingLbl.Text = "<span style=\"color:#aaaaaa;\">Disabled</span>";
			}
		}
	}

	private void BindGridData()
	{
		DataGrid1.DataSource = SqlHelper.ExecuteDataset("RPT_LoadSettings", this.TranId);
		DataGrid1.DataBind();
	}

	protected void ExportButton_Click(object sender, EventArgs e)
	{
		DataGrid1.ExportSettings.FileName = "SettingsExport_" + DateTime.Now.ToString("yyyyMMddhhmmss");
		DataGrid1.ExportSettings.IgnorePaging = true;
		DataGrid1.ExportSettings.OpenInNewWindow = true;
		DataGrid1.MasterTableView.ExportToCSV();
	}
}
