﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
//using Atlassian.Jira;
//using TechTalk.JiraRestClient;

public partial class JiraTest : System.Web.UI.Page {
    public static string JIRA_SITE_URL = "https://jerry.wincor-nixdorf.com";

    protected void Page_Load(object sender, EventArgs e) {

    }

    protected void testBtn_Click(object sender, EventArgs e) {
        //try {
        //    statusLbl.Text = "";

        //    TechTalk.JiraRestClient.JiraClient jira = new JiraClient("", "vanduyk", "K.v_1248083");
        //    List<Issue> calledList = new List<Issue>(jira.GetIssues("PLAYTMSW"));

        //    foreach (Issue i in calledList) {
        //        statusLbl.Text += "<br/>" + i.fields.status;
        //    }

        //    //var instance = Jira.CreateRestClient("https://jerry.wincor-nixdorf.com", "vanduyk", "K.v_1248083");

        //    //IEnumerable<Project> x = await instance.Projects.GetProjectsAsync();

        //    //foreach (Project p in instance.Projects.GetProjectsAsync()) {

        //    //}

        //    //var project = instance.Projects.GetProjectAsync("PLAYAGLSIM123");
        //    //if (project != null) {
        //    //    statusLbl.Text = project.IsCompleted.ToString();

        //    //}
        //    //else {
        //    //    statusLbl.Text = "null obj";
        //    //}


        //    //var JiraIssue = instance.CreateIssue("QST");
        //    //JiraIssue.Type = "Task";
        //    //JiraIssue.Priority = "Major";
        //    //JiraIssue.Summary = "PLEASE IGNORE THIS JIRA: We are Testing Jira Automation Ticket Creation.";
        //    //JiraIssue.Description = "PLEASE IGNORE THIS JIRA: Create issue using ServiceNow Incident - SN Number - INC0592429";
        //    //JiraIssue.Assignee = "My Name";
        //    //JiraIssue.SaveChanges(); //SaveChangesAsync();

        //    statusLbl.Text += "<br/>Success.";
        //}
        //catch (Exception error) {
        //    statusLbl.Text = "Error - " + error;
        //}
    }

    protected void manualBtn_Click(object sender, EventArgs e) {
        try {
            statusLbl.Text = GET("https://jerry.wincor-nixdorf.com/rest/api/2/issue/280024");
        }
        catch (Exception error) {
            statusLbl.Text = "Error - " + error;
        }
    }

    string GET(string url) {
        string retVal = "";
        HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
        try {
            WebResponse response = request.GetResponse();
            using (Stream responseStream = response.GetResponseStream()) {
                StreamReader reader = new StreamReader(responseStream, Encoding.UTF8);
                retVal = reader.ReadToEnd();
            }
        }
        catch (WebException ex) {
            WebResponse errorResponse = ex.Response;
            if (errorResponse != null) {
                using (Stream responseStream = errorResponse.GetResponseStream()) {
                    StreamReader reader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
                    String errorText = reader.ReadToEnd();
                    retVal = "Error - " + errorText;
                }
            }
            else {
                retVal = "Error - " + ex.Message;
            }
        }
        return retVal;
    }

}