using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.Serialization.Json;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Atlassian.Jira;
//using TechTalk.JiraRestClient;

public partial class JiraTest : System.Web.UI.Page {
    public static string JIRA_SITE_URL = "https://jerry.wincor-nixdorf.com";

    protected void Page_Load(object sender, EventArgs e) {

    }

    protected async void testBtn_Click(object sender, EventArgs e) {
        try {
            statusLbl.Text = "Testing Jira REST API connection...<br/>";

            // Test basic configuration
            statusLbl.Text += "Configuration Validation:<br/>";
            statusLbl.Text += JiraRestApi.ValidateConfiguration() + "<br/><br/>";

            statusLbl.Text += "Configuration Details:<br/>";
            statusLbl.Text += "URL: " + System.Configuration.ConfigurationManager.AppSettings["JiraRestApi_URL"] + "<br/>";
            statusLbl.Text += "Username: " + System.Configuration.ConfigurationManager.AppSettings["JiraRestApi_Username"] + "<br/>";
            statusLbl.Text += "<br/>";

            // Test REST client creation
            statusLbl.Text += "Creating REST client...<br/>";
            var jiraClient = JiraRestApi.GetRestClient();
            statusLbl.Text += "REST client created successfully.<br/><br/>";

            // Test getting a specific issue
            statusLbl.Text += "Testing issue retrieval...<br/>";
            try {
                var issue = await JiraRestApi.GetIssueById("PLAYHWRMV2-236");
                if (issue != null) {
                    statusLbl.Text += "Successfully retrieved issue: " + issue.Key.Value + "<br/>";
                    statusLbl.Text += "Summary: " + issue.Summary + "<br/>";
                    statusLbl.Text += "Status: " + issue.Status.Name + "<br/>";
                    statusLbl.Text += "Assignee: " + (issue.Assignee ?? "Unassigned") + "<br/>";
                } else {
                    statusLbl.Text += "Issue retrieval returned null.<br/>";
                }
            } catch (Exception issueEx) {
                statusLbl.Text += "Issue retrieval failed: " + issueEx.Message + "<br/>";
                if (issueEx.InnerException != null) {
                    statusLbl.Text += "Inner exception: " + issueEx.InnerException.Message + "<br/>";
                }
            }

            statusLbl.Text += "<br/>Test completed.";
        }
        catch (Exception error) {
            statusLbl.Text += "<br/>Error - " + error.Message;
            if (error.InnerException != null) {
                statusLbl.Text += "<br/>Inner Exception: " + error.InnerException.Message;
            }
            statusLbl.Text += "<br/>Stack Trace: " + error.StackTrace;
        }
    }

    protected void manualBtn_Click(object sender, EventArgs e) {
        try {
            statusLbl.Text = "Testing manual HTTP connection...<br/>";

            // Test with configured URL
            string configUrl = System.Configuration.ConfigurationManager.AppSettings["JiraRestApi_URL"];
            statusLbl.Text += "Testing URL: " + configUrl + "/rest/api/2/issue/PLAYHWRMV2-236<br/><br/>";

            string result = JiraRestApi.GetJsonForUrl(configUrl + "/rest/api/2/issue/PLAYHWRMV2-236");

            if (result.StartsWith("Error")) {
                statusLbl.Text += "Manual connection failed:<br/>" + result;
            } else {
                statusLbl.Text += "Manual connection successful!<br/>";
                statusLbl.Text += "Response length: " + result.Length + " characters<br/>";
                // Show first 500 characters of response
                if (result.Length > 500) {
                    statusLbl.Text += "Response preview: " + result.Substring(0, 500) + "...";
                } else {
                    statusLbl.Text += "Full response: " + result;
                }
            }
        }
        catch (Exception error) {
            statusLbl.Text += "<br/>Error - " + error.Message;
            if (error.InnerException != null) {
                statusLbl.Text += "<br/>Inner Exception: " + error.InnerException.Message;
            }
        }
    }

    string GET(string url) {
        string retVal = "";
        HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);

        // Add authentication
        string username = System.Configuration.ConfigurationManager.AppSettings["JiraRestApi_Username"];
        string password = System.Configuration.ConfigurationManager.AppSettings["JiraRestApi_Password"];
        string apiKey = System.Configuration.ConfigurationManager.AppSettings["JiraRestApi_Key"];

        // Use API key if available, otherwise fall back to password
        string authToken = !string.IsNullOrEmpty(apiKey) ? apiKey : password;

        var credentialBuffer = new UTF8Encoding().GetBytes(username + ":" + authToken);
        request.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(credentialBuffer));

        // Handle SSL
        if (url.ToLower().StartsWith("https")) {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            ServicePointManager.ServerCertificateValidationCallback = (s, cert, chain, ssl) => true;
        }

        try {
            WebResponse response = request.GetResponse();
            using (Stream responseStream = response.GetResponseStream()) {
                StreamReader reader = new StreamReader(responseStream, Encoding.UTF8);
                retVal = reader.ReadToEnd();
            }
        }
        catch (WebException ex) {
            WebResponse errorResponse = ex.Response;
            if (errorResponse != null) {
                using (Stream responseStream = errorResponse.GetResponseStream()) {
                    StreamReader reader = new StreamReader(responseStream, Encoding.GetEncoding("utf-8"));
                    String errorText = reader.ReadToEnd();
                    retVal = "Error - " + errorText + " (Status: " + ((HttpWebResponse)errorResponse).StatusCode + ")";
                }
            }
            else {
                retVal = "Error - " + ex.Message;
            }
        }
        return retVal;
    }

}