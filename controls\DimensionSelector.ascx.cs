using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Microsoft.AnalysisServices.AdomdClient;

public partial class controls_DimensionSelector : System.Web.UI.UserControl
{
	public bool IsRequired
	{
		get { if (this.ViewState["ir"] == null) return false; else return (bool)this.ViewState["ir"]; }
		set { this.ViewState["ir"] = value; }
	}

	private bool IsLegacy
	{
		get { if (this.ViewState["il"] == null) return false; else return (bool)this.ViewState["il"]; }
		set { this.ViewState["il"] = value; }
	}

	public string DimensionName
	{
		get { return UnescapeUniqueName(dimensionCombo.SelectedValue); }
		set 
		{
            if (dimensionCombo.FindItemByValue(EscapeUniqueName(value)) != null)
            {
                dimensionCombo.SelectedValue = EscapeUniqueName(value);
            }
            else
            {
                dimensionCombo.ClearSelection();
                dimensionCombo.Text = null;
            }
		}
	}

	private List<string> SelectedMembers
	{
		get { if (this.ViewState["sm"] != null) return (List<string>)this.ViewState["sm"]; else return null; }
		set 
		{
			this.ViewState["sm"] = value;
			this.ShowEditableTree = (value == null || value.Count == 0);				
		}
	}

	private bool ShowEditableTree
	{
		get { if (this.ViewState["set"] == null) return true; else return (bool)this.ViewState["set"]; }
		set { this.ViewState["set"] = value; }
	}

	private List<string> checkedDimensionMembers = null;
	public List<string> CheckedDimensionMembers
	{
		get 
		{
			checkedDimensionMembers = new List<string>();
			foreach (RadTreeNode node in memberTree.CheckedNodes)
			{
				checkedDimensionMembers.Add(UnescapeUniqueName(node.Value));
			}

			//add in any previously SelectedMembers that are no yet loaded into the tree from load-on-demand items.
			if (SelectedMembers != null)
			{
				foreach (string dimMember in this.SelectedMembers)
				{
					bool memberIsInTree = false; 
					foreach (RadTreeNode node in memberTree.GetAllNodes())
					{
						if (UnescapeUniqueName(node.Value).Equals(dimMember))
						{
							memberIsInTree = true;
							break;
						}
					}

					if (!memberIsInTree)
						checkedDimensionMembers.Add(dimMember);
				}
			}

			return checkedDimensionMembers;
		}
		set 
		{
			if (value != null)
			{
				this.SelectedMembers = value;
			}
		}
	}

	protected void Page_Load(object sender, EventArgs e)
	{
        ClearDim.Visible = !this.IsRequired;
        if (!this.IsPostBack)
        {
            dimensionCombo.Focus();
        }
		this.PreRender += new EventHandler(controls_DimensionSelector_PreRender);
	}

	private void controls_DimensionSelector_PreRender(object sender, EventArgs e)
	{
		if (this.ShowEditableTree)
		{
			memberTree.Visible = true;
			selectedItemsTree.Visible = false;
			this.clearAllBtn.Visible = true;
			if (this.DimensionName != null && (this.DimensionName.StartsWith("[Statistic]") || this.DimensionName.StartsWith("[Distribution]")))
				this.checkAllBtn.Visible = false;
			else
				this.checkAllBtn.Visible = true;

			this.editButton.Visible = false;
			//this.viewSelectedButton.Visible = true;
		}
		else
		{
			memberTree.Visible = false;
			selectedItemsTree.Visible = true;
			this.clearAllBtn.Visible = false;
			this.checkAllBtn.Visible = false;

			this.editButton.Visible = true;
			//this.viewSelectedButton.Visible = false;
		}
	}

	public void PopulateDimensions(bool isLegacy, string[] suppressedDimensions)
    {
		this.IsLegacy = isLegacy;

		this.memberTree.Nodes.Clear();
		this.selectedItemsTree.Nodes.Clear();

        dimensionCombo.ClearSelection();
        dimensionCombo.Items.Clear();
        dimensionCombo.Text = null;
        dimensionCombo.Items.Add(new RadComboBoxItem("Select...", ""));

        foreach (Dimension dim in OLAPHelper.GetDimensions(isLegacy))
        {
            bool isVisible = true;
            if (suppressedDimensions != null)
            {
                foreach (string suppressed in suppressedDimensions)
                {
                    if (string.Compare(suppressed, dim.Name, true) == 0)
                    {
                        isVisible = false;
                        break;
                    }
                }
            }
            if (isVisible)
            {
                dimensionCombo.Items.Add(new RadComboBoxItem(dim.Name, EscapeUniqueName(dim.Hierarchies[0].Levels[1].UniqueName)));
            }
        }
    }

	protected void OnDimensionChanged(object sender, EventArgs e)
	{
		if(this.SelectedMembers != null)
			this.SelectedMembers.Clear();
		this.ShowEditableTree = true;

		UpdateTreeDisplay();
	}

	public void UpdateTreeDisplay()
	{
		BuildEditTree();
		BuildViewTree();
	}

	private void BuildEditTree()
	{
		this.memberTree.Nodes.Clear();
		bool isInitialLoad = (this.SelectedMembers == null || this.SelectedMembers.Count == 0);

		if (!string.IsNullOrEmpty(dimensionCombo.SelectedValue))
		{
			string unescapedDimName = UnescapeUniqueName(dimensionCombo.SelectedValue);

            try
            {
				Level level = OLAPHelper.FindLevelByUniqueName(this.IsLegacy, unescapedDimName);
				PopulateMembers(this.memberTree.Nodes, level, isInitialLoad);
            }
            catch
            {
                try
                {
					Hierarchy hier = OLAPHelper.FindHierarchyByUniqueName(this.IsLegacy, unescapedDimName);
					PopulateMembers(this.memberTree.Nodes, hier.Levels[1], isInitialLoad);
                }
                catch
                {
					Dimension dim = OLAPHelper.FindDimensionByUniqueName(this.IsLegacy, unescapedDimName);
					PopulateMembers(this.memberTree.Nodes, dim.Hierarchies[0].Levels[1], isInitialLoad);
                }
            }

			foreach (string memberUniqueName in this.SelectedMembers)
			{
				if (!string.IsNullOrEmpty(memberUniqueName))
				{
					RadTreeNode node = memberTree.FindNodeByValue(EscapeUniqueName(memberUniqueName));
					if (node != null)
						node.Checked = true;
				}
			}
		}
	}

	private void BuildViewTree()
	{
		this.selectedItemsTree.Nodes.Clear();

		if (!string.IsNullOrEmpty(dimensionCombo.SelectedValue) && this.SelectedMembers != null)
		{
			string unescapedDimName = UnescapeUniqueName(dimensionCombo.SelectedValue);
			foreach (string memberUniqueName in this.SelectedMembers)
			{
				if (!string.IsNullOrEmpty(memberUniqueName))
				{
					try
					{
						Member mem = OLAPHelper.FindMemberByUniqueName(this.IsLegacy, memberUniqueName);
						if (mem != null)
							AddToViewTree(mem, true);
					}
					catch
					{
						//do nothing, don't add to view tree, and don't blow up!
					}
				}
			}
		}
	}

	private RadTreeNode AddToViewTree(Member mem, bool ensureChecked)
	{
		RadTreeNode node = selectedItemsTree.FindNodeByValue(EscapeUniqueName(mem.UniqueName));
		if (node == null)
		{
			node = new RadTreeNode(mem.Name);
			node.Value = EscapeUniqueName(mem.UniqueName);
			node.Expanded = true;
			if (mem.Parent != null && mem.Parent.Type != MemberTypeEnum.All)
			{
				RadTreeNode parentNode = AddToViewTree(mem.Parent, false);
				parentNode.Nodes.Add(node);
			}
			else
			{
				selectedItemsTree.Nodes.Add(node);
			}
		}

		if(ensureChecked)
			node.Checked = true;
	
		return node;
	}

    private void PopulateMembers(RadTreeNodeCollection nodes, Level level, bool isInitialLoad)
    { 
        foreach (Member mem in level.GetMembers())
        {
            RadTreeNode node = new RadTreeNode(mem.Name);
            node.Value = EscapeUniqueName(mem.UniqueName);
			//if (isInitialLoad) -- UPDATE: ALL ITEMS IN THE DIMENSION SELECTOR DEFAULT TO NOT CHECKED
			//{
			//	if (!mem.UniqueName.StartsWith("[Metric]") && !mem.UniqueName.StartsWith("[Metric Distribution]") && !mem.UniqueName.StartsWith("[Engineering Distribution]")
			//			&& !mem.UniqueName.StartsWith("[Engineering Field]") && !mem.UniqueName.StartsWith("[Event]") && !mem.UniqueName.StartsWith("[Event Status]")
			//			&& !mem.UniqueName.StartsWith("[Event Reason]") && !mem.UniqueName.StartsWith("[Setting]") && !mem.UniqueName.StartsWith("[Statistic]")
			//			&& !mem.UniqueName.StartsWith("[Distribution]") && !mem.UniqueName.StartsWith("[Distribution Values]"))
			//		node.Checked = true;
			//}

			nodes.Add(node);

            if (mem.ChildCount > 0)
				PopulateChildMembers(node.Nodes, mem, isInitialLoad);
        }
    }

	private void PopulateChildMembers(RadTreeNodeCollection nodes, Member parent, bool isInitialLoad)
    {
        foreach (Member mem in parent.GetChildren())
        {
			RadTreeNode node = new RadTreeNode(mem.Name);
            node.Value = EscapeUniqueName(mem.UniqueName);
			//if (isInitialLoad) -- UPDATE: ALL ITEMS IN THE DIMENSION SELECTOR DEFAULT TO NOT CHECKED
			//{
			//    if (!mem.UniqueName.StartsWith("[Metric]") && !mem.UniqueName.StartsWith("[Metric Distribution]") && !mem.UniqueName.StartsWith("[Engineering Distribution]")
			//            && !mem.UniqueName.StartsWith("[Engineering Field]") && !mem.UniqueName.StartsWith("[Event]") && !mem.UniqueName.StartsWith("[Event Status]")
			//            && !mem.UniqueName.StartsWith("[Event Reason]") && !mem.UniqueName.StartsWith("[Setting]") && !mem.UniqueName.StartsWith("[Statistic]")
			//            && !mem.UniqueName.StartsWith("[Distribution]") && !mem.UniqueName.StartsWith("[Distribution Values]"))
			//        node.Checked = true;
			//}

			bool hasRealChildren = false;
			foreach (Member child in mem.GetChildren())
			{
				if (string.Compare(child.Name, "Unspecified", true) != 0)
				{
					hasRealChildren = true;
					break;
				}
			}

			if (hasRealChildren)
				node.ExpandMode = TreeNodeExpandMode.ServerSideCallBack;

			nodes.Add(node);
        }
    }

	protected void Node_Expanded(object sender, RadTreeNodeEventArgs e)
	{
		bool isInitialLoad = (this.SelectedMembers == null || this.SelectedMembers.Count == 0);
		string nodeUniqueName = UnescapeUniqueName(e.Node.Value);
		Member member = OLAPHelper.FindMemberByUniqueName(this.IsLegacy, nodeUniqueName);
		PopulateChildMembers(e.Node.Nodes, member, isInitialLoad);

		//check previously selected nodes for newly loaded up load-on-demand items
		if (SelectedMembers != null)
		{
			foreach (string dimMember in this.SelectedMembers)
			{
				if (!string.IsNullOrEmpty(dimMember))
				{
					foreach (Member childMember in member.GetChildren())
					{
						if (childMember.UniqueName.Equals(dimMember))
						{
							RadTreeNode node = memberTree.FindNodeByValue(EscapeUniqueName(dimMember));
							if (node != null)
								node.Checked = true;
						}
					}
				}
			}
		}
	}

    private string EscapeUniqueName(string origName)
    {
        if (string.IsNullOrEmpty(origName))
            return null;
        else
            return origName.Replace(".", "\\~1").Replace("[", "\\~2").Replace("]", "\\~3");
    }

    private string UnescapeUniqueName(string escapedName)
    {
        if (string.IsNullOrEmpty(escapedName))
            return null;
        else
            return escapedName.Replace("\\~3", "]").Replace("\\~2", "[").Replace("\\~1", ".");
    }

	protected void SelectAll_Click(object sender, EventArgs e)
	{
		foreach (RadTreeNode node in memberTree.GetAllNodes())
		{
			node.Checked = true;
		}
	}

	protected void ClearAll_Click(object sender, EventArgs e)
	{
		if (SelectedMembers != null)
			SelectedMembers.Clear();

		foreach (RadTreeNode node in memberTree.GetAllNodes())
		{
			node.Checked = false;
		}
	}

	protected void Edit_Click(object sender, EventArgs e)
	{
		this.ShowEditableTree = true;
		this.editButton.Visible = false;
		//this.viewSelectedButton.Visible = true;

		this.clearAllBtn.Visible = true;
		if (this.DimensionName != null && (this.DimensionName.StartsWith("[Statistic]") || this.DimensionName.StartsWith("[Distribution]")))
			this.checkAllBtn.Visible = false;
		else
			this.checkAllBtn.Visible = true;
	}

    //protected void ViewSelected_Click(object sender, EventArgs e)
    //{
    //    this.ShowEditableTree = false;
    //    this.editButton.Visible = true;
    //    this.viewSelectedButton.Visible = false;

    //    this.clearAllBtn.Visible = false;
    //    this.checkAllBtn.Visible = false;
    //}

	protected void ClearDimension_Click(object sender, EventArgs e)
	{
        dimensionCombo.ClearSelection();
		dimensionCombo.Text = "";
		memberTree.Nodes.Clear();
		selectedItemsTree.Nodes.Clear();
	}

	protected void ValidateDimension(object sender, ServerValidateEventArgs e)
	{
        if (this.IsRequired && string.IsNullOrEmpty(dimensionCombo.SelectedValue))
			e.IsValid = false;
		else
			e.IsValid = true;
	}
}
