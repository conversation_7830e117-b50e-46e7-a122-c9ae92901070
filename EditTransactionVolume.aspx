<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditTransactionVolume.aspx.cs" Inherits="EditTransactionVolume" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:content id="h" contentplaceholderid="Head" runat="server">
<style type="text/css">
    #configSettingsArea div {
        padding: 0 0 10px 0;
    }
    #configSettingsArea div div {
        padding: 0 0 5px 0;
        font-weight:bold;
    }
    #configSettingsArea select {
        margin: 10px 0 0 0;
    }
    #configSettingsArea div select {
        margin: 0 0 0 40px;
    }
    #configSettingsArea div div a {
        display:inline-block;
        padding: 0 0 0 10px;
        font-weight:normal;
    }
</style>

</asp:content>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server">

<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
	<AjaxSettings>
		<telerik:AjaxSetting AjaxControlID="deviceTypeList">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="deviceList" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
				<telerik:AjaxUpdatedControl ControlID="configSettingsPanel" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
			</UpdatedControls>
		</telerik:AjaxSetting>
		<telerik:AjaxSetting AjaxControlID="deviceList">
			<UpdatedControls>
				<telerik:AjaxUpdatedControl ControlID="configSettingsPanel" LoadingPanelID="LoadingPanel1"></telerik:AjaxUpdatedControl>
			</UpdatedControls>
		</telerik:AjaxSetting>
	</AjaxSettings>
    <ClientEvents OnResponseEnd="ConfigSettingsOnResponseEnd" />
</telerik:RadAjaxManager>
<telerik:RadCodeBlock ID="RadCodeBlock1" runat="server">
<script type="text/javascript">
	function ToggleDatePopup()
    {
        $find("<%= dateField.ClientID %>").showPopup();
    } 
    function ToggleTimePopup()
    {
        $find("<%= timeField.ClientID %>").showTimePopup();
    }

    function BuildConfigObject() {
        var configObj = new Object();
        var data = $("#<%= configSettingsData.ClientID %>").val();

        if(data) {
            $.each($(data.split('~')), function(index, item) {
                var arr = item.split('/');
                if(arr && arr.length >= 2) {
                    configObj['fld_' + arr[0]] = arr[1];
                } else if(arr && arr.length == 1) {
                    configObj['fld_' + arr[0]] = null;
                }
            });
        }

        return configObj;
    }

    function SerializeConfigObject(configObj) {
        var data = '';
        $.each(configObj, function(name, value) {
            if(data) 
                data = data + '~';
            if(value)
                data = data + name.substr(4) + '/' + value;
            else
                data = data + name.substr(4);
        });
        $("#<%= configSettingsData.ClientID %>").val(data);
    }

    var fieldList = <%= BuildFieldJSON() %>;
    function ConfigSettingsOnResponseEnd(sender, args) {
        var deviceId = $("#<%= deviceList.ClientID %>").val();
        var configObj = BuildConfigObject();

        var area = $('#configSettingsArea');
        area.empty();
        if(deviceId && deviceId > 0) {
            var addFieldCtrl = $('<select></select>');
            addFieldCtrl.append($('<option></option>').attr('value', '').text('Add Field'));
            $.each($(fieldList), function(index, item) {
                if(typeof configObj['fld_' + item.Id] != "undefined") {
                    var fld = $('<div></div>').appendTo(area);
                    var label = $('<div></div>').text(item.Name).appendTo(fld);
                    var optionId = configObj['fld_' + item.Id];
                    var optionSel = $('<select></select>').appendTo(fld);
                    optionSel.append($('<option></option>').attr('value', '').text('Select Option'));
                    optionSel.change(function() {
                        var configObj = BuildConfigObject();
                        configObj['fld_' + item.Id] = $(this).val();
                        SerializeConfigObject(configObj);
                        ConfigSettingsOnResponseEnd(null, null);
                    });
                    $.each($(item.Options), function(subIndex, subItem) {
                        if(subItem.Id == optionId) {
                            optionSel.append($('<option></option>').attr('value', subItem.Id).text(subItem.Name).attr('selected','selected'));
                        } else {
                            optionSel.append($('<option></option>').attr('value', subItem.Id).text(subItem.Name));
                        }
                    });
                    $('<a>(remove)</a>').appendTo(label).click(function() { 
                        var configObj = BuildConfigObject();
                        delete configObj['fld_' + item.Id];
                        SerializeConfigObject(configObj);
                        ConfigSettingsOnResponseEnd(null, null);
                        return false;
                    });
                } else {
                    if(item.Options.length > 0) {
                        addFieldCtrl.append($('<option></option>').attr('value', item.Id).text(item.Name));
                    }
                }
            });

            addFieldCtrl.change(function () {
                if($(this).val()) {
                    var configObj = BuildConfigObject();
                    configObj['fld_' + $(this).val()] = null;
                    SerializeConfigObject(configObj);
                    ConfigSettingsOnResponseEnd(null, null);
                }
            });
            area.append(addFieldCtrl);
        } else {
            area.append($('<div>Select a device to configure settings</div>'));
        }
    }
    $(function() {
        ConfigSettingsOnResponseEnd(null, null);
    });
</script>
</telerik:RadCodeBlock>
<table width="100%" border="0" cellpadding="0" cellspacing="15">
	<tr>
		<td>
			<table width="100%" border="0" cellpadding="0" cellspacing="0">
				<tr>
					<td class="widgetTitle">Edit Transaction Volume</td>
					<td class="widgetTop" style="width:50%;">&nbsp;</td>
					<td class="widgetTop" style="text-align:right;">&nbsp;</td>
				</tr>
			</table>				
			<div class="widget">				
				<br />
				<div id="ErrorDiv" runat="server" visible="false" style="margin:10px 14px 0px 14px; border:solid 1px #ff0000; padding:10px 10px 10px 14px;">
					<asp:label id="ErrorMessage" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				</div>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr style="padding-top:10px;">
						<td style="width:55%;" valign="top">
							<table border="0" cellpadding="0" cellspacing="10">
								<tr>	
									<td style="width:190px;" class="rowHeading">Session:</td>
									<td>
										<asp:dropdownlist id="sessionList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:dropdownlist>
										<asp:requiredfieldvalidator id="val2" runat="server" controltovalidate="sessionList" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>										
									</td>
								</tr>
								<tr>	
									<td style="width:190px;" class="rowHeading">Cell:</td>
									<td>
										<asp:dropdownlist id="cellList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:dropdownlist>
										<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="cellList" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:190px;" class="rowHeading">Device Type:</td>
									<td>
										<asp:dropdownlist id="deviceTypeList" autopostback="true" onselectedindexchanged="DeviceType_IndexChanged" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:dropdownlist>										
									</td>
								</tr>
								<tr>	
									<td style="width:190px;" class="rowHeading">Device:</td>
									<td>
										<asp:dropdownlist id="deviceList" autopostback="true" onselectedindexchanged="Device_IndexChanged" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code" cssclass="entryControl" runat="server"></asp:dropdownlist>
									</td>
								</tr>								
								<tr>	
									<td style="width:190px;" class="rowHeading">Configuration Settings:</td>
									<td>
                                        <telerik:RadAjaxPanel id="configSettingsPanel" runat="server">
                                            <asp:hiddenfield id="configSettingsData" runat="server" />
                                        </telerik:RadAjaxPanel>
                                        <div id="configSettingsArea" class="entryControl"></div>
									</td>
								</tr>								
								<tr>	
									<td style="width:190px;" class="rowHeading">Date:</td>
									<td>
										<telerik:RadDatePicker id="dateField" cssclass="entryControl" runat="server" style="padding-top:1px;" width="80">        
											<calendar skin="Default2006" showrowheaders="false"></calendar>       
											<DatePopupButton Visible="False"></DatePopupButton>
											<DateInput onclick="ToggleDatePopup()"></DateInput>                           	                                             
										</telerik:RadDatePicker>
										<asp:requiredfieldvalidator id="val5" runat="server" enableclientscript="false" controltovalidate="dateField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<telerik:radtimepicker id="timeField" runat="server" timepopupbutton-visible="false" width="80">
											<dateinput onclick="ToggleTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
											<TimeView Interval="00:30:0" Columns="4" width="300" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
										</telerik:radtimepicker>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator2" runat="server" enableclientscript="false" controltovalidate="timeField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:190px;" class="rowHeading">Cumulative Tran Count:</td>
									<td>
										<asp:textbox id="transactionCountField" runat="server" cssclass="entryControl"></asp:textbox>
										<asp:requiredfieldvalidator id="val3" runat="server" controltovalidate="transactionCountField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<asp:comparevalidator id="val4" runat="server" display="dynamic" controltovalidate="transactionCountField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="* Invalid format"></asp:comparevalidator>
										<asp:customvalidator display="dynamic" id="Customvalidator1" runat="server" onservervalidate="ValidateTransactionCount" cssclass="error" errormessage="* Must be > 0"></asp:customvalidator>
									</td>
								</tr>
								<tr>	
									<td style="width:190px;" class="rowHeading">Cumulative Media Count:</td>
									<td>
										<asp:textbox id="mediaCountField" runat="server" cssclass="entryControl"></asp:textbox>
										<asp:requiredfieldvalidator id="Requiredfieldvalidator1" runat="server" controltovalidate="mediaCountField" display="dynamic" errormessage="*" cssclass="error"></asp:requiredfieldvalidator>
										<asp:comparevalidator id="Comparevalidator1" runat="server" display="dynamic" controltovalidate="mediaCountField" operator="dataTypeCheck" type="integer" cssclass="error" errormessage="* Invalid format"></asp:comparevalidator>
										<asp:customvalidator display="dynamic" id="Customvalidator2" runat="server" onservervalidate="ValidateMediaCount" cssclass="error" errormessage="* Must be >= 0"></asp:customvalidator>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
				
				<div id="ErrorDiv1" runat="server" visible="false" style="margin:10px 14px 10px 14px; border:solid 1px #ff0000; padding:10px 10px 10px 14px;">
					<asp:label id="ErrorMessage1" runat="server" style="color:#ff0000; font-weight:bold;"></asp:label>
				</div>
				
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>	
						<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" id="submitButton" onclick="SaveButton_Click">Save</asp:linkbutton></div></td>
						<td><div class="cancelButton"><asp:linkbutton runat="server" id="cancelButton" onclick="CancelButton_Click" causesvalidation="false">Cancel</asp:linkbutton></div></td>
					</tr>
				</table>
				<br />
				<br />
			</div>
		</td>
	</tr>
</table>
</asp:panel>

</asp:Content>

