<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="SaveReport.aspx.cs" Inherits="SaveReport" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript" language="javascript">
	function checkChanged(sender, args)
    {
		var btn = document.getElementById('<%= ToggleLibraryButton.ClientID %>');
        btn.click();
    }
    function closeSavePopup()
    {
        GetRadWindow().BrowserWindow.document.location.href='reportlibrary.aspx';
        CloseRadWindow();
    }
</script>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="saveButton">
	<telerik:radwindowmanager runat="server" ID="RadWindowManager" height="250" width="300" modal="true" title="" behaviors="Close, Resize, Move, Maximize, Reload" visiblestatusbar="false" destroyonclose="true">
		<windows>
			<telerik:radwindow runat="server" ID="CreateFolder" VisibleOnPageLoad="false" OffsetElementID="offsetElement"
				Top="30" Left="30" NavigateUrl="createfolderpopup.aspx" Title="" Height="250" Width="300" >
			</telerik:radwindow>
		</windows>
	</telerik:radwindowmanager>
	
	<script language="javascript" type="text/javascript">
		function clearMyLib()
		{
			var list = document.getElementById('<%= this.myLibraryList.ClientID %>');
			list.selectedIndex = -1;
		}
		function clearSharedLib()
		{
			var list = document.getElementById('<%= this.sharedLibraryList.ClientID %>');
			list.selectedIndex = -1;
		}
	</script>
	<telerik:radcodeblock id="codeBlockCntrl" runat="server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Save Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget" style="padding-top:10px;">
					<div class="rowHeading">Report Name:</div>
					<br />
					<table width="100%" border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:320px;">
								<asp:textbox id="reportNameField" runat="server" width="300" cssclass="entryControl" maxlength="100"></asp:textbox>&nbsp;
							</td>
							<td>
								<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="reportNameField" errormessage="*" display="dynamic" cssclass="error"></asp:requiredfieldvalidator>
								<asp:customvalidator id="reportNameVal" runat="server" onservervalidate="CheckReportName" controltovalidate="reportNameField" errormessage="* Report name is not unique." display="dynamic" cssclass="error"></asp:customvalidator>
							</td>
						</tr>
					</table>
					<br />
					
					<asp:hiddenfield id="hidUserName" runat="server" />
					<asp:button id="ToggleLibraryButton" onclick="ToggleLibrary" runat="server" style="display:none;" />
					<telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
						<ajaxsettings>
							<telerik:ajaxsetting ajaxcontrolid="ToggleLibraryButton">
								<updatedcontrols>
									<telerik:ajaxupdatedcontrol controlid="UpdatePanel" loadingpanelid="LoadingPanel1" />	
								</updatedcontrols>
							</telerik:ajaxsetting>
							<telerik:ajaxsetting ajaxcontrolid="saveButton">
								<updatedcontrols>
									<telerik:ajaxupdatedcontrol controlid="UpdatePanel" loadingpanelid="LoadingPanel1" />	
									<telerik:ajaxupdatedcontrol controlid="reportNameVal" />
								</updatedcontrols>
							</telerik:ajaxsetting>
						</ajaxsettings>
					</telerik:RadAjaxManager>
					<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
						<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
					</telerik:RadAjaxLoadingPanel>

					<asp:panel id="UpdatePanel" runat="server">
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td class="rowHeading" style="padding-top:8px; padding-bottom:7px;">Save Report To:</td>
							</tr>
							<tr>
								<td colspan="3" style="padding-top:10px; padding-bottom:10px;">
									<asp:radiobutton id="sessionsRadio" checked="true" onclick="checkChanged();" runat="server" cssclass="entryControl" groupname="saveReport" text="Selected Session(s)" />
									<table width="100%" border="0" cellpadding="0" cellspacing="0">
										<tr>
											<td style="width:40px;">&nbsp;</td>
											<td>
												<div style="height: 120px; width:715px; border: solid 1px #999999; overflow:auto;" >
													<asp:label id="sessionListLabel" runat="server"></asp:label>
												</div>
											</td>
										</tr>
									</table>
									<br />
									<asp:radiobutton id="libraryRadio" runat="server" onclick="checkChanged();" cssclass="entryControl" groupname="saveReport" text="Report Library" />
									<table width="100%" border="0" cellpadding="0" cellspacing="0">
										<tr>
											<td style="width:40px;">&nbsp;</td>
											<td>
												<asp:checkbox enabled="false" id="promptCheck" runat="server" text="Prompt user to choose sessions at report runtime. If left unchecked, the selected sessions above will be used each time." />
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
						
						<table width="100%" border="0" cellpadding="0" cellspacing="0">
							<tr>
								<td style="width:50%;" valign="top">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td class="rowHeading">My Library Folders:</td>
											<td class="rowHeading" style="text-align:right;"><table cellpadding="0" cellspacing="0" border="0"><tr><td><div id="Div6" runat="server" class="addButton"><a onclick="window.radopen('createfolderpopup.aspx?i=false', null);return false;">New Folder</a></div></td></tr></table></td>
										</tr>
										<tr>
											<td colspan="3" style="padding-top:10px; padding-bottom:10px;">
												<asp:listbox enabled="false" id="myLibraryList" runat="server" onchange="clearSharedLib();" width="350" rows="5" cssclass="entryControl"></asp:listbox>
											</td>
										</tr>
									</table>
								</td>
								<td style="width:50%; border-left:solid 10px #ffffff;" valign="top">
									<table border="0" cellpadding="0" cellspacing="0" width="100%">
										<tr>
											<td class="rowHeading">Shared Library Folders:</td>
											<td class="rowHeading" style="text-align: right;"><table border="0" cellpadding="0" cellspacing="0"><tr><td><div id="Div1" runat="server" class="addButton"><a onclick="window.radopen('createfolderpopup.aspx?i=true', null);return false;">NewFolder</a></div></td></tr></table></td>
										</tr>
										<tr>
											<td colspan="3" style="padding-top: 10px; padding-bottom: 10px;">
												<asp:listbox enabled="false" id="sharedLibraryList" runat="server" onchange="clearMyLib();" cssclass="entryControl" rows="5" width="350"></asp:listbox>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<tr>
								<td colspan="2"><asp:label id="reqFolderLabel" runat="server" visible="false" cssclass="entryControl, error">&nbsp;&nbsp;&nbsp;&nbsp;* A folder from either your personal library or the shared library must be selected.</asp:label></td>
							</tr>
						</table>			
					</asp:panel>	
					
					<table border="0" cellpadding="0" cellspacing="0" style="padding-top:10px;">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="saveButton">Save</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to cancel?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
				</div>
			</td>
		</tr>
	</table>
	</telerik:radcodeblock>
	
</asp:panel>

</asp:Content>

