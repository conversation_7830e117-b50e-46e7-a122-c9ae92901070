<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="Unauthorized.aspx.cs" Inherits="Unauthorized" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="15">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Application Error</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">You have encountered an error.</div>
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td class="leftPad">&nbsp;</td>
							<td>
								Insufficient user access to view the requested page.
							</td>
						</tr>
					</table>
					<br />
					<br />
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" postbackurl="~/dashboard.aspx" id="nextButton">Continue</asp:linkbutton></div></td>
						</tr>
					</table>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

