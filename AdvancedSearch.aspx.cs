using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using System.Text;
using System.Collections.Generic;

public partial class AdvancedSearch : System.Web.UI.Page
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			LoadDropDownData();

			if (Session[DieboldConstants.ADV_SEARCH_CELL] != null)
				Utility.SetListBoxSelectedValuesFromString(this.cellList, Session[DieboldConstants.ADV_SEARCH_CELL].ToString());
			
			if (Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] != null)
			{
				deviceTypeList.SelectedValue = Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE].ToString();
				DevceTypeList_SelectedIndexChanged(null, null);
			}
			
			if (Session[DieboldConstants.ADV_SEARCH_DEVICE] != null)
				Utility.SetListBoxSelectedValuesFromString(this.deviceList, Session[DieboldConstants.ADV_SEARCH_DEVICE].ToString()); 
			
			if (Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] != null)
				Utility.SetListBoxSelectedValuesFromString(this.failureLocationList, Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION].ToString()); 
			
			if (Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] != null)
				Utility.SetListBoxSelectedValuesFromString(this.failureTypeList, Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] != null)
				Utility.SetListBoxSelectedValuesFromString(this.moduleTypeList, Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] != null)
				Utility.SetListBoxSelectedValuesFromString(this.investigationAreaList, Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] != null)
				Utility.SetListBoxSelectedValuesFromString(this.triageTypeList, Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_DISCIPLINE] != null)
				Utility.SetListBoxSelectedValuesFromString(this.disciplineList, Session[DieboldConstants.ADV_SEARCH_DISCIPLINE].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_OWNER] != null)
				Utility.SetListBoxSelectedValuesFromString(this.ownerList, Session[DieboldConstants.ADV_SEARCH_OWNER].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_OPERATOR] != null)
				Utility.SetListBoxSelectedValuesFromString(this.operatorList, Session[DieboldConstants.ADV_SEARCH_OPERATOR].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_EVENT] != null)
			    Utility.SetListBoxSelectedValuesFromString(this.eventList, Session[DieboldConstants.ADV_SEARCH_EVENT].ToString());

            if (Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] != null)
                Utility.SetListBoxSelectedValuesFromString(this.mediaNumberList, Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER].ToString());

            if (Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER] != null)
                Utility.SetListBoxSelectedValuesFromString(this.fileNumberList, Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER].ToString());

            if (Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] != null)
                Utility.SetListBoxSelectedValuesFromString(this.commandNumberList, Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER].ToString());

			if (Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] != null) {
				this.projectedStartDateField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE]);
				this.projectedStartTimeField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE]);
			}

			if (Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] != null) {
				this.projectedEndDateField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE]);
				this.projectedEndTimeField.SelectedDate = Convert.ToDateTime(Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE]);
			}

			if (!string.IsNullOrEmpty(Convert.ToString(Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE])))
				minQuantizedVal.Text = Convert.ToString(Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE]);

			if (!string.IsNullOrEmpty(Convert.ToString(Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE])))
				maxQuantizedVal.Text = Convert.ToString(Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE]);

			solutionStateRep.DataSource = SqlHelper.ExecuteDataset("RPT_GetList_SolutionState");
			solutionStateRep.DataBind();

			if (!string.IsNullOrEmpty(Convert.ToString(Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE]))) {
				string value = Convert.ToString(Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE]);
				string[] savedSolutionStates = value.Split(new string[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
				foreach (string curState in savedSolutionStates) {
					string[] stateValues = curState.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
					if (stateValues != null && stateValues.Length > 0) {
						string solutionId = stateValues[0];

						foreach (RepeaterItem item in solutionStateRep.Items) {
							HiddenField hiddenId = (HiddenField)item.FindControl("SolutionStateId");
							if (hiddenId != null && hiddenId.Value == solutionId) {
								HtmlInputCheckBox pendingRadio = ((HtmlInputCheckBox)item.FindControl("pending"));
								HtmlInputCheckBox completeRadio = ((HtmlInputCheckBox)item.FindControl("complete"));
								HtmlInputCheckBox skipRadio = ((HtmlInputCheckBox)item.FindControl("skip"));

								if (stateValues[1] == "1")
									pendingRadio.Checked = true;
								if (stateValues[2] == "1")
									completeRadio.Checked = true;
								if (stateValues[3] == "1")
									skipRadio.Checked = true;

								break;
							}
						}
					}
				}
			}
		}
	}

	protected void SearchButton_Click(object sender, EventArgs e)
	{
        Utility.ClearSearchSession(true);

		if (!string.IsNullOrEmpty(cellList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_CELL] = Utility.ConvertListBoxSelectionsToString(cellList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_CELL_NAME] = Utility.ConvertListBoxSelectionsToString(cellList.Items, false);
		}
		if (!string.IsNullOrEmpty(deviceTypeList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE] = deviceTypeList.SelectedValue;
			Session[DieboldConstants.ADV_SEARCH_DEVICE_TYPE_NAME] = deviceTypeList.SelectedItem.Text;
		}
		if (!string.IsNullOrEmpty(deviceList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_DEVICE] = Utility.ConvertListBoxSelectionsToString(deviceList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_DEVICE_NAME] = Utility.ConvertListBoxSelectionsToString(deviceList.Items, false);
		}
		if (!string.IsNullOrEmpty(failureLocationList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION] = Utility.ConvertListBoxSelectionsToString(failureLocationList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_FAILURE_LOCATION_NAME] = Utility.ConvertListBoxSelectionsToString(failureLocationList.Items, false);
		}
		if (!string.IsNullOrEmpty(failureTypeList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE] = Utility.ConvertListBoxSelectionsToString(failureTypeList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_FAILURE_TYPE_NAME] = Utility.ConvertListBoxSelectionsToString(failureTypeList.Items, false);
		}
		if (!string.IsNullOrEmpty(moduleTypeList.SelectedValue)) {
			Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE] = Utility.ConvertListBoxSelectionsToString(moduleTypeList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_MODULE_TYPE_NAME] = Utility.ConvertListBoxSelectionsToString(moduleTypeList.Items, false);
		}
		if (!string.IsNullOrEmpty(investigationAreaList.SelectedValue)) {
			Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA] = Utility.ConvertListBoxSelectionsToString(investigationAreaList.Items, true);
            Session[DieboldConstants.ADV_SEARCH_INVESTIGATION_AREA_NAME] = Utility.ConvertListBoxSelectionsToString(investigationAreaList.Items, false);
		}
		if (!string.IsNullOrEmpty(this.triageTypeList.SelectedValue)) {
			Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE] = Utility.ConvertListBoxSelectionsToString(triageTypeList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_TRIAGE_TYPE_NAME] = Utility.ConvertListBoxSelectionsToString(triageTypeList.Items, false);
		}
		if (!string.IsNullOrEmpty(this.disciplineList.SelectedValue)) {
			Session[DieboldConstants.ADV_SEARCH_DISCIPLINE] = Utility.ConvertListBoxSelectionsToString(disciplineList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_DISCIPLINE_NAME] = Utility.ConvertListBoxSelectionsToString(disciplineList.Items, false);
		}
		if (!string.IsNullOrEmpty(ownerList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_OWNER] = Utility.ConvertListBoxSelectionsToString(ownerList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_OWNER_NAME] = Utility.ConvertListBoxSelectionsToString(ownerList.Items, false);
		}
		if (!string.IsNullOrEmpty(operatorList.SelectedValue)) {
			Session[DieboldConstants.ADV_SEARCH_OPERATOR] = Utility.ConvertListBoxSelectionsToString(operatorList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_OPERATOR_NAME] = Utility.ConvertListBoxSelectionsToString(operatorList.Items, false);
		}

		if (this.projectedStartDateField.SelectedDate != null && (DateTime)this.projectedStartDateField.SelectedDate != DateTime.MinValue) {
			TimeSpan time = TimeSpan.Zero;

			if (projectedStartTimeField.SelectedDate != null && projectedStartTimeField.SelectedDate != DateTime.MinValue)
				time = new TimeSpan(((DateTime)projectedStartTimeField.SelectedDate).Hour, ((DateTime)projectedStartTimeField.SelectedDate).Minute, ((DateTime)projectedStartTimeField.SelectedDate).Second);

			DateTime startDate = ((DateTime)projectedStartDateField.SelectedDate).Add(time);
			Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] = startDate;
		}
		else {
			Session[DieboldConstants.ADV_SEARCH_PROJECTED_START_DATE] = null;
		}

		if (this.projectedEndDateField.SelectedDate != null && (DateTime)this.projectedEndDateField.SelectedDate != DateTime.MinValue) {
			TimeSpan time = TimeSpan.Zero;

			if (projectedEndTimeField.SelectedDate != null && projectedEndTimeField.SelectedDate != DateTime.MinValue)
				time = new TimeSpan(((DateTime)projectedEndTimeField.SelectedDate).Hour, ((DateTime)projectedEndTimeField.SelectedDate).Minute, ((DateTime)projectedEndTimeField.SelectedDate).Second);

			DateTime endDate = ((DateTime)projectedEndDateField.SelectedDate).Add(time);
			Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] = endDate;
		}
		else {
			Session[DieboldConstants.ADV_SEARCH_PROJECTED_END_DATE] = null;
		}

		if (!string.IsNullOrEmpty(eventList.SelectedValue))
		{
			Session[DieboldConstants.ADV_SEARCH_EVENT] = Utility.ConvertListBoxSelectionsToString(eventList.Items, true);
			Session[DieboldConstants.ADV_SEARCH_EVENT_NAME] = Utility.ConvertListBoxSelectionsToString(eventList.Items, false);
		}

        if (!string.IsNullOrEmpty(mediaNumberList.SelectedValue))
        {
            Session[DieboldConstants.ADV_SEARCH_MEDIA_NUMBER] = Utility.ConvertListBoxSelectionsToString(mediaNumberList.Items, true);
        }

        if (!string.IsNullOrEmpty(fileNumberList.SelectedValue))
        {
            Session[DieboldConstants.ADV_SEARCH_FILE_NUMBER] = Utility.ConvertListBoxSelectionsToString(fileNumberList.Items, true);
        }

        if (!string.IsNullOrEmpty(commandNumberList.SelectedValue))
        {
            Session[DieboldConstants.ADV_SEARCH_COMMAND_NUMBER] = Utility.ConvertListBoxSelectionsToString(commandNumberList.Items, true);
        }

        if (!string.IsNullOrEmpty(minQuantizedVal.Text))
			Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] = minQuantizedVal.Text.Trim();
		else
			Session[DieboldConstants.ADV_SEARCH_MIN_QUANTIZED_VALUE] = null;

		if (!string.IsNullOrEmpty(maxQuantizedVal.Text))
			Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] = maxQuantizedVal.Text.Trim();
		else
			Session[DieboldConstants.ADV_SEARCH_MAX_QUANTIZED_VALUE] = null;

		statisticSelector.SaveSelectedValuesToSession();
		settingSelector.SaveSelectedValuesToSession();

		//collect SolutionState values
		StringBuilder solutionValues = new StringBuilder();
		StringBuilder solutionNames = new StringBuilder();
		foreach (RepeaterItem item in solutionStateRep.Items) {
			HiddenField hiddenId = (HiddenField)item.FindControl("SolutionStateId");
			HiddenField hiddenName = (HiddenField)item.FindControl("SolutionStateName");
			HtmlInputCheckBox pendingRadio = ((HtmlInputCheckBox)item.FindControl("pending"));
			HtmlInputCheckBox completeRadio = ((HtmlInputCheckBox)item.FindControl("complete"));
			HtmlInputCheckBox skipRadio = ((HtmlInputCheckBox)item.FindControl("skip"));

			if (hiddenId != null && ((pendingRadio != null && pendingRadio.Checked) || (completeRadio != null && completeRadio.Checked) || (skipRadio != null && skipRadio.Checked))) {
				if (solutionValues.Length > 0) {
					solutionValues.Append(";");
					solutionNames.Append("<br/>");
				}

				int pending = pendingRadio.Checked ? 1 : 0;
				int complete = completeRadio.Checked ? 1 : 0;
				int skip = skipRadio.Checked ? 1 : 0;
				solutionValues.AppendFormat("{0},{1},{2},{3}", hiddenId.Value, pending, complete, skip);
				solutionNames.AppendFormat(hiddenName.Value + ": ");

				StringBuilder checkedItemsList = new StringBuilder();
				if (pendingRadio.Checked)
					checkedItemsList.Append("Pending");
				if (completeRadio.Checked) {
					if (checkedItemsList.Length > 0)
						checkedItemsList.Append(", ");
					checkedItemsList.Append("Complete");
				}
				if (skipRadio.Checked) {
					if (checkedItemsList.Length > 0)
						checkedItemsList.Append(", ");
					checkedItemsList.Append("Skip");
				}
				solutionNames.Append(checkedItemsList.ToString());
            }
		}

		if (!string.IsNullOrEmpty(solutionValues.ToString())) {
			Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] = solutionValues.ToString();
			Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE_NAME] = solutionNames.ToString();
		}
		else {
			Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE] = null;
			Session[DieboldConstants.ADV_SEARCH_SOLUTION_STATE_NAME] = null;
		}	

		Response.Redirect("search.aspx");
	}

	protected void DevceTypeList_SelectedIndexChanged(object sender, EventArgs e)
	{
		deviceList.Items.Clear();
		failureLocationList.Items.Clear();
		eventList.Items.Clear();

		if (sender != null)
		{
			statisticSelector.ClearTree();
			settingSelector.ClearTree();
		}
			
		if (!string.IsNullOrEmpty(deviceTypeList.SelectedValue))
		{
			deviceList.DataSource = Utility.GetDevicesList(Convert.ToInt32(deviceTypeList.SelectedValue));
			deviceList.DataBind();

			failureLocationList.DataSource = Utility.GetFailureLocationList(Convert.ToInt32(deviceTypeList.SelectedValue));
			failureLocationList.DataBind();

			eventList.DataSource = Utility.GetEventTypeList(Convert.ToInt32(deviceTypeList.SelectedValue));
			eventList.DataBind();

			statisticSelector.PopulateTree(Convert.ToInt32(deviceTypeList.SelectedValue));
			settingSelector.PopulateTree(Convert.ToInt32(deviceTypeList.SelectedValue));
		}
	}

	protected void ResetButton_Click(object sender, EventArgs e) {
		cellList.ClearSelection();
		ownerList.ClearSelection();
		deviceTypeList.ClearSelection();
		deviceList.ClearSelection();
		failureLocationList.ClearSelection();
		failureTypeList.ClearSelection();
		investigationAreaList.ClearSelection();
		triageTypeList.ClearSelection();
		disciplineList.ClearSelection();
		ownerList.ClearSelection();
		operatorList.ClearSelection();
		eventList.ClearSelection();
		mediaNumberList.ClearSelection();
		fileNumberList.ClearSelection();
		commandNumberList.ClearSelection();
		moduleTypeList.ClearSelection();

		minQuantizedVal.Text = "";
		maxQuantizedVal.Text = "";

		projectedStartDateField.SelectedDate = null;
		projectedStartTimeField.SelectedDate = null;
		projectedEndDateField.SelectedDate = null;
		projectedEndTimeField.SelectedDate = null;

		statisticSelector.ClearSelections();
		settingSelector.ClearSelections();

		foreach (RepeaterItem item in solutionStateRep.Items) {
			HtmlInputCheckBox pendingRadio = ((HtmlInputCheckBox)item.FindControl("pending"));
			HtmlInputCheckBox completeRadio = ((HtmlInputCheckBox)item.FindControl("complete"));
			HtmlInputCheckBox skipRadio = ((HtmlInputCheckBox)item.FindControl("skip"));
			pendingRadio.Checked = false;
			completeRadio.Checked = false;
			skipRadio.Checked = false;
		}

		Utility.ClearSearchSession(true);
	}

	private void LoadDropDownData()
	{
		cellList.DataSource = Utility.GetCellList();
		cellList.DataBind();

		ownerList.DataSource = Utility.GetOperatorList();
		ownerList.DataBind();

		deviceTypeList.Items.Add(new RadComboBoxItem("Select...", ""));
		deviceTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
		deviceTypeList.DataBind();

		failureTypeList.DataSource = Utility.GetFailureTypeList();
		failureTypeList.DataBind();

		moduleTypeList.DataSource = Utility.GetDeviceTypeFullNameList();
		moduleTypeList.DataBind();

		investigationAreaList.DataSource = Utility.GetInvestigationAreaList();
		investigationAreaList.DataBind();

		triageTypeList.DataSource = Utility.GetTriageTypeList();
		triageTypeList.DataBind();

		disciplineList.DataSource = Utility.GetDisciplineList();
		disciplineList.DataBind();

		ownerList.DataSource = Utility.GetOperatorList();
		ownerList.DataBind();

		operatorList.DataSource = Utility.GetOperatorList();
		operatorList.DataBind();

        mediaNumberList.Items.Clear();
        for (int x = 1; x <= 150; x++)
            mediaNumberList.Items.Add(new ListItem(x.ToString(), x.ToString()));

        fileNumberList.Items.Clear();
        for (int x = 1; x <= 25; x++)
            fileNumberList.Items.Add(new ListItem(x.ToString(), x.ToString()));

        commandNumberList.Items.Clear();
        for (int x = 1; x <= 25; x++)
            commandNumberList.Items.Add(new ListItem(x.ToString(), x.ToString()));
    }
}
