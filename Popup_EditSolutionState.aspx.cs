using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

public partial class Popup_EditSolutionState: System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	public string ItemId
	{
		get { return (string)this.ViewState["i"]; }
		set { this.ViewState["i"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			PopulateOrderNumberList();

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SOLUTION_STATE_ID_KEY])) {
				this.ItemId = Request.Params[DieboldConstants.SOLUTION_STATE_ID_KEY];

				DataSet ds = SqlHelper.ExecuteDataset("RPT_GetSolutionState", this.ItemId);
				foreach (DataRow row in ds.Tables[0].Rows) {
					this.nameField.Text = DataFormatter.getString(row, "SolutionStateName");
					this.isActive.Checked = DataFormatter.getBool(row, "IsActive");
					this.orderNumList.SelectedValue = DataFormatter.getInt32(row, "OrderNumber").ToString();
				}
			}
		}
	}

	private void PopulateOrderNumberList() 
	{
		//populate order number list
		int orderNum = 1;

		DataSet solutions = SqlHelper.ExecuteDataset("RPT_GetList_SolutionState");

		if (solutions != null && solutions.Tables[0] != null && solutions.Tables[0].Rows.Count > 0) {
			foreach (DataRow row in solutions.Tables[0].Rows) {
				this.orderNumList.Items.Add(new ListItem(orderNum.ToString(), orderNum.ToString()));
				orderNum++;
			}
		}

		if (string.IsNullOrEmpty(Request.Params[DieboldConstants.SOLUTION_STATE_ID_KEY])) {
			//add one more choice for new items
			this.orderNumList.Items.Add(new ListItem(orderNum.ToString(), orderNum.ToString()));
		}

		this.orderNumList.SelectedValue = orderNum.ToString();
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (string.IsNullOrEmpty(ItemId))
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertSolutionState", this.nameField.Text, this.orderNumList.SelectedValue);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateSolutionState", Convert.ToInt32(this.ItemId), this.nameField.Text, this.isActive.Checked, this.orderNumList.SelectedValue);

		if (result == 0)
			this.ErrorLabel.Visible = true;
		else
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='EditSolutionState.aspx?page={0}';CloseRadWindow(); ", this.PageNo), true);
			else
				ScriptManager.RegisterStartupScript(this, this.GetType(), "close", "GetRadWindow().BrowserWindow.document.location.href='EditSolutionState.aspx';CloseRadWindow(); ", true);
	}
}
