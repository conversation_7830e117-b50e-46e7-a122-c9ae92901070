<device-description>

	<!-- ################################################################################################## -->
	<!-- ################################################################################################## -->
	<!-- #####   SNA - Single Note Acceptor                                                           ##### -->
	<!-- #####   RDTool Command Definition File                                                       ##### -->
	<!-- #####                                                                                        ##### -->
	<!-- #####   File Version: 0.0                                                                    ##### -->
	<!-- #####   Based on PRM: 1.00                                                                   ##### -->
	<!-- ################################################################################################## -->
	<!-- ################################################################################################## -->


	<!-- ################################################################################################## -->
	<!-- #####   Common Information Commands                                                          ##### -->
	<!-- ################################################################################################## -->

	<!-- ################################################## -->
	<!-- #####   GET_STATUS                           ##### -->
	<!-- ################################################## -->
	<command name="GET_STATUS" code="1" type="I"/>	
	<status-entities>
		<entities>
			<entity id="17000" name="SNA_GENERAL_STATUS" type="BitField">
				<values>
					<value value="0" name="SNA_MODULE_OK"/>
					<value value="1" name="SNA_MODULE_PAUSED"/>
					<value value="2" name="SNA_MODULE_CALIBRATION"/>
					<value value="4" name="SNA_MODULE_NO_PUSH"/>
					<value value="8" name="SNA_MODULE_FLASH_DOWNLOAD"/>
					<value value="16" name="SNA_MODULE_FAILURE"/>
				</values>
			</entity>
			<entity id="17001" name="SNA_LAST_NOTE_STATUS">
				<values>
					<value value="0" name="SNA_LAST_NOTE_NO_INFO"/>
					<value value="1" name="SNA_LAST_NOTE_ESCROWED"/>
					<value value="2" name="SNA_LAST_NOTE_JAMMED"/>
				</values>
			</entity>
			<entity id="17002" name="SNA_CURRENT_NOTE_STATUS">
				<values>
					<value value="0" name="SNA_NOTE_NO_INFO"/>
					<value value="1" name="SNA_NOTE_REJECTED"/>
					<value value="2" name="SNA_NOTE_STACKED"/>
					<value value="3" name="SNA_NOTE_CHEATED"/>
					<value value="4" name="SNA_NOTE_RETURNED"/>
				</values>
			</entity>
			<entity id="17003" name="SNA_CASHBOX_STATUS">
				<values>
					<value value="0" name="SNA_CASHBOX_OK"/>
					<value value="1" name="SNA_CASHBOX_FULL"/>
					<value value="2" name="SNA_CASHBOX_NOT_PRESENT"/>
				</values>
			</entity>
		</entities>
	</status-entities>

	<!-- ################################################## -->
	<!-- #####   GET_CAPABILITIES                     ##### -->
	<!-- ################################################## -->
	<command name="GET_CAPABILITIES" code="2" type="I">
		<output-parameter>
			<name>Capabilities</name>
			<type>%e</type>
			<entities>
				<entity id="1"  name="DBDDEV_HARDWARE_REVISION"/>
				<entity id="2"  name="DBDDEV_SERIAL_NUMBER"/>
				<entity id="3"  name="DBDDEV_HARDWARE_OPTIONS"/>
				<entity id="4"  name="DBDDEV_AMI_VERSION"/>
				<entity id="6"  name="DBDDEV_PRIMARY_FIRMWARE_VERSION"/>
				<entity id="9"  name="DBDDEV_LEAD_THROUGH"/>
				<entity id="50" name="DBDDEV_PART_IDENTIFIER"/>
				<entity id="17500" name="SNA_FIRMWARE_CURRENCIES"/>
				<entity id="17501" name="SNA_FIRMWARE_CURRENCY_VERSION"/>
				<entity id="17502" name="SNA_BOOTLOADER_FIRMWARE_VERSION"/>
				<entity id="17503" name="SNA_CAP_ACCEPTOR_CRC"/>
				<entity id="17504" name="SNA_CAP_ACCEPTOR_MODEL"/>
				<entity id="17505" name="SNA_CAP_ACCEPTOR_TYPE"/>
				<entity id="17506" name="SNA_CAP_NROF_ESCROWS"/>
				<entity id="17507" name="SNA_CAP_NROF_CASHBOXES"/>
			</entities>
		</output-parameter>
	</command>

	<!-- ################################################## -->
	<!-- #####   GET_CONFIGURATION                    ##### -->
	<!-- ################################################## -->
	<command name="GET_CONFIGURATION" code="3" type="I">
		<output-parameter>
			<name>Configuration</name>
			<type>%e</type>
			<entities>
				<entity id="1"  name="DBDDEV_HARDWARE_REVISION"/>
				<entity id="2"  name="DBDDEV_SERIAL_NUMBER"/>
				<entity id="3"  name="DBDDEV_HARDWARE_OPTIONS"/>
				<entity id="4"  name="DBDDEV_MANUFACTURE_LOCATION"/>
				<entity id="5"  name="DBDDEV_MANUFACTURE_DATE"/>
				<entity id="6"  name="DBDDEV_TERMINAL_POSITION"/>
				<entity id="7"  name="DBDDEV_POWER_MANAGEMENT_DEFAULT_TIMEOUT" signed="FALSE"/>
				<entity id="8"  name="DBDDEV_FLASH_DOWNLOAD_SUPPORT" signed="FALSE"/>
				<entity id="50" name="DBDDEV_PART_IDENTIFIER"/>
				<entity id="17600" name="SNA_CONF_ORIENTATION">
					<values>
						<value value="0" name="SNA_ORIENTATION_ONE_WAY"/>
						<value value="1" name="SNA_ORIENTATION_TWO_WAY"/>
						<value value="3" name="SNA_ORIENTATION_FOUR_WAY"/>
					</values>
				</entity>
				<entity id="17601" name="SNA_CONF_ESCROW_MODE">
					<values>
						<value value="0" name="SNA_CONFIG_DISABLE"/>
						<value value="1" name="SNA_CONFIG_ENABLE"/>
					</values>
				</entity>
				<entity id="17602" name="SNA_CONF_POWERUP_MODE">
					<values>
						<value value="0" name="SNA_POWERUP_A"/>
						<value value="1" name="SNA_POWERUP_B"/>
						<value value="2" name="SNA_POWERUP_C"/>
					</values>
				</entity>
			</entities>
		</output-parameter>
	</command>

	<!-- ################################################## -->
	<!-- #####   GET_METRICS                          ##### -->
	<!-- ################################################## -->
	<command name="GET_METRICS" code="4" type="I">
		<output-parameter>
			<name>Metrics</name>
			<type>%t</type>
			<entities>
        <entity id="1700"  name="SNA_PERFORMANCE_DATA_MAP_ID"/>
        <entity id="1701"  name="SNA_OPERATING_HOURS_LIFETIME"/>
        <entity id="1702"  name="SNA_MOTOR_STARTS_LIFETIME"/>
        <entity id="1703"  name="SNA_DOCUMENTS_REACHED_ESCROW_POS"/>
        <entity id="1704"  name="SNA_DOCUMENTS_THRU_RECOGNITION"/>
        <entity id="1705"  name="SNA_DOCUMENTS_THRU_VALIDATION"/>
        <entity id="1706"  name="SNA_DENOMINATION_DISABLED_REJECTIONS"/>
        <entity id="1707"  name="SNA_DOCUMENTS_DENOMINATED"/>
        <entity id="1708"  name="SNA_DOCUMENTS_INSERTED_WHILE_DISABLED"/>
        <entity id="1709"  name="SNA_DOCUMENTS_TO_ESCROW"/>
        <entity id="1710"  name="SNA_FAST_FEED_REJECTIONS"/>
        <entity id="1711"  name="SNA_TOTAL_NOTES_RETURNED"/>
        <entity id="1712"  name="SNA_ACCEPTANCE_RATE_LAST_100_BILLS"/>
        <entity id="1713"  name="SNA_MOTOR_STARTS"/>
        <entity id="1714"  name="SNA_ORIENTATION_DISABLED_REJECTIONS"/>
        <entity id="1715"  name="SNA_RECOGNITION_REJECTIONS"/>
        <entity id="1716"  name="SNA_TOTAL_NOTES_PROCESSED"/>
        <entity id="1717"  name="SNA_DOCUMENTS_PASSED_VALIDATION"/>
        <entity id="1718"  name="SNA_VALIDATION_REJECTIONS"/>
        <entity id="1719"  name="SNA_CALIBRATIONS"/>
        <entity id="1720"  name="SNA_CASSETTES_FULL"/>
        <entity id="1721"  name="SNA_CASSETTES_REMOVED"/>
        <entity id="1722"  name="SNA_CROSS_CHANNEL_1_REJECTS"/>
        <entity id="1723"  name="SNA_CROSS_CHANNEL_2_REJECTS"/>
        <entity id="1724"  name="SNA_DOWNLOADS"/>
        <entity id="1725"  name="SNA_JAM_RECOVERY_EFFORTS"/>
        <entity id="1726"  name="SNA_JAMS_WITHOUT_RECOVERY_ENABLED"/>
        <entity id="1727"  name="SNA_DOCUMENTS_EXCEEDING_MAXIMUM_LENGTH"/>
        <entity id="1728"  name="SNA_DOCUMENTS_FAILED_TO_REACH_ESCROW"/>
        <entity id="1729"  name="SNA_OUT_OF_ORDER_CONDITIONS"/>
        <entity id="1730"  name="SNA_OUT_OF_SERVICE_CONDITIONS"/>
        <entity id="1731"  name="SNA_OPERATING_HOURS"/>
        <entity id="1732"  name="SNA_REJECT_ATTEMPTS_FOLLOWED_BY_JAM"/>
        <entity id="1733"  name="SNA_RESETS"/>
        <entity id="1734"  name="SNA_DOCUMENTS_UNDER_MINIMUM_LENGTH"/>
        <entity id="1735"  name="SNA_STACKER_JAMS"/>
        <entity id="1736"  name="SNA_SUM_OF_ALL_JAMS"/>
      </entities>
		</output-parameter>
	</command>

  <!-- ################################################################################################## -->
  <!-- #####   Specific Information Commands                                                        ##### -->
  <!-- ################################################################################################## -->

  <command name="SNA_GET_NOTE_INFORMATION" code="17000" type="I">
    <output-parameter>
      <name>ulNumInfoEntities</name>
      <type>%l</type>
    </output-parameter>
    <!-- Note_1 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>
    
    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>
    
    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>
    
    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>
    
    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_2 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_3 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_4 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_5 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_6 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_7 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!-- Note_8 -->
    <output-parameter>
      <name>Currency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
    <output-parameter>
      <name>Exponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Orientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>Type</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>Series</name>
      <type>%.2s</type>
    </output-parameter>

    <!--    
    <output-parameter>
      <name>Note_1</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_2</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_3</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_4</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_5</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_6</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_7</name>
      <type>%.16h</type>
    </output-parameter>
    <output-parameter>
      <name>Note_8</name>
      <type>%.16h</type>
    </output-parameter>
    -->
  </command>


  <command name="//separator"/>
	<!-- ################################################################################################## -->
	<!-- #####   Common Execute commands                                                              ##### -->
	<!-- ################################################################################################## -->

	<!-- ################################################## -->
	<!-- #####   RESET                                ##### -->
	<!-- ################################################## -->
	<command name="RESET" code="10" type="E"/>

	<!-- ################################################## -->
	<!-- #####   FIRMWARE_DOWNLOAD                    ##### -->
	<!-- ################################################## -->
	<command name="FIRMWARE_DOWNLOAD" code="14" type="E">
		<input-parameter>
			<control>file-dialog</control>
			<name>lpszProgramFile</name>
			<type>%f</type>
			<value>281804310_CRC_8BAA_EBDS_SC83_FLASH.BIN</value>
		</input-parameter>
	</command>

	<!-- ################################################## -->
	<!-- #####   SET_CONFIGURATION                    ##### -->
	<!-- ################################################## -->
	<command name="SET_CONFIGURATION" code="11" type="E">

		<input-parameter>
			<control>edit-box</control>
			<name>Number of Entities</name>
			<type>%l</type>
			<value>8</value>
		</input-parameter>
	<!-- #################################################################################################### -->   
    <input-parameter>
      <control>hidden</control>
      <name>Entity ID</name>
      <type>%l</type>
      <value>4</value>
    </input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>String Type</name>
      <type>%l</type>
      <value>0</value>
    </input-parameter>
    <input-parameter>
      <control>edit-box</control>
      <name>DBDDEV_MANUFACTURE_LOCATION</name>
      <type>%.32s</type>
      <value>Danville, VA U.S.A</value>
    </input-parameter>
    <!-- #################################################################################################### -->
    <input-parameter>
      <control>hidden</control>
      <name>Entity ID</name>
      <type>%l</type>
      <value>5</value>
    </input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Numeric Type</name>
      <type>%l</type>
      <value>1</value>
    </input-parameter>
    <input-parameter>
      <control>edit-box</control>
      <name>DBDDEV_MANUFACTURE_DATA</name>
      <type>%l</type>
      <value>99999999</value>
    </input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Union Complement for numeric value</name>
      <type>%.028s</type>
      <value>----------------------------</value>
    </input-parameter>
    <!-- #################################################################################################### -->
		<input-parameter>
			<control>hidden</control>
			<name>Entity ID</name>
			<type>%l</type>
			<value>6</value>
		</input-parameter>
		<input-parameter>
			<control>hidden</control>
			<name>String Type</name>
			<type>%l</type>
			<value>0</value>
		</input-parameter>
		<input-parameter>
			<control>radio-button</control>
			<name>DBDDEV_TERMINAL_POSITION</name>
			<type>%.032s</type>
			<caption>TOP HAT</caption>
			<value>TOP HAT</value>
			<caption>CHEST</caption>
			<value>CHEST</value>
			<caption>SIDEKICK</caption>
			<value>SIDEKICK</value>
			<caption>DOG HOUSE</caption>
			<value>DOG HOUSE</value>
		</input-parameter>
	<!-- #################################################################################################### -->
		<input-parameter>
			<control>hidden</control>
			<name>Entity ID</name>
			<type>%l</type>
			<value>7</value>
		</input-parameter>
		<input-parameter>
			<control>hidden</control>
			<name>Number Type</name>
			<type>%l</type>
			<value>1</value>
		</input-parameter>
		<input-parameter>
			<control>edit-box</control>
			<name>DBDDEV_POWER_MANAGEMENT_DEFAULT_TIMEOUT</name>
			<type>%l</type>
			<value>150000</value>
		</input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Union Complement for numeric value</name>
      <type>%.028s</type>
      <value>----------------------------</value>
    </input-parameter>
  <!-- #################################################################################################### -->
    <input-parameter>
      <control>hidden</control>
      <name>Entity ID</name>
      <type>%l</type>
      <value>8</value>
    </input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Numeric Type</name>
      <type>%l</type>
      <value>1</value>
    </input-parameter>
    <input-parameter>
      <control>edit-box</control>
      <name>DBDDEV_FLASH_DOWNLOAD_SUPPORT</name>
      <type>%l</type>
      <value>1</value>
    </input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Union Complement for numeric value</name>
      <type>%.028s</type>
      <value>----------------------------</value>
    </input-parameter>
  <!-- #################################################################################################### -->
    <input-parameter>
			<control>hidden</control>
			<name>Entity ID</name>
			<type>%l</type>
			<value>17600</value>
		</input-parameter>
		<input-parameter>
			<control>hidden</control>
			<name>Number Type</name>
			<type>%l</type>
			<value>1</value>
		</input-parameter>
		<input-parameter>
			<control>radio-button</control>
			<name>SNA_CONF_ORIENTATION</name>
			<type>%l</type>
			<caption>ONE_WAY</caption>
			<value>0</value>
			<caption>TWO_WAY</caption>
			<value>1</value>
			<caption>FOUR_WAY</caption>
			<value>3</value>
			<caption>INVALID_VALUE</caption>
			<value>3000</value>
		</input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Union Complement for numeric value</name>
      <type>%.028s</type>
      <value>----------------------------</value>
    </input-parameter>
  <!-- #################################################################################################### -->
 		<input-parameter>
			<control>hidden</control>
			<name>Entity ID</name>
			<type>%l</type>
			<value>17601</value>
		</input-parameter>
		<input-parameter>
			<control>hidden</control>
			<name>Number Type</name>
			<type>%l</type>
			<value>1</value>
		</input-parameter>
		<input-parameter>
			<control>radio-button</control>
			<name>SNA_CONF_ESCROW_MODE</name>
			<type>%l</type>
			<caption>DISABLE</caption>
			<value>0</value>
			<caption>ENABLE</caption>
			<value>1</value>
			<caption>INVALID_VALUE</caption>
			<value>3000</value>
		</input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Union Complement for numeric value</name>
      <type>%.028s</type>
      <value>----------------------------</value>
    </input-parameter>
  <!-- #################################################################################################### -->
 		<input-parameter>
			<control>hidden</control>
			<name>Entity ID</name>
			<type>%l</type>
			<value>17602</value>
		</input-parameter>
		<input-parameter>
			<control>hidden</control>
			<name>Number Type</name>
			<type>%l</type>
			<value>1</value>
		</input-parameter>
		<input-parameter>
			<control>radio-button</control>
			<name>SNA_CONF_POWERUP_MODE</name>
			<type>%l</type>
			<caption>POWERUP_A</caption>
			<value>0</value>
			<caption>POWERUP_B</caption>
			<value>1</value>
			<caption>POWERUP_C</caption>
			<value>2</value>
			<caption>INVALID_VALUE</caption>
			<value>3000</value>
		</input-parameter>
    <input-parameter>
      <control>hidden</control>
      <name>Union Complement for numeric value</name>
      <type>%.028s</type>
      <value>----------------------------</value>
    </input-parameter>
  </command>

	<!-- ################################################## -->
	<!-- #####   SELF_TEST                            ##### -->
	<!-- ################################################## -->
	<command name="SELF_TEST" code="12" type="E" />


	<command name="//separator"/>
	<!-- ################################################################################################## -->
	<!-- #####   Single Note Acceptor Specific Execute Commands                                       ##### -->
	<!-- ################################################################################################## -->

	<!-- ################################################## -->
	<!-- #####   SNA_ENABLE                           ##### -->
	<!-- ################################################## -->
	<command name="SNA_ENABLE" code="17001" type="E" >
		<input-parameter>
			<control>radio-button</control>
			<name>EnableMode</name>
			<type>%l</type>
			<caption>SNA_BY_TABLE_POSITION</caption>
			<value>0</value>
			<caption>SNA_BY_VALUE</caption>
			<value>1</value>
			<caption>INVALID_VALUE</caption>
			<value>3000</value>
    </input-parameter>
    <input-parameter>
      <control>edit-box</control>
      <name>usNote</name>
      <type>%.100h</type>
      <value>.01.00.01.00.01.00.01.00.01.00.01.00.01.00.01.00</value>
    </input-parameter>
  </command>

  <!-- ################################################## -->
	<!-- #####   SNA_DISABLE                          ##### -->
	<!-- ################################################## -->
	<command name="SNA_DISABLE" code="17002" type="E" />

	<!-- ################################################## -->
	<!-- #####   SNA_STACK                            ##### -->
	<!-- ################################################## -->
	<command name="SNA_STACK" code="17003" type="E" />

	<!-- ################################################## -->
	<!-- #####   SNA_RETURN                           ##### -->
	<!-- ################################################## -->
	<command name="SNA_RETURN" code="17004" type="E" />

  <!-- ################################################## -->
  <!-- #####   SNA_ENABLE_BY_DENOMINATION           ##### -->
  <!-- ################################################## -->
  <command name="SNA_ENABLE_BY_DENOMINATION" code="17005" type="E" >
    <input-parameter>
      <control>radio-button</control>
      <name>lDenominations</name>
      <type>%l</type>
      <caption>SNA_DENOM1</caption>
      <value>1</value>
      <caption>SNA_DENOM2</caption>
      <value>2</value>
      <caption>SNA_DENOM3</caption>
      <value>4</value>
      <caption>SNA_DENOM4</caption>
      <value>8</value>
      <caption>SNA_DENOM5</caption>
      <value>16</value>
      <caption>SNA_DENOM6</caption>
      <value>32</value>
      <caption>SNA_DENOM7</caption>
      <value>64</value>
      <caption>SNA_ALL_DENOM</caption>
      <value>127</value>
      <caption>INVALID_VALUE</caption>
      <value>3000</value>
    </input-parameter>
  </command>

  <!-- ################################################################################################## -->
	<!-- #####   Events                                                                               ##### -->
	<!-- ################################################################################################## -->

	<command name="DEVICE_ONLINE" code="505" type="V"/>
	<command name="DEVICE_OFFLINE" code="506" type="V"/>
	<command name="LOCK_REQUESTED" code="502" type="V"/>
	<command name="LED_RESET" code="511" type="V"/>

	<command name="SNA_EVT_ESCROWED" code="17500" type="V">
    <output-parameter>
      <name>szCurrency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>lValue</name>
      <type>%l</type>
    </output-parameter>

    <output-parameter>
      <name>lExponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>lOrientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>szType</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>szSeries</name>
      <type>%.2s</type>
    </output-parameter>
  </command>
  <command name="SNA_EVT_STACKED" code="17501" type="V">
    <output-parameter>
      <name>szCurrency</name>
      <type>%.4s</type>
    </output-parameter>

    <output-parameter>
      <name>lValue</name>
      <type>%l</type>
    </output-parameter>

    <output-parameter>
      <name>lExponent</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>lOrientation</name>
      <type>%d</type>
    </output-parameter>

    <output-parameter>
      <name>szType</name>
      <type>%.2s</type>
    </output-parameter>

    <output-parameter>
      <name>szSeries</name>
      <type>%.2s</type>
    </output-parameter>
  </command>
  <command name="SNA_EVT_ESCROWED_BY_DENOM" code="17502" type="V">
    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
  </command>
  <command name="SNA_EVT_STACKED_BY_DENOM" code="17503" type="V">
    <output-parameter>
      <name>Value</name>
      <type>%l</type>
    </output-parameter>
  </command>
  <command name="SNA_EVT_RETURNED" code="17504" type="V"/>
  <command name="SNA_EVT_REJECTED" code="17505" type="V"/>
  <command name="SNA_EVT_CASHBOX_REMOVED" code="17506" type="V"/>
  <command name="SNA_EVT_CASHBOX_FULL" code="17507" type="V"/>
  <command name="SNA_EVT_CASHBOX_OK" code="17508" type="V"/>
  <command name="SNA_EVT_JAMMED" code="17509" type="V"/>
  <command name="SNA_EVT_CLEAR_JAMMED" code="17510" type="V"/>
  <command name="SNA_EVT_CHEATED" code="17511" type="V"/>

  <!-- ################################################################################################## -->
  <!-- #####   Extra                                                                                ##### -->
  <!-- ################################################################################################## -->

  <command name="//separator"/>
  <command name="SNA_INVALID_COMMAND" code="15" type="E"/>

</device-description>
