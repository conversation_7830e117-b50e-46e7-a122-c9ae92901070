using System;
using System.Data;
using System.Drawing;
using System.Configuration;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Telerik.Web.UI;
using Dundas.Charting;
using Dundas.Charting.WebControl;
using Microsoft.AnalysisServices.AdomdClient;
using QueueServiceClient;

public partial class ImportOverview : System.Web.UI.Page
{
	public bool IsUserAdmin
	{
		get { if (this.ViewState["ia"] != null) return (bool)this.ViewState["ia"]; else return false; }
		set { this.ViewState["ia"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			//this.ErrorGrid.DataSource = QueueErrors.GetErrorList();
			//this.ErrorGrid.DataBind();

			this.IsUserAdmin = Utility.IsUserAdmin();

			this.startDateField.SelectedDate = DateTime.Now.AddHours(-24);
			this.startTimeField.SelectedDate = DateTime.Now;

			this.endDateField.SelectedDate = DateTime.Now.AddDays(1);

			PopulateStatCharts();
		}

		ReprocessAllBtn.Attributes.Add("onclick", "return confirm('Are you sure you wish to reprocess all files?');");
		ClearAllBtn.Attributes.Add("onclick", "return confirm('Are you sure you wish to delete all errors?');");

		this.jobsPendingLabel.Text = QueueStatistics.GetPendingJobCount().ToString();
		this.jobsProcessingLabel.Text = QueueStatistics.GetProcessingJobCount().ToString();
	}

	protected void LoadStatistics_Click(object sender, EventArgs e)
	{
		PopulateStatCharts();
	}

	protected string GetValue(string fileName)
	{
		ErrorResult details = QueueErrors.GetErrorDetails(fileName);
		if (details != null)
		{
			if (!string.IsNullOrEmpty(details.ErrorText))
				return DataFormatter.FormatHtml(details.ErrorText, true, true);
			else
				return "The error file contains no data.";
		}
		else
		{
			return "The error has been cleared by another user.";
		}
	}

	protected void ReprocessFile_Command(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null)
		{
			QueueErrors.ReprocessError(e.CommandArgument.ToString());
			ErrorGrid.DataBind();
		}
	}
	
	protected void ReprocessAllBtn_Click(object sender, EventArgs e)
	{
		QueueErrors.ReprocessAllErrors();
		ErrorGrid.DataBind();
	}

	protected void ClearFile_Command(object sender, CommandEventArgs e)
	{
		if (e.CommandArgument != null)
		{
			QueueErrors.ClearError(e.CommandArgument.ToString());
			ErrorGrid.DataBind();
		}
	}

	protected void ClearAllBtn_Click(object sender, EventArgs e)
	{
		QueueErrors.ClearAllErrors();
		ErrorGrid.DataBind();
	}

	private void PopulateStatCharts()
	{
		int transactionCountTotal = 0;
		int databaseCountTotal = 0;
		int databaseMax = 0;
		int bigZipCountTotal = 0;
		int fileCountTotal = 0;
		int fileCountMax = 0;
		long fileVolumeTotal = 0;
		long fileVolumeMax = 0;
		
		DateTime startDate = DateTime.MinValue;
		if (!string.IsNullOrEmpty(startDateField.SelectedDate.ToString()))
			startDate = (DateTime)startDateField.SelectedDate;

		if (!DateTime.MinValue.Equals(startDate) && startTimeField.SelectedDate.HasValue)
		{
			TimeSpan time = new TimeSpan(((DateTime)startTimeField.SelectedDate).Hour, ((DateTime)startTimeField.SelectedDate).Minute, ((DateTime)startTimeField.SelectedDate).Second);
			startDate = ((DateTime)startDateField.SelectedDate).Add(time);
		}

		DateTime endDate = DateTime.Now;
		if (!string.IsNullOrEmpty(endDateField.SelectedDate.ToString()))
			endDate = (DateTime)endDateField.SelectedDate;

		if (endTimeField.SelectedDate.HasValue)
		{
			TimeSpan time = new TimeSpan(((DateTime)endTimeField.SelectedDate).Hour, ((DateTime)endTimeField.SelectedDate).Minute, ((DateTime)endTimeField.SelectedDate).Second);
			endDate = ((DateTime)endDateField.SelectedDate).Add(time);
		}

		List<StatResult> transactionResultEntries = QueueStatistics.GetDatabaseStats(startDate, endDate, (int)RecordTypeEnum.TRANSACTION);
		foreach (StatResult result in transactionResultEntries)
		{
			transactionCountTotal += result.Count;
		}

		List<StatResult> databaseResultEntries = QueueStatistics.GetDatabaseStats(startDate, endDate, 0);
		Series databaseSeries = new Series("Entries");
		databaseSeries.Type = SeriesChartType.Area;

		foreach (StatResult result in databaseResultEntries)
		{
			DataPoint dp = new DataPoint();
			dp.SetValueY(0, result.Count);
			dp.AxisLabel = result.StartTime.ToString("MM/dd\nh:mm tt");
			databaseSeries.Points.Add(dp);

			databaseCountTotal += result.Count;
			if (databaseMax < result.Count)
				databaseMax = result.Count;
		}

		List<StatResult> bigZipResultEntries = QueueStatistics.GetFileStats(startDate, endDate, (int)FileTypeEnum.BIG_ZIP);
		foreach (StatResult result in bigZipResultEntries)
		{
			bigZipCountTotal += result.Count;
		}

		List<StatResult> fileResultEntries = QueueStatistics.GetFileStats(startDate, endDate, 0);
		Series fileCountSeries = new Series("Entries");
		Series fileVolumeSeries = new Series("Entries");
		fileCountSeries.Type = SeriesChartType.Area;
		fileVolumeSeries.Type = SeriesChartType.Area;

		foreach (StatResult result in fileResultEntries)
		{
			DataPoint countDP = new DataPoint();
			countDP.SetValueY(0, result.Count);
			countDP.AxisLabel = result.StartTime.ToString("MM/dd\nh:mm tt");
			fileCountSeries.Points.Add(countDP);

			DataPoint volumeDP = new DataPoint();
			double curVol = ((double)result.Volume / 1024.0 / 1024.0);
			volumeDP.SetValueY(0, curVol);
			volumeDP.AxisLabel = result.StartTime.ToString("MM/dd\nh:mm tt");
			fileVolumeSeries.Points.Add(volumeDP);

			fileVolumeTotal += result.Volume;
			fileCountTotal += result.Count;
			if (fileCountMax < result.Count)
				fileCountMax = result.Count;

			if (fileVolumeMax < result.Volume)
				fileVolumeMax = result.Volume;
		}

		databaseChart.Series.Add(databaseSeries);
		fileCountChart.Series.Add(fileCountSeries);
		fileVolumeChart.Series.Add(fileVolumeSeries);

		FormatChartArea(databaseChart.ChartAreas[0], databaseMax, databaseSeries.Points.Count);
		FormatChartArea(fileCountChart.ChartAreas[0], fileCountMax, fileCountSeries.Points.Count);
		FormatChartArea(fileVolumeChart.ChartAreas[0], ((double)fileVolumeMax / 1024.0 / 1024.0), fileVolumeSeries.Points.Count);

		DatabaseCountLabel.Text = databaseCountTotal.ToString("#,##0") + " records<br />" + transactionCountTotal.ToString("#,##0") + " transactions";
		FileCountLabel.Text = fileCountTotal.ToString("#,##0") + " files<br />" + bigZipCountTotal.ToString("#,##0") + " Big Zip";
		FileVolumeLabel.Text = ((double)fileVolumeTotal / 1024.0 / 1024.0).ToString("#,##0.#") + " MB";
	}

	private void FormatChartArea(ChartArea area, double max, int pointCnt)
	{
		area.BackColor = ColorTranslator.FromHtml("#f2f2f2");
		
		area.AxisX.LineColor = Color.Black;
		area.AxisX.MajorTickMark.LineColor = Color.Black;
		area.AxisX.LabelStyle.FontColor = Color.Black;
		area.AxisX.LabelStyle.Font = new System.Drawing.Font("Verdana", 8);
		area.AxisX.Interval = Math.Ceiling(pointCnt / 2.0) - 1;
		area.AxisX.Margin = false;

		area.AxisY.Maximum = RoundAxis(max, 1000);
		area.AxisY.Interval = Math.Ceiling(area.AxisY.Maximum / 2.0);
		area.AxisY.LineColor = Color.Black;
		area.AxisY.MajorTickMark.LineColor = Color.Black;
		area.AxisY.LabelStyle.FontColor = Color.Black;
		area.AxisY.LabelStyle.Font = new System.Drawing.Font("Verdana", 8);
	}

	protected int RoundAxis(double newMax, int defaultValue)
	{
		int retVal = 0;
		double smallMax = 0;

		int len = ((int)Math.Ceiling(newMax)).ToString().Length - 2;

		if (len > 0)
			smallMax = Math.Ceiling(Math.Ceiling((double)newMax / (Math.Pow(10, len))));
		else
			smallMax = Math.Ceiling((double)newMax);

		smallMax++;

		if ((smallMax % 5) > 0)
			smallMax = (smallMax + 5 - (smallMax % 5));

		if (len > 0)
			retVal = (int)(smallMax * Math.Pow(10, len));
		else
		{
			retVal = (int)smallMax;
			if (retVal == 5 && newMax <= 2)
				retVal = 2;
			else if (retVal == 5 && newMax <= 4)
				retVal = 4;
			else if (retVal == 5)
				retVal = 6;
		}

		if (retVal <= 0)
			retVal = defaultValue;

		return retVal;
	}
}

