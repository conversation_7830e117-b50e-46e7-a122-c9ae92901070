<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="UploadEngFile.aspx.cs" Inherits="UploadEngFile" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<asp:panel id="DefaultPanel" runat="server" defaultbutton="uploadButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Upload Engineering Files</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Upload files</div>					
					<br />
					<table border="0" runat="server" id="UploadTable" cellpadding="0" cellspacing="0">
						<tr>
							<td colspan="3" style="padding:10px 10px 10px 14px;">
								<b>Select a Device: &nbsp;</b><asp:dropdownlist id="deviceList" runat="server"></asp:dropdownlist>
								<asp:requiredfieldvalidator id="Requiredfieldvalidator1" runat="server" controltovalidate="deviceList" display="dynamic" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator>
								<br />
								<br />
								<hr />
							</td>
						</tr>
						<tr>
							<td colspan="3" style="padding:5px 10px 30px 14px;">
								<asp:fileupload id="fileUpload1" width="350" runat="server" cssclass="entryControl" />&nbsp;
								<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="fileUpload1" display="dynamic" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator>
								<br /><br />
								<asp:fileupload id="fileUpload2" width="350" runat="server" cssclass="entryControl" />
								<br /><br />
								<asp:fileupload id="fileUpload3" width="350" runat="server" cssclass="entryControl" />
								<br /><br />
								<asp:fileupload id="fileUpload4" width="350" runat="server" cssclass="entryControl" />
								<br /><br />								
							</td>
						</tr>
						<tr>
							<td style="width:80px;padding-right:14px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="UploadButton_Click" id="uploadButton">Upload</asp:linkbutton></div></td>
							<td style="width:80px;"><div class="cancelButton"><a onclick="return confirm('Cancel file upload?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
							<td style="width:100%;">&nbsp;</td>
						</tr>
					</table>
					<asp:label style="padding:10px 10px 30px 14px;color:#cc0000;" id="NoDevicesLabel" runat="server" visible="false"><b>There are no devices on file for this transaction.  Files cannot be uploaded.</b></asp:label>
					<br />&nbsp;<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

