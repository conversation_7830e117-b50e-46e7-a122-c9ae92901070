using System;
using System.Data;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;

public partial class Files : System.Web.UI.Page
{
	public Int64 TranId
	{
		get { if (this.ViewState["t"] != null) return (Int64)this.ViewState["t"]; else return 0; }
		set { this.ViewState["t"] = value; }
	}

	public Int64 ObservationId
	{
		get { if (this.ViewState["o"] != null) return (Int64)this.ViewState["o"]; else return 0; }
		set { this.ViewState["o"] = value; }
	}

	public DateTime TransactionDate
	{
		get { if (this.ViewState["td"] != null) return (DateTime)this.ViewState["td"]; else return DateTime.MinValue; }
		set { this.ViewState["td"] = value; }
	}

	public string RedirectionUrl
	{
		get { return (string)this.ViewState["r"]; }
		set { this.ViewState["r"] = value; }
	}

    public string SessionName
    {
        get { return (string)this.ViewState["ses"]; }
        set { this.ViewState["ses"] = value; }
    }

    public string CellName
    {
        get { return (string)this.ViewState["cn"]; }
        set { this.ViewState["cn"] = value; }
    }

    public string DeviceTypeName
    {
        get { return (string)this.ViewState["dtn"]; }
        set { this.ViewState["dtn"] = value; }
    }

    public int DeviceTypeId
    {
        get { if (this.ViewState["dti"] != null) return (int)this.ViewState["dti"]; else return 0; }
        set { this.ViewState["dti"] = value; }
    }

    public string SerialNumber
    {
        get { return (string)this.ViewState["ser"]; }
        set { this.ViewState["ser"] = value; }
    }

    public int RdToolTranId
    {
        get { if (this.ViewState["rd"] != null) return (int)this.ViewState["rd"]; else return 0; }
        set { this.ViewState["rd"] = value; }
    }


    protected void Page_Load(object sender, EventArgs e)
    {
		if (!Page.IsPostBack)
		{
			if (Request.UrlReferrer != null)
				this.RedirectionUrl = Request.UrlReferrer.PathAndQuery;
			else
				this.RedirectionUrl = "Search.aspx";

			if (!string.IsNullOrEmpty(Request.Params["o"]) && Convert.ToInt64(Request.Params["o"]) != 0)
				this.ObservationId = Convert.ToInt64(Request.Params["o"]);

			if (!string.IsNullOrEmpty(Request.Params["t"]) && Convert.ToInt64(Request.Params["t"]) != 0)
				this.TranId = Convert.ToInt64(Request.Params["t"]);

			if (this.ObservationId != 0)
			{
				DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservation", this.ObservationId);
                if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0) {
                    this.TranId = DataFormatter.getInt64(ds.Tables[0].Rows[0], "TranId");
                    this.SessionName = DataFormatter.getString(ds.Tables[0].Rows[0], "SessionName");
                    this.CellName = DataFormatter.getString(ds.Tables[0].Rows[0], "CellName");
                    this.DeviceTypeName = DataFormatter.getString(ds.Tables[0].Rows[0], "DeviceTypeName");
                    this.SerialNumber = DataFormatter.getString(ds.Tables[0].Rows[0], "SerialNumber");
                    this.DeviceTypeId = DataFormatter.getInt32(ds.Tables[0].Rows[0], "DeviceTypeId");
                    this.RdToolTranId = DataFormatter.getInt32(ds.Tables[0].Rows[0], "RdToolTranId");
                    this.TransactionDate = DataFormatter.getDateTime(ds.Tables[0].Rows[0], "TranDate");
                }
			}
			else if (this.TranId != 0)
			{
                //load the observation id to keep pictures in sync if there is an observation for this transaction
                DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservationInfoForTransaction", this.TranId);
                if (ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0) {
                    this.ObservationId = DataFormatter.getInt64(ds.Tables[0].Rows[0], "ObservationId");
                    this.SessionName = DataFormatter.getString(ds.Tables[0].Rows[0], "SessionName");
                    this.CellName = DataFormatter.getString(ds.Tables[0].Rows[0], "CellName");
                    this.DeviceTypeName = DataFormatter.getString(ds.Tables[0].Rows[0], "DeviceTypeName");
                    this.SerialNumber = DataFormatter.getString(ds.Tables[0].Rows[0], "SerialNumber");
                    this.DeviceTypeId = DataFormatter.getInt32(ds.Tables[0].Rows[0], "DeviceTypeId");
                    this.RdToolTranId = DataFormatter.getInt32(ds.Tables[0].Rows[0], "RdToolTranId");
                    this.TransactionDate = DataFormatter.getDateTime(ds.Tables[0].Rows[0], "TranDate");
                }
            }

            if (this.TranId == 0) {
                ShowPanel("error");
            }
		}
    }

	protected void UploadButton_Click(object sender, EventArgs e)
	{
		if (Page.IsValid)
		{
			UploadFile(FileUpload1);

			if(!string.IsNullOrEmpty(FileUpload2.FileName))
				UploadFile(FileUpload2);

			if (!string.IsNullOrEmpty(FileUpload3.FileName))
				UploadFile(FileUpload3);

			if (!string.IsNullOrEmpty(FileUpload4.FileName))
				UploadFile(FileUpload4);
		}
	}

	private void UploadFile(FileUpload currentFile)
	{
		if ((currentFile != null) && (currentFile.PostedFile != null) && (currentFile.PostedFile.ContentLength > 0))
		{
			bool isPicture = false;
			string extension = Path.GetExtension(currentFile.PostedFile.FileName);
			string fileName = Path.GetFileName(currentFile.PostedFile.FileName);
			
			switch (extension.ToLower())
			{
				case ".jpg":
				case ".jpeg":
				case ".jpe":
				case ".png":
				case ".gif":
				case ".bmp":
					isPicture = true;
					break;
			}

			if (extension.ToLower().Equals(".gdf"))
				fileName = "TRX=" + this.RdToolTranId.ToString() + "_DV=" + this.DeviceTypeName + "_CD=" + this.DeviceTypeId.ToString() + "_SN=" + this.SerialNumber + "_" + fileName;
			else
				fileName = Path.GetFileNameWithoutExtension(fileName) + "_TRX=" + this.RdToolTranId.ToString() + "_DV=" + this.DeviceTypeName + "_CD=" + this.DeviceTypeId.ToString() + "_SN=" + this.SerialNumber + Path.GetExtension(fileName);

			Stream stream = currentFile.PostedFile.InputStream;
			byte[] contentBuffer = null;
			int offset = 0;
			try
			{
				contentBuffer = new byte[stream.Length];
				int bytesRead = stream.Read(contentBuffer, offset, contentBuffer.Length - offset);
				offset += bytesRead;
				while (bytesRead > 0 && contentBuffer.Length > offset)
				{
					bytesRead = stream.Read(contentBuffer, offset, contentBuffer.Length - offset);
					offset += bytesRead;
				}
			}
			finally
			{
				stream.Close();
			}

			//Save the file to the Server
			QueueServiceClient.TxFiles.UploadFile(contentBuffer, fileName, isPicture, this.TranId, this.ObservationId, this.SessionName, this.CellName, this.TransactionDate);
			
			SuccessMessage.Visible = true;
		}
	}

	private void popupError(string errorMessage)
	{
		if (!string.IsNullOrEmpty(errorMessage))
		{
			string errorScript = "<script language='javascript'>\r\nalert('" + errorMessage + "');\r\n</script>";
			Page.ClientScript.RegisterStartupScript(typeof(FileUpload), "ErrorScript", errorScript);
		}
	}

	protected void ShowUploadButton_Click(object sender, EventArgs e)
	{
		ShowPanel("upload");
	}

	protected void CloseButton_Click(object sender, EventArgs e)
	{
		ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("CloseRadWindow(); GetRadWindow().BrowserWindow.location.href='{0}';", this.RedirectionUrl), true);
	}
	
	private void ShowPanel(string panelName)
	{
		switch (panelName)
		{
			case "upload":
				FileUploadDiv.Attributes.Add("style", "display:block");
				ErrorDiv.Attributes.Add("style", "display:none");
				SuccessMessage.Visible = false;
				break;
			case "error":
				FileUploadDiv.Attributes.Add("style", "display:none");
				ErrorDiv.Attributes.Add("style", "display:block");
				SuccessMessage.Visible = false;
				break;
		}
	}
}
