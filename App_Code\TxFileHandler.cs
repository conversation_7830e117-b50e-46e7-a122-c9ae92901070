﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using QueueServiceClient;

public class TxFileHandler : IHttpHandler
{
	public void ProcessRequest(HttpContext context)
	{
		HttpResponse Response = context.Response;
		HttpRequest Request = context.Request;

		bool isData = false;
		bool isImage = false;
		Int64 tranId = 0;
		Int64 observationId = 0;
		string deviceTypeName = "";
		string serialNumber = "";
		DateTime tranDate = DateTime.MinValue;

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FILE_IS_DATA_KEY]))
			isData = true;

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.FILE_IS_IMAGE_KEY]))
			isImage = true;

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.TRAN_ID_KEY]))
			tranId = Convert.ToInt64(Request.Params[DieboldConstants.TRAN_ID_KEY]);

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.OBSERVATION_ID_KEY]))
			observationId = Convert.ToInt64(Request.Params[DieboldConstants.OBSERVATION_ID_KEY]);

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_NAME_KEY]))
			deviceTypeName = Request.Params[DieboldConstants.DEVICE_TYPE_NAME_KEY];

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.SERIAL_NUMBER_KEY]))
			serialNumber = Request.Params[DieboldConstants.SERIAL_NUMBER_KEY];

		if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.TRAN_DATE_KEY]))
			tranDate = Convert.ToDateTime(Request.Params[DieboldConstants.TRAN_DATE_KEY]);

		//look up info by tranId
		if (string.IsNullOrEmpty(deviceTypeName) && tranId > 0)
		{
			DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadTransaction", tranId);

			foreach (DataRow row in ds.Tables[0].Rows)
			{
				observationId = DataFormatter.getInt64(row, "ObservationId");
				deviceTypeName = DataFormatter.getString(row, "DeviceTypeName");
				serialNumber = DataFormatter.getString(row, "SerialNumber");
				tranDate = DataFormatter.getDateTime(row, "TranDate");
			}
		}

		//look up info by observationId
		if (string.IsNullOrEmpty(deviceTypeName) && observationId > 0)
		{
			DataSet ds = SqlHelper.ExecuteDataset("RPT_LoadObservation", observationId);

			foreach (DataRow row in ds.Tables[0].Rows)
			{
				tranId = DataFormatter.getInt64(row, "TranId");
				deviceTypeName = DataFormatter.getString(row, "DeviceTypeName");
				serialNumber = DataFormatter.getString(row, "SerialNumber");
				tranDate = DataFormatter.getDateTime(row, "TranDate");
			}
		}

		DataFile result = TxFiles.DownloadTxFile(context.Server.UrlDecode(Request.Params[DieboldConstants.FILE_NAME_KEY]), isImage, isData, deviceTypeName, serialNumber, Convert.ToDateTime(tranDate), tranId);

		if (result != null && result.FileData.Length > 0)
		{
			Response.ClearContent();
			Response.ClearHeaders();
			Response.ContentType = result.MimeType;
			Response.AppendHeader("content-disposition", "attachment; filename=" + result.FileName + "; size=" + result.FileSize.ToString() + ";");
			Response.BinaryWrite(result.FileData);
			Response.End();
		}
		else
		{
			throw new ApplicationException("Unable to find the requested file. " + Request.Params[DieboldConstants.FILE_NAME_KEY]);
		}
	}

	public bool IsReusable
	{
		get { return true; }
	}
}
