﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using QueueServiceClient;

public partial class UploadFieldDefinition : System.Web.UI.Page
{
	public const int MAX_FILE_SIZE = (10000 * 1024); // 10 MB

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!IsPostBack)
		{
			deviceTypeIdList.DataSource = Utility.GetDeviceTypeList();
			deviceTypeIdList.DataBind();
		}
	}

	protected void UploadBtn_Click(object sender, EventArgs e)
	{
		successLbl.Visible = false;
		if ((fileUploadCntrl != null) && (fileUploadCntrl.PostedFile != null) && (fileUploadCntrl.PostedFile.ContentLength > 0))
		{
			//validate file size
			if (fileUploadCntrl.PostedFile.ContentLength > MAX_FILE_SIZE)
			{
				successLbl.Text = "Your file size exceeds the " + MAX_FILE_SIZE / 1024 + " Mb size limit. Please lower the file size and try again.";
				successLbl.Visible = true;
				return;
			}

			string fileName = Path.GetFileName(fileUploadCntrl.PostedFile.FileName);
			string[] fileNameParts = fileName.Split(new string[] { "." }, StringSplitOptions.RemoveEmptyEntries);

			//validate file extension
			if (string.Compare(fileNameParts[1].ToLower(), "xml") != 0)
			{
				successLbl.Text = "Invalid file type, only 'xml' files are accepted.";
				successLbl.Visible = true;
			}
			else
			{
				//send file to queue service to be processed.
				UploadClient uploadClient = new UploadClient();
				bool success = uploadClient.UploadData(string.Format("AMI_DEFINITION_DEVICE={0}_AMIVERSION={1}.xml", deviceTypeIdList.SelectedValue, amiVersion.Text.Trim()), fileUploadCntrl.FileBytes);

				if (success)
				{
					deviceTypeIdList.SelectedIndex = -1;
					amiVersion.Text = "";
					successLbl.Text = "Your file has been uploaded successfully.";
					successLbl.Visible = true;
				}
				else
				{
					successLbl.Text = "The Queue Service is offline, please try your upload later.";
					successLbl.Visible = true;
				}
			}
		}
	}
}
