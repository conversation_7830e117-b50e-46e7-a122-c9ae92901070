﻿<%@ Page Language="C#" MasterPageFile="~/PopupMasterPage.master" AutoEventWireup="true" CodeFile="NewReportWizard_PRST.aspx.cs" Inherits="NewReportWizard_PRST" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>
<%@ register tagprefix="rpt" tagname="DimensionSelector" src="~/controls/DimensionSelector.ascx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
    function updateCustomizeRisk() {
        if ($('#<%= customizeCheck.ClientID %>').is(':checked')) {
            $('#<%= discrimRatioField.ClientID %>').removeAttr('disabled');
            $('#<%= producerRiskField.ClientID %>').removeAttr('disabled');
        } else {
            $('#<%= discrimRatioField.ClientID %>').val('1.5').attr('disabled', 'disabled');
            $('#<%= producerRiskField.ClientID %>').val('0.1').attr('disabled', 'disabled');
            updateRiskChoices();
        }
    }
    function updateAllInOneFormat() {
        if ($('#<%= allInOneFormat.ClientID %>').is(':checked')) {
            $('#<%= numberOfUnits.ClientID %>').removeAttr('disabled');
        } else {
            $('#<%= numberOfUnits.ClientID %>').val('').attr('disabled', 'disabled');
        }
    }
    function updateRiskChoices(isInitialLoad) {
        var parent = $('#<%= discrimRatioField.ClientID %>');
        var d = parent.val();
        var list = $('#<%= producerRiskField.ClientID %>');
        var a = list.val();

        $('#<%= producerRiskValue.ClientID %>').val('0.1'); //reset value
        list.empty(); //empty the list and rebuild it with allowed choices for parent
        list.append('<option value="0.1" selected="selected">10</option>'); //reset selection
        if (d == 1.5 || d == 2) {
            list.append('<option value="0.2">20</option>');
            list.append('<option value="0.3">30</option>');
        } else if (d == 3) {
            list.append('<option value="0.2">20</option>');
        }

        if (isInitialLoad) {
            $('#<%= producerRiskField.ClientID %>').val(a);
            $('#<%= producerRiskValue.ClientID %>').val(a);
        }
    }
    function updateRiskValue() {
        $('#<%= producerRiskValue.ClientID %>').val($('#<%= producerRiskField.ClientID %>').val());
    }
    $(function () {
        $('#<%= discrimRatioField.ClientID %>').change(updateRiskChoices);
        updateRiskChoices(true);

        $('#<%= customizeCheck.ClientID %>').change(updateCustomizeRisk);
        updateCustomizeRisk();

        $('#<%= producerRiskField.ClientID %>').change(updateRiskValue);
        updateRiskValue();

        $('#<%= allInOneFormat.ClientID %>').change(updateAllInOneFormat);
        updateAllInOneFormat();
    });
</script>

<asp:panel id="DefaultPanel" runat="server" defaultbutton="nextButton">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Create New Report</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">Step 2 of 4 &nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleBlue.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" />&nbsp;&nbsp;<img src="images/circleWhite.png" width="8" height="8" alt="" /></td>
					</tr>
				</table>				
				<div class="widget">
					<div class="title" style="padding-bottom:2px;">Step 2: Report Attributes</div>
					<div class="title" style="padding-top:0px;padding-bottom:0px; font-weight:normal;"><asp:label id="reportLabel" runat="server"></asp:label> | <asp:label id="reportTypeLabel" runat="server"></asp:label></div>
					
					<%--<div class="rowHeading">Observation Types:</div>--%>
					<table border="0" cellpadding="0" cellspacing="10" style="padding-top:3px;width:100%;">
						<tr>
							<td style="width:200px;" valign="top">
								<div class="rowHeading">Select Dimension:</div>
								<div class="leftPad" style="padding-top:15px;">
									All data items will display in the final chart unless they are filtered off by unchecking them in the list. 
								</div>
							</td>
							<td valign="top">
							    <rpt:DimensionSelector id="dimensionSel" runat="server"></rpt:DimensionSelector>
							</td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td>
								<asp:checkbox id="trendlineCheck" runat="server" text="Include Trendline" />
								<br />
								<asp:checkbox id="uncensoredSeriesCheck" runat="server" text="Include uncensored series" />
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top">
								<div class="rowHeading">X-Axis:</div>
							</td>
							<td valign="top">
								<asp:radiobuttonlist runat="server" id="xaxisList" appenddatabounditems="true" datatextfield="Name" datavaluefield="Code">
									<asp:listitem text="Cumulative Transactions" value="1" selected="true"></asp:listitem>
									<asp:listitem text="Cumulative Media (count is only available if collected through RDTool using getMetrics)" value="2"></asp:listitem>
								</asp:radiobuttonlist>
							</td>
						</tr>
						<tr>
							<td style="width:200px;" valign="top"><div class="rowHeading">Report Goals:</div></td>
							<td valign="top">
								<span style="font-size:14px; font-family:Microsoft Sans Serif;padding-left:8px;">θ</span>0:
								<asp:textbox id="mtbfSpecField" runat="server" cssclass="entryControl" text="17000"></asp:textbox> MTBF Specification
								<asp:requiredfieldvalidator id="val1" runat="server" controltovalidate="mtbfSpecField" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator>
								
								<div style="padding-left:5px;padding-top:5px;">
									<asp:checkbox causesvalidation="false" id="customizeCheck" runat="server" text="Customize Ratio & Risk" />
								</div>
								
								<table id="CustomizeTable" style="width:100%;" runat="server" border="0" cellpadding="0" cellspacing="0">
									<tr>
                                        <td style="width:15px;padding-left:30px;padding-top:10px;"><span style="font-size:14px; font-family:Microsoft Sans Serif;">&nbsp; </span></td>
										<td style="padding-top:10px;width:110px;">
                                            <asp:dropdownlist id="discrimRatioField" runat="server" style="width:100px;" enabled="false">
                                                <asp:listitem text="1.5" value="1.5" selected="True"></asp:listitem>
                                                <asp:listitem text="2" value="2"></asp:listitem>
                                                <asp:listitem text="3" value="3"></asp:listitem>
                                                <asp:listitem text="5" value="5"></asp:listitem>
                                            </asp:dropdownlist>
										</td>
										<td style="padding-top:10px;">Discrimination Ratio <asp:requiredfieldvalidator id="val2" runat="server" controltovalidate="discrimRatioField" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator></td>
									</tr>
									<tr>
										<td style="width:15px;padding-left:30px;padding-top:10px;"><span style="font-size:14px; font-family:Microsoft Sans Serif;">&nbsp; </span></td>
                                        <td style="width:110px;">
                                            <asp:dropdownlist id="producerRiskField" runat="server" style="width:100px;" enabled="false">
                                                <asp:listitem text="10" value="0.1" selected="true"></asp:listitem>
                                                <asp:listitem text="20" value="0.2"></asp:listitem>
                                                <asp:listitem text="30" value="0.3"></asp:listitem>
                                            </asp:dropdownlist>
                                            <asp:hiddenfield id="producerRiskValue" runat="server" value="0.1" />
										</td>
										<td>Risk Percent <asp:requiredfieldvalidator id="val3" runat="server" controltovalidate="producerRiskField" errormessage="* Required" cssclass="error"></asp:requiredfieldvalidator></td>
									</tr>
								</table>

                                <div style="padding-left:5px;padding-top:5px;">
								    <asp:checkbox id="allInOneFormat" runat="server" text="All in One Format" />
							    </div>
                                <table id="Table1" style="width:100%;" runat="server" border="0" cellpadding="0" cellspacing="0">
									<tr>
                                        <td style="width:15px;padding-left:30px;padding-top:10px;">&nbsp;</td>
										<td>
                                            <asp:textbox id="numberOfUnits" runat="server" style="margin-top:10px;width:95px;"></asp:textbox> # of Units
										</td>
                                        <td>&nbsp;</td>
									</tr>
                                </table>
							</td>
						</tr>
					</table>
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad"><div class="goButton"><asp:linkbutton runat="server" onclick="NextButton_Click" id="nextButton">Next</asp:linkbutton></div></td>
							<td><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to exit the report wizard?');" href="javascript:CloseRadWindow();">Cancel</a></div></td>
						</tr>
					</table>
					<br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>
