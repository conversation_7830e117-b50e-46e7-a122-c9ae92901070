using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

public partial class Popup_EditEventType : System.Web.UI.Page
{
	public string PageNo
	{
		get { return (string)this.ViewState["p"]; }
		set { this.ViewState["p"] = value; }
	}

	protected void Page_Load(object sender, EventArgs e)
	{
		if (!Page.IsPostBack)
		{
			if (!string.IsNullOrEmpty(Request.Params["page"]))
				this.PageNo = Request.Params["page"];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.DEVICE_TYPE_KEY]))
			{
				this.deviceTypeIdField.Text = Request.Params[DieboldConstants.DEVICE_TYPE_KEY];
			}
			else
			{
				ContentRow.Visible = false;
				ErrorRow.Visible = true;
			}

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.EVENT_TYPE_NAME_KEY]))
				this.eventNameField.Text = Request.Params[DieboldConstants.EVENT_TYPE_NAME_KEY];

			if (!string.IsNullOrEmpty(Request.Params[DieboldConstants.EVENT_TYPE_KEY]))
			{
				this.eventIdField.Enabled = false;
				this.eventIdField.Text = Request.Params[DieboldConstants.EVENT_TYPE_KEY];
				this.eventNameField.Focus();
			}
			else
			{
				this.eventIdField.Focus();
			}
		}
	}

	protected void SaveButton_Click(object sender, EventArgs e)
	{
		int result = 1;

		if (eventIdField.Enabled)
			result = (int)SqlHelper.ExecuteScalar("RPT_InsertEventType", deviceTypeIdField.Text, eventIdField.Text, eventNameField.Text);
		else
			SqlHelper.ExecuteNonQuery("RPT_UpdateEventType", deviceTypeIdField.Text, eventIdField.Text, eventNameField.Text);

		if (result == 0)
		{
			this.ErrorLabel.Visible = true;
		}
		else
		{
			if (!string.IsNullOrEmpty(this.PageNo) && this.PageNo != "0")
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='Events.aspx?{0}={1}&page={2}';CloseRadWindow(); ", DieboldConstants.DEVICE_TYPE_KEY, this.deviceTypeIdField.Text, this.PageNo), true);
			else
                ScriptManager.RegisterStartupScript(this, this.GetType(), "close", string.Format("GetRadWindow().BrowserWindow.document.location.href='Events.aspx?{0}={1}';CloseRadWindow(); ", DieboldConstants.DEVICE_TYPE_KEY, this.deviceTypeIdField.Text), true);
		}
	}
}
