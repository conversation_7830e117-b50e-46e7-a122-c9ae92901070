using System;

/// <summary>
/// Summary description for TypeCodeEntry
/// </summary>
public class TypeCodeEntry
{
	private string code = null;
	private string name = null;

	public TypeCodeEntry(string code, string name) { this.Code = code; this.Name = name; }

	public string Code
	{
		get { return this.code; }
		set { this.code = value; }
	}

	public string Name
	{
		get { return this.name; }
		set { this.name = value; }
	}
}
