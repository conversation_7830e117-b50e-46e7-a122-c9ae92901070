﻿<%@ Page Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="EditJiraSyncSchedule.aspx.cs" Inherits="EditJiraSyncSchedule" %>
<%@ register tagprefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI" %>

<asp:Content ID="Content1" ContentPlaceHolderID="BodyContent" Runat="Server">

<script type="text/javascript">
	function ToggleStartDatePopup() { showDateUp($find("<%= startDateField.ClientID %>")); }  
	function ToggleStartTimePopup() { showTimeUp($find("<%= startTimeField.ClientID %>")); }  
	function ToggleEndDatePopup() { showDateUp($find("<%= endDateField.ClientID %>")); }
	function ToggleEndTimePopup() { showTimeUp($find("<%= endTimeField.ClientID %>")); }  
    function showDateUp(picker)
    {
        picker.showPopup();
    }
    function showTimeUp(picker)
    {
        picker.showTimePopup();
    }
</script>

<telerik:radajaxmanager ID="RadAjaxManager1" runat="server">
	<ajaxsettings>
		<telerik:ajaxsetting ajaxcontrolid="StartDateTypeList">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="startAjax" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="EndDateTypeList" />
			</updatedcontrols>
		</telerik:ajaxsetting>
		<telerik:ajaxsetting ajaxcontrolid="EndDateTypeList">
			<updatedcontrols>
				<telerik:ajaxupdatedcontrol controlid="endAjax" loadingpanelid="LoadingPanel1" />
				<telerik:ajaxupdatedcontrol controlid="StartDateTypeList" />
			</updatedcontrols>
		</telerik:ajaxsetting>
	</ajaxsettings>
</telerik:radajaxmanager>
	
<telerik:RadAjaxLoadingPanel id="LoadingPanel1" height="75px" width="75px" Runat="server" Transparency="10">
	<asp:Image id="Image1" runat="server" ImageUrl="~/images/loading.gif" BorderWidth="0px" AlternateText="Loading"></asp:Image>
</telerik:RadAjaxLoadingPanel>

<asp:panel id="DefaultPanel" runat="server">
	<table width="100%" border="0" cellpadding="0" cellspacing="10">
		<tr>
			<td>
				<table width="100%" border="0" cellpadding="0" cellspacing="0">
					<tr>
						<td class="widgetTitle">Edit Jira Sync Schedule</td>
						<td class="widgetTop" style="width:50%;">&nbsp;</td>
						<td class="widgetTop" style="text-align:right;">&nbsp;</td>
					</tr>
				</table>				
				<div class="widget">
					<table border="0" cellpadding="0" cellspacing="13" width="100%">
						<tr>
							<td class="rowHeading" style="width:150px;">Session</td>
							<td>
								<asp:listbox id="sessionList" runat="server" datatextfield="Name" datavaluefield="Code" rows="12"></asp:listbox>
								<asp:requiredfieldvalidator id="v2" runat="server" controltovalidate="sessionList" errormessage="* Required"></asp:requiredfieldvalidator>
							</td>
						</tr>
						<tr>
							<td class="rowHeading" style="width:150px;">Schedule Run Day(s)</td>
							<td>
								<asp:checkboxlist id="scheduleList" repeatlayout="table" repeatcolumns="4" repeatdirection="Horizontal" runat="server">
									<asp:listitem text="Sunday" value="sunday"></asp:listitem>
									<asp:listitem text="Monday" value="Monday"></asp:listitem>
									<asp:listitem text="Tuesday" value="Tuesday"></asp:listitem>
									<asp:listitem text="Wednesday" value="Wednesday"></asp:listitem>
									<asp:listitem text="Thursday" value="Thursday"></asp:listitem>
									<asp:listitem text="Friday" value="Friday"></asp:listitem>
									<asp:listitem text="Saturday" value="Saturday"></asp:listitem>
								</asp:checkboxlist>
							</td>
						</tr>
                        <tr>
							<td class="rowHeading" style="width:150px;">Scheduled Run Time</td>
							<td>
								<div style="width:120px;">
									<telerik:radtimepicker id="timeField" runat="server" timepopupbutton-visible="true" width="120">
										<dateinput runat="server" dateformat="hh:mm tt"></dateinput>
										<timeview runat="server" interval="00:30:0" columns="4" width="300" height="400" timeformat="hh:mm tt" culture="en-US"></timeview>
									</telerik:radtimepicker>
								</div>
								<asp:requiredfieldvalidator id="v13" runat="server" controltovalidate="timeField" errormessage="* Required"></asp:requiredfieldvalidator>
							</td>
						</tr>
                        <tr>
                            <td colspan="2">
                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                    <tr>
							            <td class="rowHeading" style="width:45%;">Start Date:</td>
							            <td class="rowHeading" style="width:55%;">End Date:</td>
						            </tr>
						            <tr>
							            <td style="padding-left:20px;">
								            <asp:radiobuttonlist id="StartDateTypeList" repeatdirection="horizontal" autopostback="true" onselectedindexchanged="StartDateTypeList_SelectedIndexChanged" runat="server">
									            <asp:listitem text="Fixed" selected="true" value="fixed"></asp:listitem>
									            <asp:listitem text="Relative" value="relative"></asp:listitem>
								            </asp:radiobuttonlist>
							            </td>
							            <td style="padding-left:20px;">
								            <asp:radiobuttonlist id="EndDateTypeList" repeatdirection="horizontal" autopostback="true" onselectedindexchanged="EndDateTypeList_SelectedIndexChanged" runat="server">
									            <asp:listitem text="Fixed" selected="true" value="fixed"></asp:listitem>
									            <asp:listitem text="Relative" value="relative"></asp:listitem>
								            </asp:radiobuttonlist>
							            </td>
						            </tr>
						            <tr>
							            <td style="padding-left:30px;">
								            <table width="100%" cellpadding="0" cellspacing="0" border="0">
									            <tr>
										            <td style="vertical-align:bottom;">
											            <asp:panel id="startAjax" runat="Server">
												            <telerik:RadDatePicker id="startDateField" runat="server" dateinput-dateformat="MM/dd/yyyy" dateinput-displaydateformat="MM/dd/yyyy" style="padding-top:1px;" Width="110px" dateinput-autocompletetype="None" autocomplete="off">        
													            <calendar skin="Default2006" showrowheaders="false"></calendar>       
													            <DatePopupButton Visible="False"></DatePopupButton>
													            <DateInput onclick="ToggleStartDatePopup()"></DateInput>                           	                                             
												            </telerik:RadDatePicker>
												            <asp:dropdownlist id="RelativeStartList" appenddatabounditems="true" style="display:none;" datatextfield="Name" datavaluefield="Code" runat="server">
													            <asp:listitem text="Select..." value=""></asp:listitem>
												            </asp:dropdownlist>
											            </asp:panel>
										            </td>
										            <td style="padding-left:10px;vertical-align:bottom;">
											            <telerik:radtimepicker id="startTimeField" runat="server" timepopupbutton-visible="false" width="80">
												            <dateinput onclick="ToggleStartTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
												            <TimeView Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
											            </telerik:radtimepicker>
										            </td>
										            <td style="width:100%;">&nbsp;</td>
									            </tr>
								            </table>
							            </td>
							            <td style="padding-left:30px;">
								            <table width="100%" cellpadding="0" cellspacing="0" border="0">
									            <tr>
										            <td style="vertical-align:bottom;">
											            <asp:panel id="endAjax" runat="Server">
												            <telerik:RadDatePicker id="endDateField" runat="server" style="padding-top:1px;" Width="110px">        
													            <calendar skin="Default2006" showrowheaders="false"></calendar>       
													            <DatePopupButton Visible="False"></DatePopupButton>
													            <DateInput onclick="ToggleEndDatePopup()"></DateInput>                           	                                             
												            </telerik:RadDatePicker>
												            <asp:dropdownlist id="RelativeEndList" appenddatabounditems="true" style="display:none;" datatextfield="Name" datavaluefield="Code" runat="server">
													            <asp:listitem text="Select..." value=""></asp:listitem>
												            </asp:dropdownlist>
											            </asp:panel>
										            </td>
										            <td style="padding-left:10px;vertical-align:bottom;">
											            <telerik:radtimepicker id="endTimeField" runat="server" timepopupbutton-visible="false" width="80">
												            <dateinput onclick="ToggleEndTimePopup()" dateformat="hh:mm:ss tt"></dateinput>
												            <TimeView Interval="00:30:0" Columns="4" width="300" height="400" TimeFormat="hh:mm tt" Culture="en-US"></TimeView>
											            </telerik:radtimepicker>
										            </td>
										            <td style="width:100%;">&nbsp;</td>
									            </tr>
								            </table>								
							            </td>
						            </tr>
						            <tr>
							            <td colspan="2" style="padding-left:30px;"><asp:label cssclass="error" id="Label1" runat="server" visible="false">The time values must be on the hour or half hour.</asp:label></td>
						            </tr>
                                </table>
                            </td>
                        </tr>
						<tr>
							<td colspan="2" style="padding-left:30px;"><asp:label cssclass="error" id="timeError" runat="server" visible="false">The time values must be on the hour or half hour.</asp:label></td>
						</tr>
					</table>	
					
					<table border="0" cellpadding="0" cellspacing="0">
						<tr>
							<td style="width:80px;" class="leftPad" id="SaveBtnCell" runat="server"><div class="goButton"><asp:linkbutton runat="server" onclick="SaveButton_Click" id="SaveButton">Save</asp:linkbutton></div></td>
							<td style="width:90px;"><div class="cancelButton"><a onclick="return confirm('Are you sure you wish to cancel without saving?');" href="JiraSyncSchedule.aspx">Cancel</a></div></td>
							<td><div class="cancelButton"><asp:linkbutton runat="server" causesvalidation="false" visible="false" onclick="DeleteButton_Click" id="deleteBtn">Delete</asp:linkbutton></div></td>
						</tr>
					</table>
					<br /><br />
				</div>
			</td>
		</tr>
	</table>
</asp:panel>

</asp:Content>

